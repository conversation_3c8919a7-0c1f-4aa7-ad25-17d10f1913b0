timestamp,batch_id,factor_id,expression,error_status,error_message
2025-07-22T14:24:08.435747,2,11,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_90, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:08.864378,2,12,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_90, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:09.417618,2,13,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_20, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:09.810571,2,14,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_20, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:10.128900,2,15,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_20, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:10.562611,2,16,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_270, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:11.339474,2,17,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_270, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:11.659224,2,18,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_270, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:11.995946,2,19,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_30, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:12.318114,2,20,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_30, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:29.226944,3,21,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_30, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:29.608655,3,22,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_120, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:29.917632,3,23,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_120, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:30.549670,3,24,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_120, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:30.907766,3,25,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_360, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:31.314280,3,26,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_360, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:31.654665,3,27,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_360, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:31.985711,3,28,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_720, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:32.398926,3,29,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_720, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:32.706228,3,30,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_720, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:33.368772,4,31,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_60, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:33.679745,4,32,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_60, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:34.596983,4,33,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_60, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:34.908448,4,34,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_180, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:35.223383,4,35,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_180, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:35.807817,4,36,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_150-implied_volatility_put_180, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:36.150623,4,37,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_150, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:36.486917,4,38,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_150, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:36.842948,4,39,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_150, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:37.170510,4,40,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_1080, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:41.417432,5,41,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_1080, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:41.732003,5,42,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_1080, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:42.368158,5,43,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_all, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:42.687257,5,44,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_all, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:43.007132,5,45,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_all, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:43.623850,5,46,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_90, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:43.962723,5,47,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_90, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:44.285103,5,48,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_90, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:44.628080,5,49,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_20, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:44.965875,5,50,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_20, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:56.678146,6,51,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_20, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:57.484996,6,52,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_270, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:57.942244,6,53,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_270, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:24:58.301114,6,54,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_270, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:24:58.858815,6,55,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_30, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:24:59.338077,6,56,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_30, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:00.109586,6,57,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_30, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:00.420481,6,58,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_120, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:00.753694,6,59,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_120, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:01.077887,6,60,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_120, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:08.274353,7,61,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_360, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:08.597227,7,62,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_360, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:08.941612,7,63,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_360, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:09.736759,7,64,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_720, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:10.163884,7,65,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_720, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:10.506892,7,66,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_720, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:11.712532,7,67,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_60, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:12.045873,7,68,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_60, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:12.371020,7,69,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_60, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:12.717009,7,70,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_180, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:30.367101,8,71,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_180, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:30.717461,8,72,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_1080-implied_volatility_put_180, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:31.201316,8,73,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_150, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:31.567685,8,74,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_150, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:31.927130,8,75,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_150, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:32.274985,8,76,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_1080, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:32.606635,8,77,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_1080, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:32.925754,8,78,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_1080, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:33.253707,8,79,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_all, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:33.576195,8,80,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_all, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:36.096236,9,81,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_all, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:36.416859,9,82,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_90, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:36.783626,9,83,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_90, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:37.194301,9,84,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_90, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:37.607389,9,85,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_20, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:37.915997,9,86,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_20, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:38.224623,9,87,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_20, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:38.555184,9,88,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_270, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:39.218370,9,89,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_270, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:39.559868,9,90,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_270, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:41.491656,10,91,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_30, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:41.827693,10,92,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_30, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:42.163575,10,93,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_30, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:42.476910,10,94,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_120, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:43.146962,10,95,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_120, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:43.817060,10,96,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_120, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:44.147317,10,97,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_360, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:44.470162,10,98,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_360, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:44.825079,10,99,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_360, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:45.163489,10,100,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_720, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:52.657404,11,101,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_720, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:52.993434,11,102,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_720, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:53.327087,11,103,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_60, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:25:53.637110,11,104,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_60, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:25:53.945703,11,105,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_60, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:25:58.183224,11,106,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_180, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:02.358521,11,107,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_180, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:02.693782,11,108,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_all-implied_volatility_put_180, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:03.008237,11,109,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_150, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:03.332608,11,110,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_150, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:20.546726,12,111,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_150, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:20.895944,12,112,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_1080, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:21.235934,12,113,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_1080, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:21.839147,12,114,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_1080, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:22.251755,12,115,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_all, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:22.661053,12,116,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_all, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:23.067358,12,117,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_all, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:23.394785,12,118,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_90, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:23.729042,12,119,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_90, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:24.051527,12,120,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_90, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:24.705059,13,121,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_20, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:25.092365,13,122,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_20, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:26.027391,13,123,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_20, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:26.370996,13,124,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_270, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:26.701334,13,125,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_270, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:28.129461,13,126,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_270, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:28.452870,13,127,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_30, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:29.201462,13,128,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_30, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:29.626796,13,129,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_30, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:30.365192,13,130,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_120, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:32.366238,14,131,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_120, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:32.696338,14,132,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_120, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:33.708638,14,133,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_360, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:34.039129,14,134,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_360, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:34.377842,14,135,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_360, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:34.844706,14,136,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_720, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:35.254742,14,137,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_720, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:26:35.666477,14,138,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_720, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:26:35.994949,14,139,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_60, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:26:36.338427,14,140,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_60, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:00.650459,15,141,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_60, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:01.063915,15,142,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_180, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:01.484927,15,143,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_180, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:01.882962,15,144,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_90-implied_volatility_put_180, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:02.617490,15,145,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_150, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:03.031732,15,146,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_150, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:03.726389,15,147,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_150, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:04.134325,15,148,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_1080, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:04.455844,15,149,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_1080, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:04.839531,15,150,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_1080, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:05.613079,16,151,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_all, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:05.934364,16,152,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_all, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:06.297825,16,153,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_all, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:06.811761,16,154,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_90, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:07.167735,16,155,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_90, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:07.524230,16,156,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_90, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:07.857062,16,157,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_20, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:08.207265,16,158,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_20, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:08.788645,16,159,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_20, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:09.127157,16,160,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_270, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:10.074736,17,161,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_270, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:10.407566,17,162,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_270, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:10.911304,17,163,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_30, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:11.256880,17,164,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_30, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:11.667147,17,165,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_30, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:12.028526,17,166,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_120, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:12.383882,17,167,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_120, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:12.761175,17,168,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_120, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:13.348641,17,169,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_360, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:13.967429,17,170,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_360, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:41.869782,18,171,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_360, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:42.663190,18,172,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_720, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:42.994729,18,173,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_720, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:43.467631,18,174,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_720, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:43.851608,18,175,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_60, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:44.324651,18,176,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_60, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:45.111391,18,177,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_60, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:45.500341,18,178,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_180, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:45.852273,18,179,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_180, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:46.279048,18,180,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_20-implied_volatility_put_180, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:47.192579,19,181,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_150, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:47.569666,19,182,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_150, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:47.928347,19,183,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_150, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:48.269391,19,184,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_1080, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:48.677946,19,185,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_1080, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:49.192510,19,186,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_1080, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:49.601745,19,187,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_all, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:27:49.971717,19,188,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_all, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:27:50.405646,19,189,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_all, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:27:50.773145,19,190,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_90, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:28:01.508788,20,191,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_90, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:28:01.848558,20,192,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_90, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:28:02.735284,20,193,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_20, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:28:03.050853,20,194,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_20, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:28:03.730728,20,195,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_20, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:28:04.216750,20,196,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_270, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:28:04.546674,20,197,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_270, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:28:05.676863,20,198,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_270, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:28:06.041691,20,199,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_30, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:28:06.497025,20,200,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_30, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:28:07.138089,21,201,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_30, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:28:08.873270,21,202,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_120, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:28:09.199676,21,203,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_120, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:28:09.541122,21,204,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_120, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:28:09.851776,21,205,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_360, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:28:10.298408,21,206,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_360, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:28:10.846450,21,207,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_360, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:28:11.514167,21,208,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_720, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:28:12.276415,21,209,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_720, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:28:12.595372,21,210,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_720, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:13.263359,22,211,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_60, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:13.667772,22,212,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_60, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:14.182745,22,213,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_60, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:14.693919,22,214,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_180, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:15.042445,22,215,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_180, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:16.313377,22,216,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_270-implied_volatility_put_180, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:16.687790,22,217,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_150, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:17.151538,22,218,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_150, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:17.765659,22,219,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_150, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:18.103046,22,220,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_1080, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:18.972737,23,221,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_1080, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:19.329245,23,222,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_1080, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:19.676372,23,223,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_all, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:20.484029,23,224,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_all, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:20.818539,23,225,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_all, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:21.162569,23,226,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_90, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:22.170708,23,227,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_90, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:22.626208,23,228,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_90, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:22.994159,23,229,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_20, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:23.344079,23,230,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_20, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:24.071494,24,231,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_20, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:24.455418,24,232,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_270, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:24.846095,24,233,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_270, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:25.351228,24,234,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_270, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:25.675894,24,235,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_30, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:26.123234,24,236,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_30, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:27.859893,24,237,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_30, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:28.459212,24,238,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_120, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:28.861900,24,239,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_120, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:29.376200,24,240,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_120, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:48.102529,28,271,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_30, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:49.216996,28,272,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_30, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:49.833005,28,273,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_30, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:51.569447,28,274,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_120, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:52.854477,28,275,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_120, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:53.567545,28,276,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_120, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:53.947608,28,277,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_360, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:29:54.285488,28,278,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_360, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:29:54.628493,28,279,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_360, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:29:54.961707,28,280,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_720, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:01.493652,25,241,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_360, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:01.830492,25,242,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_360, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:02.284615,25,243,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_360, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:02.722391,25,244,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_720, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:03.084944,25,245,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_720, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:03.439741,25,246,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_720, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:03.850003,25,247,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_60, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:04.196009,25,248,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_60, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:04.569350,25,249,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_60, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:05.077400,25,250,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_180, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:08.961837,26,251,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_180, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:09.371508,26,252,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_30-implied_volatility_put_180, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:10.091913,26,253,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_150, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:10.502295,26,254,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_150, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:10.822267,26,255,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_150, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:11.144507,26,256,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_1080, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:11.462076,26,257,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_1080, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:11.793780,26,258,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_1080, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:12.138175,26,259,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_all, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:12.492185,26,260,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_all, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:18.811990,27,261,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_all, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:19.207702,27,262,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_90, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:19.615731,27,263,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_90, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:21.352801,27,264,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_90, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:21.722125,27,265,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_20, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:22.677045,27,266,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_20, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:23.609723,27,267,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_20, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:23.969176,27,268,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_270, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:24.299799,27,269,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_270, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:24.739406,27,270,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_270, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:52.857704,33,321,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_60, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:53.409201,33,322,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_180, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:54.231188,33,323,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_180, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:54.845688,33,324,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_180, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:55.254867,33,325,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_720-implied_volatility_put_150, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:55.877624,33,326,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_720-implied_volatility_put_150, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:56.222117,33,327,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_720-implied_volatility_put_150, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:57.567796,33,328,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_720-implied_volatility_put_1080, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:30:57.900034,33,329,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_720-implied_volatility_put_1080, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:30:58.324756,33,330,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_720-implied_volatility_put_1080, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:30:59.180281,29,281,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_720, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:31:00.232494,29,282,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_720, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:31:01.918970,29,283,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_60, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:31:03.175626,29,284,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_60, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:31:03.557470,29,285,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_60, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:31:03.915072,29,286,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_180, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:31:04.367513,29,287,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_180, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:31:04.777227,29,288,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_120-implied_volatility_put_180, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:31:05.187430,29,289,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_150, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:31:05.533119,29,290,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_150, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:31:06.250069,30,291,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_150, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:31:06.580062,30,292,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_1080, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:31:06.968283,30,293,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_1080, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:31:07.322012,30,294,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_1080, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:31:07.672641,30,295,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_all, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:31:08.011479,30,296,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_all, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:31:08.609125,30,297,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_all, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
2025-07-22T14:31:09.325810,30,298,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_90, -1),5)), 0.5>)",ERROR,"Unexpected character ')' near "",5)), 0.5>)"""
2025-07-22T14:31:09.655494,30,299,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_90, -1),5)), 1>)",ERROR,"Unexpected character ')' near ""1),5)), 1>)"""
2025-07-22T14:31:10.021683,30,300,"signed_power(normalize(ts_backfill(trade_when(pcr_oi_150 >1, implied_volatility_call_360-implied_volatility_put_90, -1),5)), 2>)",ERROR,"Unexpected character ')' near ""1),5)), 2>)"""
