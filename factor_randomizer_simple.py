#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
因子随机化工具 - 简化配置版本
只需修改下面的配置参数，然后直接运行即可
"""

# ==================== 配置参数区域 ====================
# 请根据需要修改以下参数

# 运行模式: 'single', 'mix', 'distribution'
MODE = 'single'

# 单文件模式配置
SINGLE_FILE_CONFIG = {
    'input_file': 'decoded_expressions (18).txt',  # 输入文件路径
    'output_file': 'randomized_factors2.txt',       # 输出文件路径
    'num_factors': None,                            # 要选择的因子数量，None表示全部
}

# 多文件混合模式配置
MIX_FILES_CONFIG = {
    'output_file': 'mixed_factors.txt',            # 输出文件路径
    'total_factors': 200,                          # 最终输出总数，None表示全部
    'files': [                                     # 文件列表配置
        {'file': 'decoded_expressions (12).txt', 'count': 150},
        {'file': 'another_factors.txt', 'count': 100},
        # 可以继续添加更多文件...
    ]
}

# 按比例分布模式配置
DISTRIBUTION_CONFIG = {
    'output_file': 'distributed_factors.txt',      # 输出文件路径
    'files': [                                     # 文件比例配置
        {'file': 'decoded_expressions (12).txt', 'ratio': 0.7},  # 70%
        {'file': 'another_factors.txt', 'ratio': 0.3},           # 30%
        # 可以继续添加更多文件...
    ]
}

# 随机种子（设置为None表示每次随机，设置数字确保结果可重现）
RANDOM_SEED = 42

# 是否显示详细日志
VERBOSE = True

# ==================== 配置参数区域结束 ====================

import random
import os
from typing import List, Dict, Optional

def load_factors_from_file(file_path: str) -> List[str]:
    """从文件加载因子表达式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            factors = [line.strip() for line in f if line.strip()]
        if VERBOSE:
            print(f"从 {file_path} 加载了 {len(factors)} 个因子")
        return factors
    except Exception as e:
        print(f"❌ 加载文件 {file_path} 失败: {str(e)}")
        return []

def save_factors_to_file(factors: List[str], output_file: str) -> bool:
    """保存因子到文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for factor in factors:
                f.write(factor + '\n')
        if VERBOSE:
            print(f"成功保存 {len(factors)} 个因子到 {output_file}")
        return True
    except Exception as e:
        print(f"❌ 保存文件 {output_file} 失败: {str(e)}")
        return False

def randomize_single_file(input_file: str, output_file: str, num_factors: Optional[int] = None) -> bool:
    """随机化单个文件中的因子"""
    factors = load_factors_from_file(input_file)
    if not factors:
        return False
    
    # 随机打乱
    random.shuffle(factors)
    
    # 选择指定数量的因子
    if num_factors is not None:
        factors = factors[:min(num_factors, len(factors))]
        if VERBOSE:
            print(f"选择了 {len(factors)} 个因子")
    
    return save_factors_to_file(factors, output_file)

def mix_and_randomize_files(file_configs: List[Dict], output_file: str, total_factors: Optional[int] = None) -> bool:
    """混合多个文件并随机化"""
    all_factors = []
    
    for config in file_configs:
        file_path = config['file']
        count = config.get('count', None)
        
        factors = load_factors_from_file(file_path)
        if not factors:
            continue
        
        # 随机打乱当前文件的因子
        random.shuffle(factors)
        
        # 选择指定数量
        if count is not None:
            factors = factors[:min(count, len(factors))]
        
        all_factors.extend(factors)
        if VERBOSE:
            print(f"从 {file_path} 添加了 {len(factors)} 个因子")
    
    if not all_factors:
        print("❌ 没有加载到任何因子")
        return False
    
    # 对混合后的因子进行最终随机化
    random.shuffle(all_factors)
    
    # 选择最终数量
    if total_factors is not None:
        all_factors = all_factors[:min(total_factors, len(all_factors))]
        if VERBOSE:
            print(f"最终选择了 {len(all_factors)} 个因子")
    
    return save_factors_to_file(all_factors, output_file)

def randomize_with_distribution(file_configs: List[Dict], output_file: str) -> bool:
    """按照指定分布从多个文件中选择因子"""
    # 首先加载所有文件
    file_factors = {}
    
    for config in file_configs:
        file_path = config['file']
        factors = load_factors_from_file(file_path)
        if factors:
            file_factors[file_path] = factors
    
    if not file_factors:
        print("❌ 没有加载到任何因子")
        return False
    
    # 计算每个文件应该选择的数量
    all_factors = []
    
    for config in file_configs:
        file_path = config['file']
        ratio = config.get('ratio', 0)
        
        if file_path not in file_factors:
            continue
        
        factors = file_factors[file_path]
        count = int(len(factors) * ratio)
        
        # 随机选择
        random.shuffle(factors)
        selected = factors[:count]
        all_factors.extend(selected)
        
        if VERBOSE:
            print(f"从 {file_path} 按比例 {ratio:.2%} 选择了 {len(selected)} 个因子")
    
    # 最终随机化
    random.shuffle(all_factors)
    
    return save_factors_to_file(all_factors, output_file)

def main():
    """主函数"""
    # 设置随机种子
    if RANDOM_SEED is not None:
        random.seed(RANDOM_SEED)
    
    print("🚀 因子随机化工具")
    print(f"运行模式: {MODE}")
    print(f"随机种子: {RANDOM_SEED}")
    print("-" * 50)
    
    success = False
    
    if MODE == 'single':
        print("📁 单文件随机化模式")
        config = SINGLE_FILE_CONFIG
        
        if not os.path.exists(config['input_file']):
            print(f"❌ 输入文件不存在: {config['input_file']}")
            return
        
        print(f"输入文件: {config['input_file']}")
        print(f"输出文件: {config['output_file']}")
        print(f"选择数量: {config['num_factors'] or '全部'}")
        
        success = randomize_single_file(
            config['input_file'], 
            config['output_file'], 
            config['num_factors']
        )
        
    elif MODE == 'mix':
        print("🔀 多文件混合模式")
        config = MIX_FILES_CONFIG
        
        # 检查所有输入文件
        for file_config in config['files']:
            if not os.path.exists(file_config['file']):
                print(f"❌ 输入文件不存在: {file_config['file']}")
                return
        
        print(f"输出文件: {config['output_file']}")
        print(f"最终总数: {config['total_factors'] or '全部'}")
        print("文件配置:")
        for file_config in config['files']:
            print(f"  - {file_config['file']}: {file_config['count']} 个")
        
        success = mix_and_randomize_files(
            config['files'], 
            config['output_file'], 
            config['total_factors']
        )
        
    elif MODE == 'distribution':
        print("📊 按比例分布模式")
        config = DISTRIBUTION_CONFIG
        
        # 检查所有输入文件
        for file_config in config['files']:
            if not os.path.exists(file_config['file']):
                print(f"❌ 输入文件不存在: {file_config['file']}")
                return
        
        print(f"输出文件: {config['output_file']}")
        print("文件比例配置:")
        for file_config in config['files']:
            print(f"  - {file_config['file']}: {file_config['ratio']:.1%}")
        
        success = randomize_with_distribution(
            config['files'], 
            config['output_file']
        )
    
    else:
        print(f"❌ 不支持的模式: {MODE}")
        print("支持的模式: 'single', 'mix', 'distribution'")
        return
    
    print("-" * 50)
    if success:
        print("✅ 随机化完成！")
    else:
        print("❌ 随机化失败！")

if __name__ == "__main__":
    main()
