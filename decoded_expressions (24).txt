signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_scale(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),10),0.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),100),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),40),1)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w2_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),10),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, country, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, sector, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),100),0.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact1_value, sector, 100, std = 4.0),10),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, sector, 100, std = 4.0),200),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, currency, 100, std = 4.0),100),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),100),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, country, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w3_pca_fact2_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w3_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_customer_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, sector, 100, std = 4.0),10),1)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w2_pca_fact2_value, country, 100, std = 4.0),100),1.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, currency, 100, std = 4.0),10),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w3_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),200),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w4_pca_fact2_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w1_pca_fact1_value, country, 100, std = 4.0),100),1)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_std_dev(group_backfill(oth455_customer_roam_w5_pca_fact1_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),40),0.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact1_value, currency, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w4_pca_fact3_value, sector, 100, std = 4.0),40),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, sector, 100, std = 4.0),40),1.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w5_pca_fact3_value, country, 100, std = 4.0),10),1)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, currency, 100, std = 4.0),40),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),40),1)
signed_power( ts_scale(group_backfill(oth455_relation_roam_w5_pca_fact3_value, sector, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, subindustry, 100, std = 4.0),200),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),1)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w3_pca_fact1_value, country, 100, std = 4.0),40),1.5)
signed_power( ts_zscore(group_backfill(oth455_relation_roam_w1_pca_fact3_value, sector, 100, std = 4.0),200),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact2_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),0.5)
signed_power( ts_backfill(group_backfill(oth455_relation_roam_w1_pca_fact3_value, country, 100, std = 4.0),200),1.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w1_pca_fact3_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, subindustry, 100, std = 4.0),40),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),1)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact3_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w5_pca_fact1_value, country, 100, std = 4.0),200),0.5)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_mean(group_backfill(oth455_competitor_roam_w2_pca_fact1_value, subindustry, 100, std = 4.0),40),0.5)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, currency, 100, std = 4.0),100),1.5)
signed_power( ts_av_diff(group_backfill(oth455_competitor_roam_w5_pca_fact2_value, sector, 100, std = 4.0),10),0.5)
signed_power( ts_sum(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w3_pca_fact3_value, country, 100, std = 4.0),10),0.5)
signed_power( ts_mean(group_backfill(oth455_relation_roam_w1_pca_fact2_value, currency, 100, std = 4.0),10),1.5)
signed_power( ts_av_diff(group_backfill(oth455_relation_roam_w4_pca_fact2_value, subindustry, 100, std = 4.0),10),1)
signed_power( ts_delta(group_backfill(oth455_competitor_roam_w4_pca_fact1_value, subindustry, 100, std = 4.0),10),1.5)
signed_power( ts_scale(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, currency, 100, std = 4.0),100),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w4_pca_fact3_value, country, 100, std = 4.0),100),1)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w4_pca_fact2_value, sector, 100, std = 4.0),100),1.5)
signed_power( ts_delta(group_backfill(oth455_relation_roam_w5_pca_fact2_value, currency, 100, std = 4.0),40),0.5)
signed_power( ts_backfill(group_backfill(oth455_competitor_roam_w2_pca_fact3_value, sector, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_customer_roam_w5_pca_fact1_value, currency, 100, std = 4.0),10),1)
signed_power( ts_rank(group_backfill(oth455_competitor_roam_w1_pca_fact1_value, subindustry, 100, std = 4.0),200),0.5)
signed_power( ts_sum(group_backfill(oth455_competitor_roam_w5_pca_fact3_value, currency, 100, std = 4.0),200),1)
signed_power( ts_std_dev(group_backfill(oth455_competitor_roam_w3_pca_fact2_value, currency, 100, std = 4.0),100),1)
signed_power( ts_zscore(group_backfill(oth455_competitor_roam_w1_pca_fact2_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_rank(group_backfill(oth455_relation_roam_w5_pca_fact3_value, subindustry, 100, std = 4.0),40),1.5)
signed_power( ts_std_dev(group_backfill(oth455_relation_roam_w5_pca_fact2_value, country, 100, std = 4.0),200),1.5)