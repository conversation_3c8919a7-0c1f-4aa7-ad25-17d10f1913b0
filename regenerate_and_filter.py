import sqlite3
import random

def get_matrix_fields(db_path):
    """Extracts all MATRIX type alpha_ids from the specified database."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT alpha_id FROM data_ASI_1_MINVOL1M WHERE type = 'MATRIX'")
        rows = cursor.fetchall()
        return [row[0] for row in rows]
    finally:
        conn.close()

def filter_expressions(expressions):
    """Filters out expressions based on a list of exclusion keywords."""
    exclusion_keywords = [
        '_flag', '_xrefmap', 'curperiodnum', 'security_type', 'region',
        'exchange', 'currency', 'country', 'sector', 'industry', 'subindustry',
        'gics', 'sedol', 'cusip', 'isin', 'mic', 'ticker', 'name',
        'description', 'category_name', 'alpha_id', 'cik', 'state', 'fyrc'
    ]
    
    filtered = []
    for expr in expressions:
        if not any(keyword in expr for keyword in exclusion_keywords):
            filtered.append(expr)
    return filtered

def main():
    db_path = '/Users/<USER>/Downloads/新三阶段/alphas.db'
    template_path = '/Users/<USER>/Downloads/新三阶段/alpha_template.txt'
    output_path = '/Users/<USER>/Downloads/新三阶段/filtered_shuffled_expressions.txt'
    
    # 1. Read the new template
    with open(template_path, 'r') as f:
        template = f.read().strip()
    print(f"Using template: {template}")

    # 2. Get MATRIX data fields
    print("Fetching MATRIX data fields...")
    matrix_fields = get_matrix_fields(db_path)
    print(f"Found {len(matrix_fields)} MATRIX data fields.")

    # 3. Define window sizes
    windows = [10, 20, 60, 100, 200, 400]
    all_expressions = []

    # 4. Generate expressions
    print("Generating expressions...")
    for data_field in matrix_fields:
        for short_window in windows:
            for long_window in windows:
                # Ensure long_window is greater than short_window for meaningfulness
                if long_window > short_window:
                    expression = template.replace('<data>', data_field)\
                                         .replace('<short_window>', str(short_window))\
                                         .replace('<long_window>', str(long_window))
                    all_expressions.append(expression)
    print(f"Generated {len(all_expressions)} raw expressions.")

    # 5. Filter expressions
    print("Filtering expressions...")
    filtered = filter_expressions(all_expressions)
    print(f"{len(filtered)} expressions remaining after filtering.")

    # 6. Shuffle expressions
    print("Shuffling expressions...")
    random.shuffle(filtered)

    # 7. Write to output file
    print(f"Writing final expressions to {output_path}...")
    with open(output_path, 'w') as f:
        for expr in filtered:
            f.write(expr + '\n')

    print("Process completed successfully.")

if __name__ == "__main__":
    main()