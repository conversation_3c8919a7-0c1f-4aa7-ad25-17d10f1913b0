ts_zscore(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_zscore(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_zscore(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_zscore(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_zscore(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_zscore(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_zscore(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_zscore(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_zscore(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_zscore(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_zscore(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_zscore(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_zscore(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_zscore(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_zscore(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_zscore(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_zscore(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_zscore(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_zscore(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_zscore(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_zscore(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_zscore(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_zscore(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_zscore(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_zscore(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_zscore(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_zscore(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_zscore(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_zscore(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_zscore(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_zscore(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_zscore(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_zscore(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_zscore(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_zscore(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_zscore(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_zscore(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_zscore(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_zscore(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_zscore(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_zscore(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_zscore(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_zscore(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_zscore(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_zscore(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_zscore(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_zscore(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_zscore(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_zscore(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_zscore(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_zscore(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_zscore(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_zscore(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_zscore(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_zscore(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_zscore(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_zscore(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_zscore(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_zscore(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_zscore(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_zscore(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_zscore(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_zscore(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_zscore(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_zscore(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_zscore(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_zscore(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_zscore(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_zscore(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_zscore(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_zscore(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_zscore(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_zscore(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_zscore(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_zscore(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_zscore(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_zscore(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_zscore(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_zscore(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_zscore(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_zscore(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_zscore(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_zscore(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_zscore(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_zscore(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_zscore(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_zscore(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_zscore(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_zscore(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_zscore(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_zscore(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_zscore(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_zscore(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_zscore(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_zscore(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_zscore(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_zscore(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_zscore(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_zscore(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_zscore(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_zscore(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_zscore(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_zscore(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_zscore(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_zscore(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_zscore(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_zscore(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_zscore(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_zscore(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_zscore(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_zscore(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_zscore(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_zscore(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_zscore(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_zscore(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_zscore(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_zscore(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_zscore(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_zscore(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_zscore(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_zscore(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_zscore(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_zscore(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_zscore(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_zscore(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_zscore(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_zscore(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_zscore(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_zscore(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_zscore(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_zscore(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_zscore(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_zscore(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_zscore(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_zscore(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_zscore(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_zscore(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_zscore(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_zscore(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_zscore(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_zscore(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_zscore(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_zscore(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_zscore(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_zscore(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_zscore(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_zscore(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_zscore(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_zscore(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_zscore(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_zscore(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_zscore(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_zscore(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_zscore(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_zscore(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_zscore(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_zscore(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_zscore(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_zscore(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_zscore(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_zscore(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_zscore(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_zscore(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_zscore(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_zscore(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_zscore(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_zscore(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_zscore(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_zscore(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_zscore(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_zscore(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_zscore(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_zscore(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_zscore(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_zscore(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_zscore(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_zscore(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_zscore(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_zscore(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_zscore(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_zscore(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_zscore(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_zscore(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_zscore(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_zscore(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_zscore(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_zscore(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_zscore(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_zscore(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_zscore(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_zscore(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_zscore(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_zscore(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_zscore(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_zscore(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_zscore(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_zscore(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_zscore(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_zscore(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_zscore(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_zscore(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_zscore(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_zscore(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_zscore(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_zscore(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_zscore(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_zscore(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_zscore(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_zscore(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_zscore(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_zscore(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_zscore(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_zscore(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_zscore(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_zscore(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_zscore(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_zscore(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_zscore(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_zscore(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_zscore(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_zscore(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_zscore(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_zscore(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_zscore(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_zscore(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_zscore(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_zscore(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_zscore(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_zscore(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_zscore(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_zscore(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_zscore(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_zscore(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_zscore(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_zscore(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_zscore(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_zscore(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_zscore(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_zscore(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_zscore(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_zscore(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_zscore(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_zscore(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_zscore(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_zscore(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_zscore(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_zscore(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_zscore(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_zscore(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_zscore(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_zscore(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_zscore(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_zscore(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_zscore(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_zscore(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_zscore(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_zscore(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_zscore(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_zscore(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_zscore(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_zscore(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_zscore(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_zscore(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_zscore(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_zscore(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_zscore(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_zscore(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_zscore(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_zscore(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_zscore(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_zscore(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_zscore(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_zscore(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_zscore(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_zscore(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_zscore(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_zscore(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_zscore(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_zscore(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_zscore(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_zscore(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_zscore(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_zscore(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_zscore(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_zscore(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_zscore(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_zscore(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_zscore(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_zscore(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_zscore(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_zscore(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_zscore(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_zscore(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_zscore(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_zscore(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_zscore(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_zscore(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_zscore(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_zscore(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_zscore(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_zscore(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_zscore(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_zscore(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_zscore(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_zscore(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_zscore(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_zscore(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_zscore(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_zscore(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_zscore(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_zscore(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_zscore(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_zscore(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_zscore(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_zscore(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_zscore(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_zscore(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_zscore(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_zscore(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_zscore(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_zscore(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_zscore(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_zscore(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_zscore(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_zscore(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_zscore(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_zscore(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_zscore(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_zscore(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_zscore(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_zscore(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_zscore(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_zscore(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_zscore(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_zscore(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_zscore(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_zscore(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_zscore(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_zscore(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_zscore(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_zscore(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_zscore(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_zscore(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_zscore(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_zscore(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_zscore(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_zscore(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_zscore(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_zscore(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_zscore(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_zscore(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_zscore(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_zscore(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_zscore(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_zscore(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_zscore(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_zscore(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_zscore(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_zscore(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_zscore(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_zscore(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_zscore(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_zscore(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_zscore(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_zscore(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_zscore(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_zscore(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_zscore(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_zscore(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_zscore(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_zscore(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_zscore(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_zscore(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_zscore(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_zscore(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_zscore(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_zscore(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_zscore(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_zscore(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_zscore(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_zscore(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_zscore(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_zscore(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_zscore(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_zscore(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_zscore(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_zscore(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_zscore(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_zscore(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_zscore(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_zscore(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_zscore(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_zscore(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_zscore(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_zscore(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_zscore(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_zscore(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_zscore(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_zscore(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_zscore(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_zscore(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_zscore(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_zscore(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_zscore(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_zscore(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_zscore(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_zscore(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_zscore(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_zscore(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_zscore(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_zscore(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_zscore(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_zscore(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_zscore(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_zscore(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_zscore(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_zscore(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_zscore(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_zscore(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_zscore(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_zscore(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_zscore(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_zscore(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_zscore(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_zscore(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_zscore(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_zscore(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_zscore(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_zscore(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_zscore(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_zscore(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_zscore(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_zscore(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_zscore(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_zscore(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_zscore(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_zscore(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_zscore(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_zscore(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_zscore(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_zscore(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_zscore(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_zscore(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_zscore(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_zscore(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_zscore(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_zscore(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_zscore(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_zscore(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_zscore(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_zscore(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_zscore(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_zscore(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_zscore(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_zscore(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_zscore(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_zscore(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_zscore(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_zscore(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_zscore(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_zscore(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_zscore(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_zscore(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_zscore(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_zscore(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_zscore(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_zscore(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_zscore(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_zscore(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_zscore(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_zscore(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_zscore(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_zscore(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_zscore(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_zscore(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_zscore(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_zscore(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_zscore(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_zscore(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_zscore(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_zscore(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_zscore(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_zscore(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_zscore(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_zscore(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_zscore(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_zscore(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_zscore(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_zscore(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_zscore(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_zscore(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_zscore(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_zscore(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_zscore(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_zscore(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_zscore(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_zscore(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_zscore(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_zscore(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_zscore(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_zscore(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_zscore(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_zscore(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_zscore(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_zscore(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_zscore(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_zscore(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_zscore(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_zscore(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_zscore(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_zscore(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_zscore(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_zscore(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_zscore(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_zscore(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_zscore(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_zscore(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_zscore(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_zscore(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_zscore(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_zscore(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_zscore(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_zscore(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_zscore(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_zscore(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_zscore(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_zscore(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_zscore(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_std_dev(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_std_dev(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_std_dev(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_std_dev(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_std_dev(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_std_dev(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_std_dev(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_std_dev(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_std_dev(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_std_dev(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_std_dev(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_std_dev(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_std_dev(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_std_dev(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_std_dev(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_std_dev(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_std_dev(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_std_dev(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_std_dev(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_std_dev(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_std_dev(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_std_dev(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_std_dev(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_std_dev(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_std_dev(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_std_dev(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_std_dev(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_std_dev(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_std_dev(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_std_dev(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_std_dev(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_std_dev(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_std_dev(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_std_dev(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_std_dev(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_std_dev(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_std_dev(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_std_dev(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_std_dev(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_std_dev(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_std_dev(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_std_dev(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_std_dev(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_std_dev(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_std_dev(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_std_dev(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_std_dev(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_std_dev(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_std_dev(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_std_dev(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_std_dev(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_std_dev(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_std_dev(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_std_dev(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_std_dev(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_std_dev(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_std_dev(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_std_dev(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_std_dev(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_std_dev(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_std_dev(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_std_dev(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_std_dev(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_std_dev(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_std_dev(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_std_dev(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_std_dev(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_std_dev(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_std_dev(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_std_dev(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_std_dev(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_std_dev(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_std_dev(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_std_dev(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_std_dev(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_std_dev(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_std_dev(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_std_dev(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_std_dev(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_std_dev(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_std_dev(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_std_dev(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_std_dev(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_std_dev(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_std_dev(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_std_dev(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_std_dev(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_std_dev(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_std_dev(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_std_dev(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_std_dev(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_std_dev(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_std_dev(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_std_dev(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_std_dev(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_std_dev(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_std_dev(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_std_dev(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_std_dev(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_std_dev(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_std_dev(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_std_dev(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_std_dev(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_std_dev(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_std_dev(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_std_dev(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_std_dev(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_std_dev(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_std_dev(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_std_dev(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_std_dev(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_std_dev(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_std_dev(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_std_dev(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_std_dev(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_std_dev(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_std_dev(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_std_dev(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_std_dev(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_std_dev(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_std_dev(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_std_dev(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_std_dev(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_std_dev(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_std_dev(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_std_dev(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_std_dev(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_std_dev(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_std_dev(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_std_dev(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_std_dev(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_std_dev(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_std_dev(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_std_dev(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_std_dev(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_std_dev(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_std_dev(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_std_dev(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_std_dev(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_std_dev(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_std_dev(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_std_dev(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_std_dev(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_std_dev(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_std_dev(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_std_dev(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_std_dev(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_std_dev(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_std_dev(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_std_dev(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_std_dev(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_std_dev(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_std_dev(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_std_dev(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_std_dev(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_std_dev(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_std_dev(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_std_dev(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_std_dev(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_std_dev(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_std_dev(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_std_dev(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_std_dev(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_std_dev(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_std_dev(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_std_dev(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_std_dev(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_std_dev(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_std_dev(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_std_dev(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_std_dev(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_std_dev(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_std_dev(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_std_dev(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_std_dev(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_std_dev(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_std_dev(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_std_dev(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_std_dev(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_std_dev(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_std_dev(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_std_dev(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_std_dev(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_std_dev(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_std_dev(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_std_dev(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_std_dev(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_std_dev(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_std_dev(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_std_dev(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_std_dev(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_std_dev(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_std_dev(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_std_dev(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_std_dev(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_std_dev(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_std_dev(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_std_dev(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_std_dev(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_std_dev(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_std_dev(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_std_dev(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_std_dev(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_std_dev(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_std_dev(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_std_dev(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_std_dev(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_std_dev(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_std_dev(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_std_dev(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_std_dev(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_std_dev(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_std_dev(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_std_dev(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_std_dev(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_std_dev(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_std_dev(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_std_dev(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_std_dev(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_std_dev(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_std_dev(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_std_dev(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_std_dev(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_std_dev(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_std_dev(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_std_dev(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_std_dev(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_std_dev(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_std_dev(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_std_dev(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_std_dev(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_std_dev(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_std_dev(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_std_dev(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_std_dev(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_std_dev(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_std_dev(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_std_dev(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_std_dev(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_std_dev(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_std_dev(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_std_dev(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_std_dev(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_std_dev(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_std_dev(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_std_dev(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_std_dev(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_std_dev(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_std_dev(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_std_dev(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_std_dev(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_std_dev(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_std_dev(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_std_dev(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_std_dev(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_std_dev(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_std_dev(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_std_dev(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_std_dev(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_std_dev(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_std_dev(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_std_dev(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_std_dev(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_std_dev(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_std_dev(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_std_dev(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_std_dev(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_std_dev(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_std_dev(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_std_dev(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_std_dev(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_std_dev(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_std_dev(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_std_dev(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_std_dev(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_std_dev(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_std_dev(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_std_dev(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_std_dev(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_std_dev(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_std_dev(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_std_dev(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_std_dev(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_std_dev(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_std_dev(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_std_dev(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_std_dev(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_std_dev(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_scale(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_scale(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_scale(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_scale(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_scale(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_scale(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_scale(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_scale(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_scale(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_scale(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_scale(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_scale(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_scale(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_scale(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_scale(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_scale(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_scale(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_scale(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_scale(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_scale(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_scale(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_scale(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_scale(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_scale(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_scale(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_scale(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_scale(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_scale(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_scale(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_scale(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_scale(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_scale(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_scale(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_scale(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_scale(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_scale(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_scale(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_scale(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_scale(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_scale(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_scale(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_scale(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_scale(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_scale(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_scale(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_scale(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_scale(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_scale(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_scale(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_scale(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_scale(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_scale(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_scale(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_scale(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_scale(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_scale(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_scale(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_scale(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_scale(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_scale(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_scale(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_scale(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_scale(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_scale(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_scale(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_scale(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_scale(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_scale(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_scale(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_scale(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_scale(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_scale(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_scale(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_scale(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_scale(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_scale(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_scale(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_scale(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_scale(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_scale(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_scale(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_scale(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_scale(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_scale(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_scale(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_scale(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_scale(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_scale(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_scale(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_scale(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_scale(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_scale(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_scale(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_scale(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_scale(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_scale(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_scale(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_scale(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_scale(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_scale(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_scale(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_scale(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_scale(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_scale(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_scale(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_scale(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_scale(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_scale(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_scale(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_scale(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_scale(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_scale(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_scale(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_scale(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_scale(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_scale(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_scale(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_scale(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_scale(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_scale(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_scale(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_scale(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_scale(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_scale(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_scale(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_scale(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_scale(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_scale(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_scale(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_scale(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_scale(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_scale(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_scale(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_scale(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_scale(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_scale(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_scale(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_scale(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_scale(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_scale(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_scale(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_scale(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_scale(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_scale(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_scale(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_scale(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_scale(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_scale(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_scale(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_scale(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_scale(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_scale(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_scale(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_scale(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_scale(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_scale(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_scale(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_scale(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_scale(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_scale(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_scale(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_scale(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_scale(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_scale(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_scale(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_scale(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_scale(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_scale(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_scale(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_scale(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_scale(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_scale(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_scale(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_scale(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_scale(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_scale(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_scale(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_scale(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_scale(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_scale(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_scale(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_scale(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_scale(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_scale(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_scale(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_scale(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_scale(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_scale(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_scale(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_scale(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_scale(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_scale(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_scale(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_scale(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_scale(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_scale(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_scale(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_scale(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_scale(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_scale(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_scale(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_scale(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_scale(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_scale(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_scale(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_scale(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_scale(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_scale(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_scale(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_scale(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_scale(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_scale(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_scale(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_scale(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_scale(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_scale(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_scale(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_scale(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_scale(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_scale(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_scale(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_scale(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_scale(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_scale(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_scale(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_scale(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_scale(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_scale(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_scale(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_scale(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_scale(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_scale(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_scale(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_scale(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_scale(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_scale(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_scale(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_scale(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_scale(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_scale(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_scale(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_scale(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_scale(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_scale(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_scale(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_scale(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_scale(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_scale(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_scale(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_scale(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_scale(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_scale(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_scale(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_scale(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_scale(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_scale(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_scale(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_scale(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_scale(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_scale(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_scale(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_scale(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_scale(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_scale(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_scale(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_scale(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_scale(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_scale(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_scale(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_scale(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_scale(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_scale(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_scale(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_scale(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_scale(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_scale(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_scale(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_scale(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_scale(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_scale(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_scale(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_scale(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_scale(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_scale(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_scale(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_scale(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_scale(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_scale(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_scale(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_scale(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_scale(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_scale(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_scale(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_scale(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_scale(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_scale(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_scale(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_scale(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_scale(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_scale(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_scale(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_scale(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_scale(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_scale(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_scale(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_scale(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_scale(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_scale(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_scale(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_scale(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_scale(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_scale(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_scale(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_scale(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_scale(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_scale(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_scale(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_scale(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_scale(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_scale(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_scale(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_scale(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_scale(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_scale(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_scale(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_scale(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_scale(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_scale(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_scale(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_scale(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_scale(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_scale(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_scale(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_scale(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_scale(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_scale(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_scale(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_scale(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_scale(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_scale(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_scale(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_scale(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_scale(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_scale(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_scale(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_scale(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_scale(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_scale(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_scale(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_scale(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_scale(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_scale(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_scale(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_scale(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_scale(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_scale(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_scale(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_scale(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_scale(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_scale(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_scale(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_scale(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_scale(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_scale(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_scale(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_scale(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_scale(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_scale(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_scale(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_scale(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_scale(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_scale(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_scale(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_scale(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_scale(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_scale(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_scale(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_scale(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_scale(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_scale(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_scale(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_scale(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_scale(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_scale(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_scale(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_scale(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_scale(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_scale(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_scale(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_scale(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_scale(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_scale(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_scale(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_scale(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_scale(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_scale(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_scale(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_scale(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_scale(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_scale(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_scale(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_scale(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_scale(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_scale(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_scale(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_scale(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_scale(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_scale(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_scale(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_scale(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_scale(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_scale(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_scale(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_scale(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_scale(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_scale(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_scale(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_scale(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_scale(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_scale(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_scale(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_scale(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_scale(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_scale(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_scale(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_scale(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_scale(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_scale(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_scale(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_scale(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_scale(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_scale(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_scale(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_scale(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_scale(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_scale(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_scale(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_scale(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_scale(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_scale(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_scale(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_scale(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_scale(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_scale(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_scale(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_scale(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_scale(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_scale(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_scale(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_scale(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_scale(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_scale(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_scale(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_scale(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_scale(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_scale(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_scale(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_scale(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_scale(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_scale(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_scale(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_scale(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_scale(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_scale(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_scale(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_scale(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_scale(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_scale(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_scale(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_scale(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_scale(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_scale(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_scale(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_scale(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_scale(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_scale(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_scale(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_scale(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_scale(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_scale(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_scale(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_scale(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_scale(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_scale(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_scale(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_scale(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_scale(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_scale(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_scale(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_scale(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_scale(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_scale(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_scale(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_scale(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_scale(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_scale(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_scale(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_scale(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_scale(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_scale(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_scale(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_scale(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_scale(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_scale(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_scale(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_scale(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_scale(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_scale(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_scale(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_scale(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_scale(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_scale(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_scale(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_scale(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_scale(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_scale(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_scale(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_scale(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_scale(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_scale(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_scale(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_scale(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_scale(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_scale(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_scale(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_scale(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_scale(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_scale(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_sum(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_sum(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_sum(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_sum(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_sum(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_sum(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_sum(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_sum(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_sum(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_sum(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_sum(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_sum(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_sum(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_sum(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_sum(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_sum(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_sum(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_sum(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_sum(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_sum(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_sum(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_sum(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_sum(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_sum(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_sum(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_sum(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_sum(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_sum(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_sum(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_sum(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_sum(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_sum(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_sum(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_sum(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_sum(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_sum(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_sum(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_sum(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_sum(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_sum(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_sum(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_sum(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_sum(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_sum(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_sum(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_sum(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_sum(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_sum(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_sum(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_sum(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_sum(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_sum(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_sum(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_sum(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_sum(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_sum(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_sum(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_sum(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_sum(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_sum(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_sum(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_sum(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_sum(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_sum(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_sum(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_sum(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_sum(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_sum(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_sum(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_sum(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_sum(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_sum(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_sum(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_sum(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_sum(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_sum(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_sum(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_sum(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_sum(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_sum(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_sum(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_sum(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_sum(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_sum(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_sum(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_sum(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_sum(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_sum(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_sum(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_sum(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_sum(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_sum(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_sum(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_sum(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_sum(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_sum(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_sum(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_sum(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_sum(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_sum(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_sum(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_sum(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_sum(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_sum(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_sum(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_sum(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_sum(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_sum(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_sum(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_sum(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_sum(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_sum(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_sum(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_sum(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_sum(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_sum(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_sum(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_sum(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_sum(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_sum(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_sum(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_sum(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_sum(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_sum(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_sum(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_sum(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_sum(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_sum(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_sum(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_sum(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_sum(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_sum(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_sum(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_sum(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_sum(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_sum(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_sum(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_sum(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_sum(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_sum(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_sum(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_sum(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_sum(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_sum(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_sum(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_sum(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_sum(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_sum(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_sum(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_sum(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_sum(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_sum(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_sum(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_sum(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_sum(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_sum(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_sum(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_sum(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_sum(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_sum(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_sum(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_sum(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_sum(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_sum(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_sum(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_sum(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_sum(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_sum(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_sum(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_sum(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_sum(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_sum(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_sum(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_sum(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_sum(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_sum(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_sum(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_sum(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_sum(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_sum(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_sum(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_sum(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_sum(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_sum(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_sum(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_sum(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_sum(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_sum(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_sum(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_sum(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_sum(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_sum(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_sum(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_sum(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_sum(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_sum(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_sum(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_sum(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_sum(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_sum(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_sum(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_sum(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_sum(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_sum(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_sum(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_sum(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_sum(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_sum(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_sum(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_sum(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_sum(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_sum(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_sum(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_sum(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_sum(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_sum(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_sum(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_sum(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_sum(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_sum(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_sum(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_sum(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_sum(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_sum(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_sum(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_sum(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_sum(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_sum(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_sum(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_sum(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_sum(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_sum(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_sum(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_sum(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_sum(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_sum(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_sum(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_sum(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_sum(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_sum(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_sum(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_sum(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_sum(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_sum(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_sum(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_sum(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_sum(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_sum(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_sum(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_sum(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_sum(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_sum(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_sum(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_sum(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_sum(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_sum(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_sum(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_sum(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_sum(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_sum(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_sum(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_sum(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_sum(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_sum(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_sum(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_sum(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_sum(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_sum(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_sum(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_sum(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_sum(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_sum(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_sum(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_sum(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_sum(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_sum(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_sum(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_sum(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_sum(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_sum(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_sum(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_sum(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_sum(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_sum(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_sum(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_sum(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_sum(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_sum(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_sum(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_sum(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_sum(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_sum(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_sum(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_sum(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_sum(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_sum(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_sum(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_sum(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_sum(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_sum(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_sum(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_sum(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_sum(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_sum(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_sum(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_sum(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_sum(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_sum(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_sum(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_sum(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_sum(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_sum(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_sum(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_sum(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_sum(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_sum(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_sum(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_sum(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_sum(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_sum(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_sum(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_sum(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_sum(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_sum(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_sum(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_sum(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_sum(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_sum(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_sum(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_sum(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_sum(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_sum(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_sum(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_sum(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_sum(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_sum(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_sum(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_sum(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_sum(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_sum(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_sum(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_sum(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_sum(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_sum(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_sum(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_sum(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_sum(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_sum(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_sum(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_sum(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_sum(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_sum(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_sum(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_sum(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_sum(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_sum(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_sum(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_sum(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_sum(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_sum(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_sum(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_sum(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_sum(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_sum(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_sum(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_sum(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_sum(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_sum(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_sum(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_sum(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_sum(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_sum(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_sum(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_sum(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_sum(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_sum(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_sum(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_sum(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_sum(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_sum(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_sum(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_sum(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_sum(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_sum(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_sum(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_sum(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_sum(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_sum(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_sum(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_sum(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_sum(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_sum(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_sum(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_sum(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_sum(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_sum(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_sum(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_sum(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_sum(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_sum(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_sum(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_sum(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_sum(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_sum(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_sum(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_sum(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_sum(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_sum(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_sum(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_sum(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_sum(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_sum(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_sum(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_sum(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_sum(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_sum(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_sum(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_sum(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_sum(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_sum(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_sum(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_sum(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_sum(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_sum(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_sum(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_sum(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_sum(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_sum(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_sum(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_sum(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_sum(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_sum(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_sum(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_sum(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_sum(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_sum(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_sum(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_sum(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_sum(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_sum(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_sum(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_sum(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_sum(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_sum(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_sum(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_sum(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_sum(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_sum(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_sum(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_sum(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_sum(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_sum(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_sum(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_sum(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_sum(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_sum(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_sum(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_sum(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_sum(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_sum(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_sum(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_sum(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_sum(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_sum(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_sum(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_sum(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_sum(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_sum(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_sum(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_sum(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_sum(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_sum(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_sum(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_sum(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_sum(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_sum(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_sum(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_sum(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_sum(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_sum(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_sum(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_sum(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_sum(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_sum(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_sum(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_sum(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_sum(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_sum(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_sum(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_sum(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_sum(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_sum(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_sum(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_sum(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_sum(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_sum(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_sum(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_sum(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_sum(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_sum(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_sum(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_sum(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_sum(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_sum(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_sum(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_sum(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_sum(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_sum(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_sum(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_sum(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_sum(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_sum(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_sum(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_sum(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_sum(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_sum(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_sum(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_sum(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_sum(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_sum(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_sum(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_sum(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_sum(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_sum(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_sum(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_sum(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_sum(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_sum(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_av_diff(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_av_diff(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_av_diff(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_av_diff(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_av_diff(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_av_diff(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_av_diff(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_av_diff(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_av_diff(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_av_diff(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_av_diff(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_av_diff(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_av_diff(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_av_diff(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_av_diff(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_av_diff(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_av_diff(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_av_diff(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_av_diff(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_av_diff(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_av_diff(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_av_diff(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_av_diff(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_av_diff(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_av_diff(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_av_diff(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_av_diff(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_av_diff(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_av_diff(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_av_diff(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_av_diff(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_av_diff(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_av_diff(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_av_diff(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_av_diff(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_av_diff(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_av_diff(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_av_diff(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_av_diff(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_av_diff(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_av_diff(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_av_diff(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_av_diff(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_av_diff(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_av_diff(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_av_diff(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_av_diff(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_av_diff(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_av_diff(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_av_diff(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_av_diff(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_av_diff(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_av_diff(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_av_diff(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_av_diff(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_av_diff(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_av_diff(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_av_diff(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_av_diff(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_av_diff(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_av_diff(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_av_diff(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_av_diff(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_av_diff(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_av_diff(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_av_diff(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_av_diff(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_av_diff(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_av_diff(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_av_diff(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_av_diff(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_av_diff(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_av_diff(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_av_diff(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_av_diff(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_av_diff(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_av_diff(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_av_diff(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_av_diff(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_av_diff(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_av_diff(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_av_diff(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_av_diff(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_av_diff(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_av_diff(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_av_diff(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_av_diff(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_av_diff(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_av_diff(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_av_diff(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_av_diff(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_av_diff(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_av_diff(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_av_diff(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_av_diff(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_av_diff(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_av_diff(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_av_diff(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_av_diff(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_av_diff(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_av_diff(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_av_diff(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_av_diff(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_av_diff(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_av_diff(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_av_diff(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_av_diff(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_av_diff(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_av_diff(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_av_diff(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_av_diff(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_av_diff(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_av_diff(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_av_diff(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_av_diff(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_av_diff(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_av_diff(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_av_diff(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_av_diff(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_av_diff(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_av_diff(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_av_diff(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_av_diff(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_av_diff(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_av_diff(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_av_diff(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_av_diff(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_av_diff(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_av_diff(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_av_diff(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_av_diff(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_av_diff(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_av_diff(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_av_diff(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_av_diff(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_av_diff(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_av_diff(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_av_diff(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_av_diff(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_av_diff(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_av_diff(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_av_diff(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_av_diff(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_av_diff(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_av_diff(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_av_diff(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_av_diff(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_av_diff(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_av_diff(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_av_diff(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_av_diff(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_av_diff(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_av_diff(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_av_diff(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_av_diff(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_av_diff(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_av_diff(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_av_diff(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_av_diff(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_av_diff(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_av_diff(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_av_diff(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_av_diff(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_av_diff(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_av_diff(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_av_diff(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_av_diff(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_av_diff(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_av_diff(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_av_diff(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_av_diff(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_av_diff(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_av_diff(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_av_diff(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_av_diff(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_av_diff(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_av_diff(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_av_diff(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_av_diff(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_av_diff(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_av_diff(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_av_diff(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_av_diff(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_av_diff(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_av_diff(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_av_diff(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_av_diff(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_av_diff(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_av_diff(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_av_diff(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_av_diff(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_av_diff(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_av_diff(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_av_diff(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_av_diff(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_av_diff(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_av_diff(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_av_diff(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_av_diff(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_av_diff(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_av_diff(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_av_diff(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_av_diff(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_av_diff(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_av_diff(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_av_diff(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_av_diff(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_av_diff(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_av_diff(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_av_diff(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_av_diff(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_av_diff(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_av_diff(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_av_diff(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_av_diff(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_av_diff(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_av_diff(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_av_diff(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_av_diff(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_av_diff(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_av_diff(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_av_diff(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_av_diff(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_av_diff(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_av_diff(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_av_diff(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_av_diff(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_av_diff(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_av_diff(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_av_diff(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_av_diff(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_av_diff(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_av_diff(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_av_diff(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_av_diff(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_av_diff(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_av_diff(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_av_diff(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_av_diff(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_av_diff(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_av_diff(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_av_diff(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_av_diff(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_av_diff(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_av_diff(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_av_diff(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_av_diff(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_av_diff(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_av_diff(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_av_diff(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_av_diff(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_av_diff(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_av_diff(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_av_diff(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_av_diff(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_av_diff(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_av_diff(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_av_diff(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_av_diff(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_av_diff(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_av_diff(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_av_diff(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_av_diff(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_av_diff(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_av_diff(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_av_diff(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_av_diff(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_av_diff(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_av_diff(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_av_diff(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_av_diff(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_av_diff(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_av_diff(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_av_diff(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_av_diff(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_av_diff(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_av_diff(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_av_diff(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_av_diff(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_av_diff(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_av_diff(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_av_diff(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_av_diff(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_av_diff(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_av_diff(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_av_diff(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_av_diff(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_av_diff(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_mean(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_mean(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_mean(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_mean(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_mean(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_mean(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_mean(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_mean(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_mean(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_mean(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_mean(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_mean(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_mean(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_mean(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_mean(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_mean(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_mean(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_mean(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_mean(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_mean(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_mean(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_mean(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_mean(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_mean(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_mean(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_mean(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_mean(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_mean(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_mean(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_mean(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_mean(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_mean(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_mean(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_mean(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_mean(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_mean(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_mean(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_mean(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_mean(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_mean(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_mean(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_mean(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_mean(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_mean(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_mean(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_mean(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_mean(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_mean(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_mean(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_mean(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_mean(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_mean(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_mean(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_mean(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_mean(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_mean(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_mean(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_mean(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_mean(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_mean(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_mean(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_mean(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_mean(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_mean(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_mean(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_mean(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_mean(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_mean(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_mean(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_mean(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_mean(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_mean(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_mean(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_mean(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_mean(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_mean(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_mean(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_mean(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_mean(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_mean(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_mean(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_mean(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_mean(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_mean(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_mean(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_mean(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_mean(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_mean(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_mean(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_mean(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_mean(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_mean(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_mean(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_mean(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_mean(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_mean(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_mean(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_mean(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_mean(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_mean(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_mean(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_mean(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_mean(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_mean(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_mean(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_mean(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_mean(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_mean(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_mean(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_mean(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_mean(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_mean(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_mean(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_mean(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_mean(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_mean(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_mean(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_mean(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_mean(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_mean(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_mean(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_mean(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_mean(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_mean(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_mean(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_mean(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_mean(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_mean(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_mean(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_mean(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_mean(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_mean(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_mean(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_mean(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_mean(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_mean(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_mean(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_mean(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_mean(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_mean(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_mean(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_mean(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_mean(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_mean(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_mean(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_mean(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_mean(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_mean(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_mean(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_mean(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_mean(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_mean(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_mean(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_mean(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_mean(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_mean(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_mean(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_mean(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_mean(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_mean(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_mean(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_mean(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_mean(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_mean(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_mean(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_mean(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_mean(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_mean(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_mean(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_mean(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_mean(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_mean(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_mean(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_mean(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_mean(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_mean(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_mean(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_mean(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_mean(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_mean(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_mean(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_mean(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_mean(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_mean(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_mean(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_mean(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_mean(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_mean(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_mean(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_mean(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_mean(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_mean(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_mean(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_mean(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_mean(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_mean(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_mean(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_mean(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_mean(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_mean(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_mean(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_mean(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_mean(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_mean(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_mean(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_mean(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_mean(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_mean(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_mean(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_mean(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_mean(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_mean(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_mean(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_mean(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_mean(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_mean(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_mean(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_mean(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_mean(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_mean(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_mean(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_mean(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_mean(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_mean(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_mean(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_mean(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_mean(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_mean(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_mean(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_mean(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_mean(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_mean(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_mean(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_mean(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_mean(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_mean(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_mean(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_mean(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_mean(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_mean(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_mean(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_mean(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_mean(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_mean(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_mean(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_mean(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_mean(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_mean(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_mean(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_mean(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_mean(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_mean(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_mean(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_mean(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_mean(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_mean(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_mean(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_mean(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_mean(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_mean(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_mean(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_mean(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_mean(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_mean(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_mean(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_mean(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_mean(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_mean(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_mean(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_mean(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_mean(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_mean(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_mean(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_mean(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_mean(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_mean(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_mean(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_mean(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_mean(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_mean(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_mean(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_mean(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_mean(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_mean(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_mean(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_mean(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_mean(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_mean(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_mean(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_mean(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_mean(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_mean(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_mean(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_mean(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_mean(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_mean(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_mean(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_mean(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_mean(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_mean(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_mean(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_mean(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_mean(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_mean(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_mean(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_mean(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_mean(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_mean(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_mean(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_mean(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_mean(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_mean(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_mean(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_mean(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_mean(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_mean(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_mean(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_mean(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_mean(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_mean(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_mean(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_mean(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_mean(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_mean(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_mean(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_mean(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_mean(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_mean(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_mean(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_mean(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_mean(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_mean(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_mean(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_mean(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_mean(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_mean(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_mean(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_mean(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_mean(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_mean(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_mean(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_mean(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_mean(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_mean(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_mean(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_mean(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_mean(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_mean(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_mean(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_mean(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_mean(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_mean(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_mean(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_mean(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_mean(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_mean(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_mean(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_mean(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_mean(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_mean(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_mean(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_mean(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_mean(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_mean(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_mean(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_mean(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_mean(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_mean(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_mean(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_mean(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_mean(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_mean(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_mean(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_mean(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_mean(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_mean(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_mean(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_mean(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_mean(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_mean(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_mean(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_mean(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_mean(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_mean(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_mean(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_mean(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_mean(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_mean(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_mean(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_mean(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_mean(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_mean(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_mean(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_mean(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_mean(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_mean(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_mean(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_mean(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_mean(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_mean(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_mean(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_mean(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_mean(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_mean(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_mean(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_mean(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_mean(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_mean(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_mean(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_mean(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_mean(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_mean(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_mean(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_mean(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_mean(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_mean(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_mean(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_mean(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_mean(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_mean(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_mean(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_mean(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_mean(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_mean(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_mean(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_mean(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_mean(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_mean(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_mean(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_mean(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_mean(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_mean(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_mean(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_mean(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_mean(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_mean(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_mean(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_mean(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_mean(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_mean(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_mean(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_mean(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_mean(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_mean(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_mean(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_mean(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_mean(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_mean(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_mean(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_mean(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_mean(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_mean(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_mean(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_mean(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_mean(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_mean(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_mean(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_mean(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_mean(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_mean(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_mean(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_mean(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_mean(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_mean(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_mean(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_mean(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_mean(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_mean(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_mean(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_mean(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_mean(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_mean(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_mean(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_mean(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_mean(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_mean(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_mean(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_mean(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_mean(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_mean(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_mean(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_mean(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_mean(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_mean(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_mean(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_mean(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_mean(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_mean(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_mean(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_mean(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_mean(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_mean(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_mean(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_mean(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_mean(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_mean(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_mean(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_mean(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_mean(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_mean(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_mean(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_mean(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_mean(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_mean(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_mean(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_mean(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_mean(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_mean(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_mean(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_mean(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_mean(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_mean(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_mean(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_mean(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_mean(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_mean(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_mean(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_mean(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_mean(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_mean(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_mean(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_mean(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_mean(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_mean(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_mean(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_mean(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_mean(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_mean(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_rank(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_rank(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_rank(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_rank(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_rank(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_rank(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_rank(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_rank(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_rank(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_rank(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_rank(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_rank(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_rank(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_rank(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_rank(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_rank(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_rank(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_rank(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_rank(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_rank(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_rank(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_rank(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_rank(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_rank(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_rank(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_rank(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_rank(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_rank(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_rank(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_rank(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_rank(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_rank(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_rank(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_rank(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_rank(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_rank(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_rank(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_rank(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_rank(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_rank(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_rank(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_rank(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_rank(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_rank(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_rank(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_rank(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_rank(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_rank(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_rank(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_rank(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_rank(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_rank(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_rank(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_rank(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_rank(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_rank(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_rank(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_rank(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_rank(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_rank(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_rank(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_rank(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_rank(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_rank(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_rank(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_rank(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_rank(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_rank(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_rank(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_rank(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_rank(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_rank(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_rank(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_rank(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_rank(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_rank(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_rank(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_rank(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_rank(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_rank(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_rank(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_rank(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_rank(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_rank(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_rank(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_rank(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_rank(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_rank(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_rank(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_rank(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_rank(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_rank(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_rank(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_rank(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_rank(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_rank(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_rank(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_rank(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_rank(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_rank(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_rank(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_rank(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_rank(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_rank(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_rank(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_rank(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_rank(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_rank(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_rank(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_rank(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_rank(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_rank(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_rank(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_rank(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_rank(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_rank(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_rank(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_rank(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_rank(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_rank(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_rank(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_rank(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_rank(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_rank(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_rank(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_rank(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_rank(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_rank(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_rank(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_rank(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_rank(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_rank(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_rank(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_rank(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_rank(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_rank(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_rank(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_rank(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_rank(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_rank(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_rank(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_rank(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_rank(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_rank(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_rank(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_rank(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_rank(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_rank(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_rank(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_rank(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_rank(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_rank(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_rank(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_rank(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_rank(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_rank(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_rank(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_rank(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_rank(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_rank(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_rank(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_rank(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_rank(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_rank(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_rank(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_rank(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_rank(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_rank(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_rank(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_rank(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_rank(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_rank(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_rank(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_rank(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_rank(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_rank(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_rank(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_rank(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_rank(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_rank(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_rank(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_rank(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_rank(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_rank(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_rank(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_rank(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_rank(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_rank(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_rank(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_rank(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_rank(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_rank(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_rank(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_rank(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_rank(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_rank(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_rank(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_rank(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_rank(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_rank(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_rank(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_rank(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_rank(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_rank(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_rank(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_rank(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_rank(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_rank(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_rank(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_rank(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_rank(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_rank(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_rank(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_rank(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_rank(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_rank(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_rank(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_rank(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_rank(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_rank(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_rank(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_rank(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_rank(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_rank(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_rank(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_rank(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_rank(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_rank(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_rank(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_rank(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_rank(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_rank(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_rank(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_rank(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_rank(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_rank(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_rank(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_rank(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_rank(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_rank(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_rank(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_rank(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_rank(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_rank(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_rank(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_rank(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_rank(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_rank(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_rank(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_rank(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_rank(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_rank(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_rank(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_rank(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_rank(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_rank(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_rank(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_rank(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_rank(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_rank(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_rank(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_rank(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_rank(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_rank(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_rank(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_rank(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_rank(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_rank(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_rank(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_rank(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_rank(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_rank(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_rank(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_rank(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_rank(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_rank(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_rank(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_rank(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_rank(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_rank(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_rank(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_rank(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_rank(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_rank(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_rank(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_rank(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_rank(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_rank(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_rank(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_rank(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_rank(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_rank(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_rank(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_rank(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_rank(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_rank(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_rank(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_rank(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_rank(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_rank(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_rank(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_rank(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_rank(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_rank(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_rank(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_rank(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_rank(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_rank(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_rank(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_rank(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_rank(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_rank(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_rank(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_rank(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_rank(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_rank(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_rank(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_rank(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_rank(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_rank(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_rank(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_rank(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_rank(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_rank(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_rank(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_rank(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_rank(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_rank(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_rank(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_rank(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_rank(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_rank(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_rank(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_rank(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_rank(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_rank(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_rank(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_rank(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_rank(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_rank(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_rank(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_rank(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_rank(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_rank(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_rank(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_rank(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_rank(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_rank(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_rank(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_rank(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_rank(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_rank(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_rank(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_rank(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_rank(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_rank(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_rank(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_rank(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_rank(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_rank(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_rank(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_rank(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_rank(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_rank(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_rank(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_rank(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_rank(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_rank(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_rank(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_rank(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_rank(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_rank(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_rank(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_rank(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_rank(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_rank(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_rank(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_rank(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_rank(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_rank(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_rank(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_rank(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_rank(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_rank(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_rank(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_rank(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_rank(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_rank(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_rank(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_rank(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_rank(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_rank(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_rank(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_rank(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_rank(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_rank(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_rank(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_rank(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_rank(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_rank(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_rank(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_rank(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_rank(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_rank(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_rank(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_rank(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_rank(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_rank(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_rank(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_rank(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_rank(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_rank(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_rank(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_rank(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_rank(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_rank(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_rank(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_rank(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_rank(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_rank(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_rank(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_rank(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_rank(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_rank(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_rank(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_rank(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_rank(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_rank(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_rank(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_rank(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_rank(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_rank(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_rank(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_rank(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_rank(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_rank(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_rank(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_rank(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_rank(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_rank(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_rank(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_rank(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_rank(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_rank(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_rank(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_rank(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_rank(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_rank(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_rank(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_rank(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_rank(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_rank(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_rank(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_rank(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_rank(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_rank(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_rank(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_rank(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_rank(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_rank(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_rank(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_rank(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_rank(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_rank(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_rank(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_rank(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_rank(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_rank(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_rank(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_rank(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_rank(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_rank(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_rank(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_rank(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_rank(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_rank(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_rank(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_rank(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_rank(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_rank(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_rank(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_rank(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_rank(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_rank(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_rank(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_rank(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_rank(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_rank(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_rank(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_rank(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_rank(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_rank(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_rank(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_rank(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_rank(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_rank(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_rank(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_rank(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_rank(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_rank(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_rank(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_rank(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_rank(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_rank(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_rank(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_rank(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_rank(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_rank(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_rank(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_rank(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_rank(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_rank(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_rank(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_rank(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_rank(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_rank(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_rank(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_rank(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_rank(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_rank(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_rank(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_rank(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_rank(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_rank(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_rank(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_rank(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_rank(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_rank(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_quantile(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_quantile(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_quantile(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_quantile(subtract((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_quantile(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_quantile(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_quantile(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_quantile(subtract((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_quantile(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_quantile(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_quantile(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_quantile(subtract((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_quantile(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_quantile(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_quantile(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_quantile(subtract((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_quantile(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_quantile(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_quantile(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_quantile(subtract((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_quantile(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_quantile(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_quantile(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_quantile(subtract((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_quantile(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_quantile(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_quantile(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_quantile(subtract((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_quantile(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_quantile(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_quantile(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_quantile(subtract((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_quantile(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_quantile(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_quantile(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_quantile(subtract((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_quantile(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_quantile(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_quantile(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_quantile(subtract((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_quantile(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_quantile(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_quantile(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_quantile(subtract((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_quantile(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_quantile(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_quantile(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_quantile(subtract((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_quantile(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_quantile(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_quantile(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_quantile(subtract((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_quantile(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_quantile(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_quantile(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_quantile(subtract((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_quantile(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_quantile(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_quantile(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_quantile(subtract((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_quantile(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_quantile(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_quantile(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_quantile(subtract((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_quantile(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_quantile(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_quantile(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_quantile(subtract((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_quantile(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_quantile(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_quantile(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_quantile(subtract((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_quantile(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_quantile(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_quantile(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_quantile(subtract((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_quantile(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_quantile(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_quantile(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_quantile(subtract((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_quantile(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_quantile(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_quantile(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_quantile(subtract((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_quantile(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_quantile(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_quantile(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_quantile(subtract((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_quantile(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_quantile(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_quantile(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_quantile(subtract((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_quantile(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_quantile(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_quantile(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_quantile(subtract((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_quantile(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_quantile(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_quantile(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_quantile(subtract((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_quantile(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_quantile(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_quantile(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_quantile(subtract((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_quantile(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_quantile(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_quantile(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_quantile(subtract((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_quantile(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_quantile(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_quantile(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_quantile(subtract((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_quantile(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_quantile(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_quantile(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_quantile(subtract((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_quantile(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_quantile(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_quantile(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_quantile(subtract((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_quantile(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_quantile(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_quantile(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_quantile(subtract((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_quantile(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_quantile(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_quantile(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_quantile(subtract((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_quantile(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_quantile(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_quantile(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_quantile(subtract((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_quantile(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_quantile(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_quantile(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_quantile(subtract((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_quantile(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_quantile(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_quantile(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_quantile(subtract((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_quantile(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_quantile(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_quantile(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_quantile(subtract((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_quantile(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_quantile(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_quantile(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_quantile(subtract((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_quantile(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_quantile(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_quantile(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_quantile(subtract((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_quantile(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_quantile(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_quantile(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_quantile(subtract((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_quantile(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_quantile(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_quantile(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_quantile(subtract((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_quantile(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_quantile(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_quantile(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_quantile(subtract((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_quantile(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_quantile(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_quantile(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_quantile(subtract((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_quantile(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_quantile(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_quantile(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_quantile(subtract((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_quantile(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_quantile(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_quantile(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_quantile(subtract((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_quantile(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_quantile(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_quantile(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_quantile(subtract((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_quantile(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_quantile(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_quantile(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_quantile(subtract((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_quantile(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_quantile(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_quantile(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_quantile(subtract((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_quantile(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_quantile(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_quantile(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_quantile(subtract((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_quantile(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_quantile(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_quantile(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_quantile(subtract((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_quantile(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_quantile(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_quantile(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_quantile(subtract((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_quantile(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_quantile(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_quantile(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_quantile(subtract((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_quantile(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_quantile(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_quantile(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_quantile(subtract((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_quantile(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_quantile(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_quantile(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_quantile(subtract((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_quantile(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_quantile(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_quantile(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_quantile(subtract((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_quantile(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_quantile(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_quantile(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_quantile(subtract((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_quantile(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_quantile(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_quantile(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_quantile(subtract((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_quantile(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_quantile(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_quantile(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_quantile(subtract((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_quantile(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_quantile(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_quantile(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_quantile(subtract((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_quantile(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_quantile(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_quantile(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_quantile(subtract((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_quantile(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_quantile(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_quantile(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_quantile(subtract((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_quantile(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_quantile(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_quantile(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_quantile(subtract((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_quantile(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_quantile(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_quantile(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_quantile(subtract((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_quantile(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_quantile(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_quantile(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_quantile(subtract((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_quantile(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_quantile(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_quantile(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_quantile(subtract((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_quantile(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_quantile(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_quantile(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_quantile(subtract((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_quantile(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_quantile(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_quantile(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_quantile(subtract((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)
ts_quantile(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),20)
ts_quantile(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),40)
ts_quantile(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),80)
ts_quantile(divide((opt4_122_call_vola_delta30) , (opt4_122_put_vola_delta30)),240)
ts_quantile(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),20)
ts_quantile(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),40)
ts_quantile(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),80)
ts_quantile(divide((opt4_122_call_vola_delta35) , (opt4_122_put_vola_delta35)),240)
ts_quantile(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),20)
ts_quantile(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),40)
ts_quantile(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),80)
ts_quantile(divide((opt4_122_call_vola_delta40) , (opt4_122_put_vola_delta40)),240)
ts_quantile(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),20)
ts_quantile(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),40)
ts_quantile(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),80)
ts_quantile(divide((opt4_122_call_vola_delta45) , (opt4_122_put_vola_delta45)),240)
ts_quantile(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),20)
ts_quantile(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),40)
ts_quantile(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),80)
ts_quantile(divide((opt4_122_call_vola_delta50) , (opt4_122_put_vola_delta50)),240)
ts_quantile(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),20)
ts_quantile(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),40)
ts_quantile(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),80)
ts_quantile(divide((opt4_122_call_vola_delta55) , (opt4_122_put_vola_delta55)),240)
ts_quantile(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),20)
ts_quantile(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),40)
ts_quantile(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),80)
ts_quantile(divide((opt4_122_call_vola_delta60) , (opt4_122_put_vola_delta60)),240)
ts_quantile(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),20)
ts_quantile(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),40)
ts_quantile(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),80)
ts_quantile(divide((opt4_122_call_vola_delta65) , (opt4_122_put_vola_delta65)),240)
ts_quantile(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),20)
ts_quantile(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),40)
ts_quantile(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),80)
ts_quantile(divide((opt4_122_call_vola_delta70) , (opt4_122_put_vola_delta70)),240)
ts_quantile(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),20)
ts_quantile(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),40)
ts_quantile(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),80)
ts_quantile(divide((opt4_122_call_vola_delta75) , (opt4_122_put_vola_delta75)),240)
ts_quantile(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),20)
ts_quantile(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),40)
ts_quantile(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),80)
ts_quantile(divide((opt4_122_call_vola_delta80) , (opt4_122_put_vola_delta80)),240)
ts_quantile(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),20)
ts_quantile(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),40)
ts_quantile(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),80)
ts_quantile(divide((opt4_547_call_vola_delta30) , (opt4_547_put_vola_delta30)),240)
ts_quantile(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),20)
ts_quantile(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),40)
ts_quantile(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),80)
ts_quantile(divide((opt4_547_call_vola_delta35) , (opt4_547_put_vola_delta35)),240)
ts_quantile(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),20)
ts_quantile(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),40)
ts_quantile(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),80)
ts_quantile(divide((opt4_547_call_vola_delta40) , (opt4_547_put_vola_delta40)),240)
ts_quantile(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),20)
ts_quantile(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),40)
ts_quantile(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),80)
ts_quantile(divide((opt4_547_call_vola_delta45) , (opt4_547_put_vola_delta45)),240)
ts_quantile(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),20)
ts_quantile(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),40)
ts_quantile(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),80)
ts_quantile(divide((opt4_547_call_vola_delta50) , (opt4_547_put_vola_delta50)),240)
ts_quantile(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),20)
ts_quantile(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),40)
ts_quantile(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),80)
ts_quantile(divide((opt4_547_call_vola_delta55) , (opt4_547_put_vola_delta55)),240)
ts_quantile(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),20)
ts_quantile(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),40)
ts_quantile(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),80)
ts_quantile(divide((opt4_547_call_vola_delta60) , (opt4_547_put_vola_delta60)),240)
ts_quantile(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),20)
ts_quantile(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),40)
ts_quantile(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),80)
ts_quantile(divide((opt4_547_call_vola_delta65) , (opt4_547_put_vola_delta65)),240)
ts_quantile(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),20)
ts_quantile(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),40)
ts_quantile(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),80)
ts_quantile(divide((opt4_547_call_vola_delta70) , (opt4_547_put_vola_delta70)),240)
ts_quantile(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),20)
ts_quantile(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),40)
ts_quantile(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),80)
ts_quantile(divide((opt4_547_call_vola_delta75) , (opt4_547_put_vola_delta75)),240)
ts_quantile(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),20)
ts_quantile(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),40)
ts_quantile(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),80)
ts_quantile(divide((opt4_547_call_vola_delta80) , (opt4_547_put_vola_delta80)),240)
ts_quantile(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),20)
ts_quantile(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),40)
ts_quantile(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),80)
ts_quantile(divide((opt4_273_call_vola_delta30) , (opt4_273_put_vola_delta30)),240)
ts_quantile(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),20)
ts_quantile(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),40)
ts_quantile(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),80)
ts_quantile(divide((opt4_273_call_vola_delta35) , (opt4_273_put_vola_delta35)),240)
ts_quantile(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),20)
ts_quantile(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),40)
ts_quantile(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),80)
ts_quantile(divide((opt4_273_call_vola_delta40) , (opt4_273_put_vola_delta40)),240)
ts_quantile(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),20)
ts_quantile(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),40)
ts_quantile(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),80)
ts_quantile(divide((opt4_273_call_vola_delta45) , (opt4_273_put_vola_delta45)),240)
ts_quantile(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),20)
ts_quantile(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),40)
ts_quantile(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),80)
ts_quantile(divide((opt4_273_call_vola_delta50) , (opt4_273_put_vola_delta50)),240)
ts_quantile(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),20)
ts_quantile(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),40)
ts_quantile(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),80)
ts_quantile(divide((opt4_273_call_vola_delta55) , (opt4_273_put_vola_delta55)),240)
ts_quantile(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),20)
ts_quantile(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),40)
ts_quantile(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),80)
ts_quantile(divide((opt4_273_call_vola_delta60) , (opt4_273_put_vola_delta60)),240)
ts_quantile(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),20)
ts_quantile(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),40)
ts_quantile(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),80)
ts_quantile(divide((opt4_273_call_vola_delta65) , (opt4_273_put_vola_delta65)),240)
ts_quantile(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),20)
ts_quantile(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),40)
ts_quantile(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),80)
ts_quantile(divide((opt4_273_call_vola_delta70) , (opt4_273_put_vola_delta70)),240)
ts_quantile(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),20)
ts_quantile(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),40)
ts_quantile(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),80)
ts_quantile(divide((opt4_273_call_vola_delta75) , (opt4_273_put_vola_delta75)),240)
ts_quantile(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),20)
ts_quantile(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),40)
ts_quantile(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),80)
ts_quantile(divide((opt4_273_call_vola_delta80) , (opt4_273_put_vola_delta80)),240)
ts_quantile(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),20)
ts_quantile(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),40)
ts_quantile(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),80)
ts_quantile(divide((opt4_152_call_vola_delta30) , (opt4_152_put_vola_delta30)),240)
ts_quantile(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),20)
ts_quantile(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),40)
ts_quantile(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),80)
ts_quantile(divide((opt4_152_call_vola_delta35) , (opt4_152_put_vola_delta35)),240)
ts_quantile(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),20)
ts_quantile(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),40)
ts_quantile(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),80)
ts_quantile(divide((opt4_152_call_vola_delta40) , (opt4_152_put_vola_delta40)),240)
ts_quantile(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),20)
ts_quantile(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),40)
ts_quantile(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),80)
ts_quantile(divide((opt4_152_call_vola_delta45) , (opt4_152_put_vola_delta45)),240)
ts_quantile(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),20)
ts_quantile(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),40)
ts_quantile(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),80)
ts_quantile(divide((opt4_152_call_vola_delta50) , (opt4_152_put_vola_delta50)),240)
ts_quantile(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),20)
ts_quantile(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),40)
ts_quantile(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),80)
ts_quantile(divide((opt4_152_call_vola_delta55) , (opt4_152_put_vola_delta55)),240)
ts_quantile(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),20)
ts_quantile(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),40)
ts_quantile(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),80)
ts_quantile(divide((opt4_152_call_vola_delta60) , (opt4_152_put_vola_delta60)),240)
ts_quantile(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),20)
ts_quantile(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),40)
ts_quantile(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),80)
ts_quantile(divide((opt4_152_call_vola_delta65) , (opt4_152_put_vola_delta65)),240)
ts_quantile(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),20)
ts_quantile(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),40)
ts_quantile(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),80)
ts_quantile(divide((opt4_152_call_vola_delta70) , (opt4_152_put_vola_delta70)),240)
ts_quantile(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),20)
ts_quantile(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),40)
ts_quantile(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),80)
ts_quantile(divide((opt4_152_call_vola_delta75) , (opt4_152_put_vola_delta75)),240)
ts_quantile(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),20)
ts_quantile(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),40)
ts_quantile(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),80)
ts_quantile(divide((opt4_152_call_vola_delta80) , (opt4_152_put_vola_delta80)),240)
ts_quantile(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),20)
ts_quantile(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),40)
ts_quantile(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),80)
ts_quantile(divide((opt4_365_call_vola_delta30) , (opt4_365_put_vola_delta30)),240)
ts_quantile(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),20)
ts_quantile(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),40)
ts_quantile(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),80)
ts_quantile(divide((opt4_365_call_vola_delta35) , (opt4_365_put_vola_delta35)),240)
ts_quantile(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),20)
ts_quantile(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),40)
ts_quantile(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),80)
ts_quantile(divide((opt4_365_call_vola_delta40) , (opt4_365_put_vola_delta40)),240)
ts_quantile(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),20)
ts_quantile(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),40)
ts_quantile(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),80)
ts_quantile(divide((opt4_365_call_vola_delta45) , (opt4_365_put_vola_delta45)),240)
ts_quantile(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),20)
ts_quantile(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),40)
ts_quantile(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),80)
ts_quantile(divide((opt4_365_call_vola_delta50) , (opt4_365_put_vola_delta50)),240)
ts_quantile(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),20)
ts_quantile(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),40)
ts_quantile(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),80)
ts_quantile(divide((opt4_365_call_vola_delta55) , (opt4_365_put_vola_delta55)),240)
ts_quantile(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),20)
ts_quantile(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),40)
ts_quantile(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),80)
ts_quantile(divide((opt4_365_call_vola_delta60) , (opt4_365_put_vola_delta60)),240)
ts_quantile(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),20)
ts_quantile(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),40)
ts_quantile(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),80)
ts_quantile(divide((opt4_365_call_vola_delta65) , (opt4_365_put_vola_delta65)),240)
ts_quantile(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),20)
ts_quantile(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),40)
ts_quantile(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),80)
ts_quantile(divide((opt4_365_call_vola_delta70) , (opt4_365_put_vola_delta70)),240)
ts_quantile(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),20)
ts_quantile(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),40)
ts_quantile(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),80)
ts_quantile(divide((opt4_365_call_vola_delta75) , (opt4_365_put_vola_delta75)),240)
ts_quantile(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),20)
ts_quantile(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),40)
ts_quantile(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),80)
ts_quantile(divide((opt4_365_call_vola_delta80) , (opt4_365_put_vola_delta80)),240)
ts_quantile(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),20)
ts_quantile(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),40)
ts_quantile(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),80)
ts_quantile(divide((opt4_30_call_vola_delta30) , (opt4_30_put_vola_delta30)),240)
ts_quantile(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),20)
ts_quantile(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),40)
ts_quantile(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),80)
ts_quantile(divide((opt4_30_call_vola_delta35) , (opt4_30_put_vola_delta35)),240)
ts_quantile(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),20)
ts_quantile(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),40)
ts_quantile(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),80)
ts_quantile(divide((opt4_30_call_vola_delta40) , (opt4_30_put_vola_delta40)),240)
ts_quantile(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),20)
ts_quantile(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),40)
ts_quantile(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),80)
ts_quantile(divide((opt4_30_call_vola_delta45) , (opt4_30_put_vola_delta45)),240)
ts_quantile(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),20)
ts_quantile(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),40)
ts_quantile(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),80)
ts_quantile(divide((opt4_30_call_vola_delta50) , (opt4_30_put_vola_delta50)),240)
ts_quantile(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),20)
ts_quantile(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),40)
ts_quantile(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),80)
ts_quantile(divide((opt4_30_call_vola_delta55) , (opt4_30_put_vola_delta55)),240)
ts_quantile(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),20)
ts_quantile(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),40)
ts_quantile(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),80)
ts_quantile(divide((opt4_30_call_vola_delta60) , (opt4_30_put_vola_delta60)),240)
ts_quantile(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),20)
ts_quantile(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),40)
ts_quantile(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),80)
ts_quantile(divide((opt4_30_call_vola_delta65) , (opt4_30_put_vola_delta65)),240)
ts_quantile(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),20)
ts_quantile(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),40)
ts_quantile(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),80)
ts_quantile(divide((opt4_30_call_vola_delta70) , (opt4_30_put_vola_delta70)),240)
ts_quantile(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),20)
ts_quantile(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),40)
ts_quantile(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),80)
ts_quantile(divide((opt4_30_call_vola_delta75) , (opt4_30_put_vola_delta75)),240)
ts_quantile(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),20)
ts_quantile(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),40)
ts_quantile(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),80)
ts_quantile(divide((opt4_30_call_vola_delta80) , (opt4_30_put_vola_delta80)),240)