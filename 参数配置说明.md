# Alpha提取系统参数配置说明

## 📝 参数位置

所有可调节参数都位于 `alpha提取.py` 文件的**最上方**（第13-48行），方便您快速修改。

## ⚙️ 参数详细说明

### 1. 并行处理参数

```python
MAX_WORKERS_FACTORY_FILTER = 3      # "厂"型过滤并行线程数
MAX_WORKERS_CORRELATION_FILTER = 2  # 自相关过滤并行线程数
```

**建议值**:
- **保守**: 2-3 (避免API限流)
- **标准**: 3-5 (平衡速度和稳定性)
- **激进**: 5-8 (可能触发API限流)

### 2. 自相关过滤参数

```python
CORRELATION_THRESHOLD = 0.4          # 自相关阈值，保留相关性小于此值的Alpha
SUBMITTED_ALPHAS_SAMPLE_SIZE = 100   # 已提交Alpha样本数量
```

**相关性阈值建议**:
- **0.8+**: 宽松过滤 (允许一定相似性)
- **0.7**: 标准过滤 (平衡独特性和数量)
- **0.6**: 严格过滤 (要求高独特性)
- **0.4**: 极严格 (当前设置，最高独特性要求)

### 3. Alpha提取参数

```python
ALPHA_EXTRACT_CONFIG = {
    'status': 'UNSUBMITTED',         # Alpha状态
    'region': 'ASI',                 # 地区
    'delay': 1,                      # 延迟天数
    'universe': 'MINVOL1M',          # 宇宙
    'sharpe_min': 1,                 # 最小Sharpe值
    'fitness_min': 1,                # 最小Fitness值
    'turnover_max': 0.7,             # 最大Turnover值
    'limit': 1000,                   # 最大提取数量
    'use_unlimited': True,           # 是否使用无限制模式
    'include_reverse': True,         # 是否包含反向Alpha
}
```

**重要说明**:
- `limit`: 设置最大提取数量，可以超过100
- `use_unlimited`:
  - `True`: 使用`filter_alphas`获取所有符合条件的Alpha（可能很多）
  - `False`: 使用`filter_alphas_limited`最多获取100个Alpha
- `include_reverse`: **新功能**
  - `True`: 同时获取反向Alpha（Sharpe和Fitness为负值但绝对值满足要求）
  - `False`: 只获取正向Alpha

**地区选项**:
- `'ASI'`: 亚洲市场
- `'USA'`: 美国市场
- `'EUR'`: 欧洲市场
- `'GLB'`: 全球市场

**状态选项**:
- `'UNSUBMITTED'`: 未提交的Alpha
- `'SUBMITTED'`: 已提交的Alpha
- `'LIVE'`: 正在运行的Alpha

**宇宙选项**:
- `'MINVOL1M'`: 最小成交量1M
- `'TOP3000'`: 前3000只股票
- `'TOP1000'`: 前1000只股票

### 4. 时间范围参数 (使用北京时间)

```python
ALPHA_DATE_RANGE = {
    'start_date_beijing': '2025-07-01 00:00:00',  # 开始时间 (北京时间)
    'end_date_beijing': '2025-07-20 23:59:59',    # 结束时间 (北京时间)
}
```

**重要说明**:
- **输入格式**: 使用北京时间，格式为 `'YYYY-MM-DD HH:MM:SS'`
- **自动转换**: 系统会自动将北京时间转换为美东时间调用API
- **时区差异**: 北京时间比美东时间快13小时(标准时间)或12小时(夏令时)

**示例**:
- `'2025-01-01 09:00:00'` (北京时间上午9点)
- `'2025-12-31 23:59:59'` (北京时间年末最后一刻)

### 5. "厂"型过滤参数

```python
FACTORY_FILTER_CONFIG = {
    'check_years': [str(year) for year in range(2013, 2024)],  # 检查年份范围
    'zero_sharpe_threshold': True,    # 是否启用零Sharpe值检测
    'negative_ratio_threshold': 0.5,  # 负Sharpe年份比例阈值
}
```

**年份范围自定义**:
```python
# 示例：只检查最近5年
'check_years': [str(year) for year in range(2019, 2024)]

# 示例：检查特定年份
'check_years': ['2020', '2021', '2022']
```

## 🎯 常用配置组合

### 配置1: 保守设置 (高质量，少数量)
```python
MAX_WORKERS_FACTORY_FILTER = 2
MAX_WORKERS_CORRELATION_FILTER = 2
CORRELATION_THRESHOLD = 0.3
ALPHA_EXTRACT_CONFIG['sharpe_min'] = 2.0
```

### 配置2: 标准设置 (平衡)
```python
MAX_WORKERS_FACTORY_FILTER = 3
MAX_WORKERS_CORRELATION_FILTER = 2
CORRELATION_THRESHOLD = 0.6
ALPHA_EXTRACT_CONFIG['sharpe_min'] = 1.5
```

### 配置3: 宽松设置 (更多数量)
```python
MAX_WORKERS_FACTORY_FILTER = 5
MAX_WORKERS_CORRELATION_FILTER = 3
CORRELATION_THRESHOLD = 0.8
ALPHA_EXTRACT_CONFIG['sharpe_min'] = 1.2
```

### 配置4: 美国市场设置
```python
ALPHA_EXTRACT_CONFIG['region'] = 'USA'
ALPHA_EXTRACT_CONFIG['universe'] = 'TOP3000'
ALPHA_DATE_RANGE = {
    'start_date_beijing': '2025-07-01 13:00:00',  # 北京时间下午1点
    'end_date_beijing': '2025-07-31 13:00:00',    # 北京时间下午1点
}
```

### 配置5: 时间范围示例
```python
# 获取最近一周的Alpha
ALPHA_DATE_RANGE = {
    'start_date_beijing': '2025-07-14 00:00:00',
    'end_date_beijing': '2025-07-21 23:59:59',
}

# 获取特定月份的Alpha
ALPHA_DATE_RANGE = {
    'start_date_beijing': '2025-06-01 00:00:00',
    'end_date_beijing': '2025-06-30 23:59:59',
}
```

## 🔧 修改步骤

1. **打开文件**: 编辑 `alpha提取.py`
2. **找到参数区域**: 文件开头的 "用户可调节参数" 部分
3. **修改参数值**: 直接修改对应的数值
4. **保存文件**: 保存修改
5. **运行脚本**: `python alpha提取.py`

## ⚠️ 注意事项

### API限流
- 并行线程数过高可能触发API限流
- 建议从小值开始测试

### 内存使用
- 样本数量过大可能占用较多内存
- 建议SUBMITTED_ALPHAS_SAMPLE_SIZE不超过200

### 时间范围
- 时间范围过大可能导致Alpha数量过多
- 建议单次处理不超过1000个Alpha

### 相关性阈值
- 阈值过低可能过滤掉大部分Alpha
- 建议根据实际需求调整

## 📊 参数影响预估

| 参数调整 | 对结果数量的影响 | 对质量的影响 | 对速度的影响 |
|---------|----------------|-------------|-------------|
| 降低Sharpe阈值 | ↑ 增加 | ↓ 降低 | ↓ 变慢 |
| 提高相关性阈值 | ↑ 增加 | ↓ 降低 | - 无影响 |
| 增加并行线程 | - 无影响 | - 无影响 | ↑ 加快 |
| 扩大时间范围 | ↑ 增加 | - 无影响 | ↓ 变慢 |
| 更换地区 | ± 变化 | ± 变化 | - 无影响 |

---

**建议**: 首次使用建议保持默认参数，根据结果再进行调整。
