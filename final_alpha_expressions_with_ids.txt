nQgEgRM: {'code': 'ts_mean(ts_scale(fnd23_iabs, 22), 22)', 'description': None, 'operatorCount': 2}
GxLkr6P: {'code': 'ts_mean(ts_scale(fnd23_inas, 22), 5)', 'description': None, 'operatorCount': 2}
OVM3oWY: {'code': 'ts_delta(ts_delta(ts_backfill(fnd28_value_09402,240), 22 ), 120)', 'description': None, 'operatorCount': 3}
P8YVZnK: {'code': 'ts_delta(ts_delta(ts_backfill(fnd28_value_09402,240), 120 ), 22)', 'description': None, 'operatorCount': 3}
OVz1mA7: {'code': 'ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22)', 'description': None, 'operatorCount': 3}
WJmmqzG: {'code': 'zscore(group_neutralize ( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),bucket(rank(adv20), range="0, 1, 0.1" )\n))', 'description': None, 'operatorCount': 7}
KZRlg5g: {'code': 'group_neutralize ( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),bucket(rank(adv20), range="0, 1, 0.1" )\n)', 'description': None, 'operatorCount': 6}
ExAENKJ: {'code': 'group_scale( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),market)', 'description': None, 'operatorCount': 4}
10558ZM: {'code': 'zscore(( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22)\n))', 'description': None, 'operatorCount': 4}
JrLG6Px: {'code': 'group_zscore( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),market)', 'description': None, 'operatorCount': 4}
6Nbjq5O: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),market)', 'description': None, 'operatorCount': 4}
vPVrJqz: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),subindustry)', 'description': None, 'operatorCount': 4}
jOQAwMo: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),industry)', 'description': None, 'operatorCount': 4}
p3gR1wx: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),sector)', 'description': None, 'operatorCount': 4}
oZeL9Q5: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),pv13_10_minvol_1m_sector)', 'description': None, 'operatorCount': 4}
aG3bWvO: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),pv13_10_f3_g2_minvol_1m_sector)', 'description': None, 'operatorCount': 4}
mQkm8vp: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),pv13_20_minvol_1m_sector)', 'description': None, 'operatorCount': 4}
Q6dbVvX: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),pv13_5_minvol_1m_sector)', 'description': None, 'operatorCount': 4}
r8mOlOa: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),pv13_5_f3_g2_minvol_1m_sector)', 'description': None, 'operatorCount': 4}
8XqjQ7a: {'code': 'group_neutralize( ts_delta(ts_delta(ts_backfill(fnd28_value_18265q,240), 22 ), 22),pv13_2_minvol_1m_sector)', 'description': None, 'operatorCount': 4}
oZzX3O5: {'code': 'ts_delta(ts_zscore(anl4_afv4_eps_low,22), 22)-ts_delta(ts_zscore(anl4_afv4_eps_low,10), 10)', 'description': None, 'operatorCount': 5}
r8o0Rao: {'code': 'ts_delta(ts_zscore(pv37_all_sbit,240), 240)-ts_delta(ts_zscore(pv37_all_sbit,100), 100)', 'description': None, 'operatorCount': 5}
PKZr7Yw: {'code': 'ts_av_diff(fnd28_value_08241q/ts_mean(fnd28_value_08241q, 400),200)', 'description': None, 'operatorCount': 3}
