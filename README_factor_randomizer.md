# 因子随机化工具

一个强大的Python工具，用于随机化因子文件中的因子表达式，支持多种模式和配置选项。

## 功能特性

- ✅ **单文件随机化**: 对单个因子文件进行随机打乱
- ✅ **多文件混合**: 混合多个因子文件并随机化
- ✅ **按比例分布**: 按指定比例从不同文件中选择因子
- ✅ **数量控制**: 可以指定最终输出的因子数量
- ✅ **可重现结果**: 支持随机种子，确保结果可重现
- ✅ **交互式界面**: 提供友好的命令行交互界面
- ✅ **批处理模式**: 支持命令行参数批量处理

## 文件结构

```
factor_randomizer.py              # 核心工具类
factor_randomizer_interactive.py  # 交互式界面
factor_randomizer_examples.py     # 使用示例
README_factor_randomizer.md       # 说明文档
```

## 快速开始

### 1. 交互式使用（推荐）

```bash
python factor_randomizer_interactive.py
```

这将启动交互式界面，按照提示操作即可。

### 2. 命令行使用

#### 单文件随机化
```bash
# 随机化整个文件
python factor_randomizer.py --mode single --input "decoded_expressions (12).txt" --output "randomized_factors.txt"

# 只选择前100个随机因子
python factor_randomizer.py --mode single --input "decoded_expressions (12).txt" --output "randomized_100.txt" --count 100

# 使用固定随机种子
python factor_randomizer.py --mode single --input "decoded_expressions (12).txt" --output "reproducible.txt" --seed 42
```

#### 多文件混合（需要修改代码中的配置）
```bash
python factor_randomizer.py --mode mix --output "mixed_factors.txt" --count 200
```

#### 按比例分布（需要修改代码中的配置）
```bash
python factor_randomizer.py --mode distribution --output "distributed_factors.txt"
```

### 3. 编程使用

```python
from factor_randomizer import FactorRandomizer

# 创建随机化器
randomizer = FactorRandomizer(seed=42)

# 单文件随机化
randomizer.randomize_single_file(
    input_file="decoded_expressions (12).txt",
    output_file="output.txt",
    num_factors=100
)

# 多文件混合
file_configs = [
    {'file': 'file1.txt', 'count': 50},
    {'file': 'file2.txt', 'count': 30}
]
randomizer.mix_and_randomize_files(
    file_configs=file_configs,
    output_file="mixed.txt",
    total_factors=60
)

# 按比例分布
file_configs = [
    {'file': 'file1.txt', 'ratio': 0.7},
    {'file': 'file2.txt', 'ratio': 0.3}
]
randomizer.randomize_with_distribution(
    file_configs=file_configs,
    output_file="distributed.txt"
)
```

## 使用场景

### 场景1: 因子池随机采样
从大型因子库中随机选择一定数量的因子进行测试：

```python
randomizer = FactorRandomizer()
randomizer.randomize_single_file(
    "decoded_expressions (12).txt",
    "sample_factors.txt",
    num_factors=500
)
```

### 场景2: 多策略因子混合
将不同策略的因子按比例混合：

```python
file_configs = [
    {'file': 'momentum_factors.txt', 'ratio': 0.4},
    {'file': 'mean_reversion_factors.txt', 'ratio': 0.3},
    {'file': 'volatility_factors.txt', 'ratio': 0.3}
]
randomizer.randomize_with_distribution(file_configs, "mixed_strategy.txt")
```

### 场景3: A/B测试准备
为A/B测试准备不同的因子组合：

```python
# 组合A
randomizer_a = FactorRandomizer(seed=100)
randomizer_a.randomize_single_file("factors.txt", "test_group_a.txt", 200)

# 组合B
randomizer_b = FactorRandomizer(seed=200)
randomizer_b.randomize_single_file("factors.txt", "test_group_b.txt", 200)
```

## 高级功能

### 随机种子控制
使用随机种子确保结果可重现：

```python
# 相同种子产生相同结果
randomizer1 = FactorRandomizer(seed=42)
randomizer2 = FactorRandomizer(seed=42)
# 两次运行结果完全相同
```

### 文件格式支持
工具支持以下格式的因子文件：
- `.txt` 文件：每行一个因子表达式
- 自动检测编码（UTF-8）
- 自动过滤空行

### 日志记录
工具提供详细的日志信息：
- 文件加载状态
- 处理进度
- 错误信息
- 结果统计

## 运行示例

查看完整的使用示例：

```bash
python factor_randomizer_examples.py
```

这将运行所有示例场景，展示工具的各种功能。

## 注意事项

1. **文件路径**: 确保输入文件存在且可读
2. **输出目录**: 确保输出目录有写入权限
3. **内存使用**: 大文件处理时注意内存使用情况
4. **编码格式**: 文件应使用UTF-8编码
5. **备份数据**: 处理重要数据前请先备份

## 错误处理

工具包含完善的错误处理机制：
- 文件不存在时的友好提示
- 格式错误时的详细说明
- 权限问题的解决建议
- 异常情况的日志记录

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多文件格式（CSV、JSON等）
- 添加因子去重功能
- 实现因子质量评估
- 支持正则表达式过滤
- 添加因子分类功能

## 技术支持

如果遇到问题或需要新功能，请：
1. 检查日志输出中的错误信息
2. 确认文件格式和路径正确
3. 查看示例代码中的用法
4. 检查Python环境和依赖

---

**版本**: 1.0  
**作者**: AI Assistant  
**更新日期**: 2025-07-22
