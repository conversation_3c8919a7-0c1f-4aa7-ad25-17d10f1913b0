# Alpha挖掘建议复现项目

本项目基于 `ASI_fundamental28翻译后字段(1).xlsx` 文件的Y列alpha挖掘建议，使用 `operators_2025_master_analysis.md` 中的WorldQuant Brain操作符进行复现。

## 项目文件说明

### 1. 核心文件
- `alpha_strategies_reproduction.py` - Python格式的策略实现代码
- `alpha_reproduction_guide.md` - 详细的复现指南和操作符说明
- `worldquant_brain_alphas.txt` - 可直接在WorldQuant Brain平台使用的alpha表达式
- `README.md` - 项目说明文档

### 2. 源数据文件
- `ASI_fundamental28翻译后字段(1).xlsx` - 包含alpha挖掘建议的Excel文件
- `operators_2025_master_analysis.md` - WorldQuant Brain操作符完整文档

## 复现的Alpha策略

### 主要策略类别

1. **估值策略**
   - 高Beta+低估值组合
   - 低市净率+高ROE破净反转
   - 综合估值修复

2. **质量策略**
   - TTM经营现金流质量
   - 现金流>净利润盈利质量
   - 高毛利+低估值盈利质量

3. **成长策略**
   - 高EPS增长+低市盈率
   - 高销售增长+低市销率
   - ROE持续改善动量

4. **稳健策略**
   - 现金流稳健性
   - 行业中性化策略

## 使用的核心操作符

### 算术运算
- `add`, `subtract`, `multiply`, `divide`
- `max`, `min`, `abs`, `reverse`

### 逻辑运算
- `and`, `or`, `not`
- `greater`, `less`, `equal`
- `if_else` - 条件选择

### 时间序列
- `ts_delay` - 时间延迟
- `ts_delta` - 时间差分
- `ts_mean`, `ts_sum`, `ts_std_dev` - 统计函数
- `ts_quantile` - 分位数
- `ts_decay_linear` - 线性衰减
- `ts_regression` - 回归分析

### 横截面
- `rank` - 排名
- `zscore` - Z分数标准化
- `winsorize` - 温莎化处理
- `scale` - 缩放

### 分组操作
- `group_neutralize` - 分组中性化
- `group_rank` - 分组排名

### 风险控制
- `hump` - 换手率控制
- `normalize` - 标准化

## 快速开始

### 1. 在WorldQuant Brain平台使用

直接复制 `worldquant_brain_alphas.txt` 中的表达式到WorldQuant Brain平台：

```
# 示例：简单的Beta-估值策略
rank(multiply(beta, reverse(pe_ratio)))
```

### 2. 理解策略逻辑

参考 `alpha_reproduction_guide.md` 了解每个策略的详细实现逻辑。

### 3. 自定义策略

基于 `alpha_strategies_reproduction.py` 中的模板创建自己的策略。

## 策略示例

### 示例1: 高Beta+低估值组合
```python
# 高Beta选股
high_beta = greater(beta, ts_mean(beta, 252))

# 低估值选股
low_pe = less(pe_ratio, ts_mean(pe_ratio, 252))

# 组合条件
condition = and(high_beta, low_pe)

# 构建alpha信号
alpha = if_else(condition, 
               multiply(rank(beta), rank(reverse(pe_ratio))), 
               0)

# 标准化处理
alpha = zscore(alpha)
```

### 示例2: 现金流质量策略
```python
# 现金流为正且增长
positive_growing_cf = and(greater(operating_cash_flow_ttm, 0), 
                         greater(operating_cash_flow_ttm, 
                                ts_delay(operating_cash_flow_ttm, 63)))

# 构建信号
alpha = if_else(positive_growing_cf, 
               rank(operating_cash_flow_ttm), 
               0)

# 行业中性化
alpha = group_neutralize(alpha, industry)
```

## 风险控制最佳实践

### 1. 异常值处理
```python
# 温莎化处理
alpha = winsorize(alpha, std=3)

# Z分数标准化
alpha = zscore(alpha)
```

### 2. 换手率控制
```python
# 限制变化幅度
alpha = hump(alpha, hump=0.01)

# 时间序列平滑
alpha = ts_decay_linear(alpha, 10)
```

### 3. 行业中性化
```python
# 行业中性化
alpha = group_neutralize(alpha, industry)
```

## 参数调优建议

### 时间窗口选择
- **短期**: 20-63天 (1-3个月)
- **中期**: 126-189天 (6-9个月)  
- **长期**: 252天 (1年)

### 分位数阈值
- **保守**: 0.3-0.7分位数
- **中等**: 0.2-0.8分位数
- **激进**: 0.1-0.9分位数

### 风险控制参数
- **温莎化**: std=3-4
- **换手率控制**: hump=0.005-0.02
- **衰减窗口**: 5-20天

## 注意事项

1. **数据字段映射**: 确保字段名称与平台一致
2. **回测验证**: 实施前进行充分回测
3. **参数调优**: 根据表现调整参数
4. **风险监控**: 持续监控策略表现
5. **组合管理**: 考虑多策略组合

## 扩展建议

1. **多因子组合**: 结合多个alpha信号
2. **动态权重**: 根据市场环境调整权重
3. **风险预算**: 实施风险预算管理
4. **机器学习**: 结合ML方法优化参数

## 联系方式

如有问题或建议，请参考：
- WorldQuant Brain官方文档
- `operators_2025_master_analysis.md` 操作符文档
- 本项目的详细实现指南

---

**免责声明**: 本项目仅供学习和研究使用，不构成投资建议。实际投资决策请谨慎考虑风险。
