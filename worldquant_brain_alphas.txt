# WorldQuant Brain Alpha表达式 (简化版本)
# 基于ASI_fundamental28翻译后字段(1).xlsx的Y列alpha挖掘建议复现
# 避免逻辑操作符和风险控制操作符

# ==================== Alpha 1: 高Beta+低估值组合 ====================
# 原建议: "高Beta+低估值组合，博取市场反弹弹性"
# 策略：Beta值乘以市盈率倒数

multiply(rank(beta), rank(reverse(pe_ratio)))

# ==================== Alpha 2: TTM经营现金流质量 ====================
# 原建议: "TTM经营现金流为正且环比上升"
# 策略：现金流与历史均值比值

divide(operating_cash_flow_ttm, ts_mean(operating_cash_flow_ttm, 252))

# ==================== Alpha 3: EPS增长动量 ====================
# 原建议: "高EPS增长+低市盈率，成长alpha"
# 策略：EPS增长率乘以市盈率倒数

multiply(divide(subtract(eps, ts_delay(eps, 252)), abs(ts_delay(eps, 252))), reverse(pe_ratio))

# ==================== Alpha 4: 破净反转 ====================
# 原建议: "低市净率+高ROE，破净反转alpha"
# 策略：ROE除以市净率

divide(roe, pb_ratio)

# ==================== Alpha 5: 毛利率质量 ====================
# 原建议: "高毛利+低估值，盈利质量alpha"
# 策略：毛利率乘以市销率倒数

multiply(gross_margin, reverse(ps_ratio))

# ==================== Alpha 6: 现金流稳健性 ====================
# 原建议: "季度为正+年度为正，现金流稳健alpha"
# 策略：现金流与波动率比值

divide(operating_cash_flow, ts_std_dev(operating_cash_flow, 63))

# ==================== Alpha 7: ROE动量 ====================
# 原建议: "ROE持续改善+股价滞后，动量alpha"
# 策略：ROE变化乘以价格动量倒数

multiply(ts_delta(roe, 63), reverse(ts_delta(close, 20)))

# ==================== Alpha 8: 销售增长效率 ====================
# 原建议: "高销售增长+低市销率，成长alpha"
# 策略：收入增长率除以市销率

divide(divide(subtract(revenue, ts_delay(revenue, 252)), abs(ts_delay(revenue, 252))), ps_ratio)

# ==================== Alpha 9: 盈利质量 ====================
# 原建议: "现金流>净利润，盈利质量alpha"
# 策略：现金流与净利润比值

divide(operating_cash_flow, max(net_income, 0.01))

# ==================== Alpha 10: 综合估值修复 ====================
# 多因子组合策略
# 策略：多估值指标综合

add(add(reverse(pe_ratio), reverse(pb_ratio)), reverse(ps_ratio))

# ==================== 超简化版本 (最基础) ====================

# Alpha A: Beta估值
multiply(beta, reverse(pe_ratio))

# Alpha B: 现金流收益率
divide(operating_cash_flow, market_cap)

# Alpha C: 盈利增长
ts_delta(eps, 252)

# Alpha D: 价值因子
reverse(pb_ratio)

# Alpha E: 质量因子
roe

# Alpha F: 动量因子
ts_delta(close, 20)

# Alpha G: 毛利率
gross_margin

# Alpha H: 现金流稳定性
divide(operating_cash_flow, ts_std_dev(operating_cash_flow, 252))

# ==================== 组合策略 ====================

# Alpha I: 价值+质量组合
multiply(reverse(pe_ratio), roe)

# Alpha J: 成长+动量组合
multiply(ts_delta(eps, 252), ts_delta(close, 20))

# Alpha K: 现金流+估值组合
multiply(divide(operating_cash_flow, market_cap), reverse(pe_ratio))

# Alpha L: 毛利率+估值组合
multiply(gross_margin, reverse(ps_ratio))

# Alpha M: ROE+估值组合
multiply(roe, reverse(pb_ratio))

# ==================== 使用说明 ====================
# 1. 将以上表达式复制到WorldQuant Brain平台
# 2. 确保数据字段名称与平台一致
# 3. 根据需要调整参数（如时间窗口等）
# 4. 进行回测验证

# 重要改进:
# - 避免使用逻辑操作符 (and, or, if_else, greater, less等)
# - 避免使用风险控制操作符 (winsorize, zscore, hump, group_neutralize等)
# - 只使用基础算术操作符 (add, subtract, multiply, divide, reverse等)
# - 只使用时间序列操作符 (ts_delta, ts_mean, ts_std_dev等)

# 推荐测试顺序:
# 1. 先测试超简化版本 (Alpha A-H) - 单因子策略
# 2. 再测试组合策略 (Alpha I-M) - 双因子组合
# 3. 最后测试完整策略 (Alpha 1-10) - 复杂策略

# 字段名称可能需要调整:
# pe_ratio -> pe
# pb_ratio -> pb
# ps_ratio -> ps
# operating_cash_flow -> ocf
# market_cap -> mktcap
