#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
因子随机化工具 - 交互式版本
提供简单易用的交互界面来随机化因子文件
"""

import os
from factor_randomizer import FactorRandomizer

def print_banner():
    """打印工具横幅"""
    print("=" * 60)
    print("           因子随机化工具 v1.0")
    print("=" * 60)
    print()

def get_valid_file_path(prompt: str) -> str:
    """获取有效的文件路径"""
    while True:
        file_path = input(prompt).strip()
        if os.path.exists(file_path):
            return file_path
        print(f"❌ 文件不存在: {file_path}")
        print("请重新输入正确的文件路径")

def get_positive_int(prompt: str, allow_none: bool = True) -> int:
    """获取正整数输入"""
    while True:
        value = input(prompt).strip()
        if allow_none and (value == "" or value.lower() == "none"):
            return None
        try:
            num = int(value)
            if num > 0:
                return num
            print("❌ 请输入正整数")
        except ValueError:
            print("❌ 请输入有效的数字")

def get_float_0_to_1(prompt: str) -> float:
    """获取0-1之间的浮点数"""
    while True:
        try:
            value = float(input(prompt).strip())
            if 0 <= value <= 1:
                return value
            print("❌ 请输入0到1之间的数值")
        except ValueError:
            print("❌ 请输入有效的数字")

def mode_single_file():
    """单文件随机化模式"""
    print("\n📁 单文件随机化模式")
    print("-" * 30)
    
    input_file = get_valid_file_path("请输入因子文件路径: ")
    output_file = input("请输入输出文件路径: ").strip()
    
    print(f"\n当前文件包含因子数量: ", end="")
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            total_count = len([line for line in f if line.strip()])
        print(f"{total_count}")
    except:
        print("无法读取")
        return
    
    num_factors = get_positive_int(f"请输入要选择的因子数量 (最大{total_count}, 回车表示全部): ")
    
    seed = get_positive_int("请输入随机种子 (回车表示随机): ")
    
    print("\n🔄 开始处理...")
    randomizer = FactorRandomizer(seed=seed)
    success = randomizer.randomize_single_file(input_file, output_file, num_factors)
    
    if success:
        print("✅ 随机化完成！")
    else:
        print("❌ 随机化失败！")

def mode_mix_files():
    """多文件混合模式"""
    print("\n📁 多文件混合模式")
    print("-" * 30)
    
    file_configs = []
    
    while True:
        print(f"\n当前已添加 {len(file_configs)} 个文件")
        if len(file_configs) > 0:
            for i, config in enumerate(file_configs, 1):
                print(f"  {i}. {config['file']} (选择 {config.get('count', '全部')} 个)")
        
        choice = input("\n选择操作: [a]添加文件, [d]删除文件, [c]继续处理, [q]退出: ").lower()
        
        if choice == 'a':
            file_path = get_valid_file_path("请输入文件路径: ")
            
            # 显示文件信息
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    total_count = len([line for line in f if line.strip()])
                print(f"该文件包含 {total_count} 个因子")
            except:
                print("无法读取文件")
                continue
            
            count = get_positive_int(f"从该文件选择多少个因子 (最大{total_count}, 回车表示全部): ")
            
            file_configs.append({
                'file': file_path,
                'count': count
            })
            print("✅ 文件已添加")
            
        elif choice == 'd' and file_configs:
            try:
                index = int(input("请输入要删除的文件编号: ")) - 1
                if 0 <= index < len(file_configs):
                    removed = file_configs.pop(index)
                    print(f"✅ 已删除: {removed['file']}")
                else:
                    print("❌ 无效的编号")
            except ValueError:
                print("❌ 请输入有效的数字")
                
        elif choice == 'c':
            if not file_configs:
                print("❌ 请至少添加一个文件")
                continue
            break
            
        elif choice == 'q':
            return
    
    output_file = input("\n请输入输出文件路径: ").strip()
    total_factors = get_positive_int("请输入最终输出的总因子数量 (回车表示全部): ")
    seed = get_positive_int("请输入随机种子 (回车表示随机): ")
    
    print("\n🔄 开始处理...")
    randomizer = FactorRandomizer(seed=seed)
    success = randomizer.mix_and_randomize_files(file_configs, output_file, total_factors)
    
    if success:
        print("✅ 混合随机化完成！")
    else:
        print("❌ 混合随机化失败！")

def mode_distribution():
    """按比例分布模式"""
    print("\n📊 按比例分布模式")
    print("-" * 30)
    
    file_configs = []
    total_ratio = 0.0
    
    while True:
        print(f"\n当前已添加 {len(file_configs)} 个文件，总比例: {total_ratio:.2%}")
        if len(file_configs) > 0:
            for i, config in enumerate(file_configs, 1):
                print(f"  {i}. {config['file']} (比例 {config['ratio']:.2%})")
        
        if total_ratio >= 1.0:
            print("⚠️  总比例已达到100%")
        
        choice = input("\n选择操作: [a]添加文件, [d]删除文件, [c]继续处理, [q]退出: ").lower()
        
        if choice == 'a':
            if total_ratio >= 1.0:
                print("❌ 总比例已达到100%，无法添加更多文件")
                continue
                
            file_path = get_valid_file_path("请输入文件路径: ")
            
            # 显示文件信息
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    total_count = len([line for line in f if line.strip()])
                print(f"该文件包含 {total_count} 个因子")
            except:
                print("无法读取文件")
                continue
            
            remaining_ratio = 1.0 - total_ratio
            print(f"剩余可分配比例: {remaining_ratio:.2%}")
            
            ratio = get_float_0_to_1(f"请输入该文件的选择比例 (0-{remaining_ratio:.2f}): ")
            
            if ratio > remaining_ratio:
                print(f"❌ 比例过大，最大可设置 {remaining_ratio:.2f}")
                continue
            
            file_configs.append({
                'file': file_path,
                'ratio': ratio
            })
            total_ratio += ratio
            print("✅ 文件已添加")
            
        elif choice == 'd' and file_configs:
            try:
                index = int(input("请输入要删除的文件编号: ")) - 1
                if 0 <= index < len(file_configs):
                    removed = file_configs.pop(index)
                    total_ratio -= removed['ratio']
                    print(f"✅ 已删除: {removed['file']}")
                else:
                    print("❌ 无效的编号")
            except ValueError:
                print("❌ 请输入有效的数字")
                
        elif choice == 'c':
            if not file_configs:
                print("❌ 请至少添加一个文件")
                continue
            break
            
        elif choice == 'q':
            return
    
    output_file = input("\n请输入输出文件路径: ").strip()
    seed = get_positive_int("请输入随机种子 (回车表示随机): ")
    
    print("\n🔄 开始处理...")
    randomizer = FactorRandomizer(seed=seed)
    success = randomizer.randomize_with_distribution(file_configs, output_file)
    
    if success:
        print("✅ 按比例分布随机化完成！")
    else:
        print("❌ 按比例分布随机化失败！")

def main():
    """主函数"""
    print_banner()
    
    while True:
        print("请选择运行模式:")
        print("1. 单文件随机化 - 对单个文件中的因子进行随机打乱")
        print("2. 多文件混合 - 混合多个文件并随机化")
        print("3. 按比例分布 - 按指定比例从多个文件中选择因子")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            mode_single_file()
        elif choice == '2':
            mode_mix_files()
        elif choice == '3':
            mode_distribution()
        elif choice == '4':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
