# 信息熵因子构建指南

## 基于WorldQuant Brain操作符的信息熵因子构建

### 理论基础

信息熵的数学定义：`H(X) = -∑ p(xi) * log(p(xi))`

由于WorldQuant Brain没有直接的信息熵操作符，我们需要用现有操作符来近似构建。

## 方法一：基于标准差的熵近似

### 最简单的信息熵因子
```python
# 分布离散度作为信息熵的代理
simple_entropy = rank(ts_std_dev(returns, 20))
```

**经济逻辑**: 标准差越大，分布越分散，信息熵越高

### 相对信息熵
```python
# 短期vs长期信息熵对比
relative_entropy = subtract(
    rank(ts_std_dev(returns, 5)),     # 短期熵
    rank(ts_std_dev(returns, 20))     # 长期熵
)
```

## 方法二：基于分桶的离散化熵

### 价格分布熵
```python
# 步骤1: 将收益率分桶
price_buckets = bucket(rank(returns), range="0,1,0.1")

# 步骤2: 计算每个桶内的分散度
bucket_dispersion = group_zscore(ts_std_dev(returns, 10), price_buckets)

# 步骤3: 最终熵因子
price_entropy = rank(bucket_dispersion)
```

### 成交量信息熵
```python
# 基于成交量分布的熵
volume_entropy = rank(
    group_mean(
        ts_std_dev(volume/adv20, 10),
        bucket(rank(volume/adv20), range="0,1,0.2")
    )
)
```

## 方法三：多维联合熵

### 价格-成交量联合熵
```python
# 联合分布的信息熵近似
joint_entropy = rank(
    add(
        multiply(0.6, ts_std_dev(returns, 20)),
        multiply(0.4, ts_std_dev(volume/adv20, 20))
    )
)
```

### 条件信息熵
```python
# 基于市场状态的条件熵
conditional_entropy = if_else(
    greater(volume, adv20),                    # 高成交量条件
    rank(ts_std_dev(returns, 10)),            # 高量时的价格熵
    rank(ts_std_dev(returns, 20))             # 正常量时的价格熵
)
```

## 方法四：基于排名分布的熵

### 排名熵因子
```python
# 使用排名分布计算熵
rank_entropy = rank(
    ts_std_dev(
        ts_rank(returns, 20),     # 时间序列排名
        20
    )
)
```

### 相对排名熵
```python
# 不同时间窗口排名的熵差异
rank_entropy_diff = subtract(
    rank(ts_std_dev(ts_rank(returns, 5), 5)),
    rank(ts_std_dev(ts_rank(returns, 20), 20))
)
```

## 方法五：基于分位数的熵近似

### 分位数分散度熵
```python
# 使用分位数操作近似熵
quantile_entropy = rank(
    subtract(
        ts_quantile(returns, 20, driver="gaussian"),
        ts_mean(returns, 20)
    )
)
```

## 高级信息熵因子

### 交叉熵因子
```python
# 实际分布vs理论分布的差异
cross_entropy = subtract(
    rank(ts_std_dev(returns, 20)),                    # 实际分布熵
    rank(ts_mean(abs(ts_delta(returns, 1)), 60))      # 理论分布熵
)
```

### 互信息近似因子
```python
# 价格和成交量的互信息
mutual_info_approx = subtract(
    # 联合分布的熵
    rank(ts_std_dev(
        multiply(
            ts_zscore(returns, 20),
            ts_zscore(volume/adv20, 20)
        ), 20
    )),
    # 边际分布熵之和
    add(
        rank(ts_std_dev(returns, 20)),
        rank(ts_std_dev(volume/adv20, 20))
    )
)
```

### 时变信息熵
```python
# 信息熵的时间变化率
entropy_change_rate = ts_delta(
    rank(ts_std_dev(returns, 20)),
    5
)
```

## 实用信息熵因子模板

### 模板1: 基础分布熵
```python
basic_entropy = rank(
    divide(
        ts_std_dev(<field>, <window>),
        add(abs(ts_mean(<field>, <window>)), 1e-8)
    )
)
```

### 模板2: 多时间框架熵
```python
multi_timeframe_entropy = add(
    multiply(0.5, rank(ts_std_dev(<field>, <short_window>))),
    multiply(0.3, rank(ts_std_dev(<field>, <medium_window>))),
    multiply(0.2, rank(ts_std_dev(<field>, <long_window>)))
)
```

### 模板3: 条件熵
```python
conditional_entropy = if_else(
    <condition>,
    rank(ts_std_dev(<field>, <window1>)),
    rank(ts_std_dev(<field>, <window2>))
)
```

## 参数建议

### 时间窗口选择
- **短期**: 5-10天 (捕捉短期信息变化)
- **中期**: 20-30天 (捕捉中期趋势)
- **长期**: 60-120天 (捕捉长期结构)

### 字段选择优先级
1. **returns** - 最直接的价格信息
2. **volume/adv20** - 流动性信息
3. **volatility** - 风险信息
4. **基本面比率** - 价值信息

### 风险控制
```python
# 标准风险控制流程
entropy_factor = scale(
    winsorize(
        rank(entropy_calculation),
        std=3
    )
)
```

## 经济学解释

### 信息熵的经济含义
1. **市场效率**: 高熵 → 信息分布均匀 → 市场更有效
2. **不确定性**: 熵变化 → 不确定性变化 → 风险变化
3. **信息流**: 熵的时间序列 → 信息到达模式
4. **价格发现**: 熵的分布 → 价格发现效率

### 因子预期效果
- **高信息熵期**: 市场分歧大，波动率可能上升
- **低信息熵期**: 市场一致性强，趋势可能延续
- **熵突然变化**: 信息结构改变，可能预示转折点

### 适用场景
- **趋势识别**: 熵的变化预示趋势转换
- **风险管理**: 熵作为另类风险指标
- **择时策略**: 基于信息熵的市场状态判断
- **选股策略**: 个股信息熵的相对比较

## 实际可执行的信息熵因子

### 因子1: 收益率分布熵 (Returns Distribution Entropy)
```python
# 最实用的价格信息熵因子
returns_entropy = rank(
    ts_std_dev(
        ts_zscore(returns, 20),  # 标准化收益率
        20                       # 计算20天标准差
    )
)
```

### 因子2: 成交量信息熵 (Volume Information Entropy)
```python
# 成交量分布的信息熵
volume_entropy = rank(
    ts_std_dev(
        ts_zscore(volume/adv20, 30),  # 标准化相对成交量
        30
    )
)
```

### 因子3: 波动率信息熵 (Volatility Information Entropy)
```python
# 基于已实现波动率的信息熵
volatility_entropy = rank(
    ts_std_dev(
        log(add(ts_std_dev(returns, 5), 1e-8)),  # 对数化的短期波动率
        20
    )
)
```

### 因子4: 相对信息熵 (Relative Information Entropy)
```python
# 短期vs长期信息熵的比较
relative_entropy = rank(
    divide(
        ts_std_dev(returns, 5),   # 短期信息熵
        ts_std_dev(returns, 60)   # 长期信息熵
    )
)
```

### 因子5: 条件信息熵 (Conditional Information Entropy)
```python
# 基于成交量条件的价格信息熵
conditional_price_entropy = if_else(
    greater(volume, multiply(adv20, 1.5)),        # 高成交量条件
    rank(ts_std_dev(returns, 10)),               # 高量时的价格熵
    rank(ts_std_dev(returns, 30))                # 正常量时的价格熵
)
```

### 因子6: 多维联合熵 (Multi-dimensional Joint Entropy)
```python
# 价格-成交量-波动率联合信息熵
joint_entropy = rank(
    add(
        multiply(0.5, ts_std_dev(ts_zscore(returns, 20), 20)),
        multiply(0.3, ts_std_dev(ts_zscore(volume/adv20, 20), 20)),
        multiply(0.2, ts_std_dev(ts_zscore(ts_std_dev(returns, 5), 20), 20))
    )
)
```

### 因子7: 信息熵变化率 (Entropy Change Rate)
```python
# 信息熵的时间变化率
entropy_momentum = ts_delta(
    rank(ts_std_dev(returns, 20)),  # 当前信息熵
    10                              # 10天前的信息熵
)
```

### 因子8: 分桶信息熵 (Bucketed Information Entropy)
```python
# 使用分桶操作的真正信息熵近似
bucketed_entropy = rank(
    group_mean(
        power(
            subtract(
                ts_rank(returns, 20),  # 排名作为概率
                0.5                    # 中心化
            ),
            2                          # 平方
        ),
        bucket(rank(returns), range="0,1,0.1")  # 10个桶
    )
)
```

## 组合信息熵因子

### 综合信息熵指数 (Composite Information Entropy Index)
```python
# 多个维度的信息熵综合指数
composite_entropy = scale(
    add(
        multiply(0.4, rank(ts_std_dev(returns, 20))),           # 价格熵
        multiply(0.3, rank(ts_std_dev(volume/adv20, 20))),      # 成交量熵
        multiply(0.2, rank(ts_std_dev(ts_std_dev(returns, 5), 20))), # 波动率熵
        multiply(0.1, entropy_momentum)                         # 熵变化率
    )
)
```

### 行业中性信息熵 (Industry Neutral Information Entropy)
```python
# 行业中性化的信息熵因子
industry_neutral_entropy = group_neutralize(
    rank(ts_std_dev(returns, 20)),
    industry
)
```

## 信息熵因子的实际应用策略

### 策略1: 信息熵择时
```python
# 基于市场整体信息熵的择时信号
market_entropy_timing = if_else(
    greater(
        vec_avg(rank(ts_std_dev(returns, 20))),  # 市场平均信息熵
        0.6                                      # 高熵阈值
    ),
    -1,  # 高熵时减仓
    1    # 低熵时加仓
)
```

### 策略2: 信息熵选股
```python
# 基于相对信息熵的选股策略
entropy_stock_selection = rank(
    subtract(
        rank(ts_std_dev(returns, 20)),              # 个股信息熵
        group_mean(                                 # 行业平均信息熵
            rank(ts_std_dev(returns, 20)),
            industry
        )
    )
)
```

### 策略3: 信息熵风险管理
```python
# 基于信息熵的动态风险调整
entropy_risk_adjustment = scale(
    divide(
        returns,                                    # 原始信号
        add(
            rank(ts_std_dev(returns, 20)),         # 信息熵风险调整
            0.1                                    # 避免除零
        )
    )
)
```

## 参数优化建议

### 时间窗口优化
```python
# 不同市场状态下的最优窗口
adaptive_window_entropy = if_else(
    greater(ts_std_dev(returns, 5), ts_mean(ts_std_dev(returns, 5), 60)),
    rank(ts_std_dev(returns, 10)),  # 高波动期用短窗口
    rank(ts_std_dev(returns, 30))   # 低波动期用长窗口
)
```

### 字段组合优化
```python
# 基于相关性的字段权重调整
optimized_entropy = add(
    multiply(
        if_else(
            greater(abs(ts_corr(returns, volume/adv20, 20)), 0.3),
            0.7,  # 高相关时降低成交量权重
            0.5   # 低相关时平衡权重
        ),
        rank(ts_std_dev(returns, 20))
    ),
    multiply(
        subtract(1,
            if_else(
                greater(abs(ts_corr(returns, volume/adv20, 20)), 0.3),
                0.7,
                0.5
            )
        ),
        rank(ts_std_dev(volume/adv20, 20))
    )
)
```

## 总结

虽然WorldQuant Brain没有直接的信息熵操作符，但我们可以通过以下方式有效构建信息熵因子：

1. **标准差近似法**: 使用`ts_std_dev`作为分布离散度的代理
2. **分桶离散化法**: 使用`bucket`和`group_*`操作符模拟真实信息熵计算
3. **排名分布法**: 使用`ts_rank`和相关操作符捕捉分布特征
4. **多维组合法**: 结合多个字段构建联合信息熵

这些方法能够有效捕捉数据分布的信息含量，为量化策略提供有价值的信号。

## 深度分析：为什么不直接用 log + ts_sum + rank？

### 核心问题：概率表示的挑战

信息熵定义：`H(X) = -∑ p(xi) * log(p(xi))`

**关键难点**: 如何在WorldQuant Brain中获得真实的概率 `p(xi)`？

### 错误的尝试及其问题

```python
# ❌ 错误方法1: 直接用rank作为概率
wrong_entropy_1 = reverse(
    ts_sum(
        multiply(
            rank(returns),           # 这不是概率！
            log(rank(returns))      # 对排名取对数
        ),
        20
    )
)
```

**问题**:
1. `rank(returns)` 是相对排名(0-1)，不是事件概率
2. 排名不反映真实的分布密度
3. `log(0)` = -∞，会产生数值问题

```python
# ❌ 错误方法2: 用标准化值作为概率
wrong_entropy_2 = reverse(
    ts_sum(
        multiply(
            scale_down(abs(returns)),    # 缩放到0-1，但不是概率
            log(scale_down(abs(returns)))
        ),
        20
    )
)
```

**问题**:
1. 缩放后的值不满足概率的归一化条件
2. ∑p(xi) ≠ 1
3. 不反映真实的分布特征

### 正确的概率构建方法

#### 方法1: 基于分桶频率的真实概率

```python
# ✅ 正确方法: 构建真实概率分布
def true_entropy_factor(field, window, num_buckets=10):
    # 步骤1: 分桶
    field_buckets = bucket(
        rank(field),
        range=f"0,1,{1.0/num_buckets}"
    )

    # 步骤2: 计算每个桶的频率（真实概率）
    # 使用group_mean计算每个桶内的样本比例
    bucket_frequencies = group_mean(1, field_buckets)  # 每个桶的计数
    total_samples = vec_sum(bucket_frequencies)        # 总样本数
    probabilities = divide(bucket_frequencies, total_samples)  # 真实概率

    # 步骤3: 计算信息熵
    log_probs = log(add(probabilities, 1e-8))  # 避免log(0)
    entropy_terms = multiply(probabilities, log_probs)
    information_entropy = reverse(vec_sum(entropy_terms))  # -∑p*log(p)

    return information_entropy
```

#### 方法2: 基于时间序列的概率密度估计

```python
# ✅ 时间序列概率密度方法
def ts_probability_entropy(field, window):
    # 步骤1: 标准化数据
    normalized_field = ts_zscore(field, window)

    # 步骤2: 估计概率密度
    # 使用核密度估计的简化版本
    field_buckets = bucket(
        ts_rank(normalized_field, window),
        range="0,1,0.1"  # 10个桶
    )

    # 步骤3: 计算时间窗口内的频率分布
    window_probs = group_mean(
        divide(1, window),  # 每个观测的权重
        field_buckets
    )

    # 步骤4: 计算熵
    log_window_probs = log(add(window_probs, 1e-8))
    entropy = reverse(
        ts_sum(
            multiply(window_probs, log_window_probs),
            window
        )
    )

    return entropy
```

#### 方法3: 基于相对频率的近似概率

```python
# ✅ 相对频率近似方法
def relative_frequency_entropy(field, window):
    # 步骤1: 计算相对频率作为概率近似
    field_abs = abs(field)
    field_sum = ts_sum(field_abs, window)
    relative_freq = divide(field_abs, add(field_sum, 1e-8))  # 相对频率≈概率

    # 步骤2: 计算信息熵
    log_freq = log(add(relative_freq, 1e-8))
    entropy_contribution = multiply(relative_freq, log_freq)

    # 步骤3: 时间序列求和
    entropy = reverse(ts_sum(entropy_contribution, window))

    return rank(entropy)  # 最终排名标准化
```

### 实际可执行的真实信息熵因子

#### 因子A: 分桶真实熵
```python
# 最接近真实信息熵的实现
bucket_true_entropy = rank(
    reverse(
        vec_sum(
            multiply(
                group_mean(1, bucket(rank(returns), range="0,1,0.1")),  # 真实概率
                log(add(
                    group_mean(1, bucket(rank(returns), range="0,1,0.1")),
                    1e-8
                ))
            )
        )
    )
)
```

#### 因子B: 时间序列真实熵
```python
# 基于时间窗口的真实熵计算
ts_true_entropy = rank(
    reverse(
        ts_sum(
            multiply(
                divide(
                    abs(ts_zscore(returns, 20)),
                    ts_sum(abs(ts_zscore(returns, 20)), 20)
                ),  # 归一化概率
                log(add(
                    divide(
                        abs(ts_zscore(returns, 20)),
                        ts_sum(abs(ts_zscore(returns, 20)), 20)
                    ),
                    1e-8
                ))
            ),
            20
        )
    )
)
```

#### 因子C: 混合概率熵
```python
# 结合多种概率估计方法
hybrid_entropy = rank(
    add(
        multiply(0.5, bucket_true_entropy),      # 分桶概率
        multiply(0.3, ts_true_entropy),         # 时间序列概率
        multiply(0.2, rank(ts_std_dev(returns, 20)))  # 标准差近似
    )
)
```

### 为什么这些方法更准确

1. **真实概率**: 通过频率估计获得真实的概率分布
2. **归一化**: 确保 ∑p(xi) = 1
3. **数值稳定**: 避免 log(0) 的问题
4. **分布保真**: 保持原始数据的分布特征

### 计算复杂度对比

| 方法 | 计算复杂度 | 准确性 | 实用性 |
|------|------------|--------|--------|
| 标准差近似 | 低 | 中 | 高 |
| 分桶真实熵 | 中 | 高 | 中 |
| 时间序列真实熵 | 高 | 高 | 中 |
| 混合方法 | 中 | 高 | 高 |

### 推荐使用策略

1. **快速原型**: 使用标准差近似
2. **精确计算**: 使用分桶真实熵
3. **生产环境**: 使用混合方法
4. **研究分析**: 使用时间序列真实熵
