timestamp,batch_id,factor_id,expression,error_status,error_message
2025-07-23T15:49:51.776622,16,151,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_cptnewqeventv110_nopiq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:49:52.182876,16,152,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_cptnewqeventv110_nopiq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:49:52.493935,16,153,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_cptnewqeventv110_nopiq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:58:58.063580,31,310,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_emps,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:59:22.032043,32,311,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_emps,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:59:22.434825,32,312,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_emps,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:59:52.038482,33,325,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_cstkcvq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:59:52.435843,33,326,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_cstkcvq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:59:52.754890,33,327,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_cstkcvq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:59:53.094297,33,328,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_npq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:59:53.779884,33,329,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_npq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T15:59:54.277734,33,330,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_npq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:28.438189,34,331,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_optlifeq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:28.808883,34,332,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_optlifeq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:29.210437,34,333,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_optlifeq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:29.647315,34,334,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_optvolq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:30.018517,34,335,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_optvolq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:30.427788,34,336,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_optvolq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:30.897883,34,337,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_txdbclq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:31.215319,34,338,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_txdbclq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:31.660855,34,339,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_txdbclq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:31.998151,34,340,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_xaccq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:45.020312,35,341,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_xaccq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:00:45.377395,35,342,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_eventv110_xaccq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:07:57.082698,49,487,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_naicss,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:07:57.655296,49,488,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_naicss,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:07:58.075424,49,489,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_naicss,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:25.095396,76,754,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_acchgq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:25.472850,76,755,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_acchgq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:25.975208,76,756,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_acchgq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:26.756444,76,757,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_aociderglq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:27.082352,76,758,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_aociderglq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:28.139793,76,759,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_aociderglq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:28.480920,76,760,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cheq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:57.218706,77,761,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cheq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:57.629697,77,762,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cheq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:58.058181,77,763,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cshiq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:58.449393,77,764,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cshiq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:58.988883,77,765,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cshiq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:26:59.305734,77,766,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cshopq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:00.192724,77,767,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cshopq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:00.599842,77,768,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cshopq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:01.007830,77,769,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cstkeq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:02.344715,77,770,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cstkeq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:28.618958,78,771,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_cstkeq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:28.985843,78,772,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_diladq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:29.302266,78,773,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_diladq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:29.612812,78,774,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_diladq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:29.920807,78,775,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_dlcq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:30.244247,78,776,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_dlcq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:30.675608,78,777,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_dlcq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:31.157530,78,778,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_drcq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:31.532162,78,779,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_drcq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:31.850275,78,780,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_drcq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:43.095909,79,781,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_drltq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:43.412114,79,782,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_drltq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:43.727962,79,783,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_drltq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:44.071932,79,784,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_esoprq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:44.395000,79,785,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_esoprq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:44.717906,79,786,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_esoprq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:45.048471,79,787,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_glcepq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:45.372515,79,788,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_glcepq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:45.705866,79,789,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_glcepq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:46.026934,79,790,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_intanoq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:46.697130,80,791,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_intanoq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:47.030926,80,792,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_intanoq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:47.362325,80,793,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_ltmibq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:47.683269,80,794,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_ltmibq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:48.462857,80,795,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_ltmibq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:49.225391,80,796,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_msaq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:49.564752,80,797,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_msaq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:49.889229,80,798,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_msaq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:50.280990,80,799,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_optfvgrq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:27:50.598229,80,800,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_optfvgrq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:17.680394,81,801,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_optfvgrq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:17.999437,81,802,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_optrfrq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:18.319295,81,803,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_optrfrq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:18.639835,81,804,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_optrfrq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:20.461662,81,805,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_prcraq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:20.870205,81,806,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_prcraq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:21.218742,81,807,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_prcraq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:21.547852,81,808,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_pstknq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:21.867245,81,809,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_pstknq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:22.181933,81,810,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_pstknq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:32.419227,82,811,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rdipdq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:32.736887,82,812,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rdipdq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:33.070299,82,813,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rdipdq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:33.392342,82,814,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rdipepsq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:34.249981,82,815,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rdipepsq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:34.599282,82,816,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rdipepsq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:34.926602,82,817,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rdipq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:35.262150,82,818,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rdipq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:35.612001,82,819,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rdipq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:36.072099,82,820,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rectoq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:45.871382,83,821,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rectoq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:47.050360,83,822,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rectoq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:47.426052,83,823,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rectrq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:49.248588,83,824,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rectrq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:49.563129,83,825,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_rectrq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:49.873720,83,826,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_revtq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:50.362280,83,827,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_revtq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:50.831611,83,828,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_revtq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:51.208702,83,829,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_stkcoq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:51.544612,83,830,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_stkcoq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:52.409226,84,831,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_stkcoq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:52.741383,84,832,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_tstkq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:53.168245,84,833,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_tstkq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:53.483204,84,834,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_tstkq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:54.015203,84,835,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_xidoq,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:54.330253,84,836,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_xidoq,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:28:54.680483,84,837,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_newqeventv110_xidoq,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:44:07.827790,106,1054,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_oiadps,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:44:08.238765,106,1055,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_oiadps,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:44:08.646923,106,1056,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_oiadps,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:48:00.381243,112,1111,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_ranks,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:48:00.722348,112,1112,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_ranks,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:48:01.133297,112,1113,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_ranks,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:48:28.267173,115,1141,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_stype,60),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:48:28.584608,115,1142,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_stype,200),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
2025-07-23T16:48:28.920820,115,1143,"my_group = market;my_group2 = bucket(rank(cap),range='0,1,0.1');alpha=rank(group_rank(ts_decay_linear(volume/ts_sum(volume,252),10),my_group)*group_rank(ts_rank(fnd6_stype,400),my_group)*group_rank(-ts_delta(close,5),my_group));trade_when(volume>adv20,group_neutralize(alpha,my_group2),-1)",ERROR,Operator ts_rank does not support event inputs
