import wqb
from wqb import WQBSession, print
import json
import time
import logging
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timezone, timedelta
from wqb import FilterRange

# ==================== 用户可调节参数 ====================

# 1. 并行处理参数
MAX_WORKERS_FACTORY_FILTER = 3      # "厂"型过滤并行线程数
MAX_WORKERS_CORRELATION_FILTER = 2  # 自相关过滤并行线程数

# 2. 自相关过滤参数
CORRELATION_THRESHOLD = 0.4          # 自相关阈值，保留相关性小于此值的Alpha
SUBMITTED_ALPHAS_SAMPLE_SIZE = 100   # 已提交Alpha样本数量

# 3. Alpha提取参数
ALPHA_EXTRACT_CONFIG = {
    'status': 'UNSUBMITTED',         # Alpha状态
    'region': 'ASI',                 # 地区 (ASI/USA/EUR等)
    'delay': 1,                      # 延迟天数
    'universe': 'MINVOL1M',          # 宇宙
    'sharpe_min': 1,                 # 最小Sharpe值
    'fitness_min': 1,                # 最小Fitness值
    'turnover_max': 0.7,             # 最大Turnover值
    'limit': 1000,                   # 最大提取数量 (filter_alphas_limited最大100，使用filter_alphas可以更多)
    'use_unlimited': True,           # 是否使用filter_alphas获取所有符合条件的Alpha
    'include_reverse': True,         # 是否包含反向Alpha (Sharpe和Fitness为负值但绝对值满足要求)
}

# 4. Alpha提取时间范围 (使用北京时间，系统会自动转换为美东时间)
ALPHA_DATE_RANGE = {
    'start_date_beijing': '2025-07-01 00:00:00',  # 开始时间 (北京时间)
    'end_date_beijing': '2025-07-20 23:59:59',    # 结束时间 (北京时间)
}

# 5. "厂"型过滤参数
FACTORY_FILTER_CONFIG = {
    'check_years': [str(year) for year in range(2013, 2024)],  # 检查年份范围 2013-2023
    'zero_sharpe_threshold': True,    # 是否启用零Sharpe值检测
    'negative_ratio_threshold': 0.5,  # 负Sharpe年份比例阈值
}

# ==================== 时区转换函数 ====================

def beijing_to_eastern(beijing_time_str):
    """
    将北京时间转换为美东时间

    Parameters:
    -----------
    beijing_time_str : str
        北京时间字符串，格式: 'YYYY-MM-DD HH:MM:SS'

    Returns:
    --------
    str : 美东时间的ISO格式字符串
    """
    # 解析北京时间
    beijing_dt = datetime.strptime(beijing_time_str, '%Y-%m-%d %H:%M:%S')

    # 设置北京时区 (UTC+8)
    beijing_tz = timezone(timedelta(hours=8))
    beijing_dt = beijing_dt.replace(tzinfo=beijing_tz)

    # 转换为美东时间 (UTC-5, 夏令时期间为UTC-4)
    # 这里使用UTC-5作为标准时间
    eastern_tz = timezone(timedelta(hours=-5))
    eastern_dt = beijing_dt.astimezone(eastern_tz)

    return eastern_dt.isoformat()


# ==================== 系统初始化 ====================

# Create `logger`
logger = wqb.wqb_logger()
wqb.print(f"{logger.name = }")  # print(f"{logger.name = }", flush=True)

# Manual logging
# logger.info('This is an info for testing.')
# logger.warning('This is a warning for testing.')

# Create `wqbs`
wqbs = WQBSession(('<EMAIL>', 'Kyz417442'), logger=logger)
# If `logger` was not created, use the following line instead.
# wqbs = WQBSession(('<email>', '<password>'))

# Test connectivity (Optional)
resp = wqbs.auth_request()
print(resp.status_code)           # 201
print(resp.ok)                    # True
print(resp.json()['user']['id'])  # <Your BRAIN User ID>

print(f"\n当前配置参数:")
print(f"地区: {ALPHA_EXTRACT_CONFIG['region']}")
print(f"Sharpe阈值: ≥{ALPHA_EXTRACT_CONFIG['sharpe_min']}")
print(f"自相关阈值: <{CORRELATION_THRESHOLD}")
print(f"并行线程数: {MAX_WORKERS_FACTORY_FILTER} (厂型过滤), {MAX_WORKERS_CORRELATION_FILTER} (自相关过滤)")
print(f"时间范围: {ALPHA_DATE_RANGE['start_date']} 到 {ALPHA_DATE_RANGE['end_date']}")


def get_sharpe_all_years(s, alpha_id, years_range=None):
    """
    获取Alpha在配置年份范围内所有年份的Sharpe值

    Parameters:
    -----------
    s : WQBSession
        WQB会话对象
    alpha_id : str
        Alpha ID
    years_range : list, optional
        指定年份范围，默认使用配置的年份范围

    Returns:
    --------
    dict : {year: sharpe_value}
        各年份的Sharpe值，如果没有数据则为None
    """
    if years_range is None:
        years_range = FACTORY_FILTER_CONFIG['check_years']

    # 处理API限流
    while True:
        response = s.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}/recordsets/yearly-stats")

        # 检查是否需要重试
        retry_after = response.headers.get("Retry-After")
        if retry_after:
            logging.debug(f"API限流，Alpha {alpha_id} 等待 {retry_after} 秒后重试")
            time.sleep(float(retry_after))
            continue
        break

    # 初始化结果字典
    result = {year: None for year in years_range}

    try:
        data = response.json()
        for record in data.get("records", []):
            if not record:
                continue
            record_year = record[0]

            if record_year in years_range:
                if len(record) > 6 and record[6] is not None:
                    sharpe = float(record[6])
                    result[record_year] = sharpe
                    logging.debug(f"Alpha {alpha_id} {record_year}年Sharpe值: {sharpe:.4f}")
                else:
                    logging.warning(f"Alpha {alpha_id} {record_year}年Sharpe值为空")

        return result

    except (json.JSONDecodeError, IndexError, KeyError) as e:
        logging.error(f"解析Alpha {alpha_id} 数据失败: {str(e)}")
        return result


def is_factory_alpha(sharpe_data, is_reverse=False):
    """
    判断是否为"厂"型Alpha（异常Alpha）
    新逻辑：只要有一年Sharpe值为0就判定为异常

    Parameters:
    -----------
    sharpe_data : dict
        包含各年份Sharpe值的字典
    is_reverse : bool
        是否为反向Alpha，如果是反向Alpha，会对Sharpe值取绝对值进行判断

    Returns:
    --------
    tuple : (is_factory, reason)
        is_factory: bool - True表示是"厂"型Alpha，False表示正常Alpha
        reason: str - 异常原因描述
    """
    # 检查是否有数据
    valid_data = {year: value for year, value in sharpe_data.items() if value is not None}

    if not valid_data:
        return True, "无有效数据"

    # 如果是反向Alpha，对Sharpe值取绝对值进行判断
    if is_reverse:
        valid_data = {year: abs(value) for year, value in valid_data.items()}

    # 检查是否有Sharpe值为0的年份
    zero_years = [year for year, value in valid_data.items() if value == 0.0]
    if zero_years:
        return True, f"Sharpe值为0的年份: {', '.join(zero_years)}"

    # 对于反向Alpha，不需要检查负值（因为已经取绝对值）
    if not is_reverse:
        # 检查是否有负Sharpe值（可选，根据需要启用）
        negative_years = [year for year, value in valid_data.items() if value < 0]
        if len(negative_years) > len(valid_data) * 0.5:  # 超过一半年份为负
            return True, f"过多负Sharpe年份: {', '.join(negative_years)}"

    return False, f"正常Alpha{'(反向)' if is_reverse else ''}"


def process_single_alpha(alpha_id, wqbs, is_reverse=False):
    """
    处理单个Alpha的函数，用于并行处理

    Parameters:
    -----------
    alpha_id : str
        Alpha ID
    wqbs : WQBSession
        WQB会话对象
    is_reverse : bool
        是否为反向Alpha

    Returns:
    --------
    dict : 处理结果
    """
    try:
        # 获取2013-2023年所有年份的Sharpe值
        sharpe_data = get_sharpe_all_years(wqbs, alpha_id)

        # 判断是否为"厂"型Alpha
        is_factory, reason = is_factory_alpha(sharpe_data, is_reverse)

        result = {
            'alpha_id': alpha_id,
            'sharpe_data': sharpe_data,
            'is_factory': is_factory,
            'reason': reason,
            'is_reverse': is_reverse,
            'status': 'success'
        }

        return result

    except Exception as e:
        logging.error(f"处理Alpha {alpha_id} 时出错: {str(e)}")
        return {
            'alpha_id': alpha_id,
            'sharpe_data': None,
            'is_factory': True,
            'reason': 'processing_error',
            'error': str(e),
            'is_reverse': is_reverse,
            'status': 'error'
        }


def filter_factory_alphas_parallel(alpha_ids, wqbs, alpha_reverse_flags, max_workers=5):
    """
    并行过滤"厂"型Alpha，检查2013-2023年所有年份

    Parameters:
    -----------
    alpha_ids : list
        Alpha ID列表
    wqbs : WQBSession
        WQB会话对象
    alpha_reverse_flags : dict
        Alpha反向标志字典 {alpha_id: is_reverse}
    max_workers : int
        最大并行工作线程数，默认5

    Returns:
    --------
    tuple : (normal_alphas, factory_alphas)
        正常Alpha列表和"厂"型Alpha列表
    """
    normal_alphas = []
    factory_alphas = []

    print(f"\n开始并行检查{len(alpha_ids)}个Alpha的2013-2023年Sharpe值...")
    print(f"使用{max_workers}个并行线程处理")

    # 使用线程池并行处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务，传递反向标志
        future_to_alpha = {
            executor.submit(process_single_alpha, alpha_id, wqbs, alpha_reverse_flags.get(alpha_id, False)): alpha_id
            for alpha_id in alpha_ids
        }

        # 收集结果
        completed = 0
        for future in as_completed(future_to_alpha):
            completed += 1
            alpha_id = future_to_alpha[future]

            try:
                result = future.result()

                if result['is_factory']:
                    factory_alphas.append(result)
                    print(f"进度 {completed}/{len(alpha_ids)} - ❌ {alpha_id}: {result['reason']}")
                else:
                    normal_alphas.append(result)
                    # 显示有效年份数量
                    valid_years = sum(1 for v in result['sharpe_data'].values() if v is not None)
                    print(f"进度 {completed}/{len(alpha_ids)} - ✅ {alpha_id}: {valid_years}年有效数据")

            except Exception as e:
                logging.error(f"获取Alpha {alpha_id} 结果时出错: {str(e)}")
                factory_alphas.append({
                    'alpha_id': alpha_id,
                    'sharpe_data': None,
                    'is_factory': True,
                    'reason': 'result_error',
                    'error': str(e),
                    'status': 'error'
                })
                print(f"进度 {completed}/{len(alpha_ids)} - ❌ {alpha_id}: 结果获取失败")

    return normal_alphas, factory_alphas


# ==================== 自相关计算功能 ====================

def get_alpha_pnl(session, alpha_id):
    """
    获取单个Alpha的PnL数据

    Parameters:
    -----------
    session : WQBSession
        WQB会话对象
    alpha_id : str
        Alpha ID

    Returns:
    --------
    tuple : (response, alpha_id)
        API响应和Alpha ID
    """
    count = 0
    while True:
        if count > 30:
            # 如果重试次数过多，重新登录
            logging.warning(f"Alpha {alpha_id} 重试次数过多，可能需要重新登录")
            count = 0

        try:
            pnl = session.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}/recordsets/pnl")
            retry_after = pnl.headers.get("Retry-After")

            if retry_after:
                logging.debug(f"API限流，Alpha {alpha_id} 等待 {retry_after} 秒后重试")
                time.sleep(float(retry_after))
            else:
                logging.debug(f"Alpha {alpha_id} PnL数据获取成功")
                count += 1
                return (pnl, alpha_id)

        except Exception as e:
            logging.error(f"获取Alpha {alpha_id} PnL数据失败: {str(e)}")
            time.sleep(1)
            count += 1


def fetch_pnls_parallel(session, alpha_list, max_workers=3):
    """
    并行获取多个Alpha的PnL数据

    Parameters:
    -----------
    session : WQBSession
        WQB会话对象
    alpha_list : list
        Alpha ID列表
    max_workers : int
        最大并行线程数

    Returns:
    --------
    list : PnL数据列表
    """
    pnl_ls = []
    print(f"开始并行获取{len(alpha_list)}个Alpha的PnL数据...")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(get_alpha_pnl, session, alpha_id) for alpha_id in alpha_list]

        completed = 0
        for future in as_completed(futures):
            completed += 1
            try:
                result = future.result()
                pnl_ls.append(result)
                print(f"PnL获取进度: {completed}/{len(alpha_list)}")
            except Exception as e:
                logging.error(f"获取PnL数据时出错: {str(e)}")

    return pnl_ls


def get_pnl_panel(session, alpha_list, alpha_reverse_flags=None, max_workers=3):
    """
    获取Alpha的PnL面板数据并转换为DataFrame

    Parameters:
    -----------
    session : WQBSession
        WQB会话对象
    alpha_list : list
        Alpha ID列表
    alpha_reverse_flags : dict, optional
        Alpha反向标志字典 {alpha_id: is_reverse}
    max_workers : int
        最大并行线程数

    Returns:
    --------
    pd.DataFrame : PnL数据面板
    """
    if alpha_reverse_flags is None:
        alpha_reverse_flags = {}

    alpha_pnls = fetch_pnls_parallel(session, alpha_list, max_workers)
    pnl_df = pd.DataFrame()

    print("开始处理PnL数据...")
    for i, (pnl, alpha_id) in enumerate(alpha_pnls, 1):
        is_reverse = alpha_reverse_flags.get(alpha_id, False)
        print(f"处理进度: {i}/{len(alpha_pnls)} - {alpha_id}{'(反向)' if is_reverse else ''}")

        try:
            # 检查pnl对象是否有json方法
            if hasattr(pnl, 'json') and callable(pnl.json):
                data = pnl.json()
            else:
                # 假设pnl已经是字典格式
                data = pnl

            if not data.get('records'):
                logging.warning(f"Alpha {alpha_id} 没有PnL记录")
                continue

            # 检查records的列数
            if len(data['records'][0]) == 2:
                df = pd.DataFrame(data['records'], columns=['Date', alpha_id])
                df.set_index('Date', inplace=True)
            elif len(data['records'][0]) == 3:
                properties = data['schema']['properties']
                # 如果含有'risk-neutralized-pnl'，则保留这一列
                if any(prop['name'] == 'risk-neutralized-pnl' for prop in properties):
                    records = [record[:2] for record in data['records']]
                    df = pd.DataFrame(records, columns=['Date', alpha_id])
                    df.set_index('Date', inplace=True)
                else:
                    # 如果records的列数为3，但不包含'risk-neutralized-pnl'，则跳过
                    logging.warning(f"Alpha {alpha_id} 数据格式不支持，跳过")
                    continue
            else:
                logging.warning(f"Alpha {alpha_id} 数据格式异常，跳过")
                continue

            # 如果是反向Alpha，对PnL数据取反
            if is_reverse:
                df[alpha_id] = -df[alpha_id]
                logging.debug(f"Alpha {alpha_id} 是反向Alpha，PnL数据已取反")

            # 将当前alpha_id的DataFrame与总的DataFrame合并
            if pnl_df.empty:
                pnl_df = df
            else:
                pnl_df = pd.merge(pnl_df, df, on='Date', how='outer')

        except Exception as e:
            logging.error(f"处理Alpha {alpha_id} PnL数据时出错: {str(e)}")
            continue

    return pnl_df


def get_submitted_alphas_sample(session, max_alphas=100):
    """
    获取已提交Alpha的样本数据用于自相关计算

    Parameters:
    -----------
    session : WQBSession
        WQB会话对象
    max_alphas : int
        最大获取数量

    Returns:
    --------
    pd.DataFrame : 已提交Alpha的收益率数据
    """
    print(f"开始获取{max_alphas}个已提交Alpha作为对比基准...")

    try:
        # 获取已提交的Alpha
        response = session.get(
            f"https://api.worldquantbrain.com/users/self/alphas?stage=OS&limit={max_alphas}&offset=0"
        )
        response.raise_for_status()
        os_alphas = response.json()["results"]
        os_alpha_ids = [item['id'] for item in os_alphas]

        print(f"获取到{len(os_alpha_ids)}个已提交Alpha")

        # 获取PnL数据
        os_pnl_df = get_pnl_panel(session, os_alpha_ids, max_workers=2)

        # 转换为收益率
        os_ret_df = os_pnl_df - os_pnl_df.ffill().shift(1)

        return os_ret_df

    except Exception as e:
        logging.error(f"获取已提交Alpha数据失败: {str(e)}")
        return pd.DataFrame()


def calculate_self_correlation(is_ret_df, os_ret_df, correlation_threshold=0.7):
    """
    计算自相关并过滤高相关的Alpha

    Parameters:
    -----------
    is_ret_df : pd.DataFrame
        待检查Alpha的收益率数据
    os_ret_df : pd.DataFrame
        已提交Alpha的收益率数据
    correlation_threshold : float
        相关性阈值，默认0.7

    Returns:
    --------
    tuple : (low_corr_alphas, high_corr_alphas)
        低相关Alpha列表和高相关Alpha列表
    """
    print(f"开始计算自相关，阈值设置为{correlation_threshold}")

    # 只使用最近4年的数据
    if not is_ret_df.empty:
        is_df = is_ret_df[
            pd.to_datetime(is_ret_df.index) > pd.to_datetime(is_ret_df.index).max() - pd.DateOffset(years=4)
        ]
    else:
        print("待检查Alpha数据为空")
        return [], []

    if not os_ret_df.empty:
        os_df = os_ret_df[
            pd.to_datetime(os_ret_df.index) > pd.to_datetime(os_ret_df.index).max() - pd.DateOffset(years=4)
        ]
    else:
        print("已提交Alpha数据为空，跳过自相关检查")
        return list(is_df.columns), []

    # 将0替换为NaN
    is_df = is_df.replace(0, np.nan)
    os_df = os_df.replace(0, np.nan)

    low_corr_alphas = []
    high_corr_alphas = []

    print(f"检查{len(is_df.columns)}个Alpha的自相关...")

    for i, col_is in enumerate(is_df.columns, 1):
        print(f"自相关检查进度: {i}/{len(is_df.columns)} - {col_is}")

        try:
            ret = is_df[col_is]
            # 合并当前Alpha和已提交Alpha的数据
            combined_ret = pd.concat([ret, os_df], axis=1)

            # 计算相关性矩阵
            corr_matrix = combined_ret.corr()

            # 获取当前Alpha与所有已提交Alpha的最大相关性
            if len(corr_matrix) > 1:
                cor_max = corr_matrix.iloc[0, 1:].max()
            else:
                cor_max = 0

            if pd.isna(cor_max):
                cor_max = 0
                print(f"  {col_is}: 无有效数据，相关性=0")
                low_corr_alphas.append({
                    'alpha_id': col_is,
                    'max_correlation': 0,
                    'status': 'no_data'
                })
            elif cor_max < correlation_threshold:
                print(f"  {col_is}: 最大相关性={cor_max:.4f} < {correlation_threshold} ✅")
                low_corr_alphas.append({
                    'alpha_id': col_is,
                    'max_correlation': cor_max,
                    'status': 'low_correlation'
                })
            else:
                print(f"  {col_is}: 最大相关性={cor_max:.4f} >= {correlation_threshold} ❌")
                high_corr_alphas.append({
                    'alpha_id': col_is,
                    'max_correlation': cor_max,
                    'status': 'high_correlation'
                })

        except Exception as e:
            logging.error(f"计算Alpha {col_is} 自相关时出错: {str(e)}")
            high_corr_alphas.append({
                'alpha_id': col_is,
                'max_correlation': None,
                'status': 'error',
                'error': str(e)
            })

    return low_corr_alphas, high_corr_alphas


def filter_self_correlation(session, alpha_ids, alpha_reverse_flags, correlation_threshold=0.7, max_workers=2):
    """
    对Alpha列表进行自相关过滤

    Parameters:
    -----------
    session : WQBSession
        WQB会话对象
    alpha_ids : list
        Alpha ID列表
    alpha_reverse_flags : dict
        Alpha反向标志字典 {alpha_id: is_reverse}
    correlation_threshold : float
        相关性阈值，默认0.7
    max_workers : int
        最大并行线程数

    Returns:
    --------
    tuple : (low_corr_alphas, high_corr_alphas, os_ret_df)
        低相关Alpha列表、高相关Alpha列表和已提交Alpha数据
    """
    print(f"\n开始自相关过滤，相关性阈值: {correlation_threshold}")

    # 获取已提交Alpha的收益率数据作为对比基准
    os_ret_df = get_submitted_alphas_sample(session, max_alphas=SUBMITTED_ALPHAS_SAMPLE_SIZE)

    if os_ret_df.empty:
        print("无法获取已提交Alpha数据，跳过自相关检查")
        return alpha_ids, [], os_ret_df

    # 获取待检查Alpha的PnL数据，传递反向标志
    print(f"获取{len(alpha_ids)}个待检查Alpha的PnL数据...")
    is_pnl_df = get_pnl_panel(session, alpha_ids, alpha_reverse_flags, max_workers=max_workers)

    if is_pnl_df.empty:
        print("无法获取待检查Alpha的PnL数据")
        return [], alpha_ids, os_ret_df

    # 转换为收益率
    is_ret_df = is_pnl_df - is_pnl_df.ffill().shift(1)

    # 计算自相关
    low_corr_alphas, high_corr_alphas = calculate_self_correlation(
        is_ret_df, os_ret_df, correlation_threshold
    )

    return low_corr_alphas, high_corr_alphas, os_ret_df


#登陆完成，开始提取Alpha

# 使用配置参数设置日期范围，将北京时间转换为美东时间
beijing_start = ALPHA_DATE_RANGE['start_date_beijing']
beijing_end = ALPHA_DATE_RANGE['end_date_beijing']

eastern_start = beijing_to_eastern(beijing_start)
eastern_end = beijing_to_eastern(beijing_end)

lo = datetime.fromisoformat(eastern_start)
hi = datetime.fromisoformat(eastern_end)

print(f"时间范围转换:")
print(f"北京时间: {beijing_start} 到 {beijing_end}")
print(f"美东时间: {eastern_start} 到 {eastern_end}")

# 使用配置参数提取Alpha
if ALPHA_EXTRACT_CONFIG['use_unlimited']:
    print(f"使用filter_alphas获取所有符合条件的Alpha...")

    # 收集所有结果
    alpha_ids = []
    alpha_reverse_flags = {}  # 记录哪些Alpha是反向的

    # 1. 获取正向Alpha (Sharpe >= threshold, Fitness >= threshold)
    print(f"第1步: 获取正向Alpha (Sharpe≥{ALPHA_EXTRACT_CONFIG['sharpe_min']}, Fitness≥{ALPHA_EXTRACT_CONFIG['fitness_min']})")
    resps_positive = wqbs.filter_alphas(
        status=ALPHA_EXTRACT_CONFIG['status'],
        region=ALPHA_EXTRACT_CONFIG['region'],
        delay=ALPHA_EXTRACT_CONFIG['delay'],
        universe=ALPHA_EXTRACT_CONFIG['universe'],
        sharpe=FilterRange.from_str(f"[{ALPHA_EXTRACT_CONFIG['sharpe_min']}, inf)"),
        fitness=FilterRange.from_str(f"[{ALPHA_EXTRACT_CONFIG['fitness_min']}, inf)"),
        turnover=FilterRange.from_str(f"(-inf, {ALPHA_EXTRACT_CONFIG['turnover_max']}]"),
        date_created=FilterRange.from_str(f"[{lo.isoformat()}, {hi.isoformat()})"),
        order='dateCreated',
        limit=100,  # 每次请求100个
    )

    for i, resp in enumerate(resps_positive, 1):
        batch_results = resp.json()['results']
        batch_ids = [item['id'] for item in batch_results]
        alpha_ids.extend(batch_ids)
        # 标记为正向Alpha
        for alpha_id in batch_ids:
            alpha_reverse_flags[alpha_id] = False
        print(f"正向批次 {i}: 获取到 {len(batch_ids)} 个Alpha，累计 {len(alpha_ids)} 个")

        if len(alpha_ids) >= ALPHA_EXTRACT_CONFIG['limit']:
            break

    # 2. 如果启用反向Alpha，获取反向Alpha (Sharpe <= -threshold, Fitness <= -threshold)
    if ALPHA_EXTRACT_CONFIG['include_reverse'] and len(alpha_ids) < ALPHA_EXTRACT_CONFIG['limit']:
        print(f"第2步: 获取反向Alpha (Sharpe≤-{ALPHA_EXTRACT_CONFIG['sharpe_min']}, Fitness≤-{ALPHA_EXTRACT_CONFIG['fitness_min']})")
        resps_negative = wqbs.filter_alphas(
            status=ALPHA_EXTRACT_CONFIG['status'],
            region=ALPHA_EXTRACT_CONFIG['region'],
            delay=ALPHA_EXTRACT_CONFIG['delay'],
            universe=ALPHA_EXTRACT_CONFIG['universe'],
            sharpe=FilterRange.from_str(f"(-inf, -{ALPHA_EXTRACT_CONFIG['sharpe_min']}]"),
            fitness=FilterRange.from_str(f"(-inf, -{ALPHA_EXTRACT_CONFIG['fitness_min']}]"),
            turnover=FilterRange.from_str(f"(-inf, {ALPHA_EXTRACT_CONFIG['turnover_max']}]"),
            date_created=FilterRange.from_str(f"[{lo.isoformat()}, {hi.isoformat()})"),
            order='dateCreated',
            limit=100,  # 每次请求100个
        )

        for i, resp in enumerate(resps_negative, 1):
            batch_results = resp.json()['results']
            batch_ids = [item['id'] for item in batch_results]
            alpha_ids.extend(batch_ids)
            # 标记为反向Alpha
            for alpha_id in batch_ids:
                alpha_reverse_flags[alpha_id] = True
            print(f"反向批次 {i}: 获取到 {len(batch_ids)} 个Alpha，累计 {len(alpha_ids)} 个")

            if len(alpha_ids) >= ALPHA_EXTRACT_CONFIG['limit']:
                break

    # 限制总数量
    if len(alpha_ids) > ALPHA_EXTRACT_CONFIG['limit']:
        alpha_ids = alpha_ids[:ALPHA_EXTRACT_CONFIG['limit']]
        print(f"达到设定限制 {ALPHA_EXTRACT_CONFIG['limit']} 个，停止获取")
else:
    print(f"使用filter_alphas_limited获取最多100个Alpha...")
    alpha_ids = []
    alpha_reverse_flags = {}

    # 获取正向Alpha
    resp_positive = wqbs.filter_alphas_limited(
        status=ALPHA_EXTRACT_CONFIG['status'],
        region=ALPHA_EXTRACT_CONFIG['region'],
        delay=ALPHA_EXTRACT_CONFIG['delay'],
        universe=ALPHA_EXTRACT_CONFIG['universe'],
        sharpe=FilterRange.from_str(f"[{ALPHA_EXTRACT_CONFIG['sharpe_min']}, inf)"),
        fitness=FilterRange.from_str(f"[{ALPHA_EXTRACT_CONFIG['fitness_min']}, inf)"),
        turnover=FilterRange.from_str(f"(-inf, {ALPHA_EXTRACT_CONFIG['turnover_max']}]"),
        date_created=FilterRange.from_str(f"[{lo.isoformat()}, {hi.isoformat()})"),
        order='dateCreated',
        limit=min(ALPHA_EXTRACT_CONFIG['limit'], 50),  # 留一半给反向Alpha
    )
    positive_ids = [item['id'] for item in resp_positive.json()['results']]
    alpha_ids.extend(positive_ids)
    for alpha_id in positive_ids:
        alpha_reverse_flags[alpha_id] = False
    print(f"获取到 {len(positive_ids)} 个正向Alpha")

    # 如果启用反向Alpha且还有空间
    if ALPHA_EXTRACT_CONFIG['include_reverse'] and len(alpha_ids) < min(ALPHA_EXTRACT_CONFIG['limit'], 100):
        remaining_limit = min(ALPHA_EXTRACT_CONFIG['limit'], 100) - len(alpha_ids)
        resp_negative = wqbs.filter_alphas_limited(
            status=ALPHA_EXTRACT_CONFIG['status'],
            region=ALPHA_EXTRACT_CONFIG['region'],
            delay=ALPHA_EXTRACT_CONFIG['delay'],
            universe=ALPHA_EXTRACT_CONFIG['universe'],
            sharpe=FilterRange.from_str(f"(-inf, -{ALPHA_EXTRACT_CONFIG['sharpe_min']}]"),
            fitness=FilterRange.from_str(f"(-inf, -{ALPHA_EXTRACT_CONFIG['fitness_min']}]"),
            turnover=FilterRange.from_str(f"(-inf, {ALPHA_EXTRACT_CONFIG['turnover_max']}]"),
            date_created=FilterRange.from_str(f"[{lo.isoformat()}, {hi.isoformat()})"),
            order='dateCreated',
            limit=remaining_limit,
        )
        negative_ids = [item['id'] for item in resp_negative.json()['results']]
        alpha_ids.extend(negative_ids)
        for alpha_id in negative_ids:
            alpha_reverse_flags[alpha_id] = True
        print(f"获取到 {len(negative_ids)} 个反向Alpha")

print(f"最终提取到 {len(alpha_ids)} 个Alpha (正向: {sum(1 for x in alpha_reverse_flags.values() if not x)}, 反向: {sum(1 for x in alpha_reverse_flags.values() if x)})")

print(f"\n开始使用{FACTORY_FILTER_CONFIG['check_years'][0]}-{FACTORY_FILTER_CONFIG['check_years'][-1]}年数据并行过滤'厂'型Alpha...")
print(f"过滤逻辑：只要有一年Sharpe值为0就判定为异常Alpha")
print(f"使用{MAX_WORKERS_FACTORY_FILTER}个并行线程")

# 并行过滤"厂"型Alpha
normal_alphas, factory_alphas = filter_factory_alphas_parallel(
    alpha_ids,
    wqbs,
    alpha_reverse_flags,
    max_workers=MAX_WORKERS_FACTORY_FILTER
)

# 输出结果
print(f"\n" + "="*60)
print(f"过滤结果统计:")
print(f"原始Alpha数量: {len(alpha_ids)}")
print(f"正常Alpha数量: {len(normal_alphas)}")
print(f"'厂'型Alpha数量: {len(factory_alphas)}")
print(f"过滤率: {len(factory_alphas)/len(alpha_ids)*100:.1f}%")

print(f"\n正常Alpha详细信息:")
for alpha in normal_alphas:
    alpha_id = alpha['alpha_id']
    sharpe_data = alpha['sharpe_data']

    # 显示有效年份的Sharpe值
    valid_data = {year: value for year, value in sharpe_data.items() if value is not None}
    if valid_data:
        sharpe_summary = ", ".join([f"{year}={value:.4f}" for year, value in sorted(valid_data.items())])
        print(f"  {alpha_id}: {sharpe_summary}")
    else:
        print(f"  {alpha_id}: 无有效数据")

print(f"\n被过滤的'厂'型Alpha详细信息:")
for alpha in factory_alphas:
    alpha_id = alpha['alpha_id']
    sharpe_data = alpha['sharpe_data']
    reason = alpha['reason']

    if sharpe_data:
        # 显示有问题的年份
        zero_years = [year for year, value in sharpe_data.items() if value == 0.0]
        negative_years = [year for year, value in sharpe_data.items() if value is not None and value < 0]

        problem_info = []
        if zero_years:
            problem_info.append(f"零值年份: {', '.join(zero_years)}")
        if negative_years:
            problem_info.append(f"负值年份: {', '.join(negative_years)}")

        problem_summary = "; ".join(problem_info) if problem_info else "其他异常"
        print(f"  {alpha_id} ({reason}): {problem_summary}")
    else:
        print(f"  {alpha_id} ({reason}): 数据获取失败")

# 第二步：自相关过滤
if normal_alphas:
    # 提取通过"厂"型过滤的Alpha ID
    passed_factory_filter_ids = [alpha['alpha_id'] for alpha in normal_alphas]

    print(f"\n" + "="*60)
    print(f"第二步：自相关过滤")
    print(f"对{len(passed_factory_filter_ids)}个通过'厂'型过滤的Alpha进行自相关检查")
    print(f"相关性阈值: {CORRELATION_THRESHOLD} (保留相关性小于此值的Alpha)")
    print(f"使用{MAX_WORKERS_CORRELATION_FILTER}个并行线程")

    # 构建通过"厂"型过滤的Alpha的反向标志
    passed_alpha_reverse_flags = {alpha['alpha_id']: alpha.get('is_reverse', False) for alpha in normal_alphas}

    # 进行自相关过滤
    low_corr_alphas, high_corr_alphas, os_ret_df = filter_self_correlation(
        wqbs,
        passed_factory_filter_ids,
        passed_alpha_reverse_flags,
        correlation_threshold=CORRELATION_THRESHOLD,
        max_workers=MAX_WORKERS_CORRELATION_FILTER
    )

    # 输出自相关过滤结果
    print(f"\n自相关过滤结果:")
    print(f"低相关Alpha数量: {len(low_corr_alphas)}")
    print(f"高相关Alpha数量: {len(high_corr_alphas)}")
    print(f"自相关过滤率: {len(high_corr_alphas)/(len(low_corr_alphas)+len(high_corr_alphas))*100:.1f}%")

    print(f"\n通过自相关检查的Alpha:")
    for alpha in low_corr_alphas:
        alpha_id = alpha['alpha_id']
        max_corr = alpha['max_correlation']
        status = alpha['status']
        if max_corr is not None:
            print(f"  {alpha_id}: 最大相关性={max_corr:.4f} ({status})")
        else:
            print(f"  {alpha_id}: {status}")

    print(f"\n被自相关过滤的Alpha:")
    for alpha in high_corr_alphas:
        alpha_id = alpha['alpha_id']
        max_corr = alpha['max_correlation']
        status = alpha['status']
        if max_corr is not None:
            print(f"  {alpha_id}: 最大相关性={max_corr:.4f} ({status})")
        else:
            print(f"  {alpha_id}: {status}")

    # 最终通过所有过滤的Alpha
    final_alpha_ids = [alpha['alpha_id'] for alpha in low_corr_alphas]

else:
    print(f"\n没有Alpha通过'厂'型过滤，跳过自相关检查")
    final_alpha_ids = []
    low_corr_alphas = []
    high_corr_alphas = []

# 最终结果统计
print(f"\n" + "="*60)
print(f"最终过滤结果统计:")
print(f"原始Alpha数量: {len(alpha_ids)}")
print(f"通过'厂'型过滤: {len(normal_alphas)}")
print(f"通过自相关过滤: {len(final_alpha_ids)}")
print(f"总过滤率: {(len(alpha_ids)-len(final_alpha_ids))/len(alpha_ids)*100:.1f}%")

print(f"\n最终可用的Alpha ID列表 ({len(final_alpha_ids)}个):")
for i, alpha_id in enumerate(final_alpha_ids, 1):
    print(f"  {i}. {alpha_id}")

# 保存完整结果到文件
result_summary = {
    'timestamp': datetime.now().isoformat(),
    'filter_settings': {
        'sharpe_zero_threshold': 'any_year_zero',
        'correlation_threshold': CORRELATION_THRESHOLD
    },
    'total_alphas': len(alpha_ids),
    'factory_filter': {
        'normal_alphas': len(normal_alphas),
        'factory_alphas': len(factory_alphas),
        'filter_rate': len(factory_alphas)/len(alpha_ids)*100 if alpha_ids else 0
    },
    'correlation_filter': {
        'low_correlation_alphas': len(low_corr_alphas),
        'high_correlation_alphas': len(high_corr_alphas),
        'filter_rate': len(high_corr_alphas)/(len(low_corr_alphas)+len(high_corr_alphas))*100 if (low_corr_alphas or high_corr_alphas) else 0
    },
    'final_results': {
        'final_alpha_count': len(final_alpha_ids),
        'total_filter_rate': (len(alpha_ids)-len(final_alpha_ids))/len(alpha_ids)*100 if alpha_ids else 0,
        'final_alpha_ids': final_alpha_ids
    },
    'detailed_results': {
        'factory_filter_details': {
            'normal_alphas': normal_alphas,
            'factory_alphas': factory_alphas
        },
        'correlation_filter_details': {
            'low_correlation_alphas': low_corr_alphas,
            'high_correlation_alphas': high_corr_alphas
        }
    }
}

with open('alpha_comprehensive_filter_results.json', 'w', encoding='utf-8') as f:
    json.dump(result_summary, f, ensure_ascii=False, indent=2)

print(f"\n完整结果已保存到 alpha_comprehensive_filter_results.json 文件")

# ==================== Alpha表达式提取功能 ====================

def get_alpha_expression(session, alpha_id):
    """
    获取单个Alpha的表达式

    Parameters:
    -----------
    session : WQBSession
        WQB会话对象
    alpha_id : str
        Alpha ID

    Returns:
    --------
    str : Alpha表达式，如果获取失败则返回None
    """
    try:
        # 获取Alpha详细信息
        response = session.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}")

        # 检查API限流
        retry_after = response.headers.get("Retry-After")
        if retry_after:
            logging.debug(f"API限流，Alpha {alpha_id} 等待 {retry_after} 秒后重试")
            time.sleep(float(retry_after))
            return get_alpha_expression(session, alpha_id)  # 递归重试

        if response.status_code == 200:
            data = response.json()
            expression = data.get('regular', '')
            if expression:
                logging.debug(f"成功获取Alpha {alpha_id} 表达式")
                return expression
            else:
                logging.warning(f"Alpha {alpha_id} 表达式为空")
                return None
        else:
            logging.error(f"获取Alpha {alpha_id} 失败，状态码: {response.status_code}")
            return None

    except Exception as e:
        logging.error(f"获取Alpha {alpha_id} 表达式时出错: {str(e)}")
        return None


def extract_alpha_expressions(session, alpha_ids, alpha_reverse_flags, output_file='alpha_expressions.txt'):
    """
    提取多个Alpha的表达式并保存到文件

    Parameters:
    -----------
    session : WQBSession
        WQB会话对象
    alpha_ids : list
        Alpha ID列表
    alpha_reverse_flags : dict
        Alpha反向标志字典 {alpha_id: is_reverse}
    output_file : str
        输出文件名

    Returns:
    --------
    dict : {alpha_id: expression} 成功提取的表达式字典
    """
    print(f"\n开始提取{len(alpha_ids)}个Alpha的表达式...")

    expressions = {}
    failed_alphas = []

    for i, alpha_id in enumerate(alpha_ids, 1):
        is_reverse = alpha_reverse_flags.get(alpha_id, False)
        print(f"提取进度: {i}/{len(alpha_ids)} - {alpha_id}{'(反向)' if is_reverse else ''}")

        expression = get_alpha_expression(session, alpha_id)

        if expression:
            # 如果是反向Alpha，在表达式前加负号
            if is_reverse:
                expression = f"-({expression})"
                print(f"  ✅ 成功(反向): -{expression[:97]}{'...' if len(expression) > 100 else ''}")
            else:
                print(f"  ✅ 成功: {expression[:100]}{'...' if len(expression) > 100 else ''}")

            expressions[alpha_id] = expression
        else:
            failed_alphas.append(alpha_id)
            print(f"  ❌ 失败")

        # 添加延迟避免API限流
        time.sleep(0.5)

    # 保存到文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for alpha_id in alpha_ids:  # 保持原始顺序
                if alpha_id in expressions:
                    f.write(f"{expressions[alpha_id]}\n")

        print(f"\n表达式提取完成:")
        print(f"成功提取: {len(expressions)}个")
        print(f"提取失败: {len(failed_alphas)}个")
        print(f"反向Alpha: {sum(1 for aid in alpha_ids if alpha_reverse_flags.get(aid, False) and aid in expressions)}个")
        print(f"结果已保存到: {output_file}")

        if failed_alphas:
            print(f"失败的Alpha ID: {failed_alphas}")

    except Exception as e:
        logging.error(f"保存表达式文件时出错: {str(e)}")

    return expressions


# 第三步：提取Alpha表达式
if final_alpha_ids:
    print(f"\n" + "="*60)
    print(f"第三步：提取Alpha表达式")
    print(f"开始提取{len(final_alpha_ids)}个最终Alpha的表达式")

    # 构建最终Alpha的反向标志
    final_alpha_reverse_flags = {}
    for alpha in low_corr_alphas:
        alpha_id = alpha['alpha_id']
        # 从normal_alphas中找到对应的反向标志
        for normal_alpha in normal_alphas:
            if normal_alpha['alpha_id'] == alpha_id:
                final_alpha_reverse_flags[alpha_id] = normal_alpha.get('is_reverse', False)
                break

    # 提取表达式
    alpha_expressions = extract_alpha_expressions(wqbs, final_alpha_ids, final_alpha_reverse_flags, 'final_alpha_expressions.txt')

    # 同时保存带Alpha ID的版本
    if alpha_expressions:
        with open('final_alpha_expressions_with_ids.txt', 'w', encoding='utf-8') as f:
            for alpha_id in final_alpha_ids:
                if alpha_id in alpha_expressions:
                    is_reverse = final_alpha_reverse_flags.get(alpha_id, False)
                    f.write(f"{alpha_id}{'(反向)' if is_reverse else ''}: {alpha_expressions[alpha_id]}\n")

        print(f"同时保存了带Alpha ID的版本: final_alpha_expressions_with_ids.txt")

else:
    print(f"\n没有最终可用的Alpha，跳过表达式提取")

print(f"\n" + "="*60)
print(f"🎉 Alpha综合过滤和表达式提取流程完成！")
print(f"📁 生成的文件:")
print(f"  - alpha_comprehensive_filter_results.json (完整过滤结果)")
print(f"  - final_alpha_expressions.txt (纯表达式，每行一个)")
print(f"  - final_alpha_expressions_with_ids.txt (带Alpha ID的表达式)")
print(f"🎯 最终获得 {len(final_alpha_ids)} 个高质量、独特的Alpha策略")

