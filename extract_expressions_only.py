import wqb
from wqb import WQBSession, print
import time
import logging

# Create `logger`
logger = wqb.wqb_logger()

# Create `wqbs`
wqbs = WQBSession(('<EMAIL>', 'Kyz417442'), logger=logger)

# Test connectivity
resp = wqbs.auth_request()
print(resp.status_code)
print(resp.ok)

# 最终的18个Alpha ID
final_alpha_ids = [
    'x5mOrNN', 'L3lWE7v', '5YqxPlo', 'MV5W5N6', 'ZNW5j1d', 
    'PKXag3J', 'eEqp97M', 'oZ95x7b', 'k9L2qkd', 'Og5P6KJ', 
    'ExrXQ0K', 'dvlLd6v', 'Og5aReq', 'gLopNoQ', 'oZ9M365', 
    'vPJVVA3', 'x5maPVW', 'AEOMvxR'
]

def get_alpha_expression(session, alpha_id):
    """获取单个Alpha的表达式"""
    try:
        response = session.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}")
        
        # 检查API限流
        retry_after = response.headers.get("Retry-After")
        if retry_after:
            print(f"API限流，Alpha {alpha_id} 等待 {retry_after} 秒后重试")
            time.sleep(float(retry_after))
            return get_alpha_expression(session, alpha_id)
        
        if response.status_code == 200:
            data = response.json()
            regular_data = data.get('regular', '')
            if regular_data:
                # 如果regular是字典，提取code字段；如果是字符串，直接返回
                if isinstance(regular_data, dict):
                    expression = regular_data.get('code', '')
                else:
                    expression = regular_data

                if expression:
                    return expression
                else:
                    print(f"Alpha {alpha_id} 表达式code为空")
                    return None
            else:
                print(f"Alpha {alpha_id} regular字段为空")
                return None
        else:
            print(f"获取Alpha {alpha_id} 失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"获取Alpha {alpha_id} 表达式时出错: {str(e)}")
        return None

print(f"开始提取{len(final_alpha_ids)}个Alpha的表达式...")

expressions = {}
failed_alphas = []

for i, alpha_id in enumerate(final_alpha_ids, 1):
    print(f"提取进度: {i}/{len(final_alpha_ids)} - {alpha_id}")
    
    expression = get_alpha_expression(wqbs, alpha_id)
    
    if expression:
        expressions[alpha_id] = expression
        # 安全地截取表达式前100个字符用于显示
        display_expr = str(expression)[:100] + ('...' if len(str(expression)) > 100 else '')
        print(f"  ✅ 成功: {display_expr}")
    else:
        failed_alphas.append(alpha_id)
        print(f"  ❌ 失败")
    
    # 添加延迟避免API限流
    time.sleep(0.5)

# 保存纯表达式文件（每行一个表达式）
try:
    with open('final_alpha_expressions.txt', 'w', encoding='utf-8') as f:
        for alpha_id in final_alpha_ids:
            if alpha_id in expressions:
                f.write(f"{expressions[alpha_id]}\n")
    
    # 保存带Alpha ID的版本（只保存表达式代码）
    with open('final_alpha_expressions_with_ids.txt', 'w', encoding='utf-8') as f:
        for alpha_id in final_alpha_ids:
            if alpha_id in expressions:
                f.write(f"{alpha_id}: {expressions[alpha_id]}\n")
    
    print(f"\n表达式提取完成:")
    print(f"成功提取: {len(expressions)}个")
    print(f"提取失败: {len(failed_alphas)}个")
    print(f"纯表达式文件: final_alpha_expressions.txt")
    print(f"带ID版本: final_alpha_expressions_with_ids.txt")
    
    if failed_alphas:
        print(f"失败的Alpha ID: {failed_alphas}")
        
except Exception as e:
    print(f"保存表达式文件时出错: {str(e)}")

print(f"\n🎉 Alpha表达式提取完成！")
