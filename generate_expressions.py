import sqlite3

DB_PATH = '/Users/<USER>/Downloads/新三阶段/alphas.db'
OUTPUT_FILE = '/Users/<USER>/Downloads/新三阶段/alpha_expressions_1000.txt'

TEMPLATE = 'rank(divide(subtract(ts_mean({data}, {short_window}), ts_mean({data}, {long_window})), ts_std({data}, {long_window})))'
WINDOWS = [10, 20, 60, 100, 200, 400]

def get_matrix_data_fields(db_path):
    """Fetches all MATRIX type data fields from the database."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT alpha_id FROM data_ASI_1_MINVOL1M WHERE type = 'MATRIX'")
        rows = cursor.fetchall()
        return [row[0] for row in rows]
    finally:
        conn.close()

def generate_expressions(data_fields, windows, template):
    """Generates alpha expressions based on data fields, windows, and a template."""
    expressions = []
    for data_field in data_fields:
        for i in range(len(windows)):
            for j in range(i + 1, len(windows)):
                short_window = windows[i]
                long_window = windows[j]
                expression = template.format(
                    data=data_field,
                    short_window=short_window,
                    long_window=long_window
                )
                expressions.append(expression)
    return expressions

def main():
    """Main function to generate and save alpha expressions."""
    print(f"Connecting to database at {DB_PATH}...")
    data_fields = get_matrix_data_fields(DB_PATH)
    print(f"Found {len(data_fields)} MATRIX data fields.")

    print("Generating alpha expressions...")
    expressions = generate_expressions(data_fields, WINDOWS, TEMPLATE)
    print(f"Generated {len(expressions)} expressions.")

    print(f"Saving expressions to {OUTPUT_FILE}...")
    with open(OUTPUT_FILE, 'w') as f:
        for expression in expressions:
            f.write(expression + '\n')
    print("Done.")

if __name__ == "__main__":
    main()