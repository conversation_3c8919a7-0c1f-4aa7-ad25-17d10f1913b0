INFO - [32m📧 创建WQB会话: <EMAIL>[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ WQB认证成功! 状态码: 200[0m
INFO - [32m📊 槽位启动延迟计划: ['0.0s', '4.1s', '9.1s', '8.9s', '21.9s', '42.8s', '69.4s', '50.1s'][0m
INFO - [32m🚀 优化MultiAlpha流水线系统初始化[0m
INFO - [32m📁 目标文件: filtered_shuffled_expressions.txt[0m
INFO - [32m🎲 随机种子: 42[0m
INFO - [32m🔄 断点续传: 从第125个任务开始[0m
INFO - [32m⚙️ 配置: 8Multi槽位, 10Alpha/批次[0m
INFO - [32m🛡️ 保护延迟: 2.0秒/MultiAlpha请求[0m
INFO - [32m📊 槽位启动策略: 指数退避[0m
INFO - [32m正常工作1[0m
INFO - [32m🚀 启动优化MultiAlpha流水线系统[0m
INFO - [32m================================================================================[0m
INFO - [32m🔥 开始请求预热...[0m
INFO - [32m<WQBSession ['<EMAIL>']>.get_authentication(...) [
    https://api.worldquantbrain.com/authentication
]: [0m
INFO - [32m✅ 预热请求成功[0m
INFO - [32m📁 加载Alpha因子...[0m
INFO - [32m📁 开始加载因子文件: filtered_shuffled_expressions.txt[0m
INFO - [32m✅ 成功加载 66345 个Alpha因子 (txt单列模式)[0m
INFO - [32m📋 前5个因子示例:[0m
INFO - [32m  1. 1: rank(divide(subtract(ts_mean(fnd17_spfcrpmtp, 10),...[0m
INFO - [32m  2. 2: rank(divide(subtract(ts_mean(fnd6_invchy, 10), ts_...[0m
INFO - [32m  3. 3: rank(divide(subtract(ts_mean(pv37_returns30_t, 20)...[0m
INFO - [32m  4. 4: rank(divide(subtract(ts_mean(pv37_annfv_mfm2_cnin,...[0m
INFO - [32m  5. 5: rank(divide(subtract(ts_mean(fnd6_xintsa, 20), ts_...[0m
INFO - [32m📦 创建MultiAlpha任务，每批10个Alpha[0m
INFO - [32m🔄 断点续传: 跳过前1250个Alpha，剩余65095个[0m
INFO - [32m✅ 创建6510个任务 (编号126-6635)[0m
INFO - [32m🚀 开始渐进式启动 8 个槽位...[0m
INFO - [32m🔧 槽位0 启动[0m
INFO - [32m🔧 槽位1 等待 4.1 秒后启动...[0m
INFO - [32m🔧 槽位2 等待 9.1 秒后启动...[0m
INFO - [32m🔧 槽位3 等待 8.9 秒后启动...[0m
INFO - [32m🔧 槽位4 等待 21.9 秒后启动...[0m
INFO - [32m🔧 槽位5 等待 42.8 秒后启动...[0m
INFO - [32m🔧 槽位6 等待 69.4 秒后启动...[0m
INFO - [32m🔧 槽位7 等待 50.1 秒后启动...[0m
INFO - [32m🔧 槽位1 启动[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/6510 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/65095[0m
INFO - [32m🔧 槽位: 2/8 | 待处理: 6508 | 失败: 0[0m
INFO - [32m📡 API频率: 1/5 (20.0%) | 🚀 智能并行: 4槽位 | 下次可用: 0.0s[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位3 启动[0m
INFO - [32m🔧 槽位2 启动[0m
ERROR - [31m❌ [槽位0] 任务126失败: {'children': ['3ghTgxayB56ZbsFhIb2eJnz', '4elhVV5vs5648GsWCn4wC73', '22gQgUa3J4KvbCqvO65QoSh', '29KDQXcHh4mjaWs17Wsr5t4G', '2BcB4T2ng4jucu8wfgyj5tJ', '3F6Wgl30P56Mcbe5Ft748rR', '3AtOwlcr24peaYxLqkYRW5L', 'KE5bv8Vv4KUaCYzESB53Jy', '2zjkiu9jA55a9aS2CqcKvQN', '2JAOFxfLo4Z2bZOYgj1ciPq'], 'type': 'REGULAR', 'status': 'ERROR'}[0m
INFO - [32m🔍 [槽位0] 开始自动错误诊断...[0m
INFO - [32m🔍 [槽位0] 开始错误快速定位：https://api.worldquantbrain.com/simulations/3ov5o39Nf4I6aHTY8mPe5tx[0m
INFO - [32m🔍 [槽位0] MultiAlpha状态: ERROR, Children数量: 10[0m
INFO - [32m🔍 [槽位0] 开始并发错误诊断，共10个child， 并发度 1[0m
INFO - [32m✅ [槽位0] 错误定位完成![0m
INFO - [32m📊 [槽位0] 统计: 0个CANCELLED, 1个错误源已定位[0m
ERROR - [31m🎯 [槽位0] 已定位到具体错误Alpha，详情见上方日志[0m
INFO - [32m🧠 [槽位0] 启动智能错误恢复...[0m
INFO - [32m🧠 [槽位0] 开始智能错误恢复：任务126[0m
INFO - [32m🔍 [槽位0] 分析10个Alpha的状态...[0m
WARNING - [33m❌ [槽位0] Alpha 1: rank(divide(subtract(ts_mean(fnd28_value_03063a, 1... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 2: rank(divide(subtract(ts_mean(pv37_is_a3_latest_per... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 3: rank(divide(subtract(ts_mean(mdl262_sbda_trkdpitde... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 4: rank(divide(subtract(ts_mean(fnd17_roxlcxspea, 20)... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 5: rank(divide(subtract(ts_mean(fnd6_prvy, 20), ts_me... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 6: rank(divide(subtract(ts_mean(pv37_nav_best_cur_fis... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 7: rank(divide(subtract(ts_mean(star_ccr_sector_rank,... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 8: rank(divide(subtract(ts_mean(pv37_intfv_all_vacl, ... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 9: rank(divide(subtract(ts_mean(mdl25_rd_7v, 10), ts_... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 10: rank(divide(subtract(ts_mean(oth452_accruals_d1_mi... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
INFO - [32m✅ [槽位0] 智能恢复处理完成: 好Alpha 0个, 坏Alpha 10个, 创建重试任务 0个[0m
INFO - [32m✨ [槽位0] 智能恢复完成，好的Alpha已加入重试队列[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/6510 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/65095[0m
INFO - [32m🔧 槽位: 3/8 | 待处理: 6506 | 失败: 1[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m🚫 错误Alpha: 10个 (已保存至 error_alphas_20250802_000045.csv)[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
INFO - [32m🔧 槽位4 启动[0m
ERROR - [31m❌ [槽位1] 任务127失败: {'children': ['kGEwe2Lo57zaQa1hUbk795g', 'RPagdaBn4yY90QRjvXC9mA', '2Lv8g36eB4CEbiN154YqAG7', '20ZePi7uj4xlbpZIUPAPc0O', '2kPl774uz4RDaLwzwXonujR', '3znJd11jy4CLcpmpRLePRJG', '20RrgleU157BaSrua5EPiiD', '4Aori6f5M5hKcpM17y6UHFsz', '1kfhlV6Gz5jP9UNGxvGqbWf', '2q0Sg14lQ4Yfa8S2LAZAr5J'], 'type': 'REGULAR', 'status': 'ERROR'}[0m
INFO - [32m🔍 [槽位1] 开始自动错误诊断...[0m
INFO - [32m🔍 [槽位1] 开始错误快速定位：https://api.worldquantbrain.com/simulations/1j5hhigHw4oYbzb1t1lyGNX[0m
INFO - [32m🔍 [槽位1] MultiAlpha状态: ERROR, Children数量: 10[0m
INFO - [32m🔍 [槽位1] 开始并发错误诊断，共10个child， 并发度 1[0m
ERROR - [31m❌ [槽位3] 任务128失败: {'children': ['4uM1sgbzc4of9DGfe2sEQQN', '2VaWOr2tX4P1bMANkCEPbP2', '48Q8KG9lh4LYcKU16OyEGxT5', '4Gh1qo9IZ4p19lYwFdds9Ki', '34wgCAe015fcbut66sOpnWa', '2fMfVo9TQ5i59m4XNUgEigK', 'D5KtB34p543ansLE9iWmb7', '4pgBKXen9506aQ9BoUBzak3', '3VnPHpdRQ50mbrOZUagfeEE', '4siuacfnA4XQcJ4TFNoeXie'], 'type': 'REGULAR', 'status': 'ERROR'}[0m
INFO - [32m🔍 [槽位3] 开始自动错误诊断...[0m
INFO - [32m🔍 [槽位3] 开始错误快速定位：https://api.worldquantbrain.com/simulations/4zaYnedD655ZbNM2cU0wRuF[0m
INFO - [32m🔍 [槽位3] MultiAlpha状态: ERROR, Children数量: 10[0m
INFO - [32m🔍 [槽位3] 开始并发错误诊断，共10个child， 并发度 1[0m
ERROR - [31m❌ [槽位2] 任务129失败: {'children': ['1JxWg3agS5fF99642rDnwZD', '1d1c4CggX4WTbVs15pXlzRea', '2T56xu9CB4XEb2q1eQfnqTCs', 'cPxxSaLB52rcvdf04LOAQd', '1y7n88by64OtanF11EPRhzpc', '2oFfU51dw4SC9uYeqrrXc6O', 'expXl7oD4jBcru3vP4rqrM', '3CBRuF9SR4jjb9zpCfhbwQ0', '3EbGogf675j08TggoInutS9', 'v9xYv3PB4rIbcapW7RbWrj'], 'type': 'REGULAR', 'status': 'ERROR'}[0m
INFO - [32m🔍 [槽位2] 开始自动错误诊断...[0m
INFO - [32m🔍 [槽位2] 开始错误快速定位：https://api.worldquantbrain.com/simulations/1qeRak3sd4ASaRAy8QkHugO[0m
INFO - [32m🔍 [槽位2] MultiAlpha状态: ERROR, Children数量: 10[0m
INFO - [32m🔍 [槽位2] 开始并发错误诊断，共10个child， 并发度 1[0m
INFO - [32m✅ [槽位1] 错误定位完成![0m
INFO - [32m📊 [槽位1] 统计: 0个CANCELLED, 1个错误源已定位[0m
ERROR - [31m🎯 [槽位1] 已定位到具体错误Alpha，详情见上方日志[0m
INFO - [32m🧠 [槽位1] 启动智能错误恢复...[0m
INFO - [32m🧠 [槽位1] 开始智能错误恢复：任务127[0m
INFO - [32m🔍 [槽位1] 分析10个Alpha的状态...[0m
WARNING - [33m❌ [槽位1] Alpha 1: rank(divide(subtract(ts_mean(oth455_relation_roam_... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位1] Alpha 2: rank(divide(subtract(ts_mean(fnd28_nddq1_value_043... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位1] Alpha 3: rank(divide(subtract(ts_mean(mdl77_pedwf_cf, 60), ... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位1] Alpha 4: rank(divide(subtract(ts_mean(anl4_gric_number, 100... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位1] Alpha 5: rank(divide(subtract(ts_mean(debt_lt_curr, 60), ts... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位1] Alpha 6: rank(divide(subtract(ts_mean(oth455_competitor_roa... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位1] Alpha 7: rank(divide(subtract(ts_mean(fnd28_value_01503q, 2... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位1] Alpha 8: rank(divide(subtract(ts_mean(anl15_gr_cal_fy3_gro,... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位1] Alpha 9: rank(divide(subtract(ts_mean(fnd17_anrhsfcfq, 100)... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位1] Alpha 10: rank(divide(subtract(ts_mean(oth455_partner_n2v_p5... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
INFO - [32m✅ [槽位1] 智能恢复处理完成: 好Alpha 0个, 坏Alpha 10个, 创建重试任务 0个[0m
INFO - [32m✨ [槽位1] 智能恢复完成，好的Alpha已加入重试队列[0m
INFO - [32m✅ [槽位3] 错误定位完成![0m
INFO - [32m📊 [槽位3] 统计: 0个CANCELLED, 1个错误源已定位[0m
ERROR - [31m🎯 [槽位3] 已定位到具体错误Alpha，详情见上方日志[0m
INFO - [32m🧠 [槽位3] 启动智能错误恢复...[0m
INFO - [32m🧠 [槽位3] 开始智能错误恢复：任务128[0m
INFO - [32m🔍 [槽位3] 分析10个Alpha的状态...[0m
WARNING - [33m❌ [槽位3] Alpha 1: rank(divide(subtract(ts_mean(mdl26_market_cap_u, 1... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位3] Alpha 2: rank(divide(subtract(ts_mean(oth432_stbv_trkdpitde... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位3] Alpha 3: rank(divide(subtract(ts_mean(fnd23_itla, 100), ts_... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位3] Alpha 4: rank(divide(subtract(ts_mean(oth455_relation_roam_... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位3] Alpha 5: rank(divide(subtract(ts_mean(fnd28_cfq_value_04795... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位3] Alpha 6: rank(divide(subtract(ts_mean(anl69_eqy_best_cur_fi... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位3] Alpha 7: rank(divide(subtract(ts_mean(fnd28_nddq1_value_024... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位3] Alpha 8: rank(divide(subtract(ts_mean(pv37_all_smcs, 60), t... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位3] Alpha 9: rank(divide(subtract(ts_mean(mdl262_rtlr_trkdpitde... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位3] Alpha 10: rank(divide(subtract(ts_mean(fnd17_rhsfcf1a, 20), ... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
INFO - [32m✅ [槽位3] 智能恢复处理完成: 好Alpha 0个, 坏Alpha 10个, 创建重试任务 0个[0m
INFO - [32m✨ [槽位3] 智能恢复完成，好的Alpha已加入重试队列[0m
INFO - [32m✅ [槽位2] 错误定位完成![0m
INFO - [32m📊 [槽位2] 统计: 0个CANCELLED, 1个错误源已定位[0m
ERROR - [31m🎯 [槽位2] 已定位到具体错误Alpha，详情见上方日志[0m
INFO - [32m🧠 [槽位2] 启动智能错误恢复...[0m
INFO - [32m🧠 [槽位2] 开始智能错误恢复：任务129[0m
INFO - [32m🔍 [槽位2] 分析10个Alpha的状态...[0m
WARNING - [33m❌ [槽位2] Alpha 1: rank(divide(subtract(ts_mean(fnd28_fsq1_value_0475... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位2] Alpha 2: rank(divide(subtract(ts_mean(fnd23_cstq, 10), ts_m... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位2] Alpha 3: rank(divide(subtract(ts_mean(anl4_netprofit_median... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位2] Alpha 4: rank(divide(subtract(ts_mean(anl14_mean_bvps_fy1, ... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位2] Alpha 5: rank(divide(subtract(ts_mean(fnd6_ivaeqq, 20), ts_... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位2] Alpha 6: rank(divide(subtract(ts_mean(mdl25_cfs_51v, 10), t... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位2] Alpha 7: rank(divide(subtract(ts_mean(fnd23_cpdf, 100), ts_... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位2] Alpha 8: rank(divide(subtract(ts_mean(fnd23_vnin, 10), ts_m... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位2] Alpha 9: rank(divide(subtract(ts_mean(mdl77_flowratio, 60),... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位2] Alpha 10: rank(divide(subtract(ts_mean(fnd6_oancfy, 10), ts_... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
INFO - [32m✅ [槽位2] 智能恢复处理完成: 好Alpha 0个, 坏Alpha 10个, 创建重试任务 0个[0m
INFO - [32m✨ [槽位2] 智能恢复完成，好的Alpha已加入重试队列[0m
INFO - [32m🔧 槽位5 启动[0m
INFO - [32m🔧 槽位7 启动[0m
INFO - [32m🔧 槽位6 启动[0m
ERROR - [31m❌ [槽位0] 任务130失败: {'children': ['3GKQrLdrE4qPbXpHviyxwi8', '4ijMIMdmq4nkboXXqFM8oxd', '3fcgDWeq75glb4VlUkycuHN', '4djO87ly4imcxD15WdOeY68', '35bHEkAo5fLc6Dlkfgd8ZD', '2kalzV9ac5boaIhhIRQUKqQ', '4h9Rx952n4qDa1DhRLNcAYQ', '2VYPe6ayZ4Gs8MJ1daI9aTuh', '2xsMfl5jp4nPbTMmpBqArIJ', '4iM6um8ig5cC8Cv1fYj6jXUU'], 'type': 'REGULAR', 'status': 'ERROR'}[0m
INFO - [32m🔍 [槽位0] 开始自动错误诊断...[0m
INFO - [32m🔍 [槽位0] 开始错误快速定位：https://api.worldquantbrain.com/simulations/3jnUdd1Cz5hZ9HFt3Ljumhw[0m
INFO - [32m🔍 [槽位0] MultiAlpha状态: ERROR, Children数量: 10[0m
INFO - [32m🔍 [槽位0] 开始并发错误诊断，共10个child， 并发度 1[0m
INFO - [32m✅ [槽位0] 错误定位完成![0m
INFO - [32m📊 [槽位0] 统计: 0个CANCELLED, 1个错误源已定位[0m
ERROR - [31m🎯 [槽位0] 已定位到具体错误Alpha，详情见上方日志[0m
INFO - [32m🧠 [槽位0] 启动智能错误恢复...[0m
INFO - [32m🧠 [槽位0] 开始智能错误恢复：任务130[0m
INFO - [32m🔍 [槽位0] 分析10个Alpha的状态...[0m
WARNING - [33m❌ [槽位0] Alpha 1: rank(divide(subtract(ts_mean(cash_st, 100), ts_mea... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 2: rank(divide(subtract(ts_mean(mdl26_average_revison... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 3: rank(divide(subtract(ts_mean(mdl77_pctchgcf, 10), ... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 4: rank(divide(subtract(ts_mean(anl69_ptp_expected_re... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 5: rank(divide(subtract(ts_mean(oth401_qes_gamef_psco... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 6: rank(divide(subtract(ts_mean(oth455_competitor_n2v... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 7: rank(divide(subtract(ts_mean(fnd6_dc, 60), ts_mean... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 8: rank(divide(subtract(ts_mean(fnd28_bdeq_value_0326... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 9: rank(divide(subtract(ts_mean(mdl25_83v, 100), ts_m... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
WARNING - [33m❌ [槽位0] Alpha 10: rank(divide(subtract(ts_mean(fnd17_fcfq, 60), ts_m... - 真正错误: ERROR - Attempted to use inaccessible or unknown operator "ts_std"[0m
INFO - [32m✅ [槽位0] 智能恢复处理完成: 好Alpha 0个, 坏Alpha 10个, 创建重试任务 0个[0m
INFO - [32m✨ [槽位0] 智能恢复完成，好的Alpha已加入重试队列[0m
INFO - [32m============================================================[0m
INFO - [32m📊 进度: 0/6510 (0.0%) | 成功率: 0.0%[0m
INFO - [32m⚡ 唯一效率: 0.0 唯一Alpha/分钟 | 唯一提取: 0/65095[0m
INFO - [32m🔧 槽位: 6/8 | 待处理: 6499 | 失败: 5[0m
INFO - [32m📡 API频率: 0/5 (0.0%) | 🚀 智能并行: 5槽位 | 下次可用: 0.0s[0m
INFO - [32m🚫 错误Alpha: 50个 (已保存至 error_alphas_20250802_000045.csv)[0m
INFO - [32m⏰ 预计完成: 计算中...[0m
INFO - [32m============================================================[0m
