def get_alpha_pnl(session, alpha_id):
    count = 0
    while True:
        if count>30:
            s = login()
            count = 0
        pnl = session.get("https://api.worldquantbrain.com/alphas/" + alpha_id + "/recordsets/pnl")
        retry_after = pnl.headers.get("Retry-After")
        if retry_after:
            # print(f"Sleeping for {retry_after} seconds")
            sleep(float(retry_after))
            # sleep(10)
        else:
            # print(f"{alpha_id} PnL retrieved")
            count += 1
            return (pnl, alpha_id)
def fetch_pnls(session, alpha_list):    
    pnl_ls = []
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # Create a list of tasks
        futures = [executor.submit(get_alpha_pnl, session, alpha_id) for alpha_id in alpha_list]
        for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures)):
            result = future.result()
            pnl_ls.append(result)
    return pnl_ls


def get_pnl_panel(session, alpha_list):
    alpha_pnls = fetch_pnls(session, alpha_list)
    pnl_df = pd.DataFrame()
    
    for pnl, alpha_id in tqdm(alpha_pnls):
        # 检查pnl对象是否有json方法，如果有，则调用该方法获取数据
        if hasattr(pnl, 'json') and callable(pnl.json):
            data = pnl.json()
        else:
            # 假设pnl已经是字典格式
            data = pnl

        # 检查records的列数
        if len(data['records'][0]) == 2:
            df = pd.DataFrame(data['records'], columns=['Date', alpha_id])
            df.set_index('Date', inplace=True)
        elif len(data['records'][0]) == 3:
            properties = data['schema']['properties']
            # 如果含有'risk-neutralized-pnl'，则保留这一列，并删除其他额外的列
            if any(prop['name'] == 'risk-neutralized-pnl' for prop in properties):
                records = [record[:2] for record in data['records']]
                df = pd.DataFrame(records, columns=['Date', alpha_id])
                df.set_index('Date', inplace=True)
            else:
                # 如果records的列数为3，但不包含'risk-neutralized-pnl'，则跳过这个alpha_id
                continue

        # 将当前alpha_id的DataFrame与总的DataFrame合并
        if pnl_df.empty:
            pnl_df = df
        else:
            pnl_df = pd.merge(pnl_df, df, on='Date', how='outer')

    return pnl_df



def get_n_os_alphas(session, total_alphas, limit=100, max_retries=10):
    fetched_alphas = []
    offset = 0
    retries = 0

    while len(fetched_alphas) < total_alphas and retries < max_retries:
        try:
            response = session.get(
                f"https://api.worldquantbrain.com/users/self/alphas?stage=OS&limit={limit}&offset={offset}"
            )
            response.raise_for_status()
            alphas = response.json()["results"]
            fetched_alphas.extend(alphas)
            if len(alphas) < limit:
                break
            offset += limit
            retries = 0
        except requests.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err}, retrying in {2 ** retries} seconds...")
            time.sleep(2 ** retries)  # 指数退避策略
            retries += 1  # 增加重试次数
            continue  # 继续下一次循环，不增加offset
        except Exception as err:
            print(f"An error occurred: {err}")
            break

    return fetched_alphas[:total_alphas]



def get_submitted_all(
    s,
    max: int = 300
):
    os_alpha_list = get_n_os_alphas(s, max)
    os_alpha_ids = [item['id'] for item in os_alpha_list]
    os_pnl_df = get_pnl_panel(s,os_alpha_ids)
    os_ret_df = os_pnl_df - os_pnl_df.ffill().shift(1)
    return os_ret_df



def gold_mining(s,is_ret_df, os_ret_df):
   
    is_df = is_ret_df[(pd.to_datetime(is_ret_df.index)>pd.to_datetime(is_ret_df.index).max() - pd.DateOffset(years=4)]
    os_df = os_ret_df[(pd.to_datetime(os_ret_df.index)>pd.to_datetime(os_ret_df.index).max() - pd.DateOffset(years=4)]
    
    is_df = is_df.replace(0, np.nan)
    os_df = os_df.replace(0, np.nan)

    gold_ids = []
    for col_is in is_df.columns:
        ret = is_df[col_is]
        ret = pd.concat([ret,os_df],axis=1)
        corr_=ret.corr()
        cor_max = corr_.iloc[0,1:].max()
        if cor_max<0.7:
            gold_ids.append(col_is)
        else:
            if np.isnan(cor_max):
                cor_max = 0
                set_alpha_properties(s,col_is, name= 'NO_DATA', regular_desc= cor_max, tags=['MOVE'])   
            else:
                set_alpha_properties(s,col_is, name= col_is, regular_desc= cor_max, tags=['Self Correlation'])   
    print(gold_ids)
    return gold_ids



def check_remove_self_correlation(
    s,
    ids,
    os_ret_df
):
    is_pnl_df = get_pnl_panel(s,ids)
    is_ret_df = is_pnl_df - is_pnl_df.ffill().shift(1)
    pass_is_ids = gold_mining(s,is_ret_df, os_ret_df)
    return pass_is_ids