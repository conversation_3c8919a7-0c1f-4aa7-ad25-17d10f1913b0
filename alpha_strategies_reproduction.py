# WorldQuant Brain Fast Expression Alpha策略复现
# 基于ASI_fundamental28翻译后字段(1).xlsx的Y列alpha挖掘建议
# 避免使用逻辑操作符和风险控制操作符，只使用基础算术和时间序列操作符

# ==================== Alpha策略复现 (简化Fast Expression格式) ====================

# Alpha 1: 高Beta+低估值组合，博取市场反弹弹性
# 原建议: "1. 高Beta+低估值组合，博取市场反弹弹性"
# 策略：Beta值乘以市盈率的倒数
alpha_1 = """
multiply(rank(beta), rank(reverse(pe_ratio)))
"""

# Alpha 2: TTM经营现金流质量
# 原建议: "1. TTM经营现金流为正且环比上升"
# 策略：现金流与其历史均值的比值
alpha_2 = """
divide(operating_cash_flow_ttm, ts_mean(operating_cash_flow_ttm, 252))
"""

# Alpha 3: EPS增长动量
# 原建议: "1. 高EPS增长+低市盈率，成长alpha"
# 策略：EPS增长率乘以市盈率倒数
alpha_3 = """
multiply(divide(subtract(eps, ts_delay(eps, 252)), abs(ts_delay(eps, 252))), reverse(pe_ratio))
"""

# Alpha 4: 破净反转
# 原建议: "1. 低市净率+高ROE，破净反转alpha"
# 策略：ROE除以市净率
alpha_4 = """
divide(roe, pb_ratio)
"""

# Alpha 5: 毛利率质量
# 原建议: "1. 高毛利+低估值，盈利质量alpha"
# 策略：毛利率乘以市销率倒数
alpha_5 = """
multiply(gross_margin, reverse(ps_ratio))
"""

# Alpha 6: 现金流稳健性
# 原建议: "1. 季度为正+年度为正，现金流稳健alpha"
# 策略：现金流与其波动率的比值
alpha_6 = """
divide(operating_cash_flow, ts_std_dev(operating_cash_flow, 63))
"""

# Alpha 7: ROE动量
# 原建议: "2. ROE持续改善+股价滞后，动量alpha"
# 策略：ROE变化率乘以价格动量倒数
alpha_7 = """
multiply(ts_delta(roe, 63), reverse(ts_delta(close, 20)))
"""

# Alpha 8: 销售增长效率
# 原建议: "1. 高销售增长+低市销率，成长alpha"
# 策略：收入增长率除以市销率
alpha_8 = """
divide(divide(subtract(revenue, ts_delay(revenue, 252)), abs(ts_delay(revenue, 252))), ps_ratio)
"""

# Alpha 9: 盈利质量
# 原建议: "3. 现金流>净利润，盈利质量alpha"
# 策略：现金流与净利润的比值
alpha_9 = """
divide(operating_cash_flow, max(net_income, 0.01))
"""

# Alpha 10: 综合估值
# 基于多个建议的综合策略
# 策略：多个估值指标的综合
alpha_10 = """
add(add(reverse(pe_ratio), reverse(pb_ratio)), reverse(ps_ratio))
"""

# ==================== 基于更多Excel建议的Alpha (简化版本) ====================

# Alpha 11: 连续增长动量
# 原建议: "2. 连续3季度加速增长，动量alpha"
# 策略：EPS连续变化率
alpha_11 = """
multiply(ts_delta(eps, 63), ts_delta(eps, 126))
"""

# Alpha 12: 相对行业表现
# 原建议: "4. 与行业均值差值排名alpha"
# 策略：个股ROE与行业均值的差异（简化为绝对值）
alpha_12 = """
subtract(roe, ts_mean(roe, 252))
"""

# Alpha 13: 资产重估
# 原建议: "5. 高有形账面价值+低估值，资产重估alpha"
# 策略：账面价值除以市净率
alpha_13 = """
divide(book_value, pb_ratio)
"""

# Alpha 14: 毛利率改善
# 原建议: "2. 毛利率持续改善+股价滞后，动量alpha"
# 策略：毛利率变化与价格变化的反向关系
alpha_14 = """
multiply(ts_delta(gross_margin, 63), reverse(ts_delta(close, 20)))
"""

# Alpha 15: 自由现金流效率
# 原建议: "3. 与资本支出比，自由现金流alpha"
# 策略：现金流与资本支出的比值
alpha_15 = """
divide(operating_cash_flow, max(capex, 0.01))
"""

# Alpha 16: 偿债能力
# 原建议: "4. 与流动负债比，偿债能力alpha"
# 策略：现金流与负债的比值
alpha_16 = """
divide(operating_cash_flow, max(current_liabilities, 0.01))
"""

# Alpha 17: 财务效率
# 原建议: "4. 与杠杆率结合，财务效率alpha"
# 策略：ROE乘以杠杆率倒数
alpha_17 = """
multiply(roe, reverse(debt_to_equity))
"""

# Alpha 18: 需求真实性
# 原建议: "3. 与库存结合，需求真实性alpha"
# 策略：收入增长与库存变化的比值
alpha_18 = """
divide(ts_delta(revenue, 252), max(ts_delta(inventory, 252), 0.01))
"""

# Alpha 19: 盈利质量综合
# 原建议: "5. 与净利润增长结合，盈利质量alpha"
# 策略：收入增长与净利润增长的比值
alpha_19 = """
divide(ts_delta(revenue, 252), max(ts_delta(net_income, 252), 0.01))
"""

# Alpha 20: 综合回报
# 原建议: "1. 高综合收益+低估值，综合回报α"
# 策略：综合收益除以市盈率
alpha_20 = """
divide(comprehensive_income, pe_ratio)
"""

# ==================== 超简化版本 (最基础的表达式) ====================

# 超简化Alpha A: Beta估值
alpha_ultra_a = """
multiply(beta, reverse(pe_ratio))
"""

# 超简化Alpha B: 现金流收益率
alpha_ultra_b = """
divide(operating_cash_flow, market_cap)
"""

# 超简化Alpha C: 盈利增长
alpha_ultra_c = """
ts_delta(eps, 252)
"""

# 超简化Alpha D: 价值因子
alpha_ultra_d = """
reverse(pb_ratio)
"""

# 超简化Alpha E: 质量因子
alpha_ultra_e = """
roe
"""

# 超简化Alpha F: 动量因子
alpha_ultra_f = """
ts_delta(close, 20)
"""

# 超简化Alpha G: 毛利率
alpha_ultra_g = """
gross_margin
"""

# 超简化Alpha H: 现金流稳定性
alpha_ultra_h = """
divide(operating_cash_flow, ts_std_dev(operating_cash_flow, 252))
"""

# 超简化Alpha I: 估值综合
alpha_ultra_i = """
add(reverse(pe_ratio), reverse(pb_ratio))
"""

# 超简化Alpha J: 成长综合
alpha_ultra_j = """
add(ts_delta(eps, 252), ts_delta(revenue, 252))
"""

# ==================== 波动优化版本 (降低因子波动) ====================

# 波动优化Alpha A: 平滑Beta估值
alpha_smooth_a = """
multiply(ts_mean(beta, 20), ts_mean(reverse(pe_ratio), 20))
"""

# 波动优化Alpha B: 标准化现金流收益率
alpha_smooth_b = """
divide(divide(operating_cash_flow, market_cap), ts_std_dev(divide(operating_cash_flow, market_cap), 63))
"""

# 波动优化Alpha C: 平滑盈利增长
alpha_smooth_c = """
ts_mean(ts_delta(eps, 252), 10)
"""

# 波动优化Alpha D: 稳定价值因子
alpha_smooth_d = """
divide(reverse(pb_ratio), ts_std_dev(reverse(pb_ratio), 252))
"""

# 波动优化Alpha E: 标准化质量因子
alpha_smooth_e = """
divide(roe, ts_std_dev(roe, 252))
"""

# 波动优化Alpha F: 平滑动量因子
alpha_smooth_f = """
ts_mean(ts_delta(close, 20), 5)
"""

# 波动优化Alpha G: 稳定毛利率
alpha_smooth_g = """
divide(gross_margin, ts_std_dev(gross_margin, 63))
"""

# 波动优化Alpha H: 双重平滑现金流
alpha_smooth_h = """
ts_mean(divide(operating_cash_flow, ts_std_dev(operating_cash_flow, 252)), 10)
"""

# 波动优化Alpha I: 平滑估值综合
alpha_smooth_i = """
ts_mean(add(reverse(pe_ratio), reverse(pb_ratio)), 15)
"""

# 波动优化Alpha J: 标准化成长综合
alpha_smooth_j = """
divide(add(ts_delta(eps, 252), ts_delta(revenue, 252)), ts_std_dev(add(ts_delta(eps, 252), ts_delta(revenue, 252)), 126))
"""

# ==================== 使用说明 ====================
"""
使用方法:
1. 复制任意alpha表达式到WorldQuant Brain平台
2. 确保数据字段名称与平台一致
3. 根据需要调整参数 (如时间窗口252天等)
4. 进行回测验证

推荐使用顺序:
1. 先测试超简化版本 (alpha_ultra_a 到 alpha_ultra_j) - 最基础
2. 如果因子波动剧烈，使用波动优化版本 (alpha_smooth_a 到 alpha_smooth_j)
3. 再尝试基础版本 (alpha_1 到 alpha_10) - 中等复杂度
4. 最后测试扩展版本 (alpha_11 到 alpha_20) - 较复杂

因子波动优化技巧:
- 使用 ts_mean(factor, d) 进行移动平均平滑
- 使用 divide(factor, ts_std_dev(factor, d)) 进行波动率标准化
- 使用 max(min(factor, upper), lower) 限制极值
- 使用较长的时间窗口 (如63天、126天) 降低噪音
- 组合多个平滑技术: ts_mean(divide(factor, ts_std_dev(factor, 252)), 10)

注意事项:
- 避免使用逻辑操作符 (and, or, if_else, greater, less等)
- 避免使用风险控制操作符 (winsorize, zscore, hump, group_neutralize等)
- 只使用基础算术操作符 (add, subtract, multiply, divide, reverse, max, min等)
- 只使用时间序列操作符 (ts_delta, ts_mean, ts_std_dev, ts_delay等)
- 可根据实际数据字段名称调整
- 建议先从超简化表达式开始测试

使用的操作符类别:
算术操作符: add, subtract, multiply, divide, reverse, max, min, abs
时间序列操作符: ts_delta, ts_mean, ts_std_dev, ts_delay, ts_sum
横截面操作符: rank (仅在必要时使用)

常用字段名称 (需根据实际平台调整):
基础价格数据: close, open, high, low, volume, vwap
估值指标: pe_ratio, pb_ratio, ps_ratio, market_cap
财务指标: eps, roe, revenue, net_income, operating_cash_flow
质量指标: gross_margin, debt_to_equity, current_liabilities
其他: beta, book_value, capex, inventory, comprehensive_income

时间窗口建议:
- 短期: 20天 (1个月)
- 季度: 63天 (3个月)
- 半年: 126天 (6个月)
- 年度: 252天 (1年)

波动优化具体方法:

1. 移动平均平滑:
   原始: your_factor
   优化: ts_mean(your_factor, 20)

2. 波动率标准化:
   原始: your_factor
   优化: divide(your_factor, ts_std_dev(your_factor, 252))

3. 极值限制:
   原始: your_factor
   优化: max(min(your_factor, 3), -3)

4. 双重平滑:
   原始: your_factor
   优化: ts_mean(divide(your_factor, ts_std_dev(your_factor, 252)), 10)

5. 相对波动控制:
   原始: your_factor
   优化: divide(your_factor, ts_mean(abs(your_factor), 63))

选择平滑参数的经验:
- 高频因子 (日度更新): 使用5-10天平滑
- 中频因子 (周度更新): 使用10-20天平滑
- 低频因子 (月度更新): 使用20-63天平滑
- 基本面因子: 使用63-252天平滑
"""
