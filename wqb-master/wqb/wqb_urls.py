__all__ = [
    'WQB_API_URL',
    'URL_ALPHAS',
    'URL_ALPHAS_ALPHAID',
    'URL_ALPHAS_ALPHAID_CHECK',
    'URL_AUTHENTICATION',
    'URL_DATACATEGORIES',
    'URL_DATAFIELDS',
    'URL_DATAFIELDS_FIELDID',
    'URL_DATASETS',
    'URL_DATASETS_DATASETID',
    'URL_SIMULATIONS',
    'URL_USERS',
    'URL_USERS_SELF',
    'URL_USERS_SELF_ALPHAS',
]


HTTP_API_WORLDQUANTBRAIN_COM = 'http://api.worldquantbrain.com'
HTTPS_API_WORLDQUANTBRAIN_COM = 'https://api.worldquantbrain.com'

WQB_API_URL = HTTPS_API_WORLDQUANTBRAIN_COM
URL_ALPHAS = WQB_API_URL + '/alphas'
URL_ALPHAS_ALPHAID = URL_ALPHAS + '/{}'
URL_ALPHAS_ALPHAID_CHECK = URL_ALPHAS_ALPHAID + '/check'
# URL_ALPHAS_ALPHAID_SUBMIT = URL_ALPHAS_ALPHAID + '/submit'
URL_ALPHAS_ALPHAID_SUBMIT = 'http://api.worldquantbrain.com:443/alphas/{}/submit'
URL_AUTHENTICATION = WQB_API_URL + '/authentication'
URL_DATACATEGORIES = WQB_API_URL + '/data-categories'
URL_DATAFIELDS = WQB_API_URL + '/data-fields'
URL_DATAFIELDS_FIELDID = URL_DATAFIELDS + '/{}'
URL_DATASETS = WQB_API_URL + '/data-sets'
URL_DATASETS_DATASETID = URL_DATASETS + '/{}'
URL_OPERATORS = WQB_API_URL + '/operators'
URL_SIMULATIONS = WQB_API_URL + '/simulations'
URL_USERS = WQB_API_URL + '/users'
URL_USERS_SELF = URL_USERS + '/self'
URL_USERS_SELF_ALPHAS = URL_USERS_SELF + '/alphas'
