[build-system]
requires = ['setuptools', 'wheel']
build-backend = 'setuptools.build_meta'

[project]
name = 'wqb'
dynamic = ['version']
authors = [{ name = '<PERSON>', email = '<EMAIL>' }]
description = 'A better machine lib.'
license = { file = 'LICENSE' }
readme = 'README.md'
requires-python = '>=3.11'
dependencies = ['requests']
classifiers = [
    'Intended Audience :: Developers',
    'License :: OSI Approved :: MIT License',
    'Operating System :: OS Independent',
    'Programming Language :: Python :: 3',
    'Programming Language :: Python :: 3 :: Only',
    'Programming Language :: Python :: 3.11',
    'Programming Language :: Python :: 3.12',
    'Programming Language :: Python :: 3.13',
]

[project.urls]
repository = 'https://github.com/rocky-d/wqb'

[tool.setuptools.packages.find]
include = ['wqb']

[tool.setuptools.dynamic]
version = { attr = 'wqb.__version__' }
