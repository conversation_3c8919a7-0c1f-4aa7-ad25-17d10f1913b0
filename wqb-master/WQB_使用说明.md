# WQB 库使用说明

## 📖 目录
- [概述](#概述)
- [安装配置](#安装配置)
- [快速开始](#快速开始)
- [核心功能详解](#核心功能详解)
- [高级功能](#高级功能)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🎯 概述

**wqb** 是专为 WorldQuant BRAIN 平台设计的 Python API 库，提供完整的量化研究和 Alpha 策略开发工具。

### 主要特性
- 🔐 **自动认证**：无需手动管理token，防过期机制
- ⚡ **异步并发**：支持大批量操作和并发处理
- 🎯 **完整API**：覆盖数据集、字段、Alpha的全生命周期操作
- 📊 **强大过滤**：灵活的范围过滤和搜索功能
- 📝 **完善日志**：可配置的文件和控制台日志系统

## 🛠️ 安装配置

### 环境要求
- Python >= 3.11
- 网络连接（访问 WorldQuant BRAIN）

### 安装方法

#### 使用pip安装
```bash
python -m pip install wqb
```

#### 创建虚拟环境（推荐）
```bash
# 使用conda
conda create -y -n wqb-py311 python=3.11
conda activate wqb-py311

# 或使用venv
python -m venv wqb_env
source wqb_env/bin/activate  # Linux/Mac
# wqb_env\Scripts\activate  # Windows
```

#### 升级库
```bash
python -m pip install wqb --upgrade --extra-index-url https://pypi.org/simple
```

## 🚀 快速开始

### 1. 基础导入和设置
```python
import wqb
from wqb import WQBSession, FilterRange, print

# 创建日志器（推荐）
logger = wqb.wqb_logger()
print(f"{logger.name = }")

# 创建会话
wqbs = WQBSession(('<your_email>', '<your_password>'), logger=logger)

# 测试连接
resp = wqbs.auth_request()
print(f"连接状态: {resp.status_code}")  # 应该返回 201
print(f"用户ID: {resp.json()['user']['id']}")
```

### 2. 基本使用示例
```python
# 搜索数据集
resp = wqbs.search_datasets_limited('USA', 1, 'TOP3000', search='price')
datasets = resp.json()
print(f"找到 {len(datasets)} 个数据集")

# 查找字段
resp = wqbs.search_fields_limited('USA', 1, 'TOP3000', search='open')
fields = resp.json()
print(f"找到 {len(fields)} 个字段")
```

## 🔧 核心功能详解

### 🔑 认证与会话管理

#### WQBSession 类
```python
from wqb import WQBSession

# 基础创建
wqbs = WQBSession(('<email>', '<password>'))

# 带日志创建
logger = wqb.wqb_logger(name='my_session')
wqbs = WQBSession(('<email>', '<password>'), logger=logger)

# 手动认证测试
resp = wqbs.auth_request()
print(resp.status_code)  # 201 表示成功
```

#### 自动认证机制
```python
# 库会自动处理以下情况：
# 1. Token 过期时自动重新认证
# 2. 网络异常时自动重试
# 3. 认证失败时的错误处理

# 无需手动管理认证状态
resp = wqbs.search_operators()  # 自动处理认证
```

#### 认证相关HTTP方法
```python
# GET认证状态检查
resp = wqbs.get_authentication()
print(f"认证状态: {resp.status_code}")

# POST创建新认证会话
resp = wqbs.post_authentication()
print(f"认证创建: {resp.status_code}")

# DELETE注销认证会话
resp = wqbs.delete_authentication()
print(f"认证注销: {resp.status_code}")

# HEAD检查认证端点
resp = wqbs.head_authentication()
print(f"端点检查: {resp.status_code}")
```

### 🔍 操作符查询

#### 搜索可用操作符
```python
# 获取所有可用的操作符
resp = wqbs.search_operators()
operators_data = resp.json()

# 提取操作符名称列表
operators = [item['name'] for item in operators_data]
print(f"可用操作符: {operators}")

# 按类别分组操作符
operators_by_category = {}
for item in operators_data:
    name = item['name']
    category = item['category']
    if category not in operators_by_category:
        operators_by_category[category] = []
    operators_by_category[category].append(name)

print("操作符分类:")
for category, ops in operators_by_category.items():
    print(f"  {category}: {ops}")
```

### 📊 数据集操作

#### 搜索数据集
```python
# 限制结果的搜索
resp = wqbs.search_datasets_limited(
    region='USA',           # 必需：地区
    delay=1,               # 必需：延迟类型
    universe='TOP3000',    # 必需：股票池
    search='price',        # 可选：搜索关键词
    category='pv',         # 可选：数据类别
    coverage=FilterRange.from_str('[0.8, inf)'),  # 可选：覆盖率过滤
    limit=50,              # 可选：结果限制
    offset=0               # 可选：偏移量
)
datasets = resp.json()

# 获取所有结果的搜索（迭代器）
for idx, resp in enumerate(wqbs.search_datasets('USA', 1, 'TOP3000'), 1):
    data = resp.json()
    print(f"第{idx}页，共{len(data)}条记录")
```

#### 定位特定数据集
```python
# 根据ID获取数据集详情
dataset_id = 'pv1'
resp = wqbs.locate_dataset(dataset_id)
dataset_info = resp.json()
print(f"数据集名称: {dataset_info['name']}")
```

### 🏷️ 字段操作

#### 搜索字段
```python
# 搜索特定字段
resp = wqbs.search_fields_limited(
    region='USA',
    delay=1,
    universe='TOP3000',
    dataset_id='pv1',      # 可选：限定数据集
    search='open',         # 可选：搜索关键词
    type='MATRIX',         # 可选：字段类型
    coverage=FilterRange.from_str('[0.9, inf)'),
    limit=20
)
fields = resp.json()

# 批量获取所有字段
all_fields = []
for resp in wqbs.search_fields('USA', 1, 'TOP3000'):
    all_fields.extend(resp.json())
print(f"总计字段数: {len(all_fields)}")
```

#### 定位特定字段
```python
field_id = 'open'
resp = wqbs.locate_field(field_id)
field_info = resp.json()
print(f"字段描述: {field_info.get('description', 'N/A')}")
```

### 🎯 Alpha 策略管理

#### Alpha 搜索和过滤
```python
from datetime import datetime
from wqb import FilterRange

# 创建时间范围过滤
start_date = datetime.fromisoformat('2025-01-01T00:00:00-05:00')
end_date = datetime.fromisoformat('2025-01-31T23:59:59-05:00')

# 复杂的Alpha过滤
resp = wqbs.filter_alphas_limited(
    status='UNSUBMITTED',              # Alpha状态
    region='USA',                      # 地区
    delay=1,                          # 延迟
    universe='TOP3000',               # 股票池
    sharpe=FilterRange.from_str('[1.5, inf)'),      # 夏普比率 >= 1.5
    fitness=FilterRange.from_str('[1, inf)'),        # 适应度 >= 1
    turnover=FilterRange.from_str('(-inf, 0.7]'),    # 换手率 <= 0.7
    date_created=FilterRange.from_str(f"[{start_date.isoformat()}, {end_date.isoformat()})"),
    order='dateCreated',              # 排序方式
    limit=100
)

alpha_list = resp.json()['results']
alpha_ids = [alpha['id'] for alpha in alpha_list]
print(f"找到 {len(alpha_ids)} 个符合条件的Alpha")
```

#### Alpha 属性管理
```python
from wqb import NULL

alpha_id = 'your_alpha_id'

# 修改Alpha属性
resp = wqbs.patch_properties(
    alpha_id,
    favorite=True,                    # 设为收藏
    hidden=False,                     # 不隐藏
    name='My Strategy v1.0',          # 重命名
    category='FUNDAMENTAL',           # 设置类别
    tags=['momentum', 'value'],       # 添加标签
    color='GREEN',                    # 设置颜色
    regular_description='基于价值因子的策略'  # 描述
)

# 清空某些属性（使用NULL）
resp = wqbs.patch_properties(
    alpha_id,
    tags=NULL,          # 清空标签
    color=NULL          # 清空颜色
)
```

### ⚡ Alpha 仿真系统

#### 单个Alpha仿真
```python
import asyncio

# 定义Alpha策略
alpha = {
    'type': 'REGULAR',
    'settings': {
        'instrumentType': 'EQUITY',
        'region': 'USA',
        'universe': 'TOP3000',
        'delay': 1,
        'decay': 13,
        'neutralization': 'INDUSTRY',
        'truncation': 0.13,
        'pasteurization': 'ON',
        'unitHandling': 'VERIFY',
        'nanHandling': 'OFF',
        'language': 'FASTEXPR',
        'visualization': False
    },
    'regular': 'close/lag(close, 5) - 1'  # 策略表达式
}

# 异步执行仿真
async def run_simulation():
    resp = await wqbs.simulate(
        alpha,
        on_start=lambda vars: print(f"开始仿真: {vars['url']}"),
        on_success=lambda vars: print("仿真成功完成"),
        on_failure=lambda vars: print(f"仿真失败: {vars['resp'].text}")
    )
    return resp

# 运行仿真
resp = asyncio.run(run_simulation())
print(f"仿真结果: {resp.status_code}")
```

#### 批量并发仿真
```python
import asyncio
import wqb

# 准备多个Alpha策略
alphas = [
    {'type': 'REGULAR', 'settings': {...}, 'regular': 'strategy_1'},
    {'type': 'REGULAR', 'settings': {...}, 'regular': 'strategy_2'},
    {'type': 'REGULAR', 'settings': {...}, 'regular': 'strategy_3'},
    # ... 更多策略
]

# 转换为批次处理
multi_alphas = wqb.to_multi_alphas(alphas, 5)  # 每批5个

# 并发仿真
async def batch_simulation():
    concurrency = 3  # 并发数
    results = await wqbs.concurrent_simulate(
        multi_alphas,
        concurrency,
        return_exceptions=True,  # 返回异常而不是抛出
        log='批量仿真',
        log_gap=10  # 每10个记录一次日志
    )
    
    # 处理结果
    success_count = 0
    for result in results:
        if isinstance(result, Exception):
            print(f"仿真异常: {result}")
        else:
            if result.status_code == 200:
                success_count += 1
    
    print(f"成功仿真: {success_count}/{len(results)}")
    return results

results = asyncio.run(batch_simulation())
```

### 🔍 Alpha 检查和提交

#### Alpha 合规性检查
```python
import asyncio

async def check_alpha(alpha_id):
    resp = await wqbs.check(
        alpha_id,
        on_success=lambda vars: print(f"检查通过: {alpha_id}"),
        on_failure=lambda vars: print(f"检查失败: {vars['resp'].text}")
    )
    return resp

# 单个检查
resp = asyncio.run(check_alpha('your_alpha_id'))

# 批量检查
async def batch_check(alpha_ids):
    results = await wqbs.concurrent_check(
        alpha_ids,
        concurrency=2,  # 检查并发数
        return_exceptions=True
    )
    
    passed_alphas = []
    for i, result in enumerate(results):
        if not isinstance(result, Exception) and result.status_code == 200:
            passed_alphas.append(alpha_ids[i])
    
    return passed_alphas

passed = asyncio.run(batch_check(['alpha_1', 'alpha_2', 'alpha_3']))
```

#### Alpha 正式提交
```python
import asyncio

async def submit_alpha(alpha_id):
    resp = await wqbs.submit(
        alpha_id,
        on_success=lambda vars: print(f"提交成功: {alpha_id}"),
        on_failure=lambda vars: print(f"提交失败: {vars['resp'].text}")
    )
    return resp

# 提交单个Alpha
resp = asyncio.run(submit_alpha('your_alpha_id'))
print(f"提交状态: {resp.status_code}")

# ⚠️ 重要提示：submit功能目前仍在开发中，可能无法正常工作
# 建议在生产环境中谨慎使用此功能
```

### 🔄 通用重试机制

#### retry 方法详解
```python
import asyncio

# 自定义重试逻辑
async def custom_retry_example():
    resp = await wqbs.retry(
        method='GET',
        url='https://api.worldquantbrain.com/operators',
        max_tries=range(5),           # 最多重试5次
        max_key_errors=2,             # 最多2次键错误
        max_value_errors=2,           # 最多2次值错误  
        delay_key_error=3.0,          # 键错误后延迟3秒
        delay_value_error=2.0,        # 值错误后延迟2秒
        on_start=lambda vars: print(f"开始请求: {vars['url']}"),
        on_success=lambda vars: print("请求成功"),
        on_failure=lambda vars: print(f"请求失败: {vars['resp'].status_code}"),
        log='自定义重试操作'
    )
    return resp

result = asyncio.run(custom_retry_example())
```

## 🔧 高级功能

### 📈 范围过滤系统

#### FilterRange 详细用法
```python
from wqb import FilterRange

# 1. 字符串解析方式
sharpe_filter = FilterRange.from_str('[1.5, inf)')    # 夏普比率 >= 1.5
turnover_filter = FilterRange.from_str('(0, 0.8]')    # 0 < 换手率 <= 0.8
date_filter = FilterRange.from_str('[2025-01-01, 2025-12-31)')

# 2. 条件解析方式
fitness_filter = FilterRange.from_conditions([
    '>=1.0',      # 适应度 >= 1.0
    '<5.0'        # 适应度 < 5.0
])

# 3. 直接构造
coverage_filter = FilterRange(
    lo=0.8,       # 下界
    hi=1.0,       # 上界
    lo_eq=True,   # 包含下界
    hi_eq=False   # 不包含上界
)

# 转换为查询参数
params = sharpe_filter.to_params('sharpe')
print(params)  # 'sharpe>=1.5'
```

### 🕐 时间范围处理

#### DatetimeRange 用法
```python
from datetime import datetime, timedelta
from wqb import DatetimeRange

# 创建时间序列
start = datetime(2025, 1, 1)
end = datetime(2025, 1, 31)
step = timedelta(days=1)

date_range = DatetimeRange(start, end, step)

# 迭代时间点
for date in date_range:
    print(date.strftime('%Y-%m-%d'))

# 切片操作
first_week = date_range[:7]
print(f"第一周: {len(first_week)} 天")

# 检查包含关系
target_date = datetime(2025, 1, 15)
if target_date in date_range:
    print(f"{target_date} 在范围内")
```

### 📋 批处理工具

#### to_multi_alphas 用法
```python
import wqb

# 大量Alpha策略
alphas = [{'strategy': f'strategy_{i}'} for i in range(100)]

# 转换为批次，每批10个
multi_alphas = list(wqb.to_multi_alphas(alphas, 10))
print(f"总共 {len(multi_alphas)} 个批次")

# 使用可迭代对象定义批次大小
batch_sizes = [5, 10, 15, 20]  # 不同大小的批次
custom_batches = list(wqb.to_multi_alphas(alphas, batch_sizes))
```

### 🔄 并发控制

#### concurrent_await 用法
```python
import asyncio
import wqb

async def sample_task(n):
    await asyncio.sleep(1)
    return f"任务 {n} 完成"

async def run_concurrent():
    # 创建任务列表
    tasks = [sample_task(i) for i in range(10)]
    
    # 并发执行，限制并发数为3
    results = await wqb.concurrent_await(
        tasks,
        concurrency=3,
        return_exceptions=True
    )
    
    return results

results = asyncio.run(run_concurrent())
```

## 💡 最佳实践

### 🔐 认证管理
```python
# 推荐：使用环境变量存储凭据
import os
from wqb import WQBSession

email = os.getenv('WQB_EMAIL')
password = os.getenv('WQB_PASSWORD')

if not email or not password:
    raise ValueError("请设置 WQB_EMAIL 和 WQB_PASSWORD 环境变量")

wqbs = WQBSession((email, password))
```

### 📝 日志配置
```python
import wqb

# 创建带时间戳的日志器
logger = wqb.wqb_logger(name=f'wqb_session_{datetime.now().strftime("%Y%m%d_%H%M%S")}')

# 在操作中启用日志
resp = wqbs.search_datasets_limited(
    'USA', 1, 'TOP3000',
    log='搜索价格相关数据集',  # 启用日志并添加描述
    search='price'
)
```

### ⚡ 性能优化
```python
# 1. 合理设置并发数
concurrency = min(10, len(alpha_list))  # 不超过API限制

# 2. 使用批处理减少请求次数
multi_alphas = wqb.to_multi_alphas(alphas, 20)  # 适当的批次大小

# 3. 启用异常返回，避免单个失败影响整体
results = await wqbs.concurrent_simulate(
    multi_alphas,
    concurrency,
    return_exceptions=True
)
```

### 🛡️ 错误处理
```python
import asyncio
from requests.exceptions import RequestException

async def robust_simulation(alpha):
    try:
        resp = await wqbs.simulate(
            alpha,
            max_tries=range(300),  # 最多重试300次
            on_failure=lambda vars: print(f"仿真失败: {vars['resp'].status_code}")
        )
        
        if resp and resp.status_code == 200:
            return resp.json()
        else:
            print(f"仿真未成功: {resp.status_code if resp else 'No response'}")
            return None
            
    except Exception as e:
        print(f"仿真异常: {e}")
        return None

# 使用示例
result = asyncio.run(robust_simulation(alpha))
```

## 🔧 故障排除

### 常见问题

#### 1. 认证失败
```python
# 检查凭据
resp = wqbs.auth_request()
if resp.status_code != 201:
    print(f"认证失败: {resp.status_code}")
    print(f"响应内容: {resp.text}")

# 可能原因：
# - 用户名密码错误
# - 网络连接问题
# - API服务不可用
```

#### 2. 仿真超时
```python
# 增加重试次数和超时时间
resp = await wqbs.simulate(
    alpha,
    max_tries=range(600),  # 增加到600次重试
    on_failure=lambda vars: print(f"重试中... {vars['resp'].status_code}")
)
```

#### 3. 并发限制
```python
# 降低并发数
concurrency = 2  # 从高并发降低到2

# 添加延迟
import time
time.sleep(1)  # 请求间添加延迟
```

#### 4. 内存使用过多
```python
# 使用生成器而不是列表
def process_alphas():
    for resp in wqbs.filter_alphas('USA', 1, 'TOP3000'):
        yield resp.json()['results']

# 而不是一次性加载所有结果
```

### 调试技巧

#### 启用详细日志
```python
import logging

# 设置更详细的日志级别
logging.basicConfig(level=logging.DEBUG)

# 使用库的日志功能
logger = wqb.wqb_logger()
wqbs = WQBSession(credentials, logger=logger)

# 在每个操作中启用日志
resp = wqbs.search_datasets_limited(
    'USA', 1, 'TOP3000',
    log='调试：搜索数据集'
)
```

#### 检查API响应
```python
# 详细检查响应
resp = wqbs.search_operators()
print(f"状态码: {resp.status_code}")
print(f"响应头: {resp.headers}")
print(f"响应内容: {resp.text[:500]}...")  # 显示前500字符
```

---

## 📋 完整功能清单

### 🔐 认证与会话管理
- ✅ `WQBSession()` - 主要会话类
- ✅ `auth_request()` - 认证请求  
- ✅ `get_authentication()` - GET认证状态
- ✅ `post_authentication()` - POST认证会话
- ✅ `delete_authentication()` - DELETE注销会话
- ✅ `head_authentication()` - HEAD检查认证端点

### 🔍 操作符与基础信息
- ✅ `search_operators()` - 搜索可用操作符

### 📊 数据集操作
- ✅ `locate_dataset(dataset_id)` - 定位特定数据集
- ✅ `search_datasets_limited()` - 限制条件搜索数据集
- ✅ `search_datasets()` - 迭代搜索所有数据集

### 🏷️ 字段操作  
- ✅ `locate_field(field_id)` - 定位特定字段
- ✅ `search_fields_limited()` - 限制条件搜索字段
- ✅ `search_fields()` - 迭代搜索所有字段

### 🎯 Alpha策略管理
- ✅ `locate_alpha(alpha_id)` - 定位特定Alpha
- ✅ `filter_alphas_limited()` - 限制条件过滤Alpha
- ✅ `filter_alphas()` - 迭代过滤所有Alpha
- ✅ `patch_properties()` - 修改Alpha属性

### ⚡ 仿真系统
- ✅ `simulate()` - 单个Alpha仿真
- ✅ `concurrent_simulate()` - 批量并发仿真

### 🔍 检查系统
- ✅ `check()` - 单个Alpha检查
- ✅ `concurrent_check()` - 批量并发检查

### 📤 提交系统
- ⚠️ `submit()` - Alpha提交（开发中）

### 🔧 工具函数
- ✅ `print()` - 立即刷新打印
- ✅ `wqb_logger()` - 预配置日志器
- ✅ `to_multi_alphas()` - Alpha批处理转换
- ✅ `concurrent_await()` - 并发等待工具
- ✅ `retry()` - 通用异步重试机制

### 📈 数据处理
- ✅ `FilterRange` - 范围过滤系统
- ✅ `DatetimeRange` - 时间序列处理

---

## 📚 总结

wqb 库为 WorldQuant BRAIN 平台提供了完整的 Python API 接口，支持从数据探索到策略开发的全流程操作。通过自动认证、并发处理、智能重试等特性，大大简化了量化研究的技术复杂度。

**关键优势**：
- 🔐 零维护的认证管理
- ⚡ 高性能并发处理  
- 🎯 完整的API覆盖（28个核心方法）
- 📊 强大的过滤能力
- 🛡️ 企业级稳定性

适合量化研究员、数据科学家和量化开发者使用。 