name: Publish

on:
  release:
    types: [published]

jobs:
  pypi-publish:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install setuptools wheel build twine
      - name: Build source tar
        run: |
          python -m build
      - name: Publish to PyPI
        env:
          TWINE_REPOSITORY_URL: "https://upload.pypi.org/legacy/"
          TWINE_USERNAME: ${{ secrets.PYPI_USERNAME }}
          TWINE_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: |
          python -m twine upload dist/*
        continue-on-error: true
