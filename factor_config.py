# factor_config.py

# Relevant data fields for constructing the alpha factor
DATA_FIELDS = {
    # For high-frequency returns (r_i,t)
    'returns': ['returns', 'close', 'open', 'high', 'low', 'vwap'],
    
    # For net buy order changes (netBid_i,t) - using proxies
    'order_flow_proxies': ['volume', 'adv20'],
    
    # For normalization (circulating shares)
    'normalization': ['anl14_cursharesoutstanding']
}

# Relevant operators for combining data fields
OPERATORS = {
    'time_series': ['ts_corr', 'ts_delta', 'ts_sum', 'ts_rank', 'ts_mean'],
    'arithmetic': ['divide', 'subtract', 'add', 'multiply'],
    'cross_sectional': ['rank']
}

# Time windows for time-series operations (in days)
TIME_WINDOWS = [1, 3, 5, 10, 20]
