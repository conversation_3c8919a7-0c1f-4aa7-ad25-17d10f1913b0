#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 导入asyncio库，用于运行异步主函数
import asyncio
# 从核心逻辑文件中导入主流水线类
from pipeline_core import OptimizedMultiAlphaPipeline
# 从配置文件中导入默认因子文件名
from pipeline_config_and_data_structures import DEFAULT_FACTORS_FILE

async def main(): # 定义一个异步主函数，作为程序的入口点
    """主程序""" # 函数的文档字符串
    print("🚀 优化MultiAlpha流水线回测系统") # 打印欢迎标题
    print("=" * 80) # 打印分隔线
    print(f"📁 目标文件: {DEFAULT_FACTORS_FILE}") # 显示默认的因子文件名
    print("🔧 配置: 8槽位流水线处理 + 智能错误恢复") # 显示默认配置特性
    print("📊 特性: 断点续传 + 错误Alpha自动记录") # 显示其他特性
    print("=" * 80) # 打印分隔线
    
    # 🔍 自动检测并处理断点续传
    suggested_resume, progress_info = OptimizedMultiAlphaPipeline.detect_resume_point() # 调用静态方法检测续传点
    resume_from = 0 # 初始化续传位置为0（从头开始）
    
    if suggested_resume > 0 and progress_info: # 如果检测到有效的续传点
        print(f"\n🔍 检测到上次运行进度:") # 打印提示信息
        print(f"   📁 文件: {progress_info.get('factors_file', '未知')}") # 显示上次运行的文件名
        print(f"   📊 进度: {progress_info.get('completed_tasks', 0)}/{progress_info.get('total_tasks', 0)} 任务 ({progress_info.get('progress_percentage', 0):.1f}%)") # 显示进度
        print(f"   ✅ 成功: {progress_info.get('extracted_alpha_ids', 0)} Alpha ID") # 显示成功提取的ID数
        print(f"   ❌ 失败: {progress_info.get('failed_tasks', 0)} 任务") # 显示失败任务数
        print(f"   🔄 剩余: {progress_info.get('remaining_tasks', 0)} 任务") # 显示剩余任务数
        print(f"   🛡️ 断点检测: {progress_info.get('current_vs_max', 'N/A')}") # 显示断点检测详情
        print(f"   💡 建议从第 {suggested_resume} 个任务继续 (防止断点回退)") # 给出建议
        
        resume_choice = input(f"\n是否从第{suggested_resume}个任务继续？(Y/n/手动输入): ").strip().lower() # 获取用户输入
        
        if resume_choice in ['', 'y', 'yes']: # 如果用户同意或直接回车
            resume_from = suggested_resume # 设置续传点
            print(f"✅ 将从第{resume_from}个任务继续执行") # 确认操作
        elif resume_choice in ['n', 'no']: # 如果用户选择不续传
            print("✅ 将从头开始执行") # 确认操作
            resume_from = 0 # 续传点设为0
        else: # 如果用户输入了其他内容（可能是手动指定的数字）
            # 尝试将输入转换为整数作为手动续传点
            try: # 使用try-except处理可能的输入错误
                manual_resume = int(resume_choice) # 转换输入为整数
                resume_from = manual_resume # 设置为手动续传点
                print(f"✅ 将从第{resume_from}个任务开始执行") # 确认操作
            except ValueError: # 如果转换失败
                try: # 再次尝试获取用户输入
                    manual_resume = int(input("请输入续传的任务序号: ")) # 提示用户输入数字
                    resume_from = manual_resume # 设置续传点
                    print(f"✅ 将从第{resume_from}个任务开始执行") # 确认操作
                except ValueError: # 如果再次失败
                    print("❌ 输入无效，将从头开始") # 提示错误
                    resume_from = 0 # 从头开始
    else: # 如果没有检测到有效的进度文件
        print(f"\n💡 没有检测到有效的进度文件") # 打印提示
        resume_choice = input("是否需要手动设置断点续传？(y/N): ").strip().lower() # 询问是否手动设置
        if resume_choice == 'y': # 如果用户同意
            try: # 尝试获取输入
                resume_from = int(input("请输入续传的任务序号 (从第几个任务开始): ")) # 获取数字输入
                print(f"✅ 将从第{resume_from}个任务开始执行") # 确认操作
            except ValueError: # 如果输入无效
                print("❌ 输入无效，将从头开始") # 提示错误
                resume_from = 0 # 从头开始
    
    # 获取用户输入来配置MultiAlpha的并发参数
    print("\n🔧 配置MultiAlpha并发参数:") # 打印标题
    
    # 配置MultiAlpha的并发槽位数
    multi_slots_choice = input("Multi槽数 (建议1-4，回车默认4): ").strip() # 获取用户输入
    try: # 尝试转换输入
        max_multi_slots = int(multi_slots_choice) if multi_slots_choice else 4 # 如果有输入则转换，否则用默认值4
        max_multi_slots = max(1, min(max_multi_slots, 8))  # 将值限制在1到8之间
    except ValueError: # 如果输入无效
        max_multi_slots = 4 # 使用默认值
        print(f"⚠️ 输入无效，使用默认值: {max_multi_slots}") # 提示警告
    
    # 配置MultiAlpha请求之间的保护性延迟
    delay_choice = input("MultiAlpha请求延迟秒数 (建议1-5，回车默认2): ").strip() # 获取用户输入
    try: # 尝试转换输入
        multialpha_delay = float(delay_choice) if delay_choice else 2.0 # 如果有输入则转换，否则用默认值2.0
        multialpha_delay = max(0, min(multialpha_delay, 10))  # 将值限制在0到10秒之间
    except ValueError: # 如果输入无效
        multialpha_delay = 2.0 # 使用默认值
        print(f"⚠️ 输入无效，使用默认值: {multialpha_delay}") # 提示警告
    
    print(f"✅ 配置确认: {max_multi_slots}个Multi槽位, {multialpha_delay}秒延迟") # 打印最终确认的配置
    
    # 配置进度轮询的相关参数
    polling_interval_choice = input("🔧 进度轮询间隔秒数 (默认15): ").strip() # 获取用户输入的轮询间隔
    try: # 尝试转换
        polling_interval = float(polling_interval_choice) if polling_interval_choice else 15.0 # 使用输入值或默认值15.0
    except ValueError: # 如果输入无效
        polling_interval = 15.0 # 使用默认值
    polling_concurrency_choice = input("🔧 错误诊断并发子请求数 (默认1): ").strip() # 获取用户输入的并发度
    try: # 尝试转换
        polling_concurrency = int(polling_concurrency_choice) if polling_concurrency_choice else 1 # 使用输入值或默认值1
    except ValueError: # 如果输入无效
        polling_concurrency = 1 # 使用默认值
    print(f"✅ 轮询间隔: {polling_interval}s, 并发度: {polling_concurrency}") # 打印最终确认的配置
    
    # 根据用户配置创建流水线系统实例
    pipeline = OptimizedMultiAlphaPipeline( # 创建实例
        resume_from=resume_from, # 传入续传点
        max_multi_slots=max_multi_slots, # 传入并发槽位数
        multialpha_delay=multialpha_delay, # 传入延迟
        polling_interval=polling_interval, # 传入轮询间隔
        polling_concurrency=polling_concurrency # 传入轮询并发度
    ) # 结束实例创建
    
    # ====== 插入一个唯一的日志标志，用于调试和确认代码执行路径 ======
    pipeline.logger.info("正常工作1") # 记录日志
    # ==============================================================
    
    # 让用户选择运行模式（测试或完整回测）
    print("\n📊 请选择运行模式:") # 打印标题
    print("1. 测试模式 (30个Alpha)") # 选项1
    print("2. 小规模测试 (100个Alpha)") # 选项2
    print("3. 中等规模测试 (500个Alpha)") # 选项3
    print("4. 完整回测 (所有Alpha)") # 选项4
    
    choice = input("\n请输入选择 (1-4): ").strip() # 获取用户选择
    
    if choice == "1": # 如果选择1
        print("\n🧪 开始测试模式...") # 打印提示
        result = await pipeline.run_pipeline(limit=30) # 运行流水线，限制处理30个alpha
    elif choice == "2": # 如果选择2
        print("\n🧪 开始小规模测试...") # 打印提示
        result = await pipeline.run_pipeline(limit=100) # 限制处理100个alpha
    elif choice == "3": # 如果选择3
        print("\n🧪 开始中等规模测试...") # 打印提示
        result = await pipeline.run_pipeline(limit=500) # 限制处理500个alpha
    elif choice == "4": # 如果选择4
        confirm = input("\n⚠️ 完整回测将处理所有Alpha，确认继续？ (y/N): ").strip().lower() # 获取用户确认
        if confirm == 'y': # 如果确认
            print("\n🚀 开始完整回测...") # 打印提示
            result = await pipeline.run_pipeline() # 运行完整流水线
        else: # 如果不确认
            print("❌ 用户取消") # 打印提示
            return # 结束程序
    else: # 如果输入无效
        print("❌ 无效选择") # 打印提示
        return # 结束程序
    
    if result and result.get('status') == 'completed': # 如果流水线成功完成
        print(f"\n🎉 回测完成！") # 打印成功信息
        print(f"📊 成功: {result['extracted_alpha_ids']}/{result['total_alphas']} Alpha") # 打印成功数量
        print(f"📈 成功率: {result['success_rate']:.1f}%") # 打印成功率
        print(f"⚡ 效率: {result['alphas_per_minute']:.1f} Alpha/分钟") # 打印效率
        print(f"⏱️ 耗时: {result['duration_seconds']/60:.2f} 分钟") # 打印耗时
        print(f"📝 错误记录: {result['error_alphas_file']}") # 打印错误记录文件名
        print(f"💾 进度文件: {result['progress_file']}") # 打印进度文件名
    else: # 如果流水线未完成
        print(f"\n❌ 回测未完成") # 打印失败信息


if __name__ == "__main__": # 如果该脚本是作为主程序直接运行
    asyncio.run(main()) # 使用asyncio.run()来启动异步的main函数 