# 简化Alpha表达式 - 基于Excel Y列建议
# 避免逻辑操作符和风险控制操作符，只使用基础算术和时间序列操作符

# ==================== 核心策略 (基于Excel建议) ====================

# 1. 高Beta+低估值组合，博取市场反弹弹性
multiply(beta, reverse(pe_ratio))

# 2. TTM经营现金流为正且环比上升
divide(operating_cash_flow_ttm, ts_mean(operating_cash_flow_ttm, 252))

# 3. 高EPS增长+低市盈率，成长alpha
multiply(divide(subtract(eps, ts_delay(eps, 252)), abs(ts_delay(eps, 252))), reverse(pe_ratio))

# 4. 低市净率+高ROE，破净反转alpha
divide(roe, pb_ratio)

# 5. 高毛利+低估值，盈利质量alpha
multiply(gross_margin, reverse(ps_ratio))

# 6. 季度为正+年度为正，现金流稳健alpha
divide(operating_cash_flow, ts_std_dev(operating_cash_flow, 63))

# 7. ROE持续改善+股价滞后，动量alpha
multiply(ts_delta(roe, 63), reverse(ts_delta(close, 20)))

# 8. 高销售增长+低市销率，成长alpha
divide(divide(subtract(revenue, ts_delay(revenue, 252)), abs(ts_delay(revenue, 252))), ps_ratio)

# 9. 现金流>净利润，盈利质量alpha
divide(operating_cash_flow, max(net_income, 0.01))

# 10. 估值修复alpha - 多因子组合
add(add(reverse(pe_ratio), reverse(pb_ratio)), reverse(ps_ratio))

# ==================== 扩展策略 ====================

# 11. 连续增长动量
multiply(ts_delta(eps, 63), ts_delta(eps, 126))

# 12. 相对表现
subtract(roe, ts_mean(roe, 252))

# 13. 资产重估
divide(book_value, pb_ratio)

# 14. 毛利率改善动量
multiply(ts_delta(gross_margin, 63), reverse(ts_delta(close, 20)))

# 15. 自由现金流效率
divide(operating_cash_flow, max(capex, 0.01))

# 16. 偿债能力
divide(operating_cash_flow, max(current_liabilities, 0.01))

# 17. 财务效率
multiply(roe, reverse(debt_to_equity))

# 18. 需求真实性
divide(ts_delta(revenue, 252), max(ts_delta(inventory, 252), 0.01))

# 19. 盈利质量综合
divide(ts_delta(revenue, 252), max(ts_delta(net_income, 252), 0.01))

# 20. 综合回报
divide(comprehensive_income, pe_ratio)

# ==================== 超简化版本 (最基础) ====================

# A. Beta估值
multiply(beta, reverse(pe_ratio))

# B. 现金流收益率
divide(operating_cash_flow, market_cap)

# C. 盈利增长
ts_delta(eps, 252)

# D. 价值因子
reverse(pb_ratio)

# E. 质量因子
roe

# F. 动量因子
ts_delta(close, 20)

# G. 毛利率
gross_margin

# H. 现金流稳定性
divide(operating_cash_flow, ts_std_dev(operating_cash_flow, 252))

# I. 估值综合
add(reverse(pe_ratio), reverse(pb_ratio))

# J. 成长综合
add(ts_delta(eps, 252), ts_delta(revenue, 252))

# ==================== 组合策略 ====================

# K. 价值+质量组合
multiply(reverse(pe_ratio), roe)

# L. 成长+动量组合
multiply(ts_delta(eps, 252), ts_delta(close, 20))

# M. 现金流+估值组合
multiply(divide(operating_cash_flow, market_cap), reverse(pe_ratio))

# N. 毛利率+估值组合
multiply(gross_margin, reverse(ps_ratio))

# O. ROE+估值组合
multiply(roe, reverse(pb_ratio))

# ==================== 时间序列变体 ====================

# P. 短期EPS动量 (1个月)
ts_delta(eps, 20)

# Q. 中期ROE趋势 (6个月)
ts_delta(roe, 126)

# R. 长期现金流趋势 (1年)
ts_delta(operating_cash_flow, 252)

# S. 收入增长稳定性
divide(ts_delta(revenue, 252), ts_std_dev(revenue, 252))

# T. 盈利增长稳定性
divide(ts_delta(eps, 252), ts_std_dev(eps, 252))

# ==================== 比率策略 ====================

# U. 现金流/收入比
divide(operating_cash_flow, revenue)

# V. 净利润/收入比 (净利率)
divide(net_income, revenue)

# W. 收入/资产比 (资产周转率)
divide(revenue, book_value)

# X. 现金流/负债比
divide(operating_cash_flow, max(current_liabilities, 0.01))

# Y. ROE/市净率比 (PEG类似概念)
divide(roe, pb_ratio)

# Z. 毛利率/市销率比
divide(gross_margin, ps_ratio)

# ==================== 使用说明 ====================
# 1. 直接复制任意表达式到WorldQuant Brain平台
# 2. 根据平台字段名称调整 (如pe_ratio可能是pe, pb_ratio可能是pb等)
# 3. 调整时间窗口参数 (20天=1月, 63天=1季度, 252天=1年)
# 4. 建议从超简化版本开始测试
# 5. 所有表达式避免了逻辑操作符和风险控制操作符

# 常见字段名称变体:
# pe_ratio -> pe
# pb_ratio -> pb  
# ps_ratio -> ps
# operating_cash_flow -> ocf
# operating_cash_flow_ttm -> ocf_ttm
# market_cap -> mktcap
# current_liabilities -> cur_liab
# debt_to_equity -> de_ratio

# 推荐测试顺序:
# 1. 先测试单因子 (D, E, F, G等)
# 2. 再测试简单组合 (A, B, C等)  
# 3. 最后测试复杂策略 (1-20)
