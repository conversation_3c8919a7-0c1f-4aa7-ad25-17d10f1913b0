# generate_alpha_expressions.py

import itertools
from factor_config import DATA_FIELDS, OPERATORS, TIME_WINDOWS

def generate_expressions():
    expressions = []

    # Unpack data fields from config
    returns_fields = DATA_FIELDS['returns']
    order_flow_fields = DATA_FIELDS['order_flow_proxies']
    norm_field = DATA_FIELDS['normalization'][0]  # Assuming single normalization field

    # Unpack operators from config
    ts_operators = OPERATORS['time_series']
    arithmetic_operators = OPERATORS['arithmetic']
    cs_operators = OPERATORS['cross_sectional']

    # Generate expressions based on the formula: corr(r_i,t, netBid_i,t)
    # Proxy for r_i,t: ts_delta or rank of price/volume fields
    # Proxy for netBid_i,t: ts_delta or rank of order flow fields, normalized by circulating shares

    # Loop through all combinations to generate expressions
    for ret_field in returns_fields:
        for flow_field in order_flow_fields:
            for window in TIME_WINDOWS:
                # Constructing the high-frequency return part (r_i,t)
                # Example: rank(ts_delta(close, 1))
                r_it_proxy = f"rank(ts_delta({ret_field}, {window}))"

                # Constructing the net buy order change part (netBid_i,t)
                # Example: ts_sum(volume, 5) / anl14_cursharesoutstanding
                net_bid_proxy = f"divide(ts_sum({flow_field}, {window}), {norm_field})"

                # Combine into the final correlation expression
                # Example: ts_corr(rank(ts_delta(close, 1)), divide(ts_sum(volume, 5), anl14_cursharesoutstanding), 10)
                corr_windows = [20, 40, 60, 80, 100] # Longer windows for correlation
                for corr_window in corr_windows:
                    expression = f"ts_corr({r_it_proxy}, {net_bid_proxy}, {corr_window})"
                    expressions.append(expression)

    return expressions

if __name__ == "__main__":
    generated_expressions = generate_expressions()
    with open('order_driven_correlation_factor.txt', 'w') as f:
        for expr in generated_expressions:
            f.write(expr + '\n')

    print(f"Generated {len(generated_expressions)} alpha expressions.")