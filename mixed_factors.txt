ts_delta(ts_zscore(opt4_call_theta_365d,60),60)-ts_delta(ts_zscore(opt4_call_theta_365d,400),400)
ts_delta(ts_zscore(opt4_91_call_strike_delta50,10),10)-ts_delta(ts_zscore(opt4_91_call_strike_delta50,20),20)
ts_delta(ts_zscore(opt4_91_call_dis_delta20,10),10)-ts_delta(ts_zscore(opt4_91_call_dis_delta20,400),400)
ts_delta(ts_zscore(opt4_91_put_strike_delta80,60),60)-ts_delta(ts_zscore(opt4_91_put_strike_delta80,20),20)
rank(returns)
ts_delta(ts_zscore(opt4_call_vola_152d,10),10)-ts_delta(ts_zscore(opt4_call_vola_152d,400),400)
ts_delta(ts_zscore(opt4_call_gamma_365d,200),200)-ts_delta(ts_zscore(opt4_call_gamma_365d,20),20)
ts_delta(ts_zscore(opt4_91_call_vola_delta60,60),60)-ts_delta(ts_zscore(opt4_91_call_vola_delta60,20),20)
ts_delta(ts_zscore(opt4_547_put_dis_delta80,60),60)-ts_delta(ts_zscore(opt4_547_put_dis_delta80,400),400)
ts_delta(ts_zscore(opt4_60_put_dis_delta75,60),60)-ts_delta(ts_zscore(opt4_60_put_dis_delta75,100),100)
ts_delta(ts_zscore(opt4_547_put_vola_delta75,10),10)-ts_delta(ts_zscore(opt4_547_put_vola_delta75,20),20)
correlation(close, volume, 15)
ts_delta(ts_zscore(opt4_60_call_dis_delta70,10),10)-ts_delta(ts_zscore(opt4_60_call_dis_delta70,100),100)
ts_delta(ts_zscore(opt4_60_put_strike_delta80,60),60)-ts_delta(ts_zscore(opt4_60_put_strike_delta80,20),20)
ts_delta(ts_zscore(opt4_365_put_vola_delta60,10),10)-ts_delta(ts_zscore(opt4_365_put_vola_delta60,20),20)
ts_delta(ts_zscore(opt4_60_put_pre_delta35,10),10)-ts_delta(ts_zscore(opt4_60_put_pre_delta35,20),20)
ts_max(low, 30)
ts_delta(ts_zscore(opt4_152_call_strike_delta65,10),10)-ts_delta(ts_zscore(opt4_152_call_strike_delta65,100),100)
ts_delta(ts_zscore(opt4_182_call_vola_delta20,60),60)-ts_delta(ts_zscore(opt4_182_call_vola_delta20,20),20)
ts_delta(ts_zscore(opt4_182_call_vola_delta40,200),200)-ts_delta(ts_zscore(opt4_182_call_vola_delta40,20),20)
neutralize(close, industry)
ts_delta(ts_zscore(opt4_put_delta_60d,60),60)-ts_delta(ts_zscore(opt4_put_delta_60d,100),100)
ts_delta(ts_zscore(opt4_182_call_dis_delta60,60),60)-ts_delta(ts_zscore(opt4_182_call_dis_delta60,400),400)
ts_delta(ts_zscore(opt4_122_put_vola_delta35,10),10)-ts_delta(ts_zscore(opt4_122_put_vola_delta35,20),20)
ts_delta(ts_zscore(opt4_273_put_pre_delta50,10),10)-ts_delta(ts_zscore(opt4_273_put_pre_delta50,20),20)
ts_delta(ts_zscore(opt4_122_put_strike_delta25,10),10)-ts_delta(ts_zscore(opt4_122_put_strike_delta25,100),100)
ts_delta(high, 5)
ts_delta(ts_zscore(opt4_152_call_strike_delta35,10),10)-ts_delta(ts_zscore(opt4_152_call_strike_delta35,400),400)
ts_delta(ts_zscore(opt4_60_call_vola_delta50,10),10)-ts_delta(ts_zscore(opt4_60_call_vola_delta50,100),100)
ts_delta(ts_zscore(opt4_91_call_pre_delta30,60),60)-ts_delta(ts_zscore(opt4_91_call_pre_delta30,20),20)
ts_delta(ts_zscore(opt4_152_put_pre_delta30,200),200)-ts_delta(ts_zscore(opt4_152_put_pre_delta30,100),100)
ts_delta(ts_zscore(opt4_547_call_pre_delta60,200),200)-ts_delta(ts_zscore(opt4_547_call_pre_delta60,400),400)
ts_delta(ts_zscore(opt4_152_put_pre_delta40,200),200)-ts_delta(ts_zscore(opt4_152_put_pre_delta40,400),400)
ts_delta(ts_zscore(opt4_182_put_vola_delta60,10),10)-ts_delta(ts_zscore(opt4_182_put_vola_delta60,100),100)
ts_delta(ts_zscore(opt4_547_call_dis_delta80,10),10)-ts_delta(ts_zscore(opt4_547_call_dis_delta80,400),400)
ts_delta(ts_zscore(opt4_vola_14d,60),60)-ts_delta(ts_zscore(opt4_vola_14d,100),100)
ts_mean(close, 10)
ts_delta(ts_zscore(opt4_273_put_strike_delta20,10),10)-ts_delta(ts_zscore(opt4_273_put_strike_delta20,100),100)
ts_delta(ts_zscore(opt4_91_put_strike_delta20,10),10)-ts_delta(ts_zscore(opt4_91_put_strike_delta20,400),400)
ts_delta(ts_zscore(opt4_call_pre_152d,60),60)-ts_delta(ts_zscore(opt4_call_pre_152d,400),400)
ts_min(high, 25)
ts_delta(ts_zscore(opt4_call_forwardprice_30d,10),10)-ts_delta(ts_zscore(opt4_call_forwardprice_30d,400),400)
ts_delta(ts_zscore(opt4_182_put_strike_delta70,10),10)-ts_delta(ts_zscore(opt4_182_put_strike_delta70,100),100)
ts_delta(ts_zscore(opt4_91_put_strike_delta55,10),10)-ts_delta(ts_zscore(opt4_91_put_strike_delta55,400),400)
ts_delta(ts_zscore(opt4_call_forwardprice_273d,10),10)-ts_delta(ts_zscore(opt4_call_forwardprice_273d,20),20)
ts_delta(ts_zscore(opt4_91_call_vola_delta30,60),60)-ts_delta(ts_zscore(opt4_91_call_vola_delta30,100),100)
ts_rank(volume, 20)
ts_delta(ts_zscore(opt4_122_call_pre_delta50,10),10)-ts_delta(ts_zscore(opt4_122_call_pre_delta50,100),100)
ts_delta(ts_zscore(opt4_30_call_pre_delta45,60),60)-ts_delta(ts_zscore(opt4_30_call_pre_delta45,100),100)
ts_delta(ts_zscore(opt4_30_call_pre_delta60,60),60)-ts_delta(ts_zscore(opt4_30_call_pre_delta60,400),400)
ts_delta(ts_zscore(opt4_30_put_vola_delta40,200),200)-ts_delta(ts_zscore(opt4_30_put_vola_delta40,20),20)
ts_delta(ts_zscore(opt4_122_call_pre_delta35,200),200)-ts_delta(ts_zscore(opt4_122_call_pre_delta35,400),400)
ts_delta(ts_zscore(opt4_122_call_dis_delta80,200),200)-ts_delta(ts_zscore(opt4_122_call_dis_delta80,400),400)
ts_delta(ts_zscore(opt4_547_put_vola_delta20,10),10)-ts_delta(ts_zscore(opt4_547_put_vola_delta20,20),20)
ts_delta(ts_zscore(opt4_152_call_vola_delta55,10),10)-ts_delta(ts_zscore(opt4_152_call_vola_delta55,100),100)
decay_linear(returns, 10)
ts_std(volume, 20)
ts_delta(ts_zscore(opt4_60_call_vola_delta45,60),60)-ts_delta(ts_zscore(opt4_60_call_vola_delta45,400),400)
ts_delta(ts_zscore(opt4_273_put_strike_delta70,10),10)-ts_delta(ts_zscore(opt4_273_put_strike_delta70,100),100)
ts_delta(ts_zscore(opt4_122_call_dis_delta70,200),200)-ts_delta(ts_zscore(opt4_122_call_dis_delta70,20),20)
