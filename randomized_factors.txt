ts_delta(ts_zscore(opt4_730_call_strike_delta35,10),10)-ts_delta(ts_zscore(opt4_730_call_strike_delta35,100),100)
ts_delta(ts_zscore(opt4_182_put_pre_delta55,200),200)-ts_delta(ts_zscore(opt4_182_put_pre_delta55,400),400)
ts_delta(ts_zscore(opt4_put_gamma_122d,200),200)-ts_delta(ts_zscore(opt4_put_gamma_122d,100),100)
ts_delta(ts_zscore(opt4_365_put_pre_delta55,10),10)-ts_delta(ts_zscore(opt4_365_put_pre_delta55,20),20)
ts_delta(ts_zscore(opt4_122_put_dis_delta40,200),200)-ts_delta(ts_zscore(opt4_122_put_dis_delta40,100),100)
ts_delta(ts_zscore(opt4_122_put_pre_delta30,60),60)-ts_delta(ts_zscore(opt4_122_put_pre_delta30,400),400)
ts_delta(ts_zscore(opt4_91_put_dis_delta65,10),10)-ts_delta(ts_zscore(opt4_91_put_dis_delta65,400),400)
ts_delta(ts_zscore(opt4_30_put_dis_delta65,200),200)-ts_delta(ts_zscore(opt4_30_put_dis_delta65,100),100)
ts_delta(ts_zscore(opt4_547_put_pre_delta35,60),60)-ts_delta(ts_zscore(opt4_547_put_pre_delta35,20),20)
ts_delta(ts_zscore(opt4_put_gamma_60d,10),10)-ts_delta(ts_zscore(opt4_put_gamma_60d,20),20)
ts_delta(ts_zscore(opt4_273_call_pre_delta65,60),60)-ts_delta(ts_zscore(opt4_273_call_pre_delta65,100),100)
ts_delta(ts_zscore(opt4_182_put_pre_delta70,200),200)-ts_delta(ts_zscore(opt4_182_put_pre_delta70,100),100)
ts_delta(ts_zscore(opt4_put_vola_365d,10),10)-ts_delta(ts_zscore(opt4_put_vola_365d,20),20)
ts_delta(ts_zscore(opt4_547_put_dis_delta45,200),200)-ts_delta(ts_zscore(opt4_547_put_dis_delta45,400),400)
ts_delta(ts_zscore(opt4_152_call_pre_delta75,200),200)-ts_delta(ts_zscore(opt4_152_call_pre_delta75,100),100)
ts_delta(ts_zscore(opt4_182_call_vola_delta25,200),200)-ts_delta(ts_zscore(opt4_182_call_vola_delta25,100),100)
ts_delta(ts_zscore(opt4_273_put_pre_delta40,200),200)-ts_delta(ts_zscore(opt4_273_put_pre_delta40,100),100)
ts_delta(ts_zscore(opt4_152_call_strike_delta65,60),60)-ts_delta(ts_zscore(opt4_152_call_strike_delta65,400),400)
ts_delta(ts_zscore(opt4_365_call_vola_delta65,10),10)-ts_delta(ts_zscore(opt4_365_call_vola_delta65,400),400)
ts_delta(ts_zscore(opt4_730_call_strike_delta75,200),200)-ts_delta(ts_zscore(opt4_730_call_strike_delta75,400),400)
ts_delta(ts_zscore(opt4_vola_60d,60),60)-ts_delta(ts_zscore(opt4_vola_60d,20),20)
ts_delta(ts_zscore(opt4_273_put_pre_delta45,200),200)-ts_delta(ts_zscore(opt4_273_put_pre_delta45,100),100)
ts_delta(ts_zscore(opt4_152_call_vola_delta55,10),10)-ts_delta(ts_zscore(opt4_152_call_vola_delta55,400),400)
ts_delta(ts_zscore(opt4_91_call_pre_delta25,200),200)-ts_delta(ts_zscore(opt4_91_call_pre_delta25,20),20)
ts_delta(ts_zscore(opt4_730_put_dis_delta80,10),10)-ts_delta(ts_zscore(opt4_730_put_dis_delta80,100),100)
ts_delta(ts_zscore(opt4_547_put_vola_delta80,10),10)-ts_delta(ts_zscore(opt4_547_put_vola_delta80,100),100)
ts_delta(ts_zscore(opt4_273_put_pre_delta35,60),60)-ts_delta(ts_zscore(opt4_273_put_pre_delta35,400),400)
ts_delta(ts_zscore(opt4_182_put_vola_delta60,200),200)-ts_delta(ts_zscore(opt4_182_put_vola_delta60,100),100)
ts_delta(ts_zscore(opt4_60_put_strike_delta20,200),200)-ts_delta(ts_zscore(opt4_60_put_strike_delta20,400),400)
ts_delta(ts_zscore(opt4_122_call_dis_delta80,200),200)-ts_delta(ts_zscore(opt4_122_call_dis_delta80,20),20)
ts_delta(ts_zscore(opt4_30_call_dis_delta40,200),200)-ts_delta(ts_zscore(opt4_30_call_dis_delta40,100),100)
ts_delta(ts_zscore(opt4_put_gamma_60d,200),200)-ts_delta(ts_zscore(opt4_put_gamma_60d,20),20)
ts_delta(ts_zscore(opt4_vola_122d,200),200)-ts_delta(ts_zscore(opt4_vola_122d,400),400)
ts_delta(ts_zscore(opt4_30_put_strike_delta45,60),60)-ts_delta(ts_zscore(opt4_30_put_strike_delta45,100),100)
ts_delta(ts_zscore(opt4_122_call_pre_delta60,60),60)-ts_delta(ts_zscore(opt4_122_call_pre_delta60,20),20)
ts_delta(ts_zscore(opt4_182_call_dis_delta45,10),10)-ts_delta(ts_zscore(opt4_182_call_dis_delta45,400),400)
ts_delta(ts_zscore(opt4_152_put_vola_delta40,200),200)-ts_delta(ts_zscore(opt4_152_put_vola_delta40,400),400)
ts_delta(ts_zscore(opt4_put_gamma_273d,10),10)-ts_delta(ts_zscore(opt4_put_gamma_273d,20),20)
ts_delta(ts_zscore(opt4_122_put_pre_delta20,200),200)-ts_delta(ts_zscore(opt4_122_put_pre_delta20,20),20)
ts_delta(ts_zscore(opt4_365_call_dis_delta55,200),200)-ts_delta(ts_zscore(opt4_365_call_dis_delta55,400),400)
ts_delta(ts_zscore(opt4_60_put_vola_delta60,60),60)-ts_delta(ts_zscore(opt4_60_put_vola_delta60,100),100)
ts_delta(ts_zscore(opt4_273_call_pre_delta55,200),200)-ts_delta(ts_zscore(opt4_273_call_pre_delta55,20),20)
ts_delta(ts_zscore(opt4_547_put_pre_delta75,200),200)-ts_delta(ts_zscore(opt4_547_put_pre_delta75,20),20)
ts_delta(ts_zscore(opt4_60_call_strike_delta70,200),200)-ts_delta(ts_zscore(opt4_60_call_strike_delta70,400),400)
ts_delta(ts_zscore(opt4_182_call_strike_delta65,200),200)-ts_delta(ts_zscore(opt4_182_call_strike_delta65,400),400)
ts_delta(ts_zscore(opt4_547_put_strike_delta40,10),10)-ts_delta(ts_zscore(opt4_547_put_strike_delta40,100),100)
ts_delta(ts_zscore(opt4_730_put_strike_delta50,200),200)-ts_delta(ts_zscore(opt4_730_put_strike_delta50,100),100)
ts_delta(ts_zscore(opt4_730_call_dis_delta45,200),200)-ts_delta(ts_zscore(opt4_730_call_dis_delta45,100),100)
ts_delta(ts_zscore(opt4_put_pre_122d,10),10)-ts_delta(ts_zscore(opt4_put_pre_122d,20),20)
ts_delta(ts_zscore(opt4_547_call_dis_delta30,10),10)-ts_delta(ts_zscore(opt4_547_call_dis_delta30,400),400)
ts_delta(ts_zscore(opt4_60_put_strike_delta45,60),60)-ts_delta(ts_zscore(opt4_60_put_strike_delta45,20),20)
ts_delta(ts_zscore(opt4_60_put_pre_delta65,60),60)-ts_delta(ts_zscore(opt4_60_put_pre_delta65,20),20)
ts_delta(ts_zscore(opt4_730_put_pre_delta55,60),60)-ts_delta(ts_zscore(opt4_730_put_pre_delta55,400),400)
ts_delta(ts_zscore(opt4_152_put_vola_delta20,10),10)-ts_delta(ts_zscore(opt4_152_put_vola_delta20,400),400)
ts_delta(ts_zscore(opt4_730_call_dis_delta45,60),60)-ts_delta(ts_zscore(opt4_730_call_dis_delta45,20),20)
ts_delta(ts_zscore(opt4_152_put_strike_delta65,200),200)-ts_delta(ts_zscore(opt4_152_put_strike_delta65,400),400)
ts_delta(ts_zscore(opt4_182_put_dis_delta50,10),10)-ts_delta(ts_zscore(opt4_182_put_dis_delta50,20),20)
ts_delta(ts_zscore(opt4_91_call_strike_delta45,10),10)-ts_delta(ts_zscore(opt4_91_call_strike_delta45,100),100)
ts_delta(ts_zscore(opt4_547_put_vola_delta55,60),60)-ts_delta(ts_zscore(opt4_547_put_vola_delta55,400),400)
ts_delta(ts_zscore(opt4_182_call_pre_delta55,60),60)-ts_delta(ts_zscore(opt4_182_call_pre_delta55,400),400)
ts_delta(ts_zscore(opt4_91_call_pre_delta60,10),10)-ts_delta(ts_zscore(opt4_91_call_pre_delta60,20),20)
ts_delta(ts_zscore(opt4_122_call_strike_delta55,10),10)-ts_delta(ts_zscore(opt4_122_call_strike_delta55,400),400)
ts_delta(ts_zscore(opt4_put_delta_152d,10),10)-ts_delta(ts_zscore(opt4_put_delta_152d,20),20)
ts_delta(ts_zscore(opt4_365_put_strike_delta75,60),60)-ts_delta(ts_zscore(opt4_365_put_strike_delta75,100),100)
ts_delta(ts_zscore(opt4_122_put_dis_delta30,200),200)-ts_delta(ts_zscore(opt4_122_put_dis_delta30,100),100)
ts_delta(ts_zscore(opt4_730_put_strike_delta30,60),60)-ts_delta(ts_zscore(opt4_730_put_strike_delta30,400),400)
ts_delta(ts_zscore(opt4_547_call_strike_delta45,200),200)-ts_delta(ts_zscore(opt4_547_call_strike_delta45,400),400)
ts_delta(ts_zscore(opt4_put_delta_365d,10),10)-ts_delta(ts_zscore(opt4_put_delta_365d,100),100)
ts_delta(ts_zscore(opt4_547_put_strike_delta55,60),60)-ts_delta(ts_zscore(opt4_547_put_strike_delta55,20),20)
ts_delta(ts_zscore(opt4_91_put_vola_delta60,200),200)-ts_delta(ts_zscore(opt4_91_put_vola_delta60,100),100)
ts_delta(ts_zscore(opt4_30_put_dis_delta55,200),200)-ts_delta(ts_zscore(opt4_30_put_dis_delta55,400),400)
ts_delta(ts_zscore(opt4_365_put_strike_delta80,200),200)-ts_delta(ts_zscore(opt4_365_put_strike_delta80,400),400)
ts_delta(ts_zscore(opt4_put_pre_30d,200),200)-ts_delta(ts_zscore(opt4_put_pre_30d,20),20)
ts_delta(ts_zscore(opt4_273_call_vola_delta55,60),60)-ts_delta(ts_zscore(opt4_273_call_vola_delta55,400),400)
ts_delta(ts_zscore(opt4_152_call_vola_delta50,200),200)-ts_delta(ts_zscore(opt4_152_call_vola_delta50,400),400)
ts_delta(ts_zscore(opt4_152_put_dis_delta20,200),200)-ts_delta(ts_zscore(opt4_152_put_dis_delta20,100),100)
ts_delta(ts_zscore(opt4_273_put_strike_delta40,60),60)-ts_delta(ts_zscore(opt4_273_put_strike_delta40,20),20)
ts_delta(ts_zscore(opt4_273_put_strike_delta35,10),10)-ts_delta(ts_zscore(opt4_273_put_strike_delta35,20),20)
ts_delta(ts_zscore(opt4_182_put_dis_delta50,200),200)-ts_delta(ts_zscore(opt4_182_put_dis_delta50,20),20)
ts_delta(ts_zscore(opt4_365_call_strike_delta35,60),60)-ts_delta(ts_zscore(opt4_365_call_strike_delta35,400),400)
ts_delta(ts_zscore(opt4_182_call_dis_delta70,60),60)-ts_delta(ts_zscore(opt4_182_call_dis_delta70,100),100)
ts_delta(ts_zscore(opt4_152_call_strike_delta25,60),60)-ts_delta(ts_zscore(opt4_152_call_strike_delta25,100),100)
ts_delta(ts_zscore(opt4_365_put_vola_delta55,60),60)-ts_delta(ts_zscore(opt4_365_put_vola_delta55,20),20)
ts_delta(ts_zscore(opt4_273_put_vola_delta60,10),10)-ts_delta(ts_zscore(opt4_273_put_vola_delta60,400),400)
ts_delta(ts_zscore(opt4_put_gamma_91d,200),200)-ts_delta(ts_zscore(opt4_put_gamma_91d,400),400)
ts_delta(ts_zscore(opt4_730_put_vola_delta55,60),60)-ts_delta(ts_zscore(opt4_730_put_vola_delta55,100),100)
ts_delta(ts_zscore(opt4_152_call_vola_delta45,10),10)-ts_delta(ts_zscore(opt4_152_call_vola_delta45,100),100)
ts_delta(ts_zscore(opt4_365_call_vola_delta45,10),10)-ts_delta(ts_zscore(opt4_365_call_vola_delta45,400),400)
ts_delta(ts_zscore(opt4_273_call_dis_delta60,60),60)-ts_delta(ts_zscore(opt4_273_call_dis_delta60,100),100)
ts_delta(ts_zscore(opt4_730_call_strike_delta30,200),200)-ts_delta(ts_zscore(opt4_730_call_strike_delta30,20),20)
ts_delta(ts_zscore(opt4_122_put_strike_delta55,10),10)-ts_delta(ts_zscore(opt4_122_put_strike_delta55,400),400)
ts_delta(ts_zscore(opt4_730_put_strike_delta75,200),200)-ts_delta(ts_zscore(opt4_730_put_strike_delta75,100),100)
ts_delta(ts_zscore(opt4_273_put_vola_delta70,10),10)-ts_delta(ts_zscore(opt4_273_put_vola_delta70,100),100)
ts_delta(ts_zscore(opt4_182_put_vola_delta60,200),200)-ts_delta(ts_zscore(opt4_182_put_vola_delta60,20),20)
ts_delta(ts_zscore(opt4_152_call_vola_delta30,60),60)-ts_delta(ts_zscore(opt4_152_call_vola_delta30,400),400)
ts_delta(ts_zscore(opt4_273_call_pre_delta40,60),60)-ts_delta(ts_zscore(opt4_273_call_pre_delta40,20),20)
ts_delta(ts_zscore(opt4_put_vega_30d,10),10)-ts_delta(ts_zscore(opt4_put_vega_30d,400),400)
ts_delta(ts_zscore(opt4_30_call_pre_delta30,60),60)-ts_delta(ts_zscore(opt4_30_call_pre_delta30,400),400)
ts_delta(ts_zscore(opt4_273_call_pre_delta70,60),60)-ts_delta(ts_zscore(opt4_273_call_pre_delta70,20),20)
ts_delta(ts_zscore(opt4_547_put_vola_delta70,60),60)-ts_delta(ts_zscore(opt4_547_put_vola_delta70,100),100)
