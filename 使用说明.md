# 因子随机化工具使用说明

## 🚀 快速开始

只需要修改 `factor_randomizer_simple.py` 文件顶部的配置参数，然后运行：

```bash
python factor_randomizer_simple.py
```

## 📝 配置参数说明

### 1. 基本设置

```python
# 运行模式: 'single', 'mix', 'distribution'
MODE = 'single'

# 随机种子（设置为None表示每次随机，设置数字确保结果可重现）
RANDOM_SEED = 42

# 是否显示详细日志
VERBOSE = True
```

### 2. 单文件随机化模式 (MODE = 'single')

```python
SINGLE_FILE_CONFIG = {
    'input_file': 'decoded_expressions (12).txt',  # 输入文件路径
    'output_file': 'randomized_factors.txt',       # 输出文件路径
    'num_factors': 100,                            # 要选择的因子数量，None表示全部
}
```

**使用示例：**
- 随机选择100个因子：`'num_factors': 100`
- 随机化全部因子：`'num_factors': None`
- 选择500个因子：`'num_factors': 500`

### 3. 多文件混合模式 (MODE = 'mix')

```python
MIX_FILES_CONFIG = {
    'output_file': 'mixed_factors.txt',            # 输出文件路径
    'total_factors': 200,                          # 最终输出总数，None表示全部
    'files': [                                     # 文件列表配置
        {'file': 'decoded_expressions (12).txt', 'count': 150},
        {'file': 'another_factors.txt', 'count': 100},
        # 可以继续添加更多文件...
    ]
}
```

**使用示例：**
```python
'files': [
    {'file': 'momentum_factors.txt', 'count': 200},      # 从动量因子文件选200个
    {'file': 'mean_reversion_factors.txt', 'count': 150}, # 从均值回归因子文件选150个
    {'file': 'volatility_factors.txt', 'count': 100},    # 从波动率因子文件选100个
]
```

### 4. 按比例分布模式 (MODE = 'distribution')

```python
DISTRIBUTION_CONFIG = {
    'output_file': 'distributed_factors.txt',      # 输出文件路径
    'files': [                                     # 文件比例配置
        {'file': 'decoded_expressions (12).txt', 'ratio': 0.7},  # 70%
        {'file': 'another_factors.txt', 'ratio': 0.3},           # 30%
        # 可以继续添加更多文件...
    ]
}
```

**使用示例：**
```python
'files': [
    {'file': 'high_freq_factors.txt', 'ratio': 0.5},    # 50%高频因子
    {'file': 'low_freq_factors.txt', 'ratio': 0.3},     # 30%低频因子
    {'file': 'mixed_freq_factors.txt', 'ratio': 0.2},   # 20%混合频率因子
]
```

## 🎯 常用配置示例

### 示例1：从大文件中随机选择500个因子
```python
MODE = 'single'
SINGLE_FILE_CONFIG = {
    'input_file': 'decoded_expressions (12).txt',
    'output_file': 'sample_500_factors.txt',
    'num_factors': 500,
}
RANDOM_SEED = 123  # 固定种子确保结果可重现
```

### 示例2：混合多个策略文件
```python
MODE = 'mix'
MIX_FILES_CONFIG = {
    'output_file': 'mixed_strategy_factors.txt',
    'total_factors': 300,  # 最终只要300个
    'files': [
        {'file': 'momentum_factors.txt', 'count': 200},
        {'file': 'mean_reversion_factors.txt', 'count': 150},
        {'file': 'volatility_factors.txt', 'count': 100},
    ]
}
RANDOM_SEED = None  # 每次运行结果不同
```

### 示例3：按策略比例分配
```python
MODE = 'distribution'
DISTRIBUTION_CONFIG = {
    'output_file': 'balanced_factors.txt',
    'files': [
        {'file': 'trend_factors.txt', 'ratio': 0.4},      # 40%趋势因子
        {'file': 'reversal_factors.txt', 'ratio': 0.3},   # 30%反转因子
        {'file': 'volatility_factors.txt', 'ratio': 0.2}, # 20%波动率因子
        {'file': 'volume_factors.txt', 'ratio': 0.1},     # 10%成交量因子
    ]
}
```

### 示例4：A/B测试准备
```python
# 为A组准备因子
MODE = 'single'
SINGLE_FILE_CONFIG = {
    'input_file': 'decoded_expressions (12).txt',
    'output_file': 'test_group_A.txt',
    'num_factors': 200,
}
RANDOM_SEED = 100  # A组使用种子100

# 运行一次后，修改配置为B组：
# SINGLE_FILE_CONFIG['output_file'] = 'test_group_B.txt'
# RANDOM_SEED = 200  # B组使用种子200
```

## 🔧 高级技巧

### 1. 确保结果可重现
设置固定的随机种子：
```python
RANDOM_SEED = 42  # 使用任何固定数字
```

### 2. 每次获得不同结果
```python
RANDOM_SEED = None  # 或者使用当前时间戳
```

### 3. 关闭详细日志
```python
VERBOSE = False
```

### 4. 处理大文件时的内存优化
对于非常大的文件，建议分批处理：
```python
# 先选择一个较小的子集
SINGLE_FILE_CONFIG = {
    'input_file': 'very_large_factors.txt',
    'output_file': 'subset_factors.txt',
    'num_factors': 5000,  # 先选5000个
}
```

## ⚠️ 注意事项

1. **文件路径**：确保输入文件存在且路径正确
2. **文件格式**：支持每行一个因子表达式的txt文件
3. **编码格式**：文件应使用UTF-8编码
4. **输出目录**：确保有写入权限
5. **内存使用**：处理大文件时注意内存使用情况

## 🐛 常见问题

### Q: 文件不存在错误
A: 检查文件路径是否正确，确保文件在当前目录或使用绝对路径

### Q: 编码错误
A: 确保文件使用UTF-8编码保存

### Q: 内存不足
A: 减少 `num_factors` 或 `total_factors` 的数量

### Q: 结果每次都不同
A: 设置固定的 `RANDOM_SEED` 值

---

**使用流程：**
1. 修改配置参数
2. 运行 `python factor_randomizer_simple.py`
3. 检查输出文件
4. 根据需要调整参数重新运行
