#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Alpha稳定性检验工具
基于"一键检验稳定性"notebook实现
支持一键执行，测试不同参数下的alpha稳定性
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import requests
import json
import time
import logging
from typing import List, Dict, Tuple, Optional
from itertools import product
import warnings
warnings.filterwarnings('ignore')

# 导入机器学习库中的函数
from machine_lib_concurrent import *

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 配置参数
DEFAULT_ALPHA_ID = 'r0RWaXa'  # 默认测试的alpha ID
NEUTRALIZATION_METHODS = ['SUBINDUSTRY', 'INDUSTRY', 'SECTOR', 'MARKET', 'CROWDING']

def get_decay_variations(original_decay: int) -> List[int]:
    """
    根据原始decay值生成测试用的decay变化范围
    如果decay >= 5，使用[decay-5, decay+5]
    否则使用[decay+10, decay+20]
    """
    if original_decay >= 5:
        return [original_decay - 5, original_decay + 5]
    else:
        return [original_decay + 10, original_decay + 20]

def get_pnl(session, alpha_id: str) -> Optional[pd.DataFrame]:
    """
    获取alpha的PNL数据
    """
    try:
        while True:
            pnl = session.get(f'https://api.worldquantbrain.com/alphas/{alpha_id}/recordsets/pnl')
            if pnl.headers.get('Retry-After', 0) == 0:
                break
            logger.info(f'Sleeping for {pnl.headers["Retry-After"]} seconds')
            time.sleep(float(pnl.headers['Retry-After']))
        
        json_data = pnl.json()['records']
        df = pd.DataFrame(json_data)
        
        if len(df.columns) >= 2:
            df = df.iloc[:, 0:2]
            df.columns = ['date', alpha_id]
            df.set_index('date', inplace=True)
            df.index = pd.to_datetime(df.index)
            return df
        else:
            logger.warning(f"PNL data for {alpha_id} has insufficient columns")
            return None
            
    except Exception as e:
        logger.error(f"Error getting PNL for {alpha_id}: {str(e)}")
        return None

def wait_for_simulation_completion(session, progress_urls: List[str], max_wait_time: int = 3600) -> List[str]:
    """
    等待模拟完成，智能处理retry-after和超时丢弃
    返回完成的alpha IDs
    """
    completed_alpha_ids = []
    start_time = time.time()
    # 为每个任务维护状态
    running_tasks = [
        {
            'progress_url': url,
            'next_check': time.time(),
            'start_time': time.time()
        } for url in progress_urls
    ]
    
    while running_tasks and (time.time() - start_time) < max_wait_time:
        current_time = time.time()
        for task in running_tasks.copy():
            if task['next_check'] > current_time:
                continue
            progress_url = task['progress_url']
            task_start = task['start_time']
            try:
                response = session.get(progress_url, timeout=120)
                retry_after = response.headers.get('Retry-After', 0)
                if retry_after and float(retry_after) > 0:
                    wait_time = min(max(20, float(retry_after)), 300)
                    task['next_check'] = current_time + wait_time
                    consume_time = time.time() - task_start
                    if consume_time > 300:
                        logger.info(f"🕒 任务{progress_url}已耗时{int(consume_time)}s,本次等待{wait_time}s")
                    if consume_time > 2000:
                        logger.warning(f"⚠️ 任务{progress_url}超时丢弃{int(consume_time)}s")
                        running_tasks.remove(task)
                    continue
                status = response.json().get('status', 'UNKNOWN')
                if status == 'COMPLETE':
                    alpha_id = response.json().get('alpha')
                    if alpha_id:
                        completed_alpha_ids.append(alpha_id)
                        logger.info(f"✅ 任务{progress_url}完成, 耗时{int(time.time() - task_start)}s")
                    else:
                        logger.warning(f"任务{progress_url}完成但未返回alphaId")
                    running_tasks.remove(task)
                elif status == 'ERROR':
                    logger.error(f"❌ 任务{progress_url}失败: {response.text}")
                    running_tasks.remove(task)
                elif status in ['PENDING', 'RUNNING']:
                    task['next_check'] = current_time + 20
                else:
                    logger.warning(f"任务{progress_url}状态异常: {status}")
                    task['next_check'] = current_time + 20
            except Exception as e:
                logger.error(f"任务{progress_url}监控异常: {str(e)}")
                task['next_check'] = current_time + 20
        if running_tasks:
            logger.info(f"等待{len(running_tasks)}个模拟完成...")
            time.sleep(5)
    if running_tasks:
        logger.warning(f"超时等待{len(running_tasks)}个模拟未完成")
    return completed_alpha_ids

def create_robustness_test_configs(original_alpha_info: Dict, alpha_expression: str) -> List[Dict]:
    """
    创建稳定性测试配置
    """
    configs = []
    original_decay = original_alpha_info.get('decay', 5)
    original_neutralization = original_alpha_info.get('neutralization', 'SUBINDUSTRY')
    
    # 获取decay变化范围
    decay_variations = get_decay_variations(original_decay)
    
    # 生成所有参数组合
    for decay in decay_variations:
        for neutralization in NEUTRALIZATION_METHODS:
            config = {
                'type': 'REGULAR',
                'settings': {
                    'instrument_type': original_alpha_info.get('instrumentType', 'EQUITY'),
                    'region': original_alpha_info.get('region', 'USA'),
                    'universe': original_alpha_info.get('universe', 'TOP3000'),
                    'delay': original_alpha_info.get('delay', 1),
                    'decay': decay,
                    'neutralization': neutralization,
                    'truncation': original_alpha_info.get('truncation', 0.01),
                    'pasteurization': 'ON',
                    'unit_handling': 'VERIFY',
                    'nan_handling': 'OFF',
                    'language': 'FASTEXPR',
                    'visualization': False  # 添加必需的visualization字段
                },
                'regular': alpha_expression  # 直接使用字符串而不是字典
            }
            configs.append(config)
    
    return configs

def submit_robustness_simulations(session, configs: List[Dict]) -> List[str]:
    """
    提交稳定性测试模拟
    """
    alpha_ids = []
    
    for i, config in enumerate(configs):
        try:
            logger.info(f"Submitting simulation {i+1}/{len(configs)}")
            
            # 使用正确的simulations端点
            response = session.post(
                'https://api.worldquantbrain.com/simulations',
                json=config,
                headers={'Content-Type': 'application/json'},
                timeout=(30, 120)
            )
            
            if response.status_code in [201, 202]:  # 201或202都是成功状态
                # 获取进度URL
                location = response.headers.get('Location')
                if location:
                    # 从location中提取simulation ID或使用location作为进度跟踪
                    logger.info(f"Submitted simulation {i+1}: {location}")
                    alpha_ids.append(location)  # 暂时保存location用于跟踪
                else:
                    logger.warning(f"Simulation {i+1} submitted but no location header")
            elif response.status_code == 429:
                # 模拟限制错误
                error_detail = response.json().get('detail', 'Unknown error')
                logger.warning(f"Simulation limit reached for {i+1}: {error_detail}")
                break  # 停止提交更多模拟
            else:
                logger.error(f"Failed to submit simulation {i+1}: HTTP {response.status_code}")
                if response.text:
                    logger.error(f"Response: {response.text[:200]}")
                
        except Exception as e:
            logger.error(f"Error submitting simulation {i+1}: {str(e)}")
        
        # 添加延迟避免请求过快
        time.sleep(2)
    
    return alpha_ids

def analyze_robustness_results(pnl_data: pd.DataFrame, configs: List[Dict]) -> Dict:
    """
    分析稳定性测试结果
    """
    results = {
        'performance_summary': {},
        'decay_analysis': {},
        'neutralization_analysis': {},
        'correlation_matrix': None,
        'statistics': {}
    }
    
    if pnl_data.empty:
        logger.warning("No PNL data available for analysis")
        return results
    
    # 计算基本统计指标
    returns = pnl_data.pct_change().fillna(0)
    
    # 性能汇总
    for col in pnl_data.columns:
        col_returns = returns[col]
        results['performance_summary'][col] = {
            'total_return': (pnl_data[col].iloc[-1] / pnl_data[col].iloc[0] - 1) * 100,
            'annualized_return': col_returns.mean() * 252 * 100,
            'volatility': col_returns.std() * np.sqrt(252) * 100,
            'sharpe_ratio': (col_returns.mean() / col_returns.std()) * np.sqrt(252) if col_returns.std() > 0 else 0,
            'max_drawdown': calculate_max_drawdown(pnl_data[col])
        }
    
    # 按decay分组分析
    decay_groups = {}
    neutralization_groups = {}
    
    for i, config in enumerate(configs):
        if i < len(pnl_data.columns):
            col_name = pnl_data.columns[i]
            decay = config['settings']['decay']
            neutralization = config['settings']['neutralization']
            
            if decay not in decay_groups:
                decay_groups[decay] = []
            decay_groups[decay].append(col_name)
            
            if neutralization not in neutralization_groups:
                neutralization_groups[neutralization] = []
            neutralization_groups[neutralization].append(col_name)
    
    # 计算衰减分析
    for decay, cols in decay_groups.items():
        if cols:
            group_returns = returns[cols].mean(axis=1)
            results['decay_analysis'][decay] = {
                'avg_return': group_returns.mean() * 252 * 100,
                'volatility': group_returns.std() * np.sqrt(252) * 100,
                'sharpe_ratio': (group_returns.mean() / group_returns.std()) * np.sqrt(252) if group_returns.std() > 0 else 0
            }
    
    # 计算中性化分析
    for neutralization, cols in neutralization_groups.items():
        if cols:
            group_returns = returns[cols].mean(axis=1)
            results['neutralization_analysis'][neutralization] = {
                'avg_return': group_returns.mean() * 252 * 100,
                'volatility': group_returns.std() * np.sqrt(252) * 100,
                'sharpe_ratio': (group_returns.mean() / group_returns.std()) * np.sqrt(252) if group_returns.std() > 0 else 0
            }
    
    # 计算相关性矩阵
    if len(pnl_data.columns) > 1:
        results['correlation_matrix'] = returns.corr()
    
    # 整体统计
    all_returns = returns.mean(axis=1)
    results['statistics'] = {
        'avg_correlation': results['correlation_matrix'].mean().mean() if results['correlation_matrix'] is not None else 0,
        'return_std': returns.std(axis=1).mean(),
        'consistency_score': calculate_consistency_score(returns)
    }
    
    return results

def calculate_max_drawdown(series: pd.Series) -> float:
    """
    计算最大回撤
    """
    cumulative = (1 + series.pct_change().fillna(0)).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdown = (cumulative / rolling_max - 1) * 100
    return drawdown.min()

def calculate_consistency_score(returns: pd.DataFrame) -> float:
    """
    计算一致性得分（基于所有变体的收益率标准差）
    """
    if returns.empty or len(returns.columns) <= 1:
        return 0.0
    
    daily_std = returns.std(axis=1).mean()
    return max(0, 1 - daily_std * 10)  # 简单的一致性评分

def create_visualization(pnl_data: pd.DataFrame, analysis_results: Dict, output_dir: str = "robustness_results"):
    """
    创建可视化图表
    """
    import os
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 1. PNL曲线图
    plt.figure(figsize=(15, 10))
    
    # 归一化PNL数据
    normalized_pnl = pnl_data.div(pnl_data.iloc[0]) * 100
    
    plt.subplot(2, 2, 1)
    for col in normalized_pnl.columns:
        plt.plot(normalized_pnl.index, normalized_pnl[col], label=col, alpha=0.7)
    plt.title('PNL曲线对比（归一化至100）')
    plt.xlabel('日期')
    plt.ylabel('归一化PNL')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    
    # 2. 收益率分布
    plt.subplot(2, 2, 2)
    returns = pnl_data.pct_change().fillna(0)
    returns.plot(kind='box')
    plt.title('收益率分布')
    plt.ylabel('日收益率')
    plt.xticks(rotation=45)
    
    # 3. 相关性热力图
    if analysis_results['correlation_matrix'] is not None:
        plt.subplot(2, 2, 3)
        sns.heatmap(analysis_results['correlation_matrix'], 
                   annot=True, cmap='coolwarm', center=0,
                   square=True, fmt='.2f')
        plt.title('收益率相关性矩阵')
    
    # 4. 性能对比
    plt.subplot(2, 2, 4)
    performance_df = pd.DataFrame(analysis_results['performance_summary']).T
    performance_df['sharpe_ratio'].plot(kind='bar')
    plt.title('Sharpe比率对比')
    plt.ylabel('Sharpe比率')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/robustness_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def save_results(analysis_results: Dict, pnl_data: pd.DataFrame, output_dir: str = "robustness_results"):
    """
    保存分析结果
    """
    import os
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存PNL数据
    pnl_data.to_csv(f'{output_dir}/pnl_data.csv')
    
    # 保存分析结果
    with open(f'{output_dir}/analysis_results.json', 'w', encoding='utf-8') as f:
        # 转换numpy类型为Python原生类型
        results_json = json.loads(json.dumps(analysis_results, default=str))
        json.dump(results_json, f, indent=2, ensure_ascii=False)
    
    # 保存相关性矩阵
    if analysis_results['correlation_matrix'] is not None:
        analysis_results['correlation_matrix'].to_csv(f'{output_dir}/correlation_matrix.csv')
    
    logger.info(f"Results saved to {output_dir}/")

def print_summary(analysis_results: Dict):
    """
    打印分析结果摘要
    """
    print("\n" + "="*60)
    print("                Alpha稳定性检验结果摘要")
    print("="*60)
    
    # 性能统计
    if analysis_results['performance_summary']:
        print("\n📊 性能统计:")
        performance_df = pd.DataFrame(analysis_results['performance_summary']).T
        print(performance_df.round(4).to_string())
    
    # Decay分析
    if analysis_results['decay_analysis']:
        print("\n🔄 Decay参数分析:")
        decay_df = pd.DataFrame(analysis_results['decay_analysis']).T
        print(decay_df.round(4).to_string())
    
    # 中性化分析
    if analysis_results['neutralization_analysis']:
        print("\n⚖️ 中性化方法分析:")
        neut_df = pd.DataFrame(analysis_results['neutralization_analysis']).T
        print(neut_df.round(4).to_string())
    
    # 整体统计
    stats = analysis_results['statistics']
    print(f"\n📈 整体统计:")
    print(f"   平均相关性: {stats.get('avg_correlation', 0):.4f}")
    print(f"   收益率标准差: {stats.get('return_std', 0):.4f}")
    print(f"   一致性得分: {stats.get('consistency_score', 0):.4f}")
    
    print("\n" + "="*60)

def robust_locate_alpha(session, alpha_id: str) -> Dict:
    """
    健壮的alpha信息获取函数
    返回字典格式的alpha信息
    """
    try:
        while True:
            response = session.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}")
            if "retry-after" in response.headers:
                time.sleep(float(response.headers["Retry-After"]))
            else:
                break
        
        if response.status_code != 200:
            logger.error(f"HTTP {response.status_code} when fetching alpha {alpha_id}")
            return {}
        
        alpha_data = response.json()
        
        # 提取基本信息
        result = {
            'alpha_id': alpha_id,
            'expression': alpha_data.get('regular', {}).get('code', ''),
            'sharpe': alpha_data.get('is', {}).get('sharpe', 0.0) if alpha_data.get('is') else 0.0,
            'fitness': alpha_data.get('is', {}).get('fitness', 0.0) if alpha_data.get('is') else 0.0,
            'turnover': alpha_data.get('is', {}).get('turnover', 0.0) if alpha_data.get('is') else 0.0,
            'margin': alpha_data.get('is', {}).get('margin', 0.0) if alpha_data.get('is') else 0.0,
            'decay': alpha_data.get('settings', {}).get('decay', 5),
            'region': alpha_data.get('settings', {}).get('region', 'USA'),
            'universe': alpha_data.get('settings', {}).get('universe', 'TOP3000'),
            'neutralization': alpha_data.get('settings', {}).get('neutralization', 'SUBINDUSTRY'),
            'truncation': alpha_data.get('settings', {}).get('truncation', 0.01),
            'instrument_type': alpha_data.get('settings', {}).get('instrument_type', 'EQUITY'),
            'delay': alpha_data.get('settings', {}).get('delay', 1),
            'date_created': alpha_data.get('dateCreated', ''),
            'raw_data': alpha_data  # 保存原始数据以备调试
        }
        
        return result
        
    except Exception as e:
        logger.error(f"Error fetching alpha {alpha_id}: {str(e)}")
        return {}

def check_alpha_robustness(alpha_id: str = DEFAULT_ALPHA_ID, 
                          wait_for_completion: bool = True,
                          create_plots: bool = True) -> Dict:
    """
    一键检验Alpha稳定性的主函数
    
    参数:
        alpha_id: 要测试的alpha ID
        use_hk_login: 是否使用香港登录
        wait_for_completion: 是否等待模拟完成
        create_plots: 是否创建可视化图表
    
    返回:
        Dict: 包含分析结果的字典
    """
    
    logger.info(f"开始检验Alpha {alpha_id} 的稳定性...")
    
    # 1. 登录
    try:
        session = login()
        logger.info("登录成功")
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        return {}
    
    # 2. 获取原始alpha信息
    try:
        alpha_info = robust_locate_alpha(session, alpha_id)
        if not alpha_info or not alpha_info.get('expression'):
            logger.error(f"无法获取alpha {alpha_id} 的信息或表达式为空")
            return {}
        
        alpha_expression = alpha_info['expression']
        original_alpha_info = {
            'decay': alpha_info.get('decay', 5),
            'instrumentType': alpha_info.get('instrument_type', 'EQUITY'),
            'region': alpha_info.get('region', 'USA'), 
            'universe': alpha_info.get('universe', 'TOP3000'),
            'delay': alpha_info.get('delay', 1),
            'neutralization': alpha_info.get('neutralization', 'SUBINDUSTRY'),
            'truncation': alpha_info.get('truncation', 0.01)
        }
        
        logger.info(f"原始Alpha信息获取成功")
        logger.info(f"Alpha表达式: {alpha_expression[:100]}...")  # 只显示前100个字符
        logger.info(f"原始Decay: {original_alpha_info['decay']}")
        logger.info(f"原始区域: {original_alpha_info['region']}")
        logger.info(f"原始Universe: {original_alpha_info['universe']}")
        
    except Exception as e:
        logger.error(f"获取alpha信息失败: {str(e)}")
        return {}
    
    # 3. 创建测试配置
    try:
        configs = create_robustness_test_configs(original_alpha_info, alpha_expression)
        logger.info(f"创建了 {len(configs)} 个测试配置")
    except Exception as e:
        logger.error(f"创建测试配置失败: {str(e)}")
        return {}
    
    # 4. 提交模拟
    try:
        submitted_progress_urls = submit_robustness_simulations(session, configs)
        logger.info(f"提交了 {len(submitted_progress_urls)} 个模拟")
        
        if not submitted_progress_urls:
            logger.error("没有成功提交任何模拟")
            return {}
            
    except Exception as e:
        logger.error(f"提交模拟失败: {str(e)}")
        return {}
    
    # 5. 等待完成（可选）
    completed_alpha_ids = []
    if wait_for_completion:
        try:
            logger.info("等待模拟完成...")
            completed_alpha_ids = wait_for_simulation_completion(session, submitted_progress_urls)
            logger.info(f"{len(completed_alpha_ids)} 个模拟已完成")
        except Exception as e:
            logger.error(f"等待模拟完成时出错: {str(e)}")
    
    # 6. 获取PNL数据
    try:
        pnl_data = pd.DataFrame()
        
        # 如果没有等待完成，就跳过PNL获取
        if not completed_alpha_ids:
            logger.info("没有等待模拟完成，跳过PNL数据获取")
            return {
                'submitted_progress_urls': submitted_progress_urls, 
                'configs': configs,
                'message': '模拟已提交，请稍后在平台查看结果'
            }
        
        for alpha_id_sim in completed_alpha_ids:
            df = get_pnl(session, alpha_id_sim)
            if df is not None:
                pnl_data = pd.merge(pnl_data, df, left_index=True, right_index=True, how='outer')
            time.sleep(1)  # 避免请求过快
        
        if pnl_data.empty:
            logger.warning("未获取到PNL数据")
            return {
                'submitted_progress_urls': submitted_progress_urls, 
                'completed_alpha_ids': completed_alpha_ids,
                'configs': configs
            }
        
        logger.info(f"获取到 {len(pnl_data.columns)} 个alpha的PNL数据")
        
    except Exception as e:
        logger.error(f"获取PNL数据失败: {str(e)}")
        return {
            'submitted_progress_urls': submitted_progress_urls, 
            'completed_alpha_ids': completed_alpha_ids,
            'configs': configs
        }
    
    # 7. 分析结果
    try:
        analysis_results = analyze_robustness_results(pnl_data, configs)
        logger.info("稳定性分析完成")
    except Exception as e:
        logger.error(f"分析结果失败: {str(e)}")
        return {'pnl_data': pnl_data, 'submitted_progress_urls': submitted_progress_urls, 'configs': configs}
    
    # 8. 保存结果
    try:
        save_results(analysis_results, pnl_data)
        logger.info("结果已保存")
    except Exception as e:
        logger.error(f"保存结果失败: {str(e)}")
    
    # 9. 创建可视化（可选）
    if create_plots and not pnl_data.empty:
        try:
            create_visualization(pnl_data, analysis_results)
            logger.info("可视化图表已创建")
        except Exception as e:
            logger.error(f"创建可视化失败: {str(e)}")
    
    # 10. 打印摘要
    print_summary(analysis_results)
    
    # 返回完整结果
    return {
        'analysis_results': analysis_results,
        'pnl_data': pnl_data,
        'submitted_progress_urls': submitted_progress_urls,
        'completed_alpha_ids': completed_alpha_ids,
        'configs': configs,
        'original_alpha_info': original_alpha_info
    }

if __name__ == "__main__":
    """
    主程序入口 - 一键执行Alpha稳定性检验
    
    使用方法:
    1. 直接运行: python check_alpha_robust.py
    2. 指定alpha ID: 修改DEFAULT_ALPHA_ID变量
    3. 自定义参数: 调用check_alpha_robustness()函数
    """
    
    print("🚀 Alpha稳定性检验工具")
    print("=" * 50)
    
    # 获取用户输入（可选）
    try:
        user_alpha_id = input(f"请输入要测试的Alpha ID (直接回车使用默认 {DEFAULT_ALPHA_ID}): ").strip()
        if not user_alpha_id:
            user_alpha_id = DEFAULT_ALPHA_ID
            
        use_hk = input("是否使用香港登录? (y/N): ").strip().lower() == 'y'
        
        print(f"\n开始测试Alpha: {user_alpha_id}")
        print("测试参数组合:")
        print("- Decay变化: 根据原始值自动确定")
        print(f"- 中性化方法: {', '.join(NEUTRALIZATION_METHODS)}")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n用户取消操作")
        exit(0)
    except:
        user_alpha_id = DEFAULT_ALPHA_ID
        use_hk = False
    
    # 执行稳定性检验
    results = check_alpha_robustness(
        alpha_id=user_alpha_id,
        use_hk_login=use_hk,
        wait_for_completion=True,
        create_plots=True
    )
    
    if results:
        print("\n✅ Alpha稳定性检验完成!")
        print("📁 结果已保存到 robustness_results/ 目录")
        print("📊 可视化图表已生成")
    else:
        print("\n❌ Alpha稳定性检验失败，请检查日志信息")
