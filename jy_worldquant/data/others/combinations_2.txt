close/vwap  
(close - open) / (high - low)  
high/low  
close/open  
open - close  
volume/adv20  
returns * volume  
returns/adv20  
(close - open)/open  
(high + low)/(2 * close)  
cap * returns  
ts_sum(returns, 5)  
ts_sum(returns, 20)  
ts_sum(returns, 60)  
close/ts_mean(close,20)  
ts_std_dev(returns, 30)  
ts_std_dev(returns, 60)  
close/ts_std_dev(close,20)  
close - ts_mean(close,20)  
returns/ts_std_dev(returns, 5)  
close/bookvalue_ps  
close/eps  
cap/sales  
cap/ebit  
cap/cashflow_op  
fnd6_dvt/cap  
cap/assets  
fnd6_lt/fnd6_ceq  
(cap/assets) * return_assets  
(close/bookvalue_ps) * return_equity  
(close/eps) * return_equity  
sales/assets  
sales/fnd6_lt  
sales/cashflow_op  
return_equity/(close/eps)  
return_assets/historical_volatility_20  
(return_equity + return_assets)/2  
ebit/assets  
ebit/fnd6_ceq  
cash/assets  
cash/cap  
cashflow_op/assets  
ebit/sales  
ebit * inventory_turnover  
inventory_turnover  
scl12_sentiment + mws84_sentiment  
scl12_sentiment/(scl12_buzz+1)  
mws84_sentiment - scl12_sentiment  
scl12_sentiment + mws92_bbgnews_score_m1  
mws92_bbgnews_score_m1 * mws92_bbgnews_confidence  
scl12_sentiment * scl12_buzz  
mws92_bbgnews_confidence  
mws84_sentiment/scl12_sentiment  
scl12_sentiment - mws92_bbgnews_score_m1  
historical_volatility_20 - historical_volatility_60  
historical_volatility_20/historical_volatility_60  
news_atr14/close  
returns/historical_volatility_20  
volume/ts_std_dev(volume,20)  
(high - low)/close  
(high - low)/open   
ts_corr(returns, volume, 20)  
ts_zscore(returns, 30)  
(open - close) * returns  
(close - open) * inventory_turnover  
(close - open) * (cash/assets)  
returns * return_equity  
returns * return_assets  
(cap + cash)/cap  
(cap - assets)/assets  
cap/ts_mean(cap, 20)  
open/ts_mean(open, 20)  
volume/ts_mean(volume, 20)  
close - ts_mean(close, 5)  
close - ts_mean(close, 60)  
ts_std_dev(close, 20)  
return_equity - return_assets  
return_equity/return_assets  
close * returns  
adv20 - volume  
close * inventory_turnover  
(sales/assets) - (cash/assets)  
return_equity + returns  
return_assets - returns  
ebit/cap  
sales - fnd6_lt  
sales/fnd6_dvt  
cashflow_op - ebit  
ts_sum(returns, 10)  
(close/open - 1) * volume  
(close/adv20) * returns  
returns / (fnd6_lt + 1)  
returns * (fnd6_lt / fnd6_ceq)  
(cap * return_assets) / ((close/eps) + 1)  
inventory_turnover - return_equity  
(volume * returns) / adv20  
(high/low) * returns  
(close - vwap) / adv20  
(close + open) / (2 * close)
