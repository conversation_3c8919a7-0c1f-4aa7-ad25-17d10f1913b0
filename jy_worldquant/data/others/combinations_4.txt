divide(accounts_payable, ts_mean(adv20, 22))
multiply(accum_depre, ts_zscore(adv20, 66))
ts_mean(add(anl14_all_mean_eps_fy2, anl14_all_stddev_eps_fy1), 240)
divide(ts_sum(anl14_high_revenue_fp1, 5), ts_std_dev(adv20, 22))
ts_zscore(subtract(anl14_all_high_eps_fy1, anl14_all_low_eps_fy1), 120)
multiply(anl14_all_numofests_eps_fy2, ts_rank(anl14_all_median_eps_fy2, 66))
add(ts_delta(anl14_high_ebitda_fy1, 5), ts_av_diff(anl14_low_ebitda_fy2, 22))
divide(anl14_high_roa_fy1, ts_backfill(anl14_low_roa_fy2, 120))
ts_scale(subtract(anl14_high_bvps_fy2, anl14_low_bvps_fy1), 240)
ts_product(divide(adv20, ts_mean(adv20, 66)), 120)
ts_entropy(ts_delta(anl14_high_ntprep_fp1, 3), 22)
ts_arg_max(subtract(anl14_high_ptp_fp1, anl14_low_ptp_fp1), 66)
multiply(ts_sum(anl14_estvalue_fp3, 5), ts_std_dev(adv20, 240))
ts_min_max_cps(add(anl14_high_capex_fy1, anl14_low_capex_fy2), 120)
power(divide(anl14_high_ndebt_fy1, ts_zscore(adv20, 22)), 0.5)
sigmoid(subtract(ts_mean(anl14_all_mean_eps_fy1, 66), anl14_all_median_eps_fy2))
tanh(multiply(ts_rank(accounts_payable, 120), ts_av_diff(adv20, 240)))
log(ts_sum(abs(subtract(anl14_high_revenue_fp3, anl14_low_revenue_fp2)), 5))
sqrt(ts_corr(anl14_high_roe_fy1, ts_delta(adv20, 3), 66))
ts_sum(divide(anl14_high_roa_fy1, anl14_low_roa_fy2), 22)
ts_zscore(subtract(anl14_high_ndebt_fy2, anl14_low_ndebt_fy1), 66)
multiply(ts_rank(accounts_payable, 240), ts_av_diff(adv20, 5))
divide(ts_product(adv20, 22), ts_mean(anl14_high_revenue_fp3, 66))
ts_entropy(subtract(anl14_high_ptp_fp1, anl14_low_ptp_fp1), 240)
add(ts_delta(anl14_high_ebitda_fy2, 3), power(adv20, 0.5))
ts_arg_max(abs(subtract(anl14_high_capex_fy1, anl14_low_capex_fy2)), 66)
ts_backfill(divide(anl14_high_ntprep_fp1, ts_std_dev(adv20, 240)), 120)
ts_mean(reverse(anl14_low_roa_fy2), 66)
ts_sum(sqrt(abs(anl14_high_bvps_fy1)), 120)
multiply(ts_zscore(anl14_high_roe_fy2, 22), ts_rank(adv20, 240))
ts_av_diff(divide(anl14_high_ntprep_fp1, anl14_low_ntprep_fp1), 5)
ts_min_max_cps(subtract(anl14_high_epsrep_fp1, anl14_low_epsrep_fp1), 66)
power(ts_sum(anl14_estvalue_fp3, 22), ts_zscore(adv20, 120))
ts_rank(divide(accounts_payable, anl14_high_ntprep_fp1), 66)
ts_std_dev(log(add(anl14_high_revenue_fp2, 1)), 240)
ts_zscore(subtract(anl14_high_ntprep_fp1, anl14_low_ntprep_fp1), 66)
ts_backfill(divide(anl14_high_roe_fy1, ts_std_dev(adv20, 240)), 120)
ts_min_max_cps(add(anl14_high_capex_fy1, anl14_low_capex_fy2), 240)
tanh(ts_corr(ts_sum(adv20, 5), ts_delta(anl14_high_roa_fy1, 3), 66))
ts_product(subtract(anl14_high_revenue_fp3, anl14_low_revenue_fp2), 120)
ts_scale(divide(anl14_high_ebitda_fy1, ts_mean(adv20, 66)), 22)
ts_arg_max(power(divide(accounts_payable, anl14_low_ntprep_fp1), 0.5), 66)
ts_entropy(ts_zscore(anl14_high_bvps_fy2, 120), 240)
add(ts_delta(anl14_high_ptp_fp1, 5), log(ts_sum(adv20, 22)))
ts_mean(abs(subtract(anl14_all_high_eps_fy1, anl14_all_low_eps_fy2)), 120)
divide(ts_min_max_diff(anl14_high_ndebt_fy1, 120), ts_std_dev(adv20, 22))
ts_sum(sqrt(abs(subtract(anl14_high_roa_fy2, anl14_low_roa_fy1))), 240)
ts_rank(reverse(ts_zscore(accounts_payable, 66)), 120)
ts_corr(ts_delta(anl14_high_epsrep_fp1, 3), ts_mean(adv20, 22), 66)
power(ts_scale(anl14_high_ebit_fy1, 120), ts_av_diff(adv20, 5))
ts_min_max_diff(log(add(anl14_high_revenue_fp2, 1)), 240)
multiply(ts_arg_min(anl14_low_bvps_fy1, 66), ts_sum(adv20, 120))
ts_mean(divide(anl14_high_capex_fy2, ts_backfill(anl14_low_capex_fy1, 240)), 22)
ts_mean(subtract(anl14_high_roa_fy1, anl14_low_roa_fy2), 66)
ts_zscore(divide(anl14_high_ebitda_fy1, ts_sum(adv20, 240)), 120)
ts_min_max_diff(add(anl14_high_ntprep_fp1, anl14_low_ntprep_fp1), 240)
divide(ts_product(anl14_estvalue_fp3, 5), ts_std_dev(adv20, 66))
ts_entropy(ts_delta(anl14_high_ptp_fp1, 3), 22)
add(ts_zscore(accounts_payable, 120), power(ts_mean(adv20, 240), 0.5))
ts_arg_max(abs(subtract(anl14_high_capex_fy2, anl14_low_capex_fy1)), 120)
tanh(ts_corr(ts_sum(anl14_high_roe_fy1, 5), ts_delta(adv20, 3), 22))
ts_backfill(divide(ts_min_max_diff(anl14_high_ndebt_fy1, 66), anl14_low_ndebt_fy2), 120)
ts_sum(sqrt(abs(subtract(anl14_high_bvps_fy2, anl14_low_bvps_fy1))), 66)
multiply(ts_av_diff(anl14_high_epsrep_fp1, 22), ts_zscore(adv20, 240))
ts_min_max_cps(subtract(anl14_high_roa_fy2, ts_mean(anl14_low_roa_fy1, 120)), 66)
log(add(ts_product(anl14_estvalue_fp3, 5), ts_std_dev(adv20, 240)))
ts_rank(reverse(ts_zscore(anl14_high_ebit_fy1, 66)), 120)
ts_corr(ts_delta(anl14_high_ntprep_fp1, 3), ts_mean(adv20, 22), 240)
power(ts_scale(ts_sum(anl14_high_revenue_fp2, 5), 66), divide(adv20, 1000))
ts_min_max_diff(ts_entropy(anl14_low_ptp_fp1, 120), 22)
multiply(ts_arg_min(anl14_low_ebitda_fy2, 66), ts_av_diff(adv20, 240))
ts_zscore(subtract(anl14_high_roe_fy2, anl14_low_roe_fy1), 66)
divide(anl14_high_ndebt_fy1, ts_product(adv20, 22))
ts_av_diff(multiply(adv20, anl14_high_ptp_fp1), 120)
power(ts_sum(anl14_estvalue_fp3, 5), tanh(ts_zscore(adv20, 240)))
ts_min_max_diff(add(anl14_high_capex_fy1, anl14_low_capex_fy2), 240)
log(ts_sum(abs(subtract(anl14_high_revenue_fp3, anl14_low_revenue_fp2)), 5))
ts_corr(ts_delta(anl14_high_epsrep_fp1, 3), ts_mean(adv20, 22), 66)
multiply(ts_rank(accounts_payable, 120), ts_av_diff(adv20, 240))
ts_mean(divide(anl14_high_capex_fy2, ts_backfill(anl14_low_capex_fy1, 240)), 22)
ts_scale(subtract(anl14_high_ebitda_fy1, anl14_low_ebitda_fy2), 66)
ts_entropy(ts_delta(anl14_high_ntprep_fp1, 5), 22)
add(ts_zscore(anl14_high_roa_fy1, 120), power(adv20, 0.5))
divide(ts_min_max_diff(anl14_high_bvps_fy2, 66), ts_std_dev(adv20, 240))
ts_arg_min(anl14_low_ebitda_fy2, 66)
ts_backfill(subtract(anl14_high_ptp_fp1, anl14_low_ptp_fp1), 120)
tanh(ts_corr(ts_sum(adv20, 5), ts_delta(anl14_high_roa_fy1, 3), 66))
ts_rank(reverse(ts_zscore(accounts_payable, 66)), 120)