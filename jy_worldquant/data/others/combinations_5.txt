ts_delta(close, 20) / close
ts_mean(volume, 20) / ts_mean(volume, 60)
close / eps
ts_delta(close, 20) * volume
close / ts_mean(close, 20)
ts_std_dev(returns, 20) * cap
cashflow_op / assets
debt / equity
revenue / assets
ebitda / revenue
ts_delta(revenue, 252) / revenue
ts_mean(returns, 20) / ts_std_dev(returns, 20)
ts_sum(dividend, 252) / close
cap / ts_sum(revenue, 252)
ts_std_dev(returns, 60) / ts_std_dev(returns, 20)
ts_delta(close, 5) / ts_delta(close, 20)
assets_curr / liabilities_curr
(assets_curr - inventory) / liabilities_curr
revenue / sharesout
ebit / sharesout
ts_mean(returns, 5) * ts_mean(volume, 5)
ts_delta(volume, 20) / volume
ts_sum(cashflow_op, 252) / ts_sum(capex, 252)
debt_st / debt_lt
ts_delta(assets, 252) / assets
ts_mean(returns, 20) - ts_mean(returns, 60)
cashflow_fin / cashflow_op
cap / enterprise_value
ts_delta(volume, 5) * returns
ts_std_dev(returns, 20) / ts_mean(returns, 20)
ts_delta(eps, 252) / eps
ts_sum(cashflow_op, 252) / ts_sum(revenue, 252)
debt_lt / (debt_lt + equity)
ts_delta(cap, 20) / cap
bookvalue_ps / close
ts_mean(volume, 5) / ts_mean(volume, 20)
debt / (debt + equity)
ts_delta(cashflow_op, 252) / cashflow_op
ts_sum(revenue, 252) / ts_sum(assets, 252)
cap / ts_sum(cashflow_op, 252)
ts_delta(returns, 1) * volume
ts_std_dev(returns, 252) / ts_std_dev(returns, 20)
cashflow_op / revenue
ts_sum(dividend, 252) / ts_sum(eps, 252)
ts_sum(ebit, 252) / ts_sum(interest_expense, 252)
ts_delta(bookvalue_ps, 252) / bookvalue_ps
ts_mean(returns, 252) / ts_std_dev(returns, 252)
cashflow_fin / equity
ts_delta(cap, 252) / cap
ts_sum(dividend, 252) / ts_sum(cap, 252)
debt_lt / assets
ts_sum(cashflow_op, 252) / ts_sum(debt, 252)
ts_delta(returns, 5) / ts_std_dev(returns, 20)
ts_mean(volume, 20) * close
cashflow_op / (cashflow_op + cashflow_invst + cashflow_fin)
ts_sum(revenue, 252) / ts_sum(employee, 252)
ts_delta(volume, 20) / ts_mean(volume, 20)
debt / ts_sum(cashflow_op, 252)
ts_sum(cashflow_op, 252) / ts_sum(assets, 252)
cap / ts_sum(ebitda, 252)
cashflow_op / capex
ts_delta(returns, 20) * volume
ts_sum(cashflow_fin, 252) / ts_sum(equity, 252)
ts_std_dev(returns, 5) / ts_std_dev(returns, 20)
ts_delta(volume, 5) / ts_delta(volume, 20)
ts_sum(revenue, 252) / ts_sum(cogs, 252)
cashflow_op / (debt_st + debt_lt)
ts_mean(returns, 5) / ts_mean(returns, 20)
ts_delta(eps, 5) / eps
ts_mean(volume, 20) / ts_mean(volume, 252)
cashflow_invst / cashflow_op
ts_sum(cashflow_op, 252) / ts_sum(revenue, 252)
ts_delta(eps, 252) / ts_delta(revenue, 252)
ts_mean(returns, 5) / ts_std_dev(returns, 5)
cashflow_op / (cashflow_op - cashflow_invst)
ts_sum(cashflow_fin, 252) / ts_sum(cashflow_op, 252)
ts_delta(cap, 20) * returns
ts_sum(cashflow_invst, 252) / ts_sum(assets, 252)
ts_delta(eps, 5) * volume
cashflow_op / ts_sum(cashflow_op, 252)
ts_mean(volume, 20) / ts_mean(cap, 20)
ts_sum(ebit, 252) / ts_sum(assets, 252)
ts_delta(returns, 5) / ts_mean(returns, 20)
cashflow_op / (debt_st + debt_lt + equity)
ts_delta(close, 252) / close
ts_mean(close, 20) + 2 * ts_std_dev(close, 20)
ts_mean(close, 20) - 2 * ts_std_dev(close, 20)
ts_mean(returns, 12) - ts_mean(returns, 26)
(ts_mean(greater(returns, 0), 14) / ts_mean(abs(returns), 14)) * 100
dividend / close
eps / close
close / bookvalue_ps
net_income / assets
net_income / equity
(revenue - cogs) / revenue
ebit / revenue
net_income / revenue
cogs / inventory
revenue / receivables
cogs / accounts_payable
(cashflow_op - capex) / cap
enterprise_value / ebitda
close / (revenue / sharesout)
close / (cashflow_op / sharesout)
dividend / eps
1 - (dividend / eps)
(net_income / equity) * (1 - (dividend / eps))
ts_delta(eps, 252) / eps
ts_delta(revenue, 252) / revenue
ts_delta(bookvalue_ps, 252) / bookvalue_ps
ts_delta(dividend, 252) / dividend
ts_delta(cashflow_op, 252) / cashflow_op
volume / ts_mean(volume, 20)
ts_std_dev(returns, 20) / ts_std_dev(returns, 60)
volume * close / cap
enterprise_value / revenue
close / ((cashflow_op - capex) / sharesout)
(cap + debt) / assets
cap / equity
(close / eps) / (ts_delta(revenue, 252) / revenue)
ts_mean(returns, 252) / maximum_drawdown
ts_delta(close, 20) / ts_std_dev(returns, 20)
ts_mean(returns, 252) / ts_std_dev(returns, 252)
ts_sum(cashflow_op, 252) / ts_sum(debt, 252)
ts_delta(volume, 20) * close
ebitda / assets
revenue / (assets_curr + assets_fixed)
ts_sum(net_income, 252) / ts_sum(revenue, 252)
cashflow_op / (assets_curr - liabilities_curr)
ts_delta(eps, 20) / ts_std_dev(eps, 20)
close / (ts_sum(net_income, 252) / sharesout)
ts_mean(returns, 20) * ts_mean(returns, 60)
debt / ts_sum(ebitda, 252)
ts_delta(close, 60) / ts_delta(close, 20)
ts_sum(cashflow_op, 252) / sharesout
net_income / (debt + equity)
ts_mean(volume, 60) / cap
ebit / (debt_st + debt_lt)
ts_delta(revenue, 20) / revenue
cashflow_op / ts_sum(capex, 252)
close / (ebitda / sharesout)
ts_sum(dividend, 252) / ts_sum(revenue, 252)
ts_delta(assets_curr, 252) / assets_curr
revenue / (debt + equity)
ts_mean(returns, 252) - ts_mean(returns, 20)
cashflow_op / ts_sum(assets_curr, 252)
ts_delta(close, 5) * ts_mean(volume, 5)
net_income / ts_sum(cashflow_op, 252)
ts_sum(ebit, 252) / ts_sum(revenue, 252)
debt_st / (assets_curr - inventory)
ts_delta(cogs, 252) / cogs
revenue / ts_sum(capex, 252)
ts_mean(close, 20) / ts_mean(close, 60)
cashflow_op / (revenue - cogs)
ts_sum(net_income, 252) / ts_sum(assets, 252)
ts_delta(dividend, 20) / dividend
ebitda / (debt_st + debt_lt)
ts_mean(volume, 20) / sharesout
ts_sum(cashflow_op, 252) / ts_sum(equity, 252)
close / (net_income / sharesout)
ts_delta(returns, 60) / ts_std_dev(returns, 60)
revenue / ts_sum(assets_curr, 252)
cashflow_op / ts_sum(net_income, 252)
ts_sum(dividend, 252) / ts_sum(equity, 252)
ts_delta(close, 20) / ts_mean(close, 20)
ebit / ts_sum(capex, 252)
net_income / ts_sum(revenue, 252)
ts_mean(returns, 20) / ts_mean(volume, 20)
debt / ts_sum(net_income, 252)
ts_delta(revenue, 60) / ts_delta(revenue, 20)
cashflow_op / ts_sum(cogs, 252)
ts_sum(ebitda, 252) / ts_sum(assets, 252)
close / ts_sum(cashflow_op, 252)
ts_delta(assets, 20) / assets
revenue / ts_sum(equity, 252)
ts_mean(returns, 60) * cap
ebitda / ts_sum(revenue, 252)
ts_delta(close, 252) / ts_std_dev(returns, 252)
cashflow_op / ts_sum(assets_fixed, 252)
net_income / ts_sum(capex, 252)
ts_mean(volume, 252) / cap
ts_delta(eps, 60) / eps
revenue / ts_sum(net_income, 252)
close / ts_sum(ebit, 252)
ts_sum(cashflow_op, 252) / ts_sum(cashflow_invst, 252)
ebit / ts_sum(equity, 252)
ts_delta(close, 20) * ts_std_dev(returns, 20)
cashflow_op / ts_sum(employee, 252)
ts_mean(returns, 252) / ts_mean(returns, 60)
revenue / ts_sum(cashflow_op, 252)
close / ts_sum(assets, 252)
ts_delta(revenue, 252) / ts_std_dev(revenue, 252)
ebitda / ts_sum(capex, 252)
ts_sum(net_income, 252) / ts_sum(equity, 252)
cashflow_op / ts_sum(revenue, 252)
ts_delta(close, 60) * volume
revenue / ts_sum(assets_fixed, 252)
ts_mean(close, 252) / close
net_income / ts_sum(cashflow_invst, 252)
ts_delta(eps, 252) / ts_std_dev(returns, 252)