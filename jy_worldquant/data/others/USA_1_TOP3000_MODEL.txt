close / open
high / low
volume / adv20
close / vwap
(high - low) / close
(close - open) / open
close * volume
dividend / close
close * sharesout
(high + low) / (2 * close)
(open + close) / (2 * vwap)
volume * close
(close - vwap) / vwap
high - low
close - open
volume - adv20
(high - close) / (high - low)
(close - low) / (high - low)
open / vwap
high / close
low / close
volume / close
adv20 / close
sharesout * close
(high * low) / (close * close)
(open + high + low + close) / (4 * vwap)
volume / sharesout
(close + high) / (2 * open)
(close + low) / (2 * open)
(high + open) / (2 * close)
(low + open) / (2 * close)
(high - open) / open
(low - open) / open
(vwap - open) / open
(vwap - close) / close
volume * (close - open)
volume * (high - low)
(close * close) / (high * low)
(open * close) / (high * low)
volume / (high - low)
adv20 / (high - low)
close / (high - low)
open / (high - low)
vwap / (high - low)
(close - open) * volume
(high - low) * volume
close / (close + open)
high / (high + low)
open / (open + close)
low / (high + low)
vwap / (vwap + close)
volume / (volume + adv20)
(close + open + high + low) / (4 * close)
(close * open) / (high * low)
(close + open) / (high + low)
(close - open) / (high - low)
volume * close / adv20
(close / open) * volume
(high / low) * volume
volume / (close * open)
adv20 / (close * open)
(close * volume) / (open * adv20)
(high * volume) / (low * adv20)
close / (volume / adv20)
open / (volume / adv20)
(close + open) * volume
(high + low) * volume
(close - open) / volume
(high - low) / volume
close * open / volume
high * low / volume
vwap * volume / adv20
(close / vwap) * volume
(open / vwap) * volume
(high / vwap) * volume
(low / vwap) * volume
volume * vwap / close
volume * vwap / open
adv20 * close / volume
adv20 * open / volume
(close + vwap) / (open + vwap)
(high + vwap) / (low + vwap)
close * sharesout / volume
open * sharesout / volume
volume / (close * sharesout)
adv20 / (close * sharesout)
(close * sharesout) / adv20
(open * sharesout) / adv20
dividend * sharesout / close
dividend * sharesout / open
close / (dividend * sharesout)
open / (dividend * sharesout)
(close + dividend) / open
(open + dividend) / close
close - dividend
open - dividend
high - dividend
low - dividend
vwap - dividend
volume + dividend
adv20 + dividend
dividend / vwap
dividend / high
dividend / low
dividend * close / open
dividend * open / close
dividend * high / low
dividend * low / high
(close + open + dividend) / 3
(high + low + dividend) / 3
close / (close - dividend)
open / (open - dividend)
(close * open) / dividend
(high * low) / dividend
volume / dividend
adv20 / dividend
dividend / (volume + adv20)
(close + open) / dividend
(high + low) / dividend
dividend * volume / close
dividend * volume / open
dividend * adv20 / close
dividend * adv20 / open
(close / dividend) * volume
(open / dividend) * volume
close * dividend / volume
open * dividend / volume
(close + dividend) * volume
(open + dividend) * volume
(high + dividend) * volume
(low + dividend) * volume
(vwap + dividend) * volume
volume / (close + dividend)
volume / (open + dividend)
adv20 / (close + dividend)
adv20 / (open + dividend)
(close - dividend) * volume
(open - dividend) * volume
(high - dividend) * volume
(low - dividend) * volume
(vwap - dividend) * volume
close / (high + low + open)
open / (high + low + close)
high / (close + low + open)
low / (high + close + open)
vwap / (high + low + close + open)
(close * open * high) / (low * volume)
(close * open * low) / (high * volume)
(high * low * close) / (open * volume)
(high * low * open) / (close * volume)
volume / (close * open * high)
volume / (close * open * low)
volume / (high * low * close)
volume / (high * low * open)
adv20 / (close * open * high)
adv20 / (close * open * low)
adv20 / (high * low * close)
adv20 / (high * low * open)
(close + open) / (high * low)
(high + low) / (close * open)
(close * open) + (high * low)
(close * open) - (high * low)
(high * low) - (close * open)
close / (open + high + low + vwap)
open / (close + high + low + vwap)
high / (close + open + low + vwap)
low / (close + open + high + vwap)
vwap / (close + open + high + low)
(close + open + high + low + vwap) / 5
close * open * high / (low * vwap)
close * open * low / (high * vwap)
close * high * low / (open * vwap)
open * high * low / (close * vwap)
vwap * close * open / (high * low)
vwap * high * low / (close * open)
volume / (close + open + high + low + vwap)
adv20 / (close + open + high + low + vwap)
(close + open + high + low + vwap) / volume
(close + open + high + low + vwap) / adv20
close / (volume + adv20 + sharesout)
open / (volume + adv20 + sharesout)
high / (volume + adv20 + sharesout)
low / (volume + adv20 + sharesout)
vwap / (volume + adv20 + sharesout)
(volume + adv20 + sharesout) / close
(volume + adv20 + sharesout) / open
(volume + adv20 + sharesout) / high
(volume + adv20 + sharesout) / low
(volume + adv20 + sharesout) / vwap
close * (volume + adv20) / sharesout
open * (volume + adv20) / sharesout
high * (volume + adv20) / sharesout
low * (volume + adv20) / sharesout
vwap * (volume + adv20) / sharesout
sharesout / (volume + adv20)
(volume + adv20) / sharesout
close * sharesout / (volume + adv20)
open * sharesout / (volume + adv20)
high * sharesout / (volume + adv20)
low * sharesout / (volume + adv20)
vwap * sharesout / (volume + adv20)
(close + open) * sharesout / volume
(high + low) * sharesout / volume
(close + open) * sharesout / adv20
(high + low) * sharesout / adv20
volume / ((close + open) * sharesout)
volume / ((high + low) * sharesout)
adv20 / ((close + open) * sharesout)
adv20 / ((high + low) * sharesout)