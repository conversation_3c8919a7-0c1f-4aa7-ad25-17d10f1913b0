group_rank(ts_regression(ts_zscore(anl16_1scermun, 252), ts_zscore(vec_sum(anl16_1scermun), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_2scermun, 252), ts_zscore(vec_sum(anl16_2scermun), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_3scermun, 252), ts_zscore(vec_sum(anl16_3scermun), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_4scermun, 252), ts_zscore(vec_sum(anl16_4scermun), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_5scermun, 252), ts_zscore(vec_sum(anl16_5scermun), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_actsuescore, 252), ts_zscore(vec_sum(anl16_actsuescore), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_actsurprise, 252), ts_zscore(vec_sum(anl16_actsurprise), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_aftercons_difference, 252), ts_zscore(vec_sum(anl16_aftercons_difference), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_aftercons_high, 252), ts_zscore(vec_sum(anl16_aftercons_high), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_aftercons_low, 252), ts_zscore(vec_sum(anl16_aftercons_low), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_aftercons_mean, 252), ts_zscore(vec_sum(anl16_aftercons_mean), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_aftercons_median, 252), ts_zscore(vec_sum(anl16_aftercons_median), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_aftercons_numitems, 252), ts_zscore(vec_sum(anl16_aftercons_numitems), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_aftercons_percentage, 252), ts_zscore(vec_sum(anl16_aftercons_percentage), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_aftercons_stddev, 252), ts_zscore(vec_sum(anl16_aftercons_stddev), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_afterest_difference, 252), ts_zscore(vec_sum(anl16_afterest_difference), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_afterest_percentage, 252), ts_zscore(vec_sum(anl16_afterest_percentage), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_afterest_stdvalue, 252), ts_zscore(vec_sum(anl16_afterest_stdvalue), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_afterest_value, 252), ts_zscore(vec_sum(anl16_afterest_value), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_beforecons_high, 252), ts_zscore(vec_sum(anl16_beforecons_high), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_beforecons_low, 252), ts_zscore(vec_sum(anl16_beforecons_low), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_beforecons_mean, 252), ts_zscore(vec_sum(anl16_beforecons_mean), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_beforecons_median, 252), ts_zscore(vec_sum(anl16_beforecons_median), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_beforecons_numitems, 252), ts_zscore(vec_sum(anl16_beforecons_numitems), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_beforecons_numitems_orig, 252), ts_zscore(vec_sum(anl16_beforecons_numitems_orig), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_beforecons_stddev, 252), ts_zscore(vec_sum(anl16_beforecons_stddev), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_beforeest_stdvalue, 252), ts_zscore(vec_sum(anl16_beforeest_stdvalue), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_beforeest_value, 252), ts_zscore(vec_sum(anl16_beforeest_value), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_estimate_d0_estnorm_estcomsplitadj, 252), ts_zscore(vec_sum(anl16_estimate_d0_estnorm_estcomsplitadj), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_estimate_d0_estnorm_estnormcuradj, 252), ts_zscore(vec_sum(anl16_estimate_d0_estnorm_estnormcuradj), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_estimate_d0_estunit_scale, 252), ts_zscore(vec_sum(anl16_estimate_d0_estunit_scale), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_estimate_d0_estunit_scale_ascontrib, 252), ts_zscore(vec_sum(anl16_estimate_d0_estunit_scale_ascontrib), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_estimate_d0_estunit_scale_normal, 252), ts_zscore(vec_sum(anl16_estimate_d0_estunit_scale_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_estimate_d0_estvalue, 252), ts_zscore(vec_sum(anl16_estimate_d0_estvalue), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_estimate_d0_estvalue_ascontrib, 252), ts_zscore(vec_sum(anl16_estimate_d0_estvalue_ascontrib), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_estimate_d0_estvalue_normal, 252), ts_zscore(vec_sum(anl16_estimate_d0_estvalue_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_eststddev, 252), ts_zscore(vec_sum(anl16_eststddev), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_eststddev_normal, 252), ts_zscore(vec_sum(anl16_eststddev_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_highest, 252), ts_zscore(vec_sum(anl16_highest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_highest_normal, 252), ts_zscore(vec_sum(anl16_highest_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_highrec, 252), ts_zscore(vec_sum(anl16_highrec), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_interimperiodend, 252), ts_zscore(vec_sum(anl16_interimperiodend), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_interimperiodnum, 252), ts_zscore(vec_sum(anl16_interimperiodnum), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_lowest, 252), ts_zscore(vec_sum(anl16_lowest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_lowest_normal, 252), ts_zscore(vec_sum(anl16_lowest_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_lowrec, 252), ts_zscore(vec_sum(anl16_lowrec), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_meanest, 252), ts_zscore(vec_sum(anl16_meanest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_meanest_normal, 252), ts_zscore(vec_sum(anl16_meanest_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_meanestnorm_estcomsplitadj, 252), ts_zscore(vec_sum(anl16_meanestnorm_estcomsplitadj), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_meanestnorm_estnormcuradj, 252), ts_zscore(vec_sum(anl16_meanestnorm_estnormcuradj), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_meanestunit_scale, 252), ts_zscore(vec_sum(anl16_meanestunit_scale), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_meanestunit_scale_normal, 252), ts_zscore(vec_sum(anl16_meanestunit_scale_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_meanrec, 252), ts_zscore(vec_sum(anl16_meanrec), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_medianest, 252), ts_zscore(vec_sum(anl16_medianest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_medianest_normal, 252), ts_zscore(vec_sum(anl16_medianest_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_medianrec, 252), ts_zscore(vec_sum(anl16_medianrec), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_numests, 252), ts_zscore(vec_sum(anl16_numests), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_numincests, 252), ts_zscore(vec_sum(anl16_numincests), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_presurprise, 252), ts_zscore(vec_sum(anl16_presurprise), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_presurprise_normal, 252), ts_zscore(vec_sum(anl16_presurprise_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_presurprisepct, 252), ts_zscore(vec_sum(anl16_presurprisepct), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_recommendation_d0_reccode, 252), ts_zscore(vec_sum(anl16_recommendation_d0_reccode), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_recommendation_d0_recvalue, 252), ts_zscore(vec_sum(anl16_recommendation_d0_recvalue), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_recsdown, 252), ts_zscore(vec_sum(anl16_recsdown), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_recsup, 252), ts_zscore(vec_sum(anl16_recsup), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_scermun, 252), ts_zscore(vec_sum(anl16_scermun), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_senorm_estcomsplitadj, 252), ts_zscore(vec_sum(anl16_senorm_estcomsplitadj), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_senorm_estnormcuradj, 252), ts_zscore(vec_sum(anl16_senorm_estnormcuradj), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_senumincests, 252), ts_zscore(vec_sum(anl16_senumincests), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_seunit_scale, 252), ts_zscore(vec_sum(anl16_seunit_scale), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_seunit_scale_normal, 252), ts_zscore(vec_sum(anl16_seunit_scale_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_smartest, 252), ts_zscore(vec_sum(anl16_smartest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl16_smartest_normal, 252), ts_zscore(vec_sum(anl16_smartest_normal), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ads1consaf_down, 252), ts_zscore(vec_sum(anl4_ads1consaf_down), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ads1consaf_high, 252), ts_zscore(vec_sum(anl4_ads1consaf_high), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ads1consaf_low, 252), ts_zscore(vec_sum(anl4_ads1consaf_low), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ads1consaf_mean, 252), ts_zscore(vec_sum(anl4_ads1consaf_mean), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ads1consaf_median, 252), ts_zscore(vec_sum(anl4_ads1consaf_median), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ads1consaf_numest,'avance 252), ts_zscore(vec_sum(anl4_ads1consaf_numest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ads1consaf_pu, 252), ts_zscore(vec_sum(anl4_ads1consaf_pu), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_adxqf_down, 252), ts_zscore(vec_sum(anl4_adxqf_down), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_adxqf_high, 252), ts_zscore(vec_sum(anl4_adxqf_high), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_adxqf_low, 252), ts_zscore(vec_sum(anl4_adxqf_low), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_adxqf_mean, 252), ts_zscore(vec_sum(anl4_adxqf_mean), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_adxqf_median, 252), ts_zscore(vec_sum(anl4_adxqf_median), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_adxqf_numest, 252), ts_zscore(vec_sum(anl4_adxqf_numest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_adxqf_pu, 252), ts_zscore(vec_sum(anl4_adxqf_pu), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ady_down, 252), ts_zscore(vec_sum(anl4_ady_down), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ady_high, 252), ts_zscore(vec_sum(anl4_ady_high), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ady_low, 252), ts_zscore(vec_sum(anl4_ady_low), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ady_mean, 252), ts_zscore(vec_sum(anl4_ady_mean), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ady_median, 252), ts_zscore(vec_sum(anl4_ady_median), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ady_numest, 252), ts_zscore(vec_sum(anl4_ady_numest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_ady_pu, 252), ts_zscore(vec_sum(anl4_ady_pu), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_afv4_maxguidance, 252), ts_zscore(vec_sum(anl4_afv4_maxguidance), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_afv4_minguidance, 252), ts_zscore(vec_sum(anl4_afv4_minguidance), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_bac1conqf_item, 252), ts_zscore(vec_sum(anl4_bac1conqf_item), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_basicconqf_down, 252), ts_zscore(vec_sum(anl4_basicconqf_down), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_basicconqf_high, 252), ts_zscore(vec_sum(anl4_basicconqf_high), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_basicconqf_low, 252), ts_zscore(vec_sum(anl4_basicconqf_low), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_basicconqf_mean, 252), ts_zscore(vec_sum(anl4_basicconqf_mean), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_basicconqf_median, 252), ts_zscore(vec_sum(anl4_basicconqf_median), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_basicconqf_numest, 252), ts_zscore(vec_sum(anl4_basicconqf_numest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_basicconqf_pu, 252), ts_zscore(vec_sum(anl4_basicconqf_pu), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_cuo1conaf_item, 252), ts_zscore(vec_sum(anl4_cuo1conaf_item), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_cuo1conqf_item, 252), ts_zscore(vec_sum(anl4_cuo1conqf_item), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_cuo1consaf_item, 252), ts_zscore(vec_sum(anl4_cuo1consaf_item), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_dez1advancedqfv4_est, 252), ts_zscore(vec_sum(anl4_dez1advancedqfv4_est), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_dez1advancedqfv4_preest, 252), ts_zscore(vec_sum(anl4_dez1advancedqfv4_preest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_dez1advancedsafv4_est, 252), ts_zscore(vec_sum(anl4_dez1advancedsafv4_est), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_dez1advancedsafv4_preest, 252), ts_zscore(vec_sum(anl4_dez1advancedsafv4_preest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_dez1afv4_est, 252), ts_zscore(vec_sum(anl4_dez1afv4_est), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_dez1afv4_preest, 252), ts_zscore(vec_sum(anl4_dez1afv4_preest), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_fsdtlestmtadvancedqfv4_item, 252), ts_zscore(vec_sum(anl4_fsdtlestmtadvancedqfv4_item), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_fsdtlestmtadvsafv4_item, 252), ts_zscore(vec_sum(anl4_fsdtlestmtadvsafv4_item), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_fsdtlestmtafv4_item, 252), ts_zscore(vec_sum(anl4_fsdtlestmtafv4_item), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_fsguidanceafv4_item, 252), ts_zscore(vec_sum(anl4_fsguidanceafv4_item), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl4_guiafv4_est, 252), ts_zscore(vec_sum(anl4_guiafv4_est), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl8_analyst_code, 252), ts_zscore(vec_sum(anl8_analyst_code), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl8_eur_ibessplitrecd0_analyst_maskcode, 252), ts_zscore(vec_sum(anl8_eur_ibessplitrecd0_analyst_maskcode), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl8_eur_ibessplitrecd0_ibes_reccode, 252), ts_zscore(vec_sum(anl8_eur_ibessplitrecd0_ibes_reccode), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl8_horizon, 252), ts_zscore(vec_sum(anl8_horizon), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(anl8_value, 252), ts_zscore(vec_sum(anl8_value), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(rsk60_crowding, 252), ts_zscore(vec_sum(rsk60_crowding), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(rsk60_last, 252), ts_zscore(vec_sum(rsk60_last), 252), 252), densify(sector))
group_rank(ts_regression(ts_zscore(rsk60_offer, 252), ts_zscore(vec_sum(rsk60_offer), 252), 252), densify(sector))
