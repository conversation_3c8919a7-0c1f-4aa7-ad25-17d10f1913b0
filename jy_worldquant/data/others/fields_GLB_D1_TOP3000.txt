divide(anl15_gr_12_m_1m_chg ,anl15_gr_12_m_pe)
multiply(anl15_gr_12_m_cos ,anl15_gr_12_m_pe)
subtract(anl15_gr_12_m_cos ,anl15_gr_18_m_cos)
multiply(anl15_gr_12_m_cos ,anl15_ind_12_m_pe)
divide(anl15_gr_12_m_cos ,anl15_s_12_m_cos)
add(anl15_gr_12_m_cos_dn ,anl15_gr_12_m_cos_up)
subtract(anl15_gr_12_m_cos_dn ,anl15_gr_12_m_cos_up)
divide(anl15_gr_12_m_cos_dn ,anl15_gr_12_m_cos_up)
divide(anl15_gr_12_m_cos_dn ,anl15_ind_12_m_cos)
subtract(anl15_gr_12_m_cos_dn ,anl15_ind_12_m_cos_up)
divide(anl15_gr_12_m_cos_dn ,anl15_ind_12_m_ests)
divide(anl15_gr_12_m_cos_dn ,anl15_s_12_m_cos)
subtract(anl15_gr_12_m_cos_dn ,anl15_s_12_m_cos_up)
divide(anl15_gr_12_m_cos_up ,anl15_gr_ltg_ests)
divide(anl15_gr_12_m_cos_up ,anl15_ind_12_m_cos_dn)
subtract(anl15_gr_12_m_cos_up ,anl15_ind_12_m_cos_dn)
divide(anl15_gr_12_m_cos_up ,anl15_ind_12_m_cos)
divide(anl15_gr_12_m_cos_up ,anl15_s_12_m_cos)
divide(anl15_gr_12_m_cos_up ,anl15_s_12_m_cos_dn)
subtract(anl15_gr_12_m_cos_up ,anl15_s_12_m_cos_dn)
add(anl15_gr_12_m_ests_dn ,anl15_gr_18_m_ests_dn)
add(anl15_gr_12_m_ests_dn ,anl15_s_ltg_ests_up)
add(anl15_gr_12_m_ests_up ,anl15_gr_ltg_ests_dn)
add(anl15_gr_12_m_ests_up ,anl15_s_fy3_ests_dn)
multiply(anl15_gr_12_m_gro ,anl15_gr_12_m_pe)
divide(anl15_gr_12_m_mean ,anl15_gr_12_m_pe)
multiply(anl15_gr_12_m_mean ,anl15_gr_12_m_pe)
divide(anl15_gr_12_m_mean ,anl15_gr_12_m_tr_mean)
multiply(anl15_gr_12_m_mean ,anl15_gr_cal_fy0_pe)
divide(anl15_gr_12_m_mean ,anl15_gr_cal_fy0_val)
divide(anl15_gr_12_m_mean ,anl15_gr_cal_fy1_pe)
multiply(anl15_gr_12_m_mean ,anl15_gr_cal_fy1_pe)
divide(anl15_gr_12_m_mean ,anl15_gr_prc)
divide(anl15_gr_12_m_mean ,anl15_ind_12_m_cos)
divide(anl15_gr_12_m_mean ,anl15_ind_12_m_pe)
multiply(anl15_gr_12_m_mean ,anl15_ind_12_m_pe)
divide(anl15_gr_12_m_mean ,anl15_ind_prc)
subtract(anl15_gr_12_m_mean ,anl15_s_12_m_mean)
divide(anl15_gr_12_m_mean ,anl15_s_12_m_pe)
multiply(anl15_gr_12_m_mean ,anl15_s_12_m_pe)
multiply(anl15_gr_12_m_mean ,anl15_s_cal_fy0_pe)
divide(anl15_gr_12_m_mean ,anl15_s_prc)
divide(anl15_gr_12_m_mktcap ,anl15_gr_12_m_total)
subtract(anl15_gr_12_m_mktcap ,anl15_gr_18_m_mktcap)
subtract(anl15_gr_12_m_mktcap ,anl15_gr_ltg_mktcap)
divide(anl15_gr_12_m_mktcap ,anl15_ind_12_m_total)
ts_corr(anl15_gr_12_m_pe ,anl15_ind_12_m_pe, d)
divide(anl15_gr_12_m_st_dev ,anl15_s_12_m_st_dev)
divide(anl15_gr_12_m_total ,anl15_gr_cal_fy1_cos)
multiply(anl15_gr_12_m_total ,anl15_gr_cal_fy1_pe)
divide(anl15_gr_12_m_total ,anl15_gr_prc)
divide(anl15_gr_12_m_total ,anl15_ind_12_m_cos)
divide(anl15_gr_12_m_total ,anl15_ind_12_m_ests)
divide(anl15_gr_12_m_total ,anl15_ind_12_m_pe)
divide(anl15_gr_12_m_total ,anl15_ind_12_m_total)
multiply(anl15_gr_12_m_total ,anl15_ind_cal_fy0_pe)
divide(anl15_gr_12_m_total ,anl15_ind_cal_fy1_cos)
divide(anl15_gr_12_m_total ,anl15_s_12_m_cos)
divide(anl15_gr_12_m_total ,anl15_s_12_m_ests)
divide(anl15_gr_12_m_total ,anl15_s_12_m_total)
multiply(anl15_gr_12_m_total ,anl15_s_cal_fy0_pe)
divide(anl15_gr_12_m_tr_mean ,anl15_gr_cal_fy0_val)
multiply(anl15_gr_12_m_tr_mean ,anl15_gr_cal_fy1_pe)
ts_corr(anl15_gr_12_m_tr_mean ,anl15_gr_ltg_mdn, d)
divide(anl15_gr_12_m_tr_mean ,anl15_gr_prc)
divide(anl15_gr_12_m_tr_mean ,anl15_ind_prc)
multiply(anl15_gr_12_m_tr_mean ,anl15_s_12_m_pe)
divide(anl15_gr_12_m_tr_mean ,anl15_s_prc)
subtract(anl15_gr_18_m_cos ,anl15_gr_18_m_cos_dn)
multiply(anl15_gr_18_m_cos ,anl15_gr_18_m_pe)
multiply(anl15_gr_18_m_cos ,anl15_ind_18_m_pe)
divide(anl15_gr_18_m_cos ,anl15_s_18_m_cos)
divide(anl15_gr_18_m_cos_dn ,anl15_gr_18_m_cos_up)
subtract(anl15_gr_18_m_cos_dn ,anl15_gr_18_m_cos_up)
divide(anl15_gr_18_m_cos_dn ,anl15_gr_18_m_ests)
divide(anl15_gr_18_m_cos_dn ,anl15_ind_18_m_cos)
divide(anl15_gr_18_m_cos_dn ,anl15_ind_18_m_cos_up)
subtract(anl15_gr_18_m_cos_dn ,anl15_ind_18_m_cos_up)
divide(anl15_gr_18_m_cos_dn ,anl15_ind_18_m_ests)
add(anl15_gr_18_m_cos_dn ,anl15_ind_18_m_cos_up)
divide(anl15_gr_18_m_cos_dn ,anl15_s_18_m_cos)
subtract(anl15_gr_18_m_cos_up ,anl15_ind_18_m_cos_dn)
divide(anl15_gr_18_m_cos_up ,anl15_ind_18_m_cos)
divide(anl15_gr_18_m_cos_up ,anl15_ind_18_m_cos_dn)
divide(anl15_gr_18_m_cos_up ,anl15_ind_18_m_ests)
divide(anl15_gr_18_m_cos_up ,anl15_s_18_m_cos)
divide(anl15_gr_18_m_cos_up ,anl15_s_18_m_cos_dn)
subtract(anl15_gr_18_m_cos_up ,anl15_s_18_m_cos_dn)
multiply(anl15_gr_18_m_mean ,anl15_gr_18_m_pe)
divide(anl15_gr_18_m_mean ,anl15_gr_18_m_pe)
multiply(anl15_gr_18_m_mean ,anl15_gr_cal_fy1_pe)
multiply(anl15_gr_18_m_mean ,anl15_gr_cal_fy2_pe)
multiply(anl15_gr_18_m_mean ,anl15_gr_cal_fy3_pe)
divide(anl15_gr_18_m_mean ,anl15_gr_prc)
divide(anl15_gr_18_m_mean ,anl15_ind_18_m_cos)
divide(anl15_gr_18_m_mean ,anl15_ind_18_m_pe)
multiply(anl15_gr_18_m_mean ,anl15_ind_18_m_pe)
divide(anl15_gr_18_m_mean ,anl15_ind_prc)
divide(anl15_gr_18_m_mean ,anl15_s_18_m_cos)
divide(anl15_gr_18_m_mean ,anl15_s_18_m_pe)
multiply(anl15_gr_18_m_mean ,anl15_s_18_m_pe)
divide(anl15_gr_18_m_mean ,anl15_s_cal_fy0_val)
divide(anl15_gr_18_m_mean ,anl15_s_prc)
divide(anl15_gr_18_m_mktcap ,anl15_gr_18_m_total)
divide(anl15_gr_18_m_mktcap ,anl15_ind_18_m_total)
divide(anl15_gr_18_m_mktcap ,anl15_s_18_m_total)
ts_corr(anl15_gr_18_m_pe ,anl15_ind_18_m_pe, d)
ts_corr(anl15_gr_18_m_pe ,anl15_s_18_m_pe, d)
subtract(anl15_gr_18_m_pe ,anl15_s_18_m_pe)
multiply(anl15_gr_18_m_total ,anl15_gr_cal_fy0_pe)
divide(anl15_gr_18_m_total ,anl15_gr_prc)
divide(anl15_gr_18_m_total ,anl15_ind_18_m_cos)
divide(anl15_gr_18_m_total ,anl15_ind_18_m_ests)
multiply(anl15_gr_18_m_total ,anl15_ind_18_m_pe)
divide(anl15_gr_18_m_total ,anl15_s_18_m_cos)
multiply(anl15_gr_18_m_total ,anl15_s_18_m_pe)
divide(anl15_gr_18_m_total ,anl15_s_18_m_total)
divide(anl15_gr_cal_fy0_pe ,anl15_gr_cal_fy0_val)
divide(anl15_gr_cal_fy0_total ,anl15_gr_cal_fy1_cos)
divide(anl15_gr_cal_fy0_total ,anl15_gr_cal_fy2_cos)
divide(anl15_gr_cal_fy0_total ,anl15_gr_cal_fy3_cos)
divide(anl15_gr_cal_fy0_total ,anl15_gr_div_cos)
divide(anl15_gr_cal_fy0_total ,anl15_ind_12_m_cos)
divide(anl15_gr_cal_fy0_total ,anl15_ind_18_m_cos)
divide(anl15_gr_cal_fy0_total ,anl15_ind_cal_fy1_cos)
divide(anl15_gr_cal_fy0_total ,anl15_ind_cal_fy2_cos)
divide(anl15_gr_cal_fy0_total ,anl15_ind_div_cos)
divide(anl15_gr_cal_fy0_total ,anl15_s_12_m_cos)
divide(anl15_gr_cal_fy0_total ,anl15_s_18_m_cos)
divide(anl15_gr_cal_fy0_total ,anl15_s_cal_fy1_cos)
divide(anl15_gr_cal_fy0_total ,anl15_s_cal_fy3_cos)
divide(anl15_gr_cal_fy0_val ,anl15_gr_cal_fy1_cos)
divide(anl15_gr_cal_fy0_val ,anl15_gr_prc)
multiply(anl15_gr_cal_fy0_val ,anl15_ind_cal_fy0_pe)
divide(anl15_gr_cal_fy0_val ,anl15_ind_cal_fy1_cos)
divide(anl15_gr_cal_fy0_val ,anl15_ind_prc)
divide(anl15_gr_cal_fy0_val ,anl15_s_cal_fy1_cos)
divide(anl15_gr_cal_fy1_cos ,anl15_gr_cal_fy1_ests)
add(anl15_gr_cal_fy1_cos ,anl15_gr_cal_fy2_cos)
divide(anl15_gr_cal_fy1_cos ,anl15_ind_cal_fy1_ests)
divide(anl15_gr_cal_fy1_cos ,anl15_s_cal_fy1_ests)
divide(anl15_gr_cal_fy1_ests ,anl15_ind_cal_fy1_cos)
divide(anl15_gr_cal_fy1_ests ,anl15_s_cal_fy1_cos)
divide(anl15_gr_cal_fy1_mean ,anl15_gr_prc)
divide(anl15_gr_cal_fy1_mean ,anl15_ind_cal_fy1_cos)
divide(anl15_gr_cal_fy1_mean ,anl15_s_cal_fy1_cos)
divide(anl15_gr_cal_fy1_mean ,anl15_s_prc)
divide(anl15_gr_cal_fy1_total ,anl15_gr_div_cos)
divide(anl15_gr_cal_fy1_total ,anl15_gr_ltg_cos)
divide(anl15_gr_cal_fy1_total ,anl15_ind_12_m_cos)
divide(anl15_gr_cal_fy1_total ,anl15_ind_18_m_cos)
divide(anl15_gr_cal_fy1_total ,anl15_ind_cal_fy1_cos)
divide(anl15_gr_cal_fy1_total ,anl15_s_12_m_cos)
divide(anl15_gr_cal_fy1_total ,anl15_s_18_m_cos)
divide(anl15_gr_cal_fy1_total ,anl15_s_cal_fy1_cos)
divide(anl15_gr_cal_fy2_cos ,anl15_gr_cal_fy2_ests)
divide(anl15_gr_cal_fy2_cos ,anl15_ind_cal_fy2_ests)
divide(anl15_gr_cal_fy2_mean ,anl15_gr_prc)
divide(anl15_gr_cal_fy2_mean ,anl15_ind_cal_fy2_cos)
divide(anl15_gr_cal_fy2_mean ,anl15_s_cal_fy2_cos)
divide(anl15_gr_cal_fy2_mktcap ,anl15_ind_cal_fy2_cos)
divide(anl15_gr_cal_fy2_total ,anl15_ind_cal_fy2_cos)
divide(anl15_gr_cal_fy2_total ,anl15_s_cal_fy2_cos)
divide(anl15_gr_cal_fy3_cos ,anl15_gr_cal_fy3_ests)
divide(anl15_gr_cal_fy3_cos ,anl15_ind_cal_fy3_ests)
divide(anl15_gr_cal_fy3_cos ,anl15_s_cal_fy3_ests)
divide(anl15_gr_cal_fy3_ests ,anl15_ind_cal_fy3_cos)
divide(anl15_gr_cal_fy3_mean ,anl15_gr_prc)
divide(anl15_gr_cal_fy3_mean ,anl15_ind_cal_fy3_cos)
divide(anl15_gr_cal_fy3_mean ,anl15_s_cal_fy3_cos)
divide(anl15_gr_div_cos ,anl15_s_div_cos)
subtract(anl15_gr_divyld ,anl15_s_divyld)
subtract(anl15_gr_fy1_cos_dn ,anl15_gr_fy1_cos_up)
add(anl15_gr_fy1_cos_dn ,anl15_gr_fy2_cos_dn)
divide(anl15_gr_fy1_cos_dn ,anl15_ind_cal_fy1_cos)
subtract(anl15_gr_fy1_cos_dn ,anl15_ind_fy1_cos_up)
divide(anl15_gr_fy1_cos_dn ,anl15_s_cal_fy1_cos)
divide(anl15_gr_fy1_cos_dn ,anl15_s_cal_fy1_ests)
subtract(anl15_gr_fy1_cos_dn ,anl15_s_fy1_cos_up)
divide(anl15_gr_fy1_cos_up ,anl15_ind_cal_fy1_cos)
divide(anl15_gr_fy1_cos_up ,anl15_ind_cal_fy1_ests)
add(anl15_gr_fy1_cos_up ,anl15_ind_fy1_cos_dn)
subtract(anl15_gr_fy1_cos_up ,anl15_ind_fy1_cos_dn)
divide(anl15_gr_fy1_cos_up ,anl15_s_cal_fy1_cos)
divide(anl15_gr_fy1_cos_up ,anl15_s_cal_fy1_ests)
subtract(anl15_gr_fy1_cos_up ,anl15_s_fy1_cos_dn)
add(anl15_gr_fy1_ests_up ,anl15_s_18_m_ests_dn)
add(anl15_gr_fy2_cos_dn ,anl15_gr_fy2_cos_up)
subtract(anl15_gr_fy2_cos_dn ,anl15_gr_fy2_cos_up)
divide(anl15_gr_fy2_cos_dn ,anl15_ind_cal_fy2_cos)
divide(anl15_gr_fy2_cos_dn ,anl15_ind_cal_fy2_ests)
divide(anl15_gr_fy2_cos_dn ,anl15_ind_fy2_cos_up)
add(anl15_gr_fy2_cos_dn ,anl15_ind_fy2_cos_up)
divide(anl15_gr_fy2_cos_dn ,anl15_s_cal_fy2_ests)
subtract(anl15_gr_fy2_cos_up ,anl15_gr_ltg_cos_dn)
add(anl15_gr_fy2_cos_up ,anl15_ind_fy2_cos_dn)
subtract(anl15_gr_fy2_cos_up ,anl15_ind_fy2_cos_dn)
divide(anl15_gr_fy2_cos_up ,anl15_s_cal_fy2_ests)
subtract(anl15_gr_fy2_cos_up ,anl15_s_fy2_cos_dn)
add(anl15_gr_fy2_ests_up ,anl15_ind_fy3_ests_up)
add(anl15_gr_fy3_cos_dn ,anl15_gr_fy3_cos_up)
subtract(anl15_gr_fy3_cos_dn ,anl15_gr_fy3_cos_up)
divide(anl15_gr_fy3_cos_dn ,anl15_ind_cal_fy3_cos)
divide(anl15_gr_fy3_cos_dn ,anl15_ind_cal_fy3_ests)
subtract(anl15_gr_fy3_cos_dn ,anl15_ind_fy3_cos_up)
add(anl15_gr_fy3_cos_dn ,anl15_ind_fy3_cos_up)
divide(anl15_gr_fy3_cos_dn ,anl15_s_cal_fy3_ests)
divide(anl15_gr_fy3_cos_up ,anl15_ind_cal_fy3_cos)
divide(anl15_gr_fy3_cos_up ,anl15_ind_cal_fy3_ests)
subtract(anl15_gr_fy3_cos_up ,anl15_ind_fy3_cos_dn)
divide(anl15_gr_fy3_cos_up ,anl15_s_cal_fy3_ests)
subtract(anl15_gr_fy3_cos_up ,anl15_s_fy3_cos_dn)
add(anl15_gr_fy3_ests_up ,anl15_ind_fy1_ests_up)
divide(anl15_gr_ltg_cos ,anl15_gr_ltg_ests)
divide(anl15_gr_ltg_cos ,anl15_ind_ltg_ests)
divide(anl15_gr_ltg_cos ,anl15_s_ltg_cos)
add(anl15_gr_ltg_cos_dn ,anl15_gr_ltg_cos_up)
subtract(anl15_gr_ltg_cos_dn ,anl15_gr_ltg_cos_up)
divide(anl15_gr_ltg_cos_dn ,anl15_gr_ltg_ests)
divide(anl15_gr_ltg_cos_dn ,anl15_ind_ltg_ests)
divide(anl15_gr_ltg_cos_dn ,anl15_s_ltg_cos)
divide(anl15_gr_ltg_cos_dn ,anl15_s_ltg_ests)
divide(anl15_gr_ltg_cos_up ,anl15_ind_ltg_cos)
subtract(anl15_gr_ltg_cos_up ,anl15_ind_ltg_cos_dn)
divide(anl15_gr_ltg_cos_up ,anl15_ind_ltg_ests)
divide(anl15_gr_ltg_cos_up ,anl15_s_ltg_cos)
divide(anl15_gr_ltg_cos_up ,anl15_s_ltg_ests)
divide(anl15_gr_ltg_ests ,anl15_ind_ltg_cos)
divide(anl15_gr_ltg_ests ,anl15_s_ltg_cos)
add(anl15_gr_ltg_ests_dn ,anl15_ind_fy3_ests_up)
add(anl15_gr_ltg_ests_dn ,anl15_ind_ltg_ests_up)
add(anl15_gr_ltg_ests_up ,anl15_ind_fy2_ests_up)
divide(anl15_gr_ltg_mdn ,anl15_gr_prc)
divide(anl15_gr_ltg_mdn ,anl15_ind_ltg_cos)
divide(anl15_gr_ltg_mdn ,anl15_s_ltg_cos)
divide(anl15_gr_prc ,anl15_ind_12_m_cos)
divide(anl15_gr_prc ,anl15_ind_12_m_mean)
divide(anl15_gr_prc ,anl15_ind_12_m_tr_mean)
divide(anl15_gr_prc ,anl15_s_12_m_cos)
divide(anl15_gr_prc ,anl15_s_12_m_mean)
multiply(anl15_ind_12_m_1m_chg ,anl15_ind_12_m_pe)
ts_corr(anl15_ind_12_m_1m_chg ,anl15_s_12_m_1m_chg, d)
ts_corr(anl15_ind_12_m_3m_chg ,anl15_ind_12_m_6m_chg, d)
ts_corr(anl15_ind_12_m_6m_chg ,anl15_s_12_m_6m_chg, d)
subtract(anl15_ind_12_m_cos ,anl15_ind_12_m_cos_dn)
multiply(anl15_ind_12_m_cos ,anl15_ind_12_m_pe)
add(anl15_ind_12_m_cos ,anl15_ind_18_m_cos)
add(anl15_ind_12_m_cos_dn ,anl15_ind_12_m_cos_up)
divide(anl15_ind_12_m_cos_dn ,anl15_ind_12_m_cos_up)
subtract(anl15_ind_12_m_cos_dn ,anl15_ind_12_m_cos_up)
divide(anl15_ind_12_m_cos_dn ,anl15_ind_12_m_ests)
divide(anl15_ind_12_m_cos_dn ,anl15_ind_cal_fy1_cos)
divide(anl15_ind_12_m_cos_dn ,anl15_ind_cal_fy1_ests)
divide(anl15_ind_12_m_cos_dn ,anl15_s_12_m_cos)
divide(anl15_ind_12_m_cos_up ,anl15_ind_12_m_ests)
multiply(anl15_ind_12_m_cos_up ,anl15_ind_12_m_pe)
subtract(anl15_ind_12_m_cos_up ,anl15_ind_fy1_cos_dn)
subtract(anl15_ind_12_m_cos_up ,anl15_ind_fy2_cos_dn)
subtract(anl15_ind_12_m_cos_up ,anl15_ind_ltg_cos_dn)
divide(anl15_ind_12_m_cos_up ,anl15_ind_ltg_ests)
divide(anl15_ind_12_m_cos_up ,anl15_s_12_m_cos)
subtract(anl15_ind_12_m_cos_up ,anl15_s_12_m_cos_dn)
divide(anl15_ind_12_m_ests ,anl15_s_12_m_ests)
multiply(anl15_ind_12_m_gro ,anl15_ind_12_m_pe)
divide(anl15_ind_12_m_mean ,anl15_ind_12_m_pe)
multiply(anl15_ind_12_m_mean ,anl15_ind_12_m_pe)
divide(anl15_ind_12_m_mean ,anl15_ind_12_m_tr_mean)
divide(anl15_ind_12_m_mean ,anl15_ind_cal_fy0_pe)
multiply(anl15_ind_12_m_mean ,anl15_ind_cal_fy0_pe)
subtract(anl15_ind_12_m_mean ,anl15_ind_cal_fy0_val)
multiply(anl15_ind_12_m_mean ,anl15_ind_cal_fy1_pe)
multiply(anl15_ind_12_m_mean ,anl15_ind_cal_fy2_pe)
multiply(anl15_ind_12_m_mean ,anl15_ind_cal_fy3_pe)
divide(anl15_ind_12_m_mean ,anl15_ind_prc)
multiply(anl15_ind_12_m_mean ,anl15_ind_prc)
divide(anl15_ind_12_m_mean ,anl15_s_12_m_cos)
divide(anl15_ind_12_m_mean ,anl15_s_12_m_pe)
multiply(anl15_ind_12_m_mean ,anl15_s_12_m_pe)
multiply(anl15_ind_12_m_mean ,anl15_s_cal_fy0_pe)
multiply(anl15_ind_12_m_mean ,anl15_s_cal_fy1_pe)
divide(anl15_ind_12_m_mean ,anl15_s_prc)
divide(anl15_ind_12_m_mktcap ,anl15_ind_12_m_total)
subtract(anl15_ind_12_m_mktcap ,anl15_ind_ltg_mktcap)
divide(anl15_ind_12_m_mktcap ,anl15_s_12_m_total)
subtract(anl15_ind_12_m_pe ,anl15_ind_18_m_pe)
ts_corr(anl15_ind_12_m_pe ,anl15_ind_18_m_pe, d)
divide(anl15_ind_12_m_pe ,anl15_s_12_m_pe)
ts_corr(anl15_ind_12_m_pe ,anl15_s_12_m_pe, d)
divide(anl15_ind_12_m_st_dev ,anl15_s_12_m_st_dev)
ts_corr(anl15_ind_12_m_st_dev ,anl15_s_12_m_st_dev, d)
multiply(anl15_ind_12_m_total ,anl15_ind_cal_fy0_pe)
divide(anl15_ind_12_m_total ,anl15_ind_cal_fy0_total)
divide(anl15_ind_12_m_total ,anl15_ind_cal_fy1_cos)
multiply(anl15_ind_12_m_total ,anl15_ind_cal_fy2_pe)
divide(anl15_ind_12_m_total ,anl15_ind_div_cos)
divide(anl15_ind_12_m_total ,anl15_ind_prc)
divide(anl15_ind_12_m_total ,anl15_s_12_m_cos)
divide(anl15_ind_12_m_total ,anl15_s_12_m_ests)
multiply(anl15_ind_12_m_total ,anl15_s_12_m_pe)
divide(anl15_ind_12_m_total ,anl15_s_12_m_total)
multiply(anl15_ind_12_m_total ,anl15_s_cal_fy0_pe)
multiply(anl15_ind_12_m_tr_mean ,anl15_ind_cal_fy0_pe)
multiply(anl15_ind_12_m_tr_mean ,anl15_ind_cal_fy1_pe)
multiply(anl15_ind_12_m_tr_mean ,anl15_ind_cal_fy2_pe)
ts_corr(anl15_ind_12_m_tr_mean ,anl15_ind_divyld, d)
ts_corr(anl15_ind_12_m_tr_mean ,anl15_ind_ltg_mdn, d)
multiply(anl15_ind_12_m_tr_mean ,anl15_ind_prc)
divide(anl15_ind_12_m_tr_mean ,anl15_ind_prc)
ts_corr(anl15_ind_12_m_tr_mean ,anl15_ind_prc, d)
multiply(anl15_ind_12_m_tr_mean ,anl15_s_cal_fy0_pe)
divide(anl15_ind_12_m_tr_mean ,anl15_s_prc)
multiply(anl15_ind_18_m_3m_chg ,anl15_ind_18_m_pe)
multiply(anl15_ind_18_m_cos ,anl15_ind_18_m_pe)
multiply(anl15_ind_18_m_cos ,anl15_s_18_m_pe)
subtract(anl15_ind_18_m_cos_dn ,anl15_ind_18_m_cos_up)
divide(anl15_ind_18_m_cos_dn ,anl15_ind_18_m_ests)
divide(anl15_ind_18_m_cos_dn ,anl15_ind_18_m_cos_up)
add(anl15_ind_18_m_cos_dn ,anl15_ind_18_m_cos_up)
divide(anl15_ind_18_m_cos_dn ,anl15_s_18_m_cos)
subtract(anl15_ind_18_m_cos_dn ,anl15_s_18_m_cos_up)
divide(anl15_ind_18_m_cos_up ,anl15_ind_18_m_ests)
multiply(anl15_ind_18_m_cos_up ,anl15_ind_18_m_pe)
subtract(anl15_ind_18_m_cos_up ,anl15_ind_fy2_cos_dn)
divide(anl15_ind_18_m_cos_up ,anl15_s_18_m_cos)
subtract(anl15_ind_18_m_cos_up ,anl15_s_18_m_cos_dn)
add(anl15_ind_18_m_ests_dn ,anl15_ind_fy1_ests_up)
add(anl15_ind_18_m_ests_dn ,anl15_s_18_m_ests_dn)
add(anl15_ind_18_m_ests_up ,anl15_s_12_m_ests_dn)
ts_corr(anl15_ind_18_m_mean ,anl15_ind_18_m_pe, d)
multiply(anl15_ind_18_m_mean ,anl15_ind_18_m_pe)
divide(anl15_ind_18_m_mean ,anl15_ind_18_m_pe)
divide(anl15_ind_18_m_mean ,anl15_ind_cal_fy0_pe)
multiply(anl15_ind_18_m_mean ,anl15_ind_cal_fy0_pe)
multiply(anl15_ind_18_m_mean ,anl15_ind_cal_fy1_pe)
multiply(anl15_ind_18_m_mean ,anl15_ind_cal_fy2_pe)
multiply(anl15_ind_18_m_mean ,anl15_ind_cal_fy3_pe)
divide(anl15_ind_18_m_mean ,anl15_ind_prc)
ts_corr(anl15_ind_18_m_mean ,anl15_ind_prc, d)
multiply(anl15_ind_18_m_mean ,anl15_ind_prc)
divide(anl15_ind_18_m_mean ,anl15_s_18_m_cos)
divide(anl15_ind_18_m_mean ,anl15_s_18_m_pe)
multiply(anl15_ind_18_m_mean ,anl15_s_18_m_pe)
divide(anl15_ind_18_m_mean ,anl15_s_prc)
divide(anl15_ind_18_m_mktcap ,anl15_ind_18_m_total)
divide(anl15_ind_18_m_mktcap ,anl15_s_18_m_total)
divide(anl15_ind_18_m_pe ,anl15_s_18_m_pe)
ts_corr(anl15_ind_18_m_pe ,anl15_s_18_m_pe, d)
multiply(anl15_ind_18_m_total ,anl15_ind_cal_fy0_pe)
multiply(anl15_ind_18_m_total ,anl15_ind_cal_fy1_pe)
divide(anl15_ind_18_m_total ,anl15_ind_ltg_cos)
divide(anl15_ind_18_m_total ,anl15_ind_prc)
divide(anl15_ind_cal_fy0_pe ,anl15_ind_cal_fy0_val)
divide(anl15_ind_cal_fy0_total ,anl15_ind_cal_fy1_cos)
divide(anl15_ind_cal_fy0_total ,anl15_ind_cal_fy2_cos)
divide(anl15_ind_cal_fy0_total ,anl15_ind_cal_fy3_cos)
divide(anl15_ind_cal_fy0_total ,anl15_ind_div_cos)
divide(anl15_ind_cal_fy0_total ,anl15_ind_ltg_cos)
divide(anl15_ind_cal_fy0_total ,anl15_s_18_m_cos)
divide(anl15_ind_cal_fy0_total ,anl15_s_cal_fy1_cos)
divide(anl15_ind_cal_fy0_total ,anl15_s_cal_fy2_cos)
divide(anl15_ind_cal_fy0_total ,anl15_s_cal_fy3_cos)
divide(anl15_ind_cal_fy0_val ,anl15_ind_cal_fy1_cos)
divide(anl15_ind_cal_fy0_val ,anl15_ind_cal_fy2_mean)
subtract(anl15_ind_cal_fy0_val ,anl15_ind_ltg_mdn)
divide(anl15_ind_cal_fy0_val ,anl15_ind_prc)
divide(anl15_ind_cal_fy0_val ,anl15_s_cal_fy0_val)
subtract(anl15_ind_cal_fy0_val ,anl15_s_cal_fy0_val)
divide(anl15_ind_cal_fy0_val ,anl15_s_cal_fy1_cos)
divide(anl15_ind_cal_fy1_cos ,anl15_ind_cal_fy1_ests)
add(anl15_ind_cal_fy1_cos ,anl15_ind_cal_fy2_cos)
divide(anl15_ind_cal_fy1_cos ,anl15_s_cal_fy1_ests)
divide(anl15_ind_cal_fy1_ests ,anl15_s_cal_fy1_cos)
divide(anl15_ind_cal_fy1_mean ,anl15_ind_cal_fy1_pe)
divide(anl15_ind_cal_fy1_mean ,anl15_ind_prc)
divide(anl15_ind_cal_fy1_mean ,anl15_s_cal_fy1_cos)
divide(anl15_ind_cal_fy1_mean ,anl15_s_cal_fy1_mean)
divide(anl15_ind_cal_fy1_mktcap ,anl15_ind_cal_fy1_total)
divide(anl15_ind_cal_fy1_total ,anl15_ind_div_cos)
divide(anl15_ind_cal_fy1_total ,anl15_ind_ltg_cos)
divide(anl15_ind_cal_fy1_total ,anl15_s_12_m_cos)
divide(anl15_ind_cal_fy1_total ,anl15_s_cal_fy1_cos)
divide(anl15_ind_cal_fy1_total ,anl15_s_ltg_cos)
divide(anl15_ind_cal_fy2_cos ,anl15_ind_cal_fy2_ests)
divide(anl15_ind_cal_fy2_cos ,anl15_s_cal_fy2_ests)
divide(anl15_ind_cal_fy2_mean ,anl15_ind_prc)
divide(anl15_ind_cal_fy2_mean ,anl15_s_cal_fy2_mean)
divide(anl15_ind_cal_fy2_mean ,anl15_s_cal_fy2_cos)
divide(anl15_ind_cal_fy2_total ,anl15_ind_ltg_cos)
divide(anl15_ind_cal_fy2_total ,anl15_s_cal_fy2_cos)
divide(anl15_ind_cal_fy3_cos ,anl15_ind_cal_fy3_ests)
subtract(anl15_ind_cal_fy3_cos ,anl15_ind_fy3_cos_dn)
divide(anl15_ind_cal_fy3_cos ,anl15_s_cal_fy3_ests)
divide(anl15_ind_cal_fy3_mean ,anl15_s_cal_fy3_cos)
divide(anl15_ind_cal_fy3_total ,anl15_ind_div_cos)
divide(anl15_ind_cal_fy3_total ,anl15_ind_ltg_cos)
divide(anl15_ind_cal_fy3_total ,anl15_s_18_m_cos)
divide(anl15_ind_cal_fy3_total ,anl15_s_cal_fy3_cos)
divide(anl15_ind_cal_fy3_total ,anl15_s_ltg_cos)
add(anl15_ind_div_cos ,anl15_s_div_cos)
add(anl15_ind_fy1_cos_dn ,anl15_ind_fy1_cos_up)
subtract(anl15_ind_fy1_cos_dn ,anl15_ind_fy1_cos_up)
divide(anl15_ind_fy1_cos_dn ,anl15_s_cal_fy1_cos)
divide(anl15_ind_fy1_cos_dn ,anl15_s_cal_fy1_ests)
subtract(anl15_ind_fy1_cos_dn ,anl15_s_fy1_cos_up)
add(anl15_ind_fy1_cos_up ,anl15_ind_fy2_cos_up)
divide(anl15_ind_fy1_cos_up ,anl15_ind_ltg_cos)
subtract(anl15_ind_fy1_cos_up ,anl15_ind_ltg_cos_dn)
divide(anl15_ind_fy1_cos_up ,anl15_s_cal_fy1_cos)
divide(anl15_ind_fy1_cos_up ,anl15_s_cal_fy1_ests)
add(anl15_ind_fy1_cos_up ,anl15_s_fy1_cos_dn)
subtract(anl15_ind_fy1_cos_up ,anl15_s_fy1_cos_dn)
add(anl15_ind_fy1_ests_dn ,anl15_ind_fy1_ests_up)
add(anl15_ind_fy1_ests_dn ,anl15_s_12_m_ests_dn)
add(anl15_ind_fy1_ests_dn ,anl15_s_fy1_ests_dn)
add(anl15_ind_fy1_ests_up ,anl15_ind_fy3_ests_dn)
subtract(anl15_ind_fy2_cos_dn ,anl15_ind_fy2_cos_up)
divide(anl15_ind_fy2_cos_dn ,anl15_s_cal_fy2_cos)
subtract(anl15_ind_fy2_cos_up ,anl15_ind_ltg_cos_dn)
divide(anl15_ind_fy2_cos_up ,anl15_s_cal_fy2_ests)
subtract(anl15_ind_fy2_cos_up ,anl15_s_fy2_cos_dn)
add(anl15_ind_fy2_ests_dn ,anl15_s_fy1_ests_up)
add(anl15_ind_fy2_ests_up ,anl15_ind_ltg_ests_dn)
add(anl15_ind_fy2_ests_up ,anl15_s_18_m_ests_up)
subtract(anl15_ind_fy3_cos_dn ,anl15_ind_fy3_cos_up)
divide(anl15_ind_fy3_cos_dn ,anl15_s_cal_fy3_cos)
divide(anl15_ind_fy3_cos_up ,anl15_s_cal_fy3_cos)
divide(anl15_ind_fy3_cos_up ,anl15_s_cal_fy3_ests)
subtract(anl15_ind_fy3_cos_up ,anl15_s_fy3_cos_dn)
ts_corr(anl15_ind_hist_eps_gro ,anl15_s_hist_eps_gro, d)
divide(anl15_ind_ltg_1m_chg ,anl15_ind_ltg_cos)
subtract(anl15_ind_ltg_cos ,anl15_ind_ltg_cos_dn)
divide(anl15_ind_ltg_cos ,anl15_ind_ltg_ests)
add(anl15_ind_ltg_cos_dn ,anl15_ind_ltg_cos_up)
subtract(anl15_ind_ltg_cos_dn ,anl15_ind_ltg_cos_up)
divide(anl15_ind_ltg_cos_dn ,anl15_ind_ltg_ests)
divide(anl15_ind_ltg_cos_dn ,anl15_s_ltg_ests)
divide(anl15_ind_ltg_cos_up ,anl15_ind_ltg_ests)
divide(anl15_ind_ltg_cos_up ,anl15_s_ltg_ests)
add(anl15_ind_ltg_ests_dn ,anl15_ind_ltg_ests_up)
divide(anl15_ind_ltg_mdn ,anl15_ind_prc)
multiply(anl15_s_12_m_cos ,anl15_s_12_m_pe)
multiply(anl15_s_12_m_cos ,anl15_s_cal_fy0_pe)
add(anl15_s_12_m_cos_dn ,anl15_s_12_m_cos_up)
divide(anl15_s_12_m_cos_dn ,anl15_s_12_m_cos_up)
subtract(anl15_s_12_m_cos_dn ,anl15_s_12_m_cos_up)
divide(anl15_s_12_m_cos_dn ,anl15_s_12_m_ests)
add(anl15_s_12_m_cos_dn ,anl15_s_ltg_cos_dn)
divide(anl15_s_12_m_cos_up ,anl15_s_12_m_ests)
multiply(anl15_s_12_m_cos_up ,anl15_s_12_m_pe)
subtract(anl15_s_12_m_cos_up ,anl15_s_fy2_cos_dn)
subtract(anl15_s_12_m_cos_up ,anl15_s_fy3_cos_dn)
subtract(anl15_s_12_m_cos_up ,anl15_s_ltg_cos_dn)
add(anl15_s_12_m_cos_up ,anl15_s_ltg_cos_up)
divide(anl15_s_12_m_cos_up ,anl15_s_ltg_ests)
multiply(anl15_s_12_m_gro ,anl15_s_12_m_pe)
multiply(anl15_s_12_m_mean ,anl15_s_12_m_pe)
divide(anl15_s_12_m_mean ,anl15_s_12_m_pe)
subtract(anl15_s_12_m_mean ,anl15_s_12_m_tr_mean)
divide(anl15_s_12_m_mean ,anl15_s_12_m_tr_mean)
ts_corr(anl15_s_12_m_mean ,anl15_s_12_m_tr_mean, d)
divide(anl15_s_12_m_mean ,anl15_s_cal_fy0_pe)
multiply(anl15_s_12_m_mean ,anl15_s_cal_fy0_pe)
divide(anl15_s_12_m_mean ,anl15_s_cal_fy0_val)
subtract(anl15_s_12_m_mean ,anl15_s_cal_fy0_val)
multiply(anl15_s_12_m_mean ,anl15_s_cal_fy1_pe)
multiply(anl15_s_12_m_mean ,anl15_s_cal_fy2_pe)
divide(anl15_s_12_m_mean ,anl15_s_prc)
ts_corr(anl15_s_12_m_mean ,anl15_s_prc, d)
divide(anl15_s_12_m_mean ,anl15_s_ltg_mdn)
divide(anl15_s_12_m_mktcap ,anl15_s_12_m_total)
multiply(anl15_s_12_m_mean ,anl15_s_prc)
subtract(anl15_s_12_m_mktcap ,anl15_s_18_m_mktcap)
subtract(anl15_s_12_m_mktcap ,anl15_s_ltg_mktcap)
ts_corr(anl15_s_12_m_pe ,anl15_s_18_m_pe, d)
multiply(anl15_s_12_m_total ,anl15_s_cal_fy0_pe)
divide(anl15_s_12_m_total ,anl15_s_cal_fy1_cos)
multiply(anl15_s_12_m_total ,anl15_s_cal_fy1_pe)
divide(anl15_s_12_m_total ,anl15_s_cal_fy2_cos)
divide(anl15_s_12_m_total ,anl15_s_ltg_cos)
divide(anl15_s_12_m_total ,anl15_s_prc)
multiply(anl15_s_12_m_tr_mean ,anl15_s_cal_fy0_pe)
multiply(anl15_s_12_m_tr_mean ,anl15_s_cal_fy1_pe)
divide(anl15_s_12_m_tr_mean ,anl15_s_prc)
divide(anl15_s_18_m_1m_chg ,anl15_s_18_m_pe)
subtract(anl15_s_18_m_cos ,anl15_s_18_m_cos_dn)
multiply(anl15_s_18_m_cos ,anl15_s_18_m_pe)
divide(anl15_s_18_m_cos_dn ,anl15_s_18_m_cos_up)
add(anl15_s_18_m_cos_dn ,anl15_s_18_m_cos_up)
subtract(anl15_s_18_m_cos_dn ,anl15_s_18_m_cos_up)
divide(anl15_s_18_m_cos_dn ,anl15_s_18_m_ests)
add(anl15_s_18_m_cos_dn ,anl15_s_ltg_cos_dn)
divide(anl15_s_18_m_cos_up ,anl15_s_18_m_ests)
multiply(anl15_s_18_m_cos_up ,anl15_s_18_m_pe)
subtract(anl15_s_18_m_cos_up ,anl15_s_fy2_cos_dn)
add(anl15_s_18_m_ests_dn ,anl15_s_18_m_ests_up)
add(anl15_s_18_m_ests_up ,anl15_s_fy1_ests_dn)
multiply(anl15_s_18_m_gro ,anl15_s_18_m_pe)
multiply(anl15_s_18_m_mean ,anl15_s_18_m_pe)
divide(anl15_s_18_m_mean ,anl15_s_cal_fy0_pe)
multiply(anl15_s_18_m_mean ,anl15_s_cal_fy0_pe)
divide(anl15_s_18_m_mean ,anl15_s_18_m_pe)
multiply(anl15_s_18_m_mean ,anl15_s_cal_fy1_pe)
multiply(anl15_s_18_m_mean ,anl15_s_cal_fy2_pe)
multiply(anl15_s_18_m_mean ,anl15_s_cal_fy3_pe)
ts_corr(anl15_s_18_m_mean ,anl15_s_prc, d)
divide(anl15_s_18_m_mean ,anl15_s_prc)
divide(anl15_s_18_m_mktcap ,anl15_s_18_m_total)
multiply(anl15_s_18_m_total ,anl15_s_cal_fy3_pe)
divide(anl15_s_18_m_total ,anl15_s_ltg_cos)
divide(anl15_s_18_m_total ,anl15_s_prc)
divide(anl15_s_cal_fy0_pe ,anl15_s_cal_fy0_val)
divide(anl15_s_cal_fy0_total ,anl15_s_cal_fy1_ests)
divide(anl15_s_cal_fy0_total ,anl15_s_cal_fy1_cos)
divide(anl15_s_cal_fy0_total ,anl15_s_cal_fy1_mktcap)
divide(anl15_s_cal_fy0_total ,anl15_s_cal_fy2_cos)
divide(anl15_s_cal_fy0_total ,anl15_s_cal_fy2_total)
divide(anl15_s_cal_fy0_total ,anl15_s_cal_fy3_cos)
divide(anl15_s_cal_fy0_total ,anl15_s_div_cos)
divide(anl15_s_cal_fy0_total ,anl15_s_ltg_cos)
divide(anl15_s_cal_fy0_val ,anl15_s_cal_fy1_cos)
divide(anl15_s_cal_fy0_val ,anl15_s_prc)
divide(anl15_s_cal_fy1_cos ,anl15_s_cal_fy1_ests)
add(anl15_s_cal_fy1_cos ,anl15_s_cal_fy2_cos)
divide(anl15_s_cal_fy1_mean ,anl15_s_prc)
divide(anl15_s_cal_fy1_mktcap ,anl15_s_cal_fy1_total)
divide(anl15_s_cal_fy1_total ,anl15_s_div_cos)
divide(anl15_s_cal_fy1_total ,anl15_s_ltg_cos)
divide(anl15_s_cal_fy2_cos ,anl15_s_cal_fy2_ests)
divide(anl15_s_cal_fy2_mean ,anl15_s_prc)
divide(anl15_s_cal_fy2_total ,anl15_s_div_cos)
divide(anl15_s_cal_fy2_total ,anl15_s_ltg_cos)
divide(anl15_s_cal_fy3_cos ,anl15_s_cal_fy3_ests)
multiply(anl15_s_cal_fy3_mean ,anl15_s_cal_fy3_pe)
divide(anl15_s_cal_fy3_mean ,anl15_s_prc)
divide(anl15_s_cal_fy3_total ,anl15_s_div_cos)
divide(anl15_s_cal_fy3_total ,anl15_s_ltg_cos)
subtract(anl15_s_fy1_cos_dn ,anl15_s_fy1_cos_up)
subtract(anl15_s_fy1_cos_up ,anl15_s_ltg_cos_dn)
add(anl15_s_fy1_ests_up ,anl15_s_fy3_ests_up)
subtract(anl15_s_fy2_cos_dn ,anl15_s_fy2_cos_up)
subtract(anl15_s_fy2_cos_up ,anl15_s_ltg_cos_dn)
subtract(anl15_s_fy3_cos_dn ,anl15_s_fy3_cos_up)
subtract(anl15_s_fy3_cos_up ,anl15_s_ltg_cos_dn)
subtract(anl15_s_ltg_cos ,anl15_s_ltg_cos_dn)
subtract(anl15_s_ltg_cos_dn ,anl15_s_ltg_cos_up)
divide(anl15_s_ltg_cos_dn ,anl15_s_ltg_ests)
divide(anl15_s_ltg_cos_up ,anl15_s_ltg_ests)
divide(anl15_s_ltg_mdn ,anl15_s_prc)