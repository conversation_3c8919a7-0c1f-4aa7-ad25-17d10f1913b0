close / anl14_mean_eps_fp4
close / (equity / sharesout)
(close * sharesout) / anl14_mean_revenue_fp4
debt / equity
assets_current / liabilities_current
(assets_current - inventory) / liabilities_current
(revenue - cogs) / revenue
operating_income / revenue
earnings / revenue
earnings / assets
earnings / equity
anl11_e - anl11_g
anl11_esg_totalcor - anl11_gse
snt_social_value * snt_social_volume
ma5 - ma20
ma5 / ma20
rsi14 - 50
close / vwap
volume / adv20
anl11_creptcesgergse * snt_social_value
anl14_mean_div_fp4 / close
dividends / earnings
volume / sharesout
returns - rel_ret_comp
returns - rel_ret_cust
snt_buzz * snt_value
snt_buzz + snt_value
cumret10
cumret20
ma50 - ma200
ma50 / ma200
high - low
(close - low) / (high - low)
close * volume
(high - low) / close
anl11_e / anl11_g
anl11_e / anl11_gse
anl11_e - anl11_gse
anl14_high_eps_fp1 - anl14_low_eps_fp1
anl14_high_revenue_fp1 - anl14_low_revenue_fp1
(anl14_high_eps_fp1 - anl14_low_eps_fp1) / anl14_mean_eps_fp1
cumret5 / cumret20
(ma5 / ma20) - 1
anl11_e / anl11_pme
anl11_g / anl11_e
anl11_esg_totalcor - anl11_e
returns - rel_ret_all
sales_growth * snt_social_value
anl11_gse * return_equity
anl14_high_eps_fp1 / anl14_low_eps_fp1
anl14_high_revenue_fp1 / anl14_low_revenue_fp1
(rtk_ptg_mean - close) / close
(rtk_ptg_high - close) / close
(rtk_ptg_low - close) / close
rtk_ptg_stddev / rtk_ptg_mean
revenue / assets
cogs / inventory
revenue / receivables
cogs / payables
debt / assets
ebit / interest_expense
rsi14 * snt_social_value
volume * snt_social_volume
volume / snt_social_volume
returns / (1 + snt_social_value)
((close * sharesout) + debt - cash) / anl14_mean_ebitda_fp4
close / anl14_mean_cfps_fp4
anl14_mean_eps_fp4 / close
sales_growth / return_equity
sales_growth - return_equity
returns / (1 + rsk60_crowding)
assets / equity
(close - open) / open
high / low
anl11_gse * (return_equity + return_assets)
cumret5 * (volume / adv20)
anl11_creptcesgergse * cumret5
rsk82_raw_m1d_tni_su_fte * returns
(anl14_mean_eps_fp4 / close) + ((equity / sharesout) / close)
(sales_growth + return_equity) / 2
return_equity + return_assets + (earnings / revenue)
ma10 - ma50
ma10 / ma50
rsk60_crowding * returns
rsk82_raw_m1g_tni_su_fte
sales_growth + return_equity
anl11_esg_totalcor - anl11_gse
close * snt_social_value
rsi14 + (ma5 / ma20)
volume / adv20
returns - rel_ret_part
revenue / anl11_gse