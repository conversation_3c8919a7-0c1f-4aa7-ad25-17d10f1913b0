# !/usr/bin/env python3
# -*- coding: utf-8 -*-

from machine_lib_concurrent import *
from datetime import datetime, timedelta
import configparser
import logging
import os

# 配置日志记录
def setup_logging():
    # 创建logs目录（如果不存在）
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 设置日志文件名（使用当前时间）
    log_file = os.path.join(log_dir, f"view_alpha_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log")
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info("日志系统初始化完成")
    return log_file

# 读取配置文件
def load_config():
    config = configparser.ConfigParser()
    config.read('config_check.ini')
    return config

# 1 获取当前执行时间（根据配置决定是否使用动态日期）
def get_current_time(config):
    logging.info("1 获取时间")
    if config.getboolean('dates', 'use_dynamic_dates'):
        current_date = datetime.today()
        end_date = current_date.strftime("%m-%d")
        start_date = (current_date - timedelta(days=1)).strftime("%m-%d")
    else:
        start_date = config.get('dates', 'start_date')
        end_date = config.get('dates', 'end_date')

    logging.info(f"start:{start_date}, end:{end_date}")
    return start_date, end_date

# 2 从配置文件获取参数
def get_config_check(config):
    logging.info("2 获取配置参数")
    # 获取各个配置参数并存储在变量中
    dataset_id = config.get('parameters', 'dataset_id')
    region = config.get('parameters', 'region')
    universe = config.get('parameters', 'universe')
    delay = config.getint('parameters', 'delay')
    neutralization = config.get('parameters', 'neutralization')
    prefix = config.get('parameters', 'prefix')
    submit_sharp_th = config.getfloat('parameters', 'submit_sharp_th')
    submit_fitness_th = config.getfloat('parameters', 'submit_fitness_th')
    submit_margin_th = config.getfloat('parameters', 'submit_margin_th')

    # 获取其他参数
    alpha_num_submit = config.getint('parameters', 'alpha_num_submit')

    # 打印所有获取到的参数及其值
    logging.info(f"dataset_id: {dataset_id}")
    logging.info(f"region: {region}")
    logging.info(f"universe: {universe}")
    logging.info(f"delay: {delay}")
    logging.info(f"neutralization: {neutralization}")
    logging.info(f"prefix: {prefix}")
    logging.info(f"alpha_num_submit: {alpha_num_submit}")
    logging.info(f"submit_sharp_th: {submit_sharp_th}")
    logging.info(f"submit_fitness_th: {submit_fitness_th}")
    logging.info(f"submit_margin_th: {submit_margin_th}")

    # 返回参数字典
    return {
        'dataset_id': dataset_id,
        'region': region,
        'universe': universe,
        'delay': delay,
        'neutralization': neutralization,
        'prefix': prefix,
        'alpha_num_submit': alpha_num_submit,
        'submit_sharp_th': submit_sharp_th,
        'submit_fitness_th': submit_fitness_th,
        'submit_margin_th': submit_margin_th
    }

def get_submit_alpha(start_date, end_date, region, alpha_num, sharpe_th, fitness_th, margin_th):
    logging.info("11 获取可提交的Alpha")
    # 1.58 sharpe, 1 fitness, "submit"参数
    th_tracker = get_alphas(start_date, end_date, region, alpha_num, "submit", sharpe_th, fitness_th, margin_th)

    ## 将get的alpha的id取出至stone_bag，用api check submission
    stone_bag = []
    for alpha in th_tracker:
        stone_bag.append(alpha[0])
    logging.info(f"count stone_bag: {len(stone_bag)}")

    gold_bag = []
    check_submission(stone_bag, gold_bag, 0)

    # 打印可提交的alpha信息并按sharpe排序，在网页上找到alpha手动提交
    logging.info("12 打印可提交的Alpha")
    view_alphas(gold_bag)

if __name__ == '__main__':
    # 设置日志
    log_file = setup_logging()
    logging.info(f"日志文件路径: {log_file}")
    
    # 加载配置
    config = load_config()
    # 获取日期参数
    start_date, end_date = get_current_time(config)
    # 从配置获取参数
    params = get_config_check(config)
    
    # 提取需要的参数
    region = params['region']
    alpha_num_submit = params['alpha_num_submit']
    submit_sharp_th = params['submit_sharp_th']
    submit_fitness_th = params['submit_fitness_th']
    submit_margin_th = params['submit_margin_th']
    
    # 打印可提交alpha
    get_submit_alpha(start_date, end_date, region, alpha_num_submit, submit_sharp_th, submit_fitness_th, submit_margin_th)