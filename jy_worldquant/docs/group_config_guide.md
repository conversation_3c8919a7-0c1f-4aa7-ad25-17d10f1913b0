# Alpha Factory 分组配置使用指南

## 概述

Alpha Factory 现在支持灵活的分组配置系统，提供了多种具有经济学意义的分组类别。这个系统将分组配置从代码中分离出来，使得添加和修改分组变得更加容易。

## 新增功能

### 1. 分组配置文件
- **文件位置**: `core/group_config.py`
- **功能**: 集中管理所有分组定义
- **优势**: 易于维护、扩展和自定义

### 2. 分组类别

#### 基础分组 (base)
- 市场、行业分类分组
- 市值和资产规模分组
- 基础波动性和流动性分组
- **数量**: 10个

#### 盈利能力分组 (profitability)
- ROE、ROA、净利润率分组
- EBITDA利润率、毛利率分组
- **数量**: 5个
- **示例**: `bucket(rank(divide(netincome, sales)), range='0.1, 1, 0.1')`

#### 估值分组 (valuation)
- P/E、P/B、EV/EBITDA分组
- P/S、每股账面价值分组
- **数量**: 5个
- **示例**: `bucket(rank(divide(cap, netincome)), range='0.1, 1, 0.1')`

#### 财务健康度分组 (financial_health)
- 流动比率、负债率分组
- 现金比率、现金流健康度分组
- **数量**: 5个

#### 成长性分组 (growth)
- 销售、EPS、资产增长分组
- 净利润、EBITDA增长分组
- **数量**: 5个

#### 动量因子分组 (momentum)
- 短期、中期、长期价格动量
- 收益率排名、价格相对强度
- **数量**: 5个

#### 流动性分组 (liquidity)
- 换手率、平均交易量分组
- 成交额、交易活跃度分组
- **数量**: 5个

#### 风险特征分组 (risk)
- 收益波动性、价格波动性
- 最大回撤、Beta分组
- **数量**: 5个

#### 技术指标分组 (technical)
- RSI、价格相对位置
- 移动平均、布林带位置
- **数量**: 4个

#### 行业相对表现分组 (industry_relative)
- 行业相对收益、估值
- 行业相对盈利能力、成长性
- **数量**: 5个

#### 综合指标分组 (composite)
- 综合规模、质量因子
- 价值动量综合、员工生产力
- **数量**: 4个

## 使用方法

### 1. 基础用法

```python
from core.alpha_factory import AlphaFactory

# 初始化Alpha工厂
factory = AlphaFactory(config)

# 使用默认基础分组
first_order = factory.create_first_order_alphas(fields)
second_order = factory.create_second_order_alphas(first_order)
```

### 2. 自定义分组类别

```python
# 查看可用分组类别
categories = factory.get_available_group_categories()
print(categories)
# ['base', 'profitability', 'valuation', 'financial_health', ...]

# 使用特定分组类别
selected_categories = ['base', 'valuation', 'momentum']
custom_second_order = factory.create_second_order_alphas_with_custom_groups(
    first_order, 
    selected_categories
)
```

### 3. 获取特定分组

```python
# 获取特定类别的分组
base_groups = factory._get_groups_for_region(['base'])
valuation_groups = factory._get_groups_for_region(['valuation'])
multi_groups = factory._get_groups_for_region(['base', 'valuation', 'momentum'])
```

## 配置管理

### 1. 直接使用GroupConfig

```python
from core.group_config import GroupConfig

gc = GroupConfig()

# 获取各类分组
base_groups = gc.get_base_groups()
profitability_groups = gc.get_profitability_groups()
valuation_groups = gc.get_valuation_groups()

# 获取所有分组
all_groups = gc.get_all_groups()
custom_groups = gc.get_all_groups(['base', 'valuation'])
```

### 2. 添加新分组

要添加新的分组类别：

1. 在 `GroupConfig` 类中添加新的静态方法
2. 在 `get_all_groups` 方法中注册新类别
3. 在 `AlphaFactory` 的 `get_available_group_categories` 中添加类别名

示例：
```python
@staticmethod
def get_sentiment_groups():
    """获取情绪分组"""
    return [
        "bucket(rank(news_sentiment), range='0.1, 1, 0.1')",
        "bucket(rank(analyst_sentiment), range='0.1, 1, 0.1')",
    ]
```

## 区域特殊处理

系统支持不同区域的特殊处理：

- **GLB区域**: 自动将 `assets` 字段替换为 `fnd23_tot_assets`
- **区域特定分组**: 每个区域都有专门的分组集合

## 统计信息

- **总分组数量**: 58个 (不含区域特定分组)
- **基础分组**: 10个
- **扩展分组**: 48个 (分布在10个类别中)
- **区域特定分组**: CHN(5), USA(13), HKG(18), EUR(17), GLB(11)

## 测试

运行以下命令测试分组配置功能：

```bash
# 激活虚拟环境
.\worldquant\Scripts\activate

# 测试分组配置
python test_groups_simple.py

# 测试Alpha工厂集成
python test_alpha_factory_integration.py
```

## 经济学意义

这些分组具有重要的金融经济学意义：

1. **风险管理**: 通过财务健康度和风险特征分组控制组合风险
2. **价值投资**: 估值分组帮助识别被低估或高估的股票
3. **成长投资**: 成长性分组捕捉具有增长潜力的公司
4. **动量策略**: 动量分组利用价格趋势和市场情绪
5. **行业分析**: 行业相对分组提供更精细的同业比较
6. **流动性管理**: 流动性分组确保交易的可执行性

这个分组系统为Alpha策略提供了丰富的特征维度，能够更好地捕捉股票的不同特征，提高Alpha因子的有效性。 