add(x, y, filter = false), x + y; Add all inputs (at least 2 inputs required). If filter = true, filter all input NaN to 0 before adding
divide(x, y)
max(x, y, ..) Maximum value of all inputs. At least 2 inputs are required
min(x, y ..) Minimum value of all inputs. At least 2 inputs are required
multiply(x ,y, ... , filter=false), x * y; Multiply all inputs. At least 2 inputs are required. Filter sets the NaN values to 1
power(x, y) x ^ y
signed_power(x, y) x raised to the power of y such that final result preserves sign of x
subtract(x, y, filter=false), x - y; x-y. If filter = true, filter all input NaN to 0 before subtracting
ts_corr(x, y, d); Returns correlation of x and y for the past d days