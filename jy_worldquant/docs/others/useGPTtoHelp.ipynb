{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Import Lib"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# import all functions from machine_lib.py \n", "from machine_lib import * "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Case1: Use Data provided by contributor, credit to GrandMaster(2025Q1)-XX42289"]}, {"cell_type": "markdown", "metadata": {}, "source": ["实用案例1：使用论坛中提供的数据，感谢GrandMaster(2025Q1)-XX42289"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"detail\":\"INVALID_CREDENTIALS\"}'\n"]}], "source": ["s = login()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["doubao_fields_3 = [\n", "\"fn_profit_loss_a /fn_assets_fair_val_a\", # 反映资产盈利能力，净利润与资产公允价值的比率\n", "\"fn_income_from_equity_investments_a /fn_assets_fair_val_a\", # 反映来自权益投资的收益占资产的比例\n", "\"fn_comp_options_out_intrinsic_value_a /fn_comp_options_out_number_a\", # 反映每份期权的平均内在价值\n", "\"fn_amortization_of_intangible_assets_a /fn_finite_lived_intangible_assets_gross_a\", # 反映无形资产的摊销比例\n", "\"fn_liab_fair_val_a /fn_assets_fair_val_a\", # 反映负债与资产的比例，衡量偿债能力\n", "\"fn_def_tax_liab_a /fn_def_tax_assets_net_a\", # 反映递延所得税负债与递延所得税资产净额的比例\n", "\"fn_derivative_fair_value_of_derivative_asset_a /fn_derivative_fair_value_of_derivative_liability_a\", # 反映衍生资产与衍生负债的公允价值比例\n", "\"fn_income_tax_expense_a /fn_profit_loss_a\", # 反映所得税费用占净利润的比例\n", "\"fn_interest_paid_net_a /fn_debt_instrument_carrying_amount_a\", # 反映债务的利息支付水平\n", "\"fn_op_lease_rent_exp_a /fn_ppne_gross_a\", # 反映经营租赁租金费用占固定资产原值的比例\n", "\"fn_proceeds_from_issuance_of_common_stock_a /fn_entity_common_stock_shares_out_a\", # 反映每股发行普通股所获得的现金流入\n", "\"fn_repayments_of_debt_a /fn_proceeds_from_issuance_of_debt_a\", # 反映债务偿还与发行债务所得的比例\n", "\"fn_taxes_payable_a /fn_employee_related_liab_a\", # 反映应付税款占员工相关负债的比例\n", "\"fn_unrecognized_tax_benefits_a /fn_def_tax_assets_net_a\", # 反映未确认税收利益与递延所得税资产净额的比例\n", "\"fn_accum_oth_income_loss_net_of_tax_a /fn_profit_loss_a\", # 反映其他综合收益净额占净利润的比例\n", "\"fn_business_combination_purchase_price_a /fn_assets_fair_val_a\", # 反映企业合并购买价格与资产公允价值的比例\n", "\"fn_comp_non_opt_vested_a /fn_comp_non_opt_grants_a\", # 反映非期权类权益支付工具的授予与归属比例\n", "\"fn_debt_instrument_interest_rate_stated_percentage_a /fn_debt_instrument_carrying_amount_a\", # 反映债务的实际利率水平\n", "\"fn_eff_income_tax_rate_continuing_operations_a /fn_income_tax_expense_a\", # 反映有效所得税税率与所得税费用的关系\n", "\"fn_finite_lived_intangible_assets_net_a /fn_assets_fair_val_a\", # 反映有限寿命无形资产净额占资产公允价值的比例\n", "\"fn_goodwill_acquired_during_period_a /fn_assets_fair_val_a\", # 反映当期获得的商誉占资产公允价值的比例\n", "\"fn_incremental_shares_attributable_to_share_based_payment_a /fn_entity_common_stock_shares_out_a\", # 反映基于股份支付的增量股份占普通股流通股的比例\n", "\"fn_intangible_assets_accum_amort_a /fn_finite_lived_intangible_assets_gross_a\", # 反映无形资产累计摊销占原值的比例\n", "\"fn_line_of_credit_facility_amount_out_a /fn_line_of_credit_facility_max_borrowing_capacity_a\", # 反映信用额度的使用比例\n", "\"fn_mne_a /fn_assets_fair_val_a\", # 反映机器设备等资产占总资产的比例\n", "\"fn_new_shares_issued_a /fn_entity_common_stock_shares_out_a\", # 反映新发行股份占流通股的比例\n", "\"fn_op_lease_min_pay_due_a /fn_liab_fair_val_a\", # 反映经营租赁最低付款额占负债公允价值的比例\n", "\"fn_oth_income_loss_net_of_tax_a /fn_profit_loss_a\", # 反映其他综合收益净额占净利润的比例\n", "\"fn_payments_for_repurchase_of_common_stock_a /fn_proceeds_from_issuance_of_common_stock_a\", # 反映普通股回购支付与发行所得的比例\n", "\"fn_ppne_gross_a /fn_assets_fair_val_a\", # 反映固定资产原值占资产公允价值的比例\n", "\"fn_prepaid_expense_a /fn_assets_fair_val_a\", # 反映预付费用占资产公允价值的比例\n", "\"fn_repurchased_shares_a /fn_entity_common_stock_shares_out_a\", # 反映回购股份占流通股的比例\n", "\"subtract (fn_assets_fair_val_a, fn_liab_fair_val_a)\", # 反映净资产，资产减去负债\n", "\"divide (fn_profit_loss_a, fn_avg_diluted_sharesout_adj_a)\", # 每股收益，净利润除以稀释后平均流通股数\n", "\"divide (fn_income_from_equity_investments_a, fn_entity_common_stock_shares_out_a)\", # 每股权益投资收益\n", "\"divide (fn_comp_options_out_intrinsic_value_a, fn_comp_options_out_weighted_avg_a)\", # 基于期权加权平均价格的内在价值比例\n", "\"divide (fn_amortization_of_intangible_assets_a, fn_finite_lived_intangible_assets_net_a)\", # 基于无形资产净值的摊销比例\n", "\"divide (fn_liab_fair_val_a, fn_assets_fair_val_l1_a)\", # 负债公允价值与一级资产公允价值的比例\n", "\"divide (fn_def_tax_liab_a, fn_def_tax_assets_liab_net_a)\", # 递延所得税负债与递延所得税资产负债净额的比例\n", "\"divide (fn_derivative_fair_value_of_derivative_asset_a, fn_derivative_notional_amount_a)\", # 衍生资产公允价值与名义金额的比例\n", "\"divide (fn_income_tax_expense_a, fn_income_from_equity_investments_a)\", # 权益投资收益的所得税费用比例\n", "\"divide (fn_interest_paid_net_a, fn_interest_payable_a)\", # 已支付利息与应付利息的比例\n", "\"divide (fn_op_lease_rent_exp_a, fn_op_lease_min_pay_due_a)\", # 经营租赁租金费用与最低付款额的比例\n", "\"divide (fn_proceeds_from_issuance_of_common_stock_a, fn_new_shares_issued_a)\", # 每股新发行普通股的筹资额\n", "\"divide (fn_repayments_of_debt_a, fn_debt_instrument_carrying_amount_a)\", # 债务偿还与债务账面价值的比例\n", "\"divide (fn_taxes_payable_a, fn_profit_loss_a)\", # 应付税款占净利润的比例\n", "\"divide (fn_unrecognized_tax_benefits_a, fn_def_tax_liab_a)\", # 未确认税收利益与递延所得税负债的比例\n", "\"divide (fn_accum_oth_income_loss_fx_adj_net_of_tax_a, fn_profit_loss_a)\", # 外汇调整的其他综合收益净额占净利润的比例\n", "\"divide (fn_business_combination_assets_aquired_goodwill_a, fn_business_combination_purchase_price_a)\", # 企业合并中商誉占购买价格的比例\n", "\"divide (fn_comp_non_opt_nonvested_number_a, fn_comp_non_opt_grants_a)\", # 非期权类未归属权益工具数量与授予数量的比例\n", "\"divide (fn_debt_instrument_face_amount_a, fn_debt_instrument_carrying_amount_a)\", # 债务面值与账面价值的比例\n", "\"divide (fn_eff_income_tax_rate_continuing_operations_a, fn_income_tax_expense_a)\", # 有效所得税税率与所得税费用的比例（重复但从不同角度理解）\n", "\"divide (fn_finite_lived_intangible_assets_acq_a, fn_assets_fair_val_a)\", # 当期收购的有限寿命无形资产占资产公允价值的比例\n", "\"divide (fn_goodwill_acquired_during_period_a, fn_business_combination_purchase_price_a)\", # 企业合并中当期获得商誉占购买价格的比例\n", "\"divide (fn_incremental_shares_attributable_to_share_based_payment_a, fn_avg_diluted_sharesout_adj_a)\", # 基于股份支付的增量股份占稀释后平均流通股数的比例\n", "\"divide (fn_intangible_assets_accum_amort_a, fn_finite_lived_intangible_assets_acq_a)\", # 无形资产累计摊销与当期收购无形资产的比例\n", "\"divide (fn_line_of_credit_facility_amount_out_a, fn_liab_fair_val_a)\", # 信用额度使用金额占负债公允价值的比例\n", "\"divide (fn_mne_a, fn_ppne_gross_a)\", # 机器设备等资产占固定资产原值的比例\n", "\"divide (fn_new_shares_options_a, fn_entity_common_stock_shares_out_a)\", # 新行使的股份期权占流通股的比例\n", "\"divide (fn_op_lease_min_pay_due_in_2y_a, fn_op_lease_min_pay_due_a)\", # 未来第二年经营租赁最低付款额占总最低付款额的比例\n", "\"divide (fn_oth_income_loss_available_for_sale_securities_adj_of_tax_a, fn_profit_loss_a)\", # 可供出售证券调整的其他综合收益净额占净利润的比例\n", "\"divide (fn_payments_to_acquire_businesses_net_of_cash_acquired_a, fn_assets_fair_val_a)\", # 企业收购净现金流出占资产公允价值的比例\n", "\"divide (fn_ppne_gross_a, fn_liab_fair_val_a)\", # 固定资产原值与负债公允价值的比例\n", "\"divide (fn_prepaid_expense_a, fn_liab_fair_val_a)\", # 预付费用与负债公允价值的比例\n", "\"divide (fn_repurchased_shares_value_a, fn_proceeds_from_issuance_of_common_stock_a)\", # 回购股份价值与发行普通股所得的比例\n", "\"subtract (fn_assets_fair_val_l1_a, fn_assets_fair_val_l2_a)\", # 一级资产公允价值减去二级资产公允价值\n", "\"add (fn_liab_fair_val_l1_a, fn_liab_fair_val_l2_a)\", # 一级负债公允价值加上二级负债公允价值\n", "\"multiply (fn_profit_loss_a, 100 /fn_assets_fair_val_a)\", # 资产利润率，净利润乘以 100 再除以资产公允价值\n", "\"log (fn_assets_fair_val_a)\", # 资产公允价值的对数\n", "\"power (fn_income_from_equity_investments_a, 2)\", # 权益投资收益的平方\n", "\"signed_power (fn_comp_options_out_intrinsic_value_a, 0.5)\", # 期权内在价值的 0.5 次方\n", "\"abs (subtract (fn_derivative_fair_value_of_derivative_asset_a, fn_derivative_fair_value_of_derivative_liability_a))\", # 衍生资产与衍生负债公允价值差值的绝对值\n", "\"ts_mean (fn_profit_loss_a, d=12)\", # 过去 12 期净利润的时间序列均值\n", "\"ts_std_dev (fn_income_tax_expense_a, d=6)\", # 过去 6 期所得税费用的时间序列标准差\n", "\"ts_corr (fn_profit_loss_a, fn_assets_fair_val_a, d=10)\", # 过去 10 期净利润与资产公允价值的时间序列相关性\n", "\"group_mean (fn_profit_loss_a, weight=1, group='industry')\", # 按行业分组的净利润均值\n", "\"group_zscore (fn_liab_fair_val_a, group='size')\", # 按规模分组的负债公允价值的 z 分数\n", "\"scale (fn_income_from_equity_investments_a, scale=100)\", # 权益投资收益缩放 100 倍\n", "\"winsorize (fn_derivative_fair_value_of_derivative_asset_a, std=3)\", # 对衍生资产公允价值进行缩尾处理\n", "\"vec_sum (fn_assets_fair_val_l1_a, fn_assets_fair_val_l2_a, fn_assets_fair_val_l3_a)\", # 一级、二级、三级资产公允价值的向量和\n", "\"vec_avg (fn_liab_fair_val_l1_a, fn_liab_fair_val_l2_a, fn_liab_fair_val_l3_a)\", # 一级、二级、三级负债公允价值的向量均值\n", "\"bucket (rank (fn_profit_loss_a, rate=2), range='0, 1, 0.1')\", # 对净利润进行排名并分桶\n", "\"if_else (greater (fn_profit_loss_a, 0), 1, -1)\", # 判断净利润是否大于 0，大于为 1，否则为 - 1\n", "\"and (greater (fn_profit_loss_a, 0), less (fn_liab_fair_val_a, fn_assets_fair_val_a))\", # 判断净利润大于 0 且负债小于资产\n", "\"or (greater (fn_income_from_equity_investments_a, 0), greater (fn_comp_options_out_intrinsic_value_a, 0))\" # 判断权益投资收益大于 0 或期权内在价值大于 0\n", "]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fn_profit_loss_a /fn_assets_fair_val_a\n", "ts_rank(fn_profit_loss_a /fn_assets_fair_val_a, 5)\n", "ts_rank(fn_profit_loss_a /fn_assets_fair_val_a, 22)\n", "ts_rank(fn_profit_loss_a /fn_assets_fair_val_a, 66)\n", "ts_rank(fn_profit_loss_a /fn_assets_fair_val_a, 120)\n", "ts_rank(fn_profit_loss_a /fn_assets_fair_val_a, 240)\n", "ts_zscore(fn_profit_loss_a /fn_assets_fair_val_a, 5)\n", "ts_zscore(fn_profit_loss_a /fn_assets_fair_val_a, 22)\n", "ts_zscore(fn_profit_loss_a /fn_assets_fair_val_a, 66)\n", "ts_zscore(fn_profit_loss_a /fn_assets_fair_val_a, 120)\n", "总共在一阶产生了7310个表达式\n"]}], "source": ["first_order = first_order_factory(doubao_fields_3, ts_ops)\n", "for expression in first_order[:10]:\n", "    print(expression)\n", "print(f\"总共在一阶产生了{len(first_order)}个表达式\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Case2: Create New datafield by leveraging platform info"]}, {"cell_type": "markdown", "metadata": {}, "source": ["实用案例2：使用数据集和运算符信息创建新的数据字段"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>anl4_adjusted_netincome_ft</td>\n", "      <td>Adjusted net income - forecast type (revision/...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>anl4_bvps_flag</td>\n", "      <td>Book value per share - forecast type (revision...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>anl4_bvps_high</td>\n", "      <td>Book value - the highest estimation, per share</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>anl4_bvps_low</td>\n", "      <td>Book value - the lowest estimation, per share</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>anl4_bvps_mean</td>\n", "      <td>Book value per share - average of estimations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>345</th>\n", "      <td>est_sga</td>\n", "      <td>SGA - mean of estimations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>346</th>\n", "      <td>est_shequity</td>\n", "      <td>Mean of SH Equity segment - mean of estimations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>347</th>\n", "      <td>est_tbv_ps</td>\n", "      <td>Tangible Book Value per Share - mean of estima...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>348</th>\n", "      <td>est_tot_assets</td>\n", "      <td>Total Assets - mean of estimations</td>\n", "    </tr>\n", "    <tr>\n", "      <th>349</th>\n", "      <td>est_tot_goodwill</td>\n", "      <td>Total Goodwill - mean of estimations</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>202 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                             id  \\\n", "0    anl4_adjusted_netincome_ft   \n", "70               anl4_bvps_flag   \n", "71               anl4_bvps_high   \n", "72                anl4_bvps_low   \n", "73               anl4_bvps_mean   \n", "..                          ...   \n", "345                     est_sga   \n", "346                est_shequity   \n", "347                  est_tbv_ps   \n", "348              est_tot_assets   \n", "349            est_tot_goodwill   \n", "\n", "                                           description  \n", "0    Adjusted net income - forecast type (revision/...  \n", "70   Book value per share - forecast type (revision...  \n", "71      Book value - the highest estimation, per share  \n", "72       Book value - the lowest estimation, per share  \n", "73       Book value per share - average of estimations  \n", "..                                                 ...  \n", "345                          SGA - mean of estimations  \n", "346    Mean of SH Equity segment - mean of estimations  \n", "347  Tangible Book Value per Share - mean of estima...  \n", "348                 Total Assets - mean of estimations  \n", "349               Total Goodwill - mean of estimations  \n", "\n", "[202 rows x 2 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df = get_datafields(s, dataset_id = 'analyst4', region='USA', universe='TOP3000', delay=1)\n", "df = df[df['type'] == 'MATRIX']\n", "df = df[['id','description']]\n", "df.to_csv('analyst4.csv')\n", "df"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# read the operatorDefinitions from the file\n", "with open('operatorDescription.txt', 'r') as f:\n", "    operator_definitions = f.readlines()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    \"add(anl4_bvps_high, anl4_bvps_low, filter=True)\",\n", "    \"subtract(anl4_bvps_high, anl4_bvps_low)\",\n", "    \"divide(anl4_adjusted_netincome_ft, est_shequity)\",\n", "    \"multiply(est_sga, est_shequity, filter=True)\",\n", "    \"power(est_tbv_ps, 2)\",\n", "    \"signed_power(anl4_bvps_mean, 0.5)\",\n", "    \"max(anl4_bvps_high, est_tbv_ps, est_shequity)\",\n", "    \"min(anl4_bvps_low, est_sga)\",\n", "    \"ts_corr(anl4_adjusted_netincome_ft, est_shequity, 30)\",\n", "    \"divide(est_tot_assets, est_shequity)\",\n", "    \"subtract(est_shequity, est_tbv_ps, filter=True)\",\n", "    \"add(anl4_bvps_mean, est_shequity, filter=False)\",\n", "    \"multiply(anl4_bvps_high, anl4_bvps_low)\",\n", "    \"power(est_shequity, 0.333)\",\n", "    \"signed_power(est_shequity, -1)\",\n", "    \"max(anl4_bvps_high, est_shequity, est_tbv_ps)\",\n", "    \"min(anl4_bvps_low, est_sga, est_tbv_ps)\",\n", "    \"ts_corr(est_shequity, est_sga, 60)\",\n", "    \"divide(anl4_bvps_high, anl4_bvps_low)\",\n", "    \"subtract(anl4_bvps_mean, est_shequity, filter=False)\",\n", "    \"add(est_shequity, est_sga, filter=True)\",\n", "    \"multiply(anl4_bvps_high, est_shequity, filter=False)\",\n", "    \"power(anl4_bvps_mean, 1.5)\",\n", "    \"signed_power(est_shequity, 2)\",\n", "    \"max(anl4_bvps_high, est_shequity, est_sga)\",\n", "    \"min(anl4_bvps_low, est_tbv_ps, est_shequity)\",\n", "    \"ts_corr(anl4_bvps_flag, est_shequity, 90)\",\n", "    \"divide(est_shequity, est_tbv_ps)\",\n", "    \"subtract(est_shequity, est_sga, filter=True)\",\n", "    \"add(anl4_bvps_mean, est_shequity, filter=False)\",\n", "    \"multiply(anl4_bvps_high, est_shequity, filter=True)\",\n", "    \"power(est_shequity, 0.25)\",\n", "    \"signed_power(anl4_bvps_mean, -0.5)\",\n", "    \"max(anl4_bvps_high, est_shequity, est_sga)\",\n", "    \"min(anl4_bvps_low, est_tbv_ps, est_shequity)\",\n", "    \"ts_corr(est_shequity, est_sga, 180)\"\n", "]\n"]}], "source": ["from openai import OpenAI\n", " \n", "client = OpenAI(\n", "    api_key=\"sk-xxxxxxxxxxx\", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key\n", "    base_url=\"https://api.moonshot.cn/v1\",\n", ")\n", " \n", "completion = client.chat.completions.create(\n", "    model = \"kimi-thinking-preview\",\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": f\"{operator_definitions} \\n based on the operator description and the datafield id description, try to give me as much meaningful combination as you can.\"},\n", "        {\"role\":\"user\", \"content\":\"output in a python list without explaination, as much as you can, as diversify as you can\"},\n", "        {\"role\":\"user\", \"content\": f\"the datafield id and description is {df}\"},\n", "    ],\n", "    temperature = 0.3,\n", "    max_tokens= 120*1024\n", ")\n", " \n", "# 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）\n", "print(completion.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["['add(anl4_bvps_high, anl4_bvps_low, filter=True)',\n", " 'subtract(anl4_bvps_high, anl4_bvps_low)',\n", " 'divide(anl4_adjusted_netincome_ft, est_shequity)',\n", " 'multiply(est_sga, est_shequity, filter=True)',\n", " 'power(est_tbv_ps, 2)',\n", " 'signed_power(anl4_bvps_mean, 0.5)',\n", " 'max(anl4_bvps_high, est_tbv_ps, est_shequity)',\n", " 'min(anl4_bvps_low, est_sga)',\n", " 'ts_corr(anl4_adjusted_netincome_ft, est_shequity, 30)',\n", " 'divide(est_tot_assets, est_shequity)',\n", " 'subtract(est_shequity, est_tbv_ps, filter=True)',\n", " 'add(anl4_bvps_mean, est_shequity, filter=False)',\n", " 'multiply(anl4_bvps_high, anl4_bvps_low)',\n", " 'power(est_shequity, 0.333)',\n", " 'signed_power(est_shequity, -1)',\n", " 'max(anl4_bvps_high, est_shequity, est_tbv_ps)',\n", " 'min(anl4_bvps_low, est_sga, est_tbv_ps)',\n", " 'ts_corr(est_shequity, est_sga, 60)',\n", " 'divide(anl4_bvps_high, anl4_bvps_low)',\n", " 'subtract(anl4_bvps_mean, est_shequity, filter=False)',\n", " 'add(est_shequity, est_sga, filter=True)',\n", " 'multiply(anl4_bvps_high, est_shequity, filter=False)',\n", " 'power(anl4_bvps_mean, 1.5)',\n", " 'signed_power(est_shequity, 2)',\n", " 'max(anl4_bvps_high, est_shequity, est_sga)',\n", " 'min(anl4_bvps_low, est_tbv_ps, est_shequity)',\n", " 'ts_corr(anl4_bvps_flag, est_shequity, 90)',\n", " 'divide(est_shequity, est_tbv_ps)',\n", " 'subtract(est_shequity, est_sga, filter=True)',\n", " 'add(anl4_bvps_mean, est_shequity, filter=False)',\n", " 'multiply(anl4_bvps_high, est_shequity, filter=True)',\n", " 'power(est_shequity, 0.25)',\n", " 'signed_power(anl4_bvps_mean, -0.5)',\n", " 'max(anl4_bvps_high, est_shequity, est_sga)',\n", " 'min(anl4_bvps_low, est_tbv_ps, est_shequity)',\n", " 'ts_corr(est_shequity, est_sga, 180)']"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["eval(completion.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Case3: Use yourself with fieldknowledge to generate and search New Datafield"]}, {"cell_type": "markdown", "metadata": {}, "source": ["实用案例3：使用领域知识生成和搜索新的数据字段"]}, {"cell_type": "markdown", "metadata": {}, "source": ["datamodel = \"log(a)/log(b)\" #这里的前提假设是什么？"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# go through df to create all combinations of log(a)/log(b) and new description\n", "new_fields = []\n", "new_descriptions = []\n", "for i in range(len(df)):\n", "    for j in range(len(df)):\n", "        if i != j:\n", "            new_fields.append(f\"log({df.iloc[i]['id']})/log({df.iloc[j]['id']})\")\n", "            new_descriptions.append(f\"log({df.iloc[i]['description']})/log({df.iloc[j]['description']})\")\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总共生成了40602个表达式\n"]}], "source": ["new_df = pd.DataFrame({'id': new_fields, 'description': new_descriptions})\n", "new_df['GPT_judgement'] = \"\"\n", "print(f\"总共生成了{len(new_df)}个表达式\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["'log(Adjusted net income - forecast type (revision/new/...))/log(Book value - the highest estimation, per share)'"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["new_df.iloc[1]['description']"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["no\n"]}], "source": ["from openai import OpenAI\n", " \n", "client = OpenAI(\n", "    api_key=\"sk-xxxxxxxxxxxxxxx\", # 在这里将 MOONSHOT_API_KEY 替换为你从 Kimi 开放平台申请的 API Key\n", "    base_url=\"https://api.moonshot.cn/v1\",\n", ")\n", " \n", "completion = client.chat.completions.create(\n", "    model = \"kimi-thinking-preview\",\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": f\"以下的计算是否合理或有经济学含义,仅回答yes or no\"},\n", "        {\"role\":\"user\", \"content\": f\"{new_df.iloc[1]['description']}\"},\n", "    ],\n", "    temperature = 0.3,\n", "    max_tokens= 1024\n", ")\n", " \n", "# 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）\n", "print(completion.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["log(Adjusted net income - forecast type (revision/new/...))/log(Book value per share - forecast type (revision/new/...))的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Book value - the highest estimation, per share)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Book value - the lowest estimation, per share)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Book value per share - average of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Book value per share - Median value among forecasts)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Book value per share - number of estimations)的判断结果是no\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Book value per share - announced financial value)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Capital Expenditures - forecast type (revision/new/...))的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Capital Expenditures - The highest estimation)的判断结果是no\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Capital Expenditures - The lowest estimation)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Capital Expenditures - mean of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Capital Expenditures - number of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Capital Expenditures - standard deviation of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Capital Expenditures - announced financial value)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Financing Activities - forecast type (revision/new/...))的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Financing - The highest of forecasted values)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Financing - The lowest estimation)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Financing - mean of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Financing Activities - Median value among forecasts)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Financing - number of estimations)的判断结果是no\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Financing - announced financial value)的判断结果是no\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Investing - forecast type (revision/new/...))的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Investing - The highest estimation)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Investing - The lowest estimation)的判断结果是no\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Investing - mean of estimations)的判断结果是◁think用户▷的问题是让我判断一个公式是否有经济学含义，并且只需要回答“yes”或“no”。这个问题看似简单，但其实背后可能有一些潜在的意图需要我去揣摩。\n", "\n", "首先，我需要明确用户的需求。用户并没有要求我解释公式的具体含义，也没有让我分析公式的合理性，而是直接问它是否有经济学含义。这意味着我的回答需要基于公式的表面逻辑，而不是深入探讨它的实际应用或理论背景。用户可能只是想快速确认这个公式是否在经济学上有意义，而不是让我展开复杂的分析。\n", "\n", "接下来，我需要仔细阅读公式，理解它的结构和变量。公式中涉及“调整后的净收入”“预测类型”“现金流量从投资中获得”“估计值的平均值”等术语。这些变量在经济学中确实有明确的含义，比如“净收入”通常与企业的盈利能力相关，“现金流量”是企业财务健康的重要指标，而“预测类型”和“估计值的平均值”则可能与经济预测或财务分析有关。从这些变量来看，公式似乎是在尝试将企业的盈利能力与投资活动的现金流进行某种比较或分析。\n", "\n", "然后，我需要判断公式的逻辑是否合理。公式中使用了对数运算，这在经济学中很常见，因为对数可以用来处理比例关系或增长率问题。分子是“调整后的净收入减去预测类型”，这可能是在衡量实际收入与预测值之间的差异；分母是“现金流量从投资中获得的估计值的平均值”，这可能是在用投资活动的现金流作为基准进行比较。从这个角度看，公式的逻辑是合理的，它试图通过比例关系来分析企业的财务表现。\n", "\n", "最后，我需要决定回答的风格。用户明确要求只回答“yes”或“no”，所以我不能添加任何额外的解释或分析。回答需要简洁明了，直接给出结论。\n", "\n", "总结下来，我认为这个公式在经济学上有一定的含义，因为它涉及企业的收入、预测和现金流等关键指标，并且使用了对数运算来处理比例关系。因此，我的回答是“yes”。◁/think▷yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Investing - median of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Investing - number of estimations)的判断结果是no\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Investing - announced financial value)的判断结果是no\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Operations - forecast type (revision/new/...))的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Operations - The highest value among forecasts)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Operations - The lowest estimation)的判断结果是no\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Operations - mean of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Operations - median of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Operations - number of estimations)的判断结果是no\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Cash Flow From Operations - announced financial value)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Pretax income - std of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Reported Earnings per share - standard deviation of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest and taxes - The highest estimation)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest and taxes - The lowest estimation)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest and taxes - mean of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest and taxes - median of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest and taxes - number of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest and taxes - standard deviation of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest and taxes - announced financial value)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest, taxes, depreciation and amortization - forecast type (revision/new/...))的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest, taxes, depreciation, and amortization - the highest estimation)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest, taxes, depreciation and amortization - The lowest estimation)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest, taxes, depreciation and amortization - mean of estimations)的判断结果是I need to determine if the formula \\(\\frac{\\log(\\text{Adjusted net income} - \\text{forecast type (revision/new/...)})}{\\log(\\text{Earnings before interest, taxes, depreciation and amortization} - \\textmean{ of estimations})}\\) has economic meaning.\n", "\n", "1. **Adjusted net income**: This is a modified version of net income, adjusted for non-recurring items or other factors.\n", "2. **Forecast type (revision/new/...)**: This refers to different types of earnings forecasts (e.g., initial forecasts, revised forecasts). However, subtracting a \"forecast type\" from adjusted net income is problematic because \"forecast type\" is a categorical descriptor, not a numerical value.\n", "3. **Earnings before interest, taxes, depreciation and amortization (EBITDA)**: This is a common measure of a company's operating performance.\n", "4. **Mean of estimations**: This likely refers to the average of analysts' forecasts or estimates for EBITDA.\n", "\n", "The formula subtracts a categorical variable (\"forecast type\") from a numerical variable (\"adjusted net income\"), which is not mathematically valid. Additionally, taking the ratio of two logarithms does not have a standard interpretation in economics or finance.\n", "\n", "Given these issues, the formula does not have clear economic meaning.\n", "\n", "**Final Answer**\n", "No\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest, taxes, depreciation and amortization - number of estimations)的判断结果是yes\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest, taxes, depreciation, and amortization - standard deviation of estimations)的判断结果是\n", "log(Adjusted net income - forecast type (revision/new/...))/log(Earnings before interest, taxes, depreciation and amortization - announced financial value)的判断结果是yes\n"]}], "source": ["# go through every row of new_df to get the GPT judgement\n", "for i in range(len(new_df)):\n", "#仅测试前50行\n", "    if i > 50:\n", "        break\n", "    completion = client.chat.completions.create(\n", "        model = \"kimi-thinking-preview\",\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": f\"判断用户的计算是否合理或有经济学含义,切记仅回答yes or no\"},\n", "            {\"role\":\"user\", \"content\": f\"{new_df.iloc[i]['description']}\"},\n", "        ],\n", "        temperature = 0.3,\n", "        max_tokens= 1024\n", "    )\n", "    print(f\"{new_df.iloc[i]['description']}的判断结果是{completion.choices[0].message.content}\")\n", "    # 通过 API 我们获得了 Kimi 大模型给予我们的回复消息（role=assistant）\n", "    new_df.at[i, 'GPT_judgement'] = completion.choices[0].message.content"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 2}