{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 登录"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["201\n", "{'user': {'id': 'JX28185'}, 'token': {'expiry': 14400.0}, 'permissions': ['TUTORIAL']}\n"]}], "source": ["import requests\n", "import json\n", "from os.path import expanduser\n", "from requests.auth import HTTPBasicAuth\n", "def sign_in():\n", "    # locd credentials\n", "    with open(expanduser('user_name.txt')) as f:\n", "        credentials=json.load(f) \n", "\n", "    # Extract usernane and possword fron the list\n", "    username, password = credentials\n", "\n", "    # 创建会话对象\n", "    sess = requests.Session()\n", "\n", "    # 设置基本身份验证\n", "    sess.auth = HTTPBasicAuth(username, password)\n", "\n", "    # 向API发送POST请求进行身份验证\n", "    response = sess.post('https://api.worldquantbrain.com/authentication')\n", "\n", "    # 打印响应状态和内容以调试\n", "       \n", "    print(response.status_code)\n", "    print(response.json())\n", "    return sess  \n", "\n", "sess = sign_in()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取数据集ID为fundamental6(Company Fundamental Data for Equity)下的所有数据字段"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["### Get Data_fields like Data Explorer 获取所有满足条件的数据字段及其ID\n", "def get_datafields(\n", "        s,\n", "        searchScope,\n", "        dataset_id: str = '',\n", "        search: str = ''\n", "):\n", "    import pandas as pd\n", "    instrument_type = searchScope['instrumentType']\n", "    region = searchScope['region']\n", "    delay = searchScope['delay']\n", "    universe = searchScope['universe']\n", "\n", "    if len(search) == 0:\n", "        url_template = \"https://api.worldquantbrain.com/data-fields?\" + \\\n", "                       f\"&instrumentType={instrument_type}\" + \\\n", "                       f\"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50\" + \\\n", "                       \"&offset={x}\"\n", "        count = s.get(url_template.format(x=0)).json()['count']\n", "    else:\n", "        url_template = \"https://api.worldquantbrain.com/data-fields?\" + \\\n", "                       f\"&instrumentType={instrument_type}\" + \\\n", "                       f\"&region={region}&delay={str(delay)}&universe={universe}&limit=50\" + \\\n", "                       f\"&search={search}\" + \\\n", "                       \"&offset={x}\"\n", "        count = 100\n", "\n", "    datafields_list = []\n", "    for x in range(0, count, 50):\n", "        datafields = s.get(url_template.format(x=x))\n", "        datafields_list.append(datafields.json()['results'])\n", "\n", "    datafields_list_flat = [item for sublist in datafields_list for item in sublist]\n", "\n", "    datafields_df = pd.DataFrame(datafields_list_flat)\n", "    return datafields_df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>description</th>\n", "      <th>dataset</th>\n", "      <th>category</th>\n", "      <th>subcategory</th>\n", "      <th>region</th>\n", "      <th>delay</th>\n", "      <th>universe</th>\n", "      <th>type</th>\n", "      <th>coverage</th>\n", "      <th>userCount</th>\n", "      <th>alphaCount</th>\n", "      <th>themes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>assets</td>\n", "      <td>Assets - Total</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.9524</td>\n", "      <td>16922</td>\n", "      <td>49277</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>assets_curr</td>\n", "      <td>Current Assets - Total</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.7655</td>\n", "      <td>1501</td>\n", "      <td>9277</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>bookvalue_ps</td>\n", "      <td>Book Value Per Share</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.9754</td>\n", "      <td>1467</td>\n", "      <td>7427</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>capex</td>\n", "      <td>Capital Expenditures</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.9646</td>\n", "      <td>6072</td>\n", "      <td>16966</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>cash</td>\n", "      <td>Cash</td>\n", "      <td>{'id': 'fundamental6', 'name': 'Company Fundam...</td>\n", "      <td>{'id': 'fundamental', 'name': 'Fundamental'}</td>\n", "      <td>{'id': 'fundamental-fundamental-data', 'name':...</td>\n", "      <td>USA</td>\n", "      <td>1</td>\n", "      <td>TOP3000</td>\n", "      <td>MATRIX</td>\n", "      <td>0.7529</td>\n", "      <td>1409</td>\n", "      <td>9051</td>\n", "      <td>[]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             id             description  \\\n", "0        assets          Assets - Total   \n", "1   assets_curr  Current Assets - Total   \n", "2  bookvalue_ps    Book Value Per Share   \n", "3         capex    Capital Expenditures   \n", "4          cash                    Cash   \n", "\n", "                                             dataset  \\\n", "0  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "1  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "2  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "3  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "4  {'id': 'fundamental6', 'name': 'Company Fundam...   \n", "\n", "                                       category  \\\n", "0  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "1  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "2  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "3  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "4  {'id': 'fundamental', 'name': 'Fundamental'}   \n", "\n", "                                         subcategory region  delay universe  \\\n", "0  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "1  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "2  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "3  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "4  {'id': 'fundamental-fundamental-data', 'name':...    USA      1  TOP3000   \n", "\n", "     type  coverage  userCount  alphaCount themes  \n", "0  MATRIX    0.9524      16922       49277     []  \n", "1  MATRIX    0.7655       1501        9277     []  \n", "2  MATRIX    0.9754       1467        7427     []  \n", "3  MATRIX    0.9646       6072       16966     []  \n", "4  MATRIX    0.7529       1409        9051     []  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 爬取id\n", "searchScope = {'region': 'USA', 'delay': '1', 'universe': 'TOP3000', 'instrumentType': 'EQUITY'}\n", "fnd6 = get_datafields(s=sess, searchScope=searchScope, dataset_id='fundamental6') # id设置\n", "fnd6 = fnd6[fnd6['type'] == \"MATRIX\"]  # 筛选（这里是type的MATRIX）\n", "fnd6.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['assets', 'assets_curr', 'bookvalue_ps', 'capex', 'cash',\n", "       'cash_st', 'cashflow', 'cashflow_dividends', 'cashflow_fin',\n", "       'cashflow_invst', 'cashflow_op', 'cogs', 'current_ratio', 'debt',\n", "       'debt_lt', 'debt_st', 'depre_amort', 'ebit', 'ebitda', 'employee',\n", "       'enterprise_value', 'eps', 'equity', 'fnd6_acdo', 'fnd6_acodo',\n", "       'fnd6_acox', 'fnd6_acqgdwl', 'fnd6_acqintan',\n", "       'fnd6_adesinda_curcd', 'fnd6_aldo', 'fnd6_am', 'fnd6_aodo',\n", "       'fnd6_aox', 'fnd6_aqc', 'fnd6_aqi', 'fnd6_aqs', 'fnd6_beta',\n", "       'fnd6_capxv', 'fnd6_ceql', 'fnd6_ch', 'fnd6_ci', 'fnd6_cibegni',\n", "       'fnd6_cicurr', 'fnd6_cidergl', 'fnd6_cik', 'fnd6_cimii',\n", "       'fnd6_ciother', 'fnd6_cipen', 'fnd6_cisecgl', 'fnd6_citotal',\n", "       'fnd6_city', 'fnd6_cld2', 'fnd6_cld3', 'fnd6_cld4', 'fnd6_cld5',\n", "       'fnd6_cptmfmq_actq', 'fnd6_cptmfmq_atq', 'fnd6_cptmfmq_ceqq',\n", "       'fnd6_cptmfmq_dlttq', 'fnd6_cptmfmq_dpq', 'fnd6_cptmfmq_lctq',\n", "       'fnd6_cptmfmq_oibdpq', 'fnd6_cptmfmq_opepsq', 'fnd6_cptmfmq_saleq',\n", "       'fnd6_cptnewqv1300_actq', 'fnd6_cptnewqv1300_apq',\n", "       'fnd6_cptnewqv1300_atq', 'fnd6_cptnewqv1300_ceqq',\n", "       'fnd6_cptnewqv1300_dlttq', 'fnd6_cptnewqv1300_dpq',\n", "       'fnd6_cptnewqv1300_epsf12', 'fnd6_cptnewqv1300_epsfxq',\n", "       'fnd6_cptnewqv1300_epsx12', 'fnd6_cptnewqv1300_lctq',\n", "       'fnd6_cptnewqv1300_ltq', 'fnd6_cptnewqv1300_nopiq',\n", "       'fnd6_cptnewqv1300_oeps12', 'fnd6_cptnewqv1300_oiadpq',\n", "       'fnd6_cptnewqv1300_oibdpq', 'fnd6_cptnewqv1300_opepsq',\n", "       'fnd6_cptnewqv1300_rectq', 'fnd6_cptnewqv1300_req',\n", "       'fnd6_cptnewqv1300_saleq', 'fnd6_cptrank_gvkeymap', 'fnd6_cshpri',\n", "       'fnd6_cshr', 'fnd6_cshtr', 'fnd6_cshtrq', 'fnd6_cstkcv',\n", "       'fnd6_cstkcvq', 'fnd6_currencya_curcd',\n", "       'fnd6_currencyqv1300_curcd', 'fnd6_dc', 'fnd6_dclo', 'fnd6_dcpstk',\n", "       'fnd6_dcvsr', 'fnd6_dcvsub', 'fnd6_dcvt', 'fnd6_dd', 'fnd6_dd1',\n", "       'fnd6_dd1q', 'fnd6_dd2', 'fnd6_dd3', 'fnd6_dd4', 'fnd6_dd5',\n", "       'fnd6_dilavx', 'fnd6_dlcch', 'fnd6_dltis', 'fnd6_dlto',\n", "       'fnd6_dltp', 'fnd6_dltr', 'fnd6_dm', 'fnd6_dn', 'fnd6_donr',\n", "       'fnd6_dpvieb', 'fnd6_drc', 'fnd6_drlt', 'fnd6_ds', 'fnd6_dudd',\n", "       'fnd6_dvpa', 'fnd6_dxd2', 'fnd6_dxd3', 'fnd6_dxd4', 'fnd6_dxd5',\n", "       'fnd6_ein', 'fnd6_esopct', 'fnd6_esopnr', 'fnd6_esopr',\n", "       'fnd6_esubc', 'fnd6_exre', 'fnd6_fatb', 'fnd6_fatc', 'fnd6_fate',\n", "       'fnd6_fatl', 'fnd6_fatn', 'fnd6_fato', 'fnd6_fatp', 'fnd6_fiao',\n", "       'fnd6_fic', 'fnd6_fopo', 'fnd6_fopox', 'fnd6_fyrc', 'fnd6_ibmii',\n", "       'fnd6_idesindq_curcd', 'fnd6_idit', 'fnd6_incorp', 'fnd6_intan',\n", "       'fnd6_intc', 'fnd6_intpn', 'fnd6_invfg', 'fnd6_invo', 'fnd6_invrm',\n", "       'fnd6_invwip', 'fnd6_itcb', 'fnd6_itci', 'fnd6_ivaco',\n", "       'fnd6_ivaeq', 'fnd6_ivao', 'fnd6_ivch', 'fnd6_ivst', 'fnd6_ivstch',\n", "       'fnd6_lcox', 'fnd6_lcoxdr', 'fnd6_lifr', 'fnd6_lno', 'fnd6_loc',\n", "       'fnd6_lol2', 'fnd6_loxdr', 'fnd6_lqpl1', 'fnd6_lul3',\n", "       'fnd6_mfma1_aoloch', 'fnd6_mfma1_apalch', 'fnd6_mfma1_at',\n", "       'fnd6_mfma1_capx', 'fnd6_mfma1_csho', 'fnd6_mfma1_dp',\n", "       'fnd6_mfma1_dpc', 'fnd6_mfma1_invch', 'fnd6_mfma2_oancf',\n", "       'fnd6_mfma2_opeps', 'fnd6_mfma2_recch', 'fnd6_mfma2_revt',\n", "       'fnd6_mfma2_txach', 'fnd6_mfmq_cheq', 'fnd6_mfmq_cogsq',\n", "       'fnd6_mfmq_cshprq', 'fnd6_mfmq_dlcq', 'fnd6_mfmq_ibcomq',\n", "       'fnd6_mfmq_mibtq', 'fnd6_mfmq_piq', 'fnd6_mibn', 'fnd6_mibt',\n", "       'fnd6_mkvalt', 'fnd6_mkvaltq', 'fnd6_mrc1', 'fnd6_mrc2',\n", "       'fnd6_mrc3', 'fnd6_mrc4', 'fnd6_mrc5', 'fnd6_mrct', 'fnd6_mrcta',\n", "       'fnd6_msa', 'fnd6_newa1v1300_aco', 'fnd6_newa1v1300_acominc',\n", "       'fnd6_newa1v1300_act', 'fnd6_newa1v1300_ano', 'fnd6_newa1v1300_ao',\n", "       'fnd6_newa1v1300_aocidergl', 'fnd6_newa1v1300_aociother',\n", "       'fnd6_newa1v1300_aocipen', 'fnd6_newa1v1300_aol2',\n", "       'fnd6_newa1v1300_aoloch', 'fnd6_newa1v1300_ap',\n", "       'fnd6_newa1v1300_apalch', 'fnd6_newa1v1300_aqpl1',\n", "       'fnd6_newa1v1300_at', 'fnd6_newa1v1300_aul3',\n", "       'fnd6_newa1v1300_bkvlps', 'fnd6_newa1v1300_caps',\n", "       'fnd6_newa1v1300_capx', 'fnd6_newa1v1300_ceq',\n", "       'fnd6_newa1v1300_ceqt', 'fnd6_newa1v1300_che',\n", "       'fnd6_newa1v1300_chech', 'fnd6_newa1v1300_cogs',\n", "       'fnd6_newa1v1300_cshfd', 'fnd6_newa1v1300_cshi',\n", "       'fnd6_newa1v1300_csho', 'fnd6_newa1v1300_cstk',\n", "       'fnd6_newa1v1300_dcom', 'fnd6_newa1v1300_dlc',\n", "       'fnd6_newa1v1300_dltt', 'fnd6_newa1v1300_dp',\n", "       'fnd6_newa1v1300_dpact', 'fnd6_newa1v1300_dpc',\n", "       'fnd6_newa1v1300_dv', 'fnd6_newa1v1300_dvc', 'fnd6_newa1v1300_dvt',\n", "       'fnd6_newa1v1300_ebit', 'fnd6_newa1v1300_ebitda',\n", "       'fnd6_newa1v1300_emp', 'fnd6_newa1v1300_epsfi',\n", "       'fnd6_newa1v1300_epsfx', 'fnd6_newa1v1300_epspi',\n", "       'fnd6_newa1v1300_epspx', 'fnd6_newa1v1300_fca',\n", "       'fnd6_newa1v1300_fincf', 'fnd6_newa1v1300_gdwl',\n", "       'fnd6_newa1v1300_gp', 'fnd6_newa1v1300_ib',\n", "       'fnd6_newa1v1300_ibadj', 'fnd6_newa1v1300_ibc',\n", "       'fnd6_newa1v1300_ibcom', 'fnd6_newa1v1300_icapt',\n", "       'fnd6_newa1v1300_intano', 'fnd6_newa1v1300_invch',\n", "       'fnd6_newa1v1300_invt', 'fnd6_newa1v1300_ivncf',\n", "       'fnd6_newa1v1300_lco', 'fnd6_newa1v1300_lct', 'fnd6_newa1v1300_lo',\n", "       'fnd6_newa1v1300_lse', 'fnd6_newa1v1300_lt', 'fnd6_newa2v1300_mib',\n", "       'fnd6_newa2v1300_mii', 'fnd6_newa2v1300_ni',\n", "       'fnd6_newa2v1300_nopi', 'fnd6_newa2v1300_oancf',\n", "       'fnd6_newa2v1300_oiadp', 'fnd6_newa2v1300_oibdp',\n", "       'fnd6_newa2v1300_opeps', 'fnd6_newa2v1300_optexd',\n", "       'fnd6_newa2v1300_pi', 'fnd6_newa2v1300_ppegt',\n", "       'fnd6_newa2v1300_ppent', 'fnd6_newa2v1300_prsho',\n", "       'fnd6_newa2v1300_rdip', 'fnd6_newa2v1300_rdipa',\n", "       'fnd6_newa2v1300_rdipd', 'fnd6_newa2v1300_rdipeps',\n", "       'fnd6_newa2v1300_re', 'fnd6_newa2v1300_recch',\n", "       'fnd6_newa2v1300_rect', 'fnd6_newa2v1300_reuna',\n", "       'fnd6_newa2v1300_revt', 'fnd6_newa2v1300_sale',\n", "       'fnd6_newa2v1300_seq', 'fnd6_newa2v1300_seqo',\n", "       'fnd6_newa2v1300_spced', 'fnd6_newa2v1300_spceeps',\n", "       'fnd6_newa2v1300_spi', 'fnd6_newa2v1300_stkco',\n", "       'fnd6_newa2v1300_tstk', 'fnd6_newa2v1300_tstkn',\n", "       'fnd6_newa2v1300_txach', 'fnd6_newa2v1300_txdb',\n", "       'fnd6_newa2v1300_txditc', 'fnd6_newa2v1300_txp',\n", "       'fnd6_newa2v1300_txt', 'fnd6_newa2v1300_wcap',\n", "       'fnd6_newa2v1300_xidoc', 'fnd6_newa2v1300_xint',\n", "       'fnd6_newa2v1300_xoptd', 'fnd6_newa2v1300_xopteps',\n", "       'fnd6_newa2v1300_xrd', 'fnd6_newa2v1300_xsga', 'fnd6_newq_xoptdqp',\n", "       'fnd6_newq_xoptepsqp', 'fnd6_newq_xoptqp',\n", "       'fnd6_newqv1300_acomincq', 'fnd6_newqv1300_acoq',\n", "       'fnd6_newqv1300_altoq', 'fnd6_newqv1300_ancq',\n", "       'fnd6_newqv1300_anoq', 'fnd6_newqv1300_aociderglq',\n", "       'fnd6_newqv1300_aociotherq', 'fnd6_newqv1300_aocipenq',\n", "       'fnd6_newqv1300_aocisecglq', 'fnd6_newqv1300_aol2q',\n", "       'fnd6_newqv1300_aoq', 'fnd6_newqv1300_aqpl1q',\n", "       'fnd6_newqv1300_aul3q', 'fnd6_newqv1300_capsq',\n", "       'fnd6_newqv1300_chq', 'fnd6_newqv1300_cibegniq',\n", "       'fnd6_newqv1300_cicurrq', 'fnd6_newqv1300_ciderglq',\n", "       'fnd6_newqv1300_cimiiq', 'fnd6_newqv1300_ciotherq',\n", "       'fnd6_newqv1300_cipenq', 'fnd6_newqv1300_ciq',\n", "       'fnd6_newqv1300_cisecglq', 'fnd6_newqv1300_citotalq',\n", "       'fnd6_newqv1300_cogsq', 'fnd6_newqv1300_csh12q',\n", "       'fnd6_newqv1300_cshfdq', 'fnd6_newqv1300_cshiq',\n", "       'fnd6_newqv1300_cshopq', 'fnd6_newqv1300_cshoq',\n", "       'fnd6_newqv1300_cshprq', 'fnd6_newqv1300_cstkq',\n", "       'fnd6_newqv1300_dcomq', 'fnd6_newqv1300_dilavq',\n", "       'fnd6_newqv1300_dlcq', 'fnd6_newqv1300_dpactq',\n", "       'fnd6_newqv1300_drcq', 'fnd6_newqv1300_drltq',\n", "       'fnd6_newqv1300_epsfiq', 'fnd6_newqv1300_epspiq',\n", "       'fnd6_newqv1300_epspxq', 'fnd6_newqv1300_esopnrq',\n", "       'fnd6_newqv1300_esoprq', 'fnd6_newqv1300_fcaq',\n", "       'fnd6_newqv1300_gdwlq', 'fnd6_newqv1300_glcea12',\n", "       'fnd6_newqv1300_glced12', 'fnd6_newqv1300_glceeps12',\n", "       'fnd6_newqv1300_ibadj12', 'fnd6_newqv1300_ibadjq',\n", "       'fnd6_newqv1300_ibcomq', 'fnd6_newqv1300_ibmiiq',\n", "       'fnd6_newqv1300_ibq', 'fnd6_newqv1300_icaptq',\n", "       'fnd6_newqv1300_intanoq', 'fnd6_newqv1300_intanq',\n", "       'fnd6_newqv1300_invfgq', 'fnd6_newqv1300_invoq',\n", "       'fnd6_newqv1300_invrmq', 'fnd6_newqv1300_invtq',\n", "       'fnd6_newqv1300_invwipq', 'fnd6_newqv1300_ivltq',\n", "       'fnd6_newqv1300_ivstq', 'fnd6_newqv1300_lcoq',\n", "       'fnd6_newqv1300_lltq', 'fnd6_newqv1300_lnoq',\n", "       'fnd6_newqv1300_lol2q', 'fnd6_newqv1300_loq',\n", "       'fnd6_newqv1300_loxdrq', 'fnd6_newqv1300_lqpl1q',\n", "       'fnd6_newqv1300_lseq', 'fnd6_newqv1300_ltmibq',\n", "       'fnd6_newqv1300_lul3q', 'fnd6_newqv1300_mibnq',\n", "       'fnd6_newqv1300_mibtq', 'fnd6_newqv1300_miiq',\n", "       'fnd6_newqv1300_msaq', 'fnd6_newqv1300_oepf12',\n", "       'fnd6_newqv1300_oepsxq', 'fnd6_newqv1300_optfvgrq',\n", "       'fnd6_newqv1300_optrfrq', 'fnd6_newqv1300_piq',\n", "       'fnd6_newqv1300_pncq', 'fnd6_newqv1300_ppegtq',\n", "       'fnd6_newqv1300_ppentq', 'fnd6_newqv1300_prcaq',\n", "       'fnd6_newqv1300_prcdq', 'fnd6_newqv1300_prcepsq',\n", "       'fnd6_newqv1300_prcraq', 'fnd6_newqv1300_rcpq',\n", "       'fnd6_newqv1300_rdipaq', 'fnd6_newqv1300_rdipdq',\n", "       'fnd6_newqv1300_rdipepsq', 'fnd6_newqv1300_rdipq',\n", "       'fnd6_newqv1300_recdq', 'fnd6_newqv1300_rectaq',\n", "       'fnd6_newqv1300_rectoq', 'fnd6_newqv1300_rectrq',\n", "       'fnd6_newqv1300_reunaq', 'fnd6_newqv1300_revtq',\n", "       'fnd6_newqv1300_seqoq', 'fnd6_newqv1300_seqq',\n", "       'fnd6_newqv1300_spcedpq', 'fnd6_newqv1300_spcedq',\n", "       'fnd6_newqv1300_spceepsp12', 'fnd6_newqv1300_spceepspq',\n", "       'fnd6_newqv1300_spceepsq', 'fnd6_newqv1300_spcep12',\n", "       'fnd6_newqv1300_spcepd12', 'fnd6_newqv1300_spcepq',\n", "       'fnd6_newqv1300_spceq', 'fnd6_newqv1300_spiq',\n", "       'fnd6_newqv1300_stkcoq', 'fnd6_newqv1300_stkcpaq',\n", "       'fnd6_newqv1300_teqq', 'fnd6_newqv1300_tfvaq',\n", "       'fnd6_newqv1300_tfvceq', 'fnd6_newqv1300_tfvlq',\n", "       'fnd6_newqv1300_tstknq', 'fnd6_newqv1300_tstkq',\n", "       'fnd6_newqv1300_txdbaq', 'fnd6_newqv1300_txdbq',\n", "       'fnd6_newqv1300_txdiq', 'fnd6_newqv1300_txditcq',\n", "       'fnd6_newqv1300_txpq', 'fnd6_newqv1300_txtq',\n", "       'fnd6_newqv1300_txwq', 'fnd6_newqv1300_wcapq',\n", "       'fnd6_newqv1300_xintq', 'fnd6_newqv1300_xoprq',\n", "       'fnd6_newqv1300_xoptdq', 'fnd6_newqv1300_xoptepsq',\n", "       'fnd6_newqv1300_xoptq', 'fnd6_newqv1300_xrdq',\n", "       'fnd6_newqv1300_xsgaq', 'fnd6_niadj', 'fnd6_nopio', 'fnd6_np',\n", "       'fnd6_npq', 'fnd6_oprepsx', 'fnd6_optca', 'fnd6_optdr',\n", "       'fnd6_optdrq', 'fnd6_optex', 'fnd6_optfvgr', 'fnd6_optgr',\n", "       'fnd6_optlife', 'fnd6_optlifeq', 'fnd6_optosby', 'fnd6_optosey',\n", "       'fnd6_optprcby', 'fnd6_optprcca', 'fnd6_optprcex', 'fnd6_optprcey',\n", "       'fnd6_optprcgr', 'fnd6_optprcwa', 'fnd6_optrfr', 'fnd6_optvol',\n", "       'fnd6_optvolq', 'fnd6_pidom', 'fnd6_pifo', 'fnd6_pncdq',\n", "       'fnd6_pncepsq', 'fnd6_pnrsho', 'fnd6_ppeveb', 'fnd6_prcc',\n", "       'fnd6_prccq', 'fnd6_prch', 'fnd6_prchq', 'fnd6_prcl', 'fnd6_prclq',\n", "       'fnd6_prstkc', 'fnd6_pstkc', 'fnd6_pstkl', 'fnd6_pstkrv',\n", "       'fnd6_rank', 'fnd6_rea', 'fnd6_reajo', 'fnd6_recco', 'fnd6_recd',\n", "       'fnd6_recta', 'fnd6_rectr', 'fnd6_siv', 'fnd6_spce', 'fnd6_sppe',\n", "       'fnd6_sppiv', 'fnd6_sstk', 'fnd6_state', 'fnd6_stkcpa', 'fnd6_teq',\n", "       'fnd6_tfva', 'fnd6_tfvce', 'fnd6_tfvl', 'fnd6_tlcf', 'fnd6_tstkc',\n", "       'fnd6_txbco', 'fnd6_txbcof', 'fnd6_txc', 'fnd6_txdba',\n", "       'fnd6_txdbca', 'fnd6_txdbcl', 'fnd6_txdbclq', 'fnd6_txdc',\n", "       'fnd6_txdfed', 'fnd6_txdfo', 'fnd6_txdi', 'fnd6_txds',\n", "       'fnd6_txfed', 'fnd6_txfo', 'fnd6_txndb', 'fnd6_txndba',\n", "       'fnd6_txndbl', 'fnd6_txndbr', 'fnd6_txo', 'fnd6_txpd', 'fnd6_txr',\n", "       'fnd6_txs', 'fnd6_txtubadjust', 'fnd6_txtubbegin', 'fnd6_txtubend',\n", "       'fnd6_txtubposdec', 'fnd6_txtubposinc', 'fnd6_txtubpospdec',\n", "       'fnd6_txtubpospinc', 'fnd6_txtubsettle', 'fnd6_txtubsoflimit',\n", "       'fnd6_txtubtxtr', 'fnd6_txtubxintbs', 'fnd6_txtubxintis',\n", "       'fnd6_txw', 'fnd6_weburl', 'fnd6_xacc', 'fnd6_xaccq', 'fnd6_xad',\n", "       'fnd6_xintopt', 'fnd6_xopr', 'fnd6_xpp', 'fnd6_xpr', 'fnd6_xrent',\n", "       'fnd6_zipcode', 'goodwill', 'income', 'income_beforeextra',\n", "       'income_tax', 'interest_expense', 'inventory',\n", "       'inventory_turnover', 'invested_capital', 'liabilities',\n", "       'liabilities_curr', 'operating_expense', 'operating_income',\n", "       'ppent', 'pretax_income', 'rd_expense', 'receivable',\n", "       'retained_earnings', 'return_assets', 'return_equity', 'revenue',\n", "       'sales', 'sales_growth', 'sales_ps', 'sga_expense',\n", "       'working_capital'], dtype=object)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["datafields_list_fnd6 = fnd6['id'].values\n", "datafields_list_fnd6"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["574"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(datafields_list_fnd6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 将datafield和operator替换到Alpha模板（框架）中group_rank(ts_rank{fundamental model data}, 252), industry), 批量生成Alpha"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 模板 ☞ <group_compare_op>(<ts_compare_op>(<company_fundamentals>, <days>), <group>)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["there are total 51660 alpha expressions\n"]}], "source": ["group_compare_op = ['group_rank', 'group_zscore', 'group_neutralize']\n", "ts_compare_op = ['ts_rank', 'ts_zscore', 'ts_av_diff']\n", "company_fundamentals = datafields_list_fnd6\n", "days = [600, 200]\n", "group = ['market', 'industry', 'subindustry', 'sector', 'densify(pv13_h_f1_sector)']   ###……\n", "\n", "alpha_expressions = []\n", "\n", "for gco in group_compare_op:\n", "    for tco in ts_compare_op:\n", "        # for index, row in company_fundamentals.iterrows():\n", "        for cf in company_fundamentals:\n", "            for d in days:\n", "                for grp in group:\n", "                    # alpha_expressions.append(f\"{gco}({tco}({row['id']}, {d}), {grp})\")\n", "                    alpha_expressions.append(f\"{gco}({tco}({cf}, {d}), {grp})\")\n", "                    \n", "# Print or return the result_strings list\n", "print(f'there are total {len(alpha_expressions)} alpha expressions')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["['group_rank(ts_rank(assets, 600), market)',\n", " 'group_rank(ts_rank(assets, 600), industry)',\n", " 'group_rank(ts_rank(assets, 600), subindustry)',\n", " 'group_rank(ts_rank(assets, 600), sector)',\n", " 'group_rank(ts_rank(assets, 600), densify(pv13_h_f1_sector))',\n", " 'group_rank(ts_rank(assets, 200), market)',\n", " 'group_rank(ts_rank(assets, 200), industry)',\n", " 'group_rank(ts_rank(assets, 200), subindustry)',\n", " 'group_rank(ts_rank(assets, 200), sector)',\n", " 'group_rank(ts_rank(assets, 200), densify(pv13_h_f1_sector))',\n", " 'group_rank(ts_rank(assets_curr, 600), market)',\n", " 'group_rank(ts_rank(assets_curr, 600), industry)',\n", " 'group_rank(ts_rank(assets_curr, 600), subindustry)',\n", " 'group_rank(ts_rank(assets_curr, 600), sector)',\n", " 'group_rank(ts_rank(assets_curr, 600), densify(pv13_h_f1_sector))',\n", " 'group_rank(ts_rank(assets_curr, 200), market)',\n", " 'group_rank(ts_rank(assets_curr, 200), industry)',\n", " 'group_rank(ts_rank(assets_curr, 200), subindustry)',\n", " 'group_rank(ts_rank(assets_curr, 200), sector)',\n", " 'group_rank(ts_rank(assets_curr, 200), densify(pv13_h_f1_sector))']"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["alpha_expressions[:20]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["there are 51660 Alphas to simulate\n"]}], "source": ["alpha_list = []\n", "\n", "for alpha_expression in alpha_expressions:\n", "    # 将如下alpha表达式与setting封装\")\n", "    simulation_data = {\n", "    \"type\": \"REGULAR\",\n", "    \"settings\": {\n", "          \"instrumentType\": \"EQUITY\",\n", "          \"region\": \"USA\",\n", "          \"universe\": \"TOP3000\",\n", "          \"delay\": 1,\n", "          \"decay\": 0,\n", "          \"neutralization\": \"SUBINDUSTRY\",\n", "          \"truncation\": 0.01,\n", "          \"pasteurization\": \"ON\",\n", "          \"unitHandling\": \"VERIFY\",\n", "          \"nanHandling\": \"OFF\",\n", "          \"language\": \"FASTEXPR\",\n", "          \"visualization\": <PERSON><PERSON><PERSON>,\n", "                },\n", "    \"regular\": alpha_expression\n", "    }\n", "\n", "    alpha_list.append(simulation_data)\n", "print(f\"there are {len(alpha_list)} Alphas to simulate\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'type': 'REGULAR',\n", " 'settings': {'instrumentType': 'EQUITY',\n", "  'region': 'USA',\n", "  'universe': 'TOP3000',\n", "  'delay': 1,\n", "  'decay': 0,\n", "  'neutralization': 'SUBINDUSTRY',\n", "  'truncation': 0.01,\n", "  'pasteurization': 'ON',\n", "  'unitHandling': 'VERIFY',\n", "  'nanHandling': 'OFF',\n", "  'language': 'FASTEXPR',\n", "  'visualization': Fals<PERSON>},\n", " 'regular': 'group_rank(ts_rank(assets, 600), market)'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["alpha_list[0]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["51660"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["len(alpha_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 将Alpha_list存到CSV文件中来, headers of the csv:type,settings,regular"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import csv\n", "# Write the CSV file\n", "with open('alpha_list_pending_simulated.csv', 'w', newline='') as csvfile:\n", "    fieldnames = ['type', 'settings', 'regular']\n", "    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)\n", "\n", "    writer.writeheader()\n", "    for item in alpha_list:\n", "        writer.writerow({\n", "            'type': item['type'],\n", "            'settings': item['settings'],\n", "            'regular': item['regular']\n", "        })"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}