{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AlphaSimulator 路线图\n", "\n", "## 1.初始化 AlphaSimulator\n", "使用以下参数初始化类：\n", "- `max_concurrent`：允许的最大并发回测数。\n", "- `username` 和 `password`：API 认证所需的登录凭证。\n", "- `alpha_list_file_path`：包含待回测 alphas 的 CSV 文件路径。\n", "\n", "使用 `Sign In` 进行会话初始化。\n", "\n", "## 2.登录过程\n", "- 尝试 API 登录认证。\n", "    - 如果成功：\n", "        - 继续管理回测任务。\n", "    - 如果失败：\n", "        - 记录错误日志并停止流程。\n", "\n", "## 3.主要回测管理循环\n", "持续管理回测任务，包括以下步骤：\n", "\n", "### （1）检查回测状态\n", "- 检查每个活动回测的当前状态。\n", "    - 如果有任何回测已完成，记录结果并将其从活动列表中移除。\n", "    - 如果没有活动回测，则记录日志消息并继续加载新的 alpha。\n", "\n", "### （2）加载新 Alpha 并进行回测\n", "- 如果 `sim_queue_ls` 为空，则从 CSV 文件中重新填充。\n", "- 如果当前回测数量未达到 `max_concurrent` 上限，从 `sim_queue_ls` 中弹出一个 alpha 并开始回测。\n", "\n", "### （3）回测 Alpha\n", "- 尝试进行 alpha 回测。\n", "    - **错误处理**：如果失败，重试直到达到限制次数。\n", "    - **成功时**：记录该回测的 location URL。\n", "    - **超过最大重试次数**：将 alpha 记录为失败，并写入 `fail_alphas.csv`。\n", "\n", "## 4.结束条件\n", "- **无活动回测**：记录日志消息，显示空闲状态。\n", "- **达到错误阈值时**：记录错误详情并尝试重新登录。\n", "- **会话认证失败**：若无法重新认证则正常退出流程。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取账号密码"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "from os.path import expanduser\n", "from requests.auth import HTTPBasicAuth\n", "\n", "with open(expanduser('user_name.txt')) as f:\n", "    credentials = json.load(f)\n", "\n", "# Extract username and password from the list\n", "username, password = credentials"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## AlphaSimulator"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import time\n", "import csv\n", "import requests\n", "import os\n", "import ast\n", "from datetime import datetime\n", "from pytz import timezone\n", "\n", "# 获取美国东部时间\n", "eastern = timezone('US/Eastern')\n", "fmt = '%Y-%m-%d'\n", "loc_dt = datetime.now(eastern)\n", "print(\"Current time in Eastern is\", loc_dt.strftime(fmt))\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', filename='simulation.log', filemode='a')\n", "\n", "class AlphaSimulator:\n", "    def __init__(self, max_concurrent, username, password, alpha_list_file_path,batch_number_for_every_queue):\n", "        self.fail_alphas = 'fail_alphas.csv'\n", "        self.simulated_alphas = f'simulated_alphas_{loc_dt.strftime(fmt)}.csv'\n", "        self.max_concurrent = max_concurrent\n", "        self.active_simulations = []\n", "        self.username = username\n", "        self.password = password\n", "        self.session = self.sign_in(username, password)\n", "        self.alpha_list_file_path = alpha_list_file_path\n", "        self.sim_queue_ls = []\n", "        self.batch_number_for_every_queue = batch_number_for_every_queue\n", "\n", "    def sign_in(self, username, password):\n", "        s = requests.Session()\n", "        s.auth = (username, password)\n", "        count = 0\n", "        count_limit = 30\n", "        while True:\n", "            try:\n", "                response = s.post('https://api.worldquantbrain.com/authentication')\n", "                response.raise_for_status()\n", "                break\n", "            except:\n", "                count += 1\n", "                logging.error(\"Connection down, trying to login again...\")\n", "                time.sleep(15)\n", "                if count > count_limit:\n", "                    logging.error(f\"{username} failed too many times, returning None.\")\n", "                    return None\n", "        logging.info(\"Login to BRAIN successfully.\")\n", "        return s\n", "\n", "    def read_alphas_from_csv_in_batches(self, batch_size=50):\n", "        '''\n", "        1. 打开alpha_list_pending_simulated\n", "        2. 取出batch_size个alpha,放入列表变量alphas\n", "        3. 取出后覆写（overwrite）alpha_list_pending_simulated\n", "        4. 把取出的alphas,写到sim_queue.csv文件中，方便随时监控在排队的alpha有多少\n", "        5. 返回列表变量alphas\n", "        '''\n", "        alphas = []\n", "        temp_file_name = self.alpha_list_file_path + '.tmp'\n", "        with open(self.alpha_list_file_path, 'r') as file, open(temp_file_name, 'w', newline='') as temp_file:\n", "            reader = csv.DictReader(file)\n", "            fieldnames = reader.fieldnames\n", "            writer = csv.DictWriter(temp_file, fieldnames=fieldnames)\n", "            writer.writeheader()\n", "            for _ in range(batch_size):\n", "                try:\n", "                    row = next(reader)\n", "                    if 'settings' in row:\n", "                        if isinstance(row['settings'], str):\n", "                            try:\n", "                                row['settings'] = ast.literal_eval(row['settings'])\n", "                            except (<PERSON><PERSON><PERSON><PERSON>, SyntaxError):\n", "                                print(f\"Error evaluating settings: {row['settings']}\")\n", "                        elif isinstance(row['settings'], dict):\n", "                            pass\n", "                        else:\n", "                            print(f\"Unexpected type for settings: {type(row['settings'])}\")\n", "                    alphas.append(row)\n", "                except StopIteration:\n", "                    break\n", "            for remaining_row in reader:\n", "                writer.writerow(remaining_row)\n", "        os.replace(temp_file_name, self.alpha_list_file_path)\n", "        if alphas:\n", "            with open('sim_queue.csv', 'w', newline='') as file:\n", "                writer = csv.DictWriter(file, fieldnames=alphas[0].keys())\n", "                if file.tell() == 0:\n", "                    writer.writeheader()\n", "                writer.writerows(alphas)\n", "        return alphas\n", "\n", "    def simulate_alpha(self, alpha):\n", "        count = 0\n", "        while True:\n", "            try:\n", "                response = self.session.post('https://api.worldquantbrain.com/simulations', json=alpha)\n", "                response.raise_for_status()\n", "                if \"Location\" in response.headers:\n", "                    logging.info(\"Alpha location retrieved successfully.\")\n", "                    logging.info(f\"Location: {response.headers['Location']}\")\n", "                    return response.headers['Location']\n", "            except requests.exceptions.RequestException as e:\n", "                logging.error(f\"Error in sending simulation request: {e}\")\n", "                if count > 35:\n", "                    self.session = self.sign_in(self.username, self.password)\n", "                    logging.error(\"Error occurred too many times, skipping this alpha and re-logging in.\")\n", "                    break\n", "                logging.error(\"Error in sending simulation request. Retrying after 5s...\")\n", "                time.sleep(5)\n", "                count += 1\n", "        logging.error(f\"Simulation request failed after {count} attempts.\")\n", "        with open(self.fail_alphas, 'a', newline='') as file:\n", "            writer = csv.DictWriter(file, fieldnames=alpha.keys())\n", "            writer.writerow(alpha)\n", "        return None\n", "\n", "    def load_new_alpha_and_simulate(self):\n", "        if len(self.sim_queue_ls) < 1:\n", "            self.sim_queue_ls = self.read_alphas_from_csv_in_batches(self.batch_number_for_every_queue)  \n", "    \n", "        if len(self.active_simulations) >= self.max_concurrent:\n", "            logging.info(f\"Max concurrent simulations reached ({self.max_concurrent}). Waiting 2 seconds\")\n", "            time.sleep(2)\n", "            return\n", "       \n", "        logging.info('Loading new alpha...')\n", "        try:\n", "            alpha = self.sim_queue_ls.pop(0)\n", "            logging.info(f\"Starting simulation for alpha: {alpha['regular']} with settings: {alpha['settings']}\")\n", "            location_url = self.simulate_alpha(alpha)\n", "            if location_url:\n", "                self.active_simulations.append(location_url)\n", "        except IndexError:\n", "            logging.info(\"No more alphas available in the queue.\")\n", "\n", "    def check_simulation_progress(self, simulation_progress_url):\n", "        try:\n", "            simulation_progress = self.session.get(simulation_progress_url)\n", "            simulation_progress.raise_for_status()\n", "            if simulation_progress.headers.get(\"Retry-After\", 0) == 0:\n", "                alpha_id = simulation_progress.json().get(\"alpha\")\n", "                if alpha_id:\n", "                    alpha_response = self.session.get(f\"https://api.worldquantbrain.com/alphas/{alpha_id}\")\n", "                    alpha_response.raise_for_status()\n", "                    return alpha_response.json()\n", "                else:\n", "                    return simulation_progress.json()\n", "            else:\n", "                return None\n", "        except requests.exceptions.RequestException as e:\n", "            logging.error(f\"Error fetching simulation progress: {e}\")\n", "            self.session = self.sign_in(self.username, self.password)\n", "            return None\n", "        \n", "    def check_simulation_status(self):\n", "        count = 0\n", "        if len(self.active_simulations) == 0:\n", "            logging.info(\"No one is in active simulation now\")\n", "            return None\n", "        for sim_url in self.active_simulations:\n", "            sim_progress = self.check_simulation_progress(sim_url)\n", "            if sim_progress is None:\n", "                count += 1\n", "                continue\n", "            alpha_id = sim_progress.get(\"id\")\n", "            status = sim_progress.get(\"status\")\n", "            logging.info(f\"Alpha id: {alpha_id} ended with status: {status}. Removing from active list.\")\n", "            self.active_simulations.remove(sim_url)\n", "            with open(self.simulated_alphas, 'a', newline='') as file:\n", "                writer = csv.DictWriter(file, fieldnames=sim_progress.keys())\n", "                writer.writerow(sim_progress)\n", "        logging.info(f\"Total {count} simulations are in process for account {self.username}.\")\n", "\n", "    def manage_simulations(self):\n", "        if not self.session:\n", "            logging.error(\"Failed to sign in. Exiting...\")\n", "            return\n", "        while True:\n", "            self.check_simulation_status()\n", "            self.load_new_alpha_and_simulate()\n", "            time.sleep(3)\n", "\n", "# Example usage\n", "alpha_list_file_path = 'alpha_list_pending_simulated.csv'  # replace with your actual file path\n", "simulator = AlphaSimulator(max_concurrent=3, username=username, password=password, alpha_list_file_path=alpha_list_file_path,batch_number_for_every_queue=20)\n", "simulator.manage_simulations()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}