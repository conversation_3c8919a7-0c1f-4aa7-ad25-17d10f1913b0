import pandas as pd
import requests


def login():
    username = ""
    password = ""

    # Create a session to persistently store the headers
    s = requests.Session()

    # Save credentials into session
    s.auth = (username, password)

    # Send a POST request to the /authentication API
    response = s.post('https://api.worldquantbrain.com/authentication')
    print(response.content)
    return s


def get_operators(s):
    url = "https://api.worldquantbrain.com/operators"

    # Add headers
    headers = {
        'Accept': 'application/json;version=2.0',
        'Content-Type': 'application/json',
        'authority': 'api.worldquantbrain.com',
        'origin': 'https://platform.worldquantbrain.com',
        'referer': 'https://platform.worldquantbrain.com/learn/operators'
    }
    s.headers.update(headers)

    try:
        result = s.get(url)
        print(f"Status Code: {result.status_code}")

        if result.status_code == 200:
            data = result.json()
            # List to DataFrame
            df = pd.DataFrame(data)
            print(f"Fetched {len(df)} operators")
            return df
        else:
            print(f"Error: Status code {result.status_code}")
            print(f"Response content: {result.text}")
            # Print errors
            print(f"Request headers: {result.request.headers}")
            print(f"Auth info present: {s.auth is not None}")
            return pd.DataFrame()
    except Exception as e:
        print(f"Error fetching operators: {e}")
        if hasattr(e, 'response'):
            print(f"Response content: {e.response.text}")
        return pd.DataFrame()

s = login()
# Fetch and save operators
operators_df = get_operators(s)
if not operators_df.empty:
    operators_df.to_csv("user_operators.csv", index=False)
    operators_df
    print("Operators saved to 'user_operators.csv'")
else:
    print("No operators data to save.")