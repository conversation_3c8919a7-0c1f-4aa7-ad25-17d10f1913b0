{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### <b>一. 自相关检测的重要性</b>\n", "##### 在投资组合构建中，降低因子之间的自相关性（self-correlation）非常重要，原因可从以下几个维度来理解：\n", "##### 1. 提升组合多样性：当因子之间高度相关时，它们往往在相似的市场条件下同时赚钱或同时亏钱，本质上只是放大了单一逻辑的风险敞口；相反，低相关性的因子在不同市场情境下可能表现出互补特性，从而在整体上平滑组合的波动，提高稳定性；\n", "##### 2. 高相关因子一起使用时，容易导致模型捕捉到数据中的噪声结构而非真正的市场信号；\n", "##### 3. 若该因子与已提交因子高度相关，它对整体表现的边际增益将很小，甚至可能降低表现，因为我们每个评价周期可以提交的因子是有限的，所以应该尽可能提升每个Alpha的边际增益。\n", "##### 注：BRAIN平台的自相关性计算以半年为周期滚动。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <b>二. 工具简介</b>\n", "##### 使用平台计算资源检验自相关性有着速度慢，被限流检验失败，挤占ProdCorr计算资源等问题。同时，计算自相关性的所有数据均可以获取到本地，并且Pandas等本地工具已内建成熟高效的自相关性计算函数，支持灵活地按日期滑窗、按分组股票池等维度进行分析。因此，在本地环境下进行自相关性检验，不仅可以规避平台资源限制带来的不确定性，也具备更高的效率、灵活性与性价比，是值得优先采用的方式。\n", "#### <b>主要应用</b>\n", "##### 1. 将自相关性检查加入CheckCorrelation流程，在检查ProdCorr之前先在本地检验自相关性，只检验自相关性小于某个阈值的ProdCorr，从而降低平台计算资源的请求频率，避免被限流，从而显著提升CheckCorr的效率，\n", "##### 2. 自相关性可以嵌入到set_alpha_property之前。最近的PPAC Theme要求为因子打上desc，但是如果为每个因子都打desc会浪费不少的时间，因此可以提前检测自相关性，只有PPAC_Corr <= 0.5的再打desc。\n", "##### <b>源代码来自KZ79256</b>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <b>三. 工作流程</b>\n", "##### 1. 增量获取已提交alpha的PnL和alpha_id, 并保存为pickle文件\n", "##### 2. 获取目标alpha的全量信息\n", "##### 3. 准备目标alpha的PnL和日收益率\n", "##### 4. 截取近4年alpha_rets数据\n", "##### 5. 计算目标alpha与同region的已提交alpha的皮尔逊相关性\n", "\n", "###### *注：2025/5/20后平台自相关算法估计有小幅变化，导致普通自相关计算出现小幅误差，PPAC自相关计算不受影响。*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <b>四. 环境准备</b>\n", "#####  python_version >= 3.13.2\n", "#####  numpy >= 2.2.4\n", "#####  pandas >= 2.2.3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入官方库\n", "from collections import defaultdict\n", "from concurrent.futures import ThreadPoolExecutor\n", "import logging\n", "import os\n", "from pathlib import Path\n", "import pickle\n", "import requests\n", "import time\n", "from typing import Dict, List, Optional, Tuple\n", "\n", "# 导入第三方库\n", "from dotenv import load_dotenv  # 导入环境变量，非必需 pip install python-dotenv\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# 读取环境变量文件\n", "load_dotenv()\n", "\n", "# 定义用户名, 密码, 文件存储路径，如果明文写入了用户名密码，千万不要把代码上传到公开平台（GitHub, <PERSON><PERSON><PERSON>, B站, 小红书等）！！！\n", "class cfg:\n", "    username = os.getenv('WQ_EMAIL')  # \" \"\n", "    password = os.getenv('WQ_PASSWORD')  # \" \"\n", "    data_path = Path('./data')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <b>五. 定义工具函数</b>"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 定义基础功能函数\n", "def sign_in(username, password):\n", "    \"\"\"\n", "    登录BRAIN平台\n", "\n", "    Arguments:\n", "        username (str): 用户名。\n", "        password (str): 密码。\n", "\n", "    Returns:\n", "        requests.Session or None\n", "    \"\"\"\n", "    s = requests.Session()\n", "    s.auth = (username, password)\n", "    try:\n", "        response = s.post('https://api.worldquantbrain.com/authentication')\n", "        response.raise_for_status()\n", "        logging.info(\"Successfully signed in\")\n", "        return s\n", "    except requests.exceptions.RequestException as e:\n", "        logging.error(f\"Lo<PERSON> failed: {e}\")\n", "        return None\n", "\n", "\n", "def save_obj(obj: object, name: str) -> None:\n", "    \"\"\"\n", "    保存对象到文件中，以 pickle 格式序列化。\n", "\n", "    Args:\n", "        obj (object): 需要保存的对象。\n", "        name (str): 文件名（不包含扩展名），保存的文件将以 '.pickle' 为扩展名。\n", "\n", "    Returns:\n", "        None: 此函数无返回值。\n", "\n", "    Raises:\n", "        pickle.PickleError: 如果序列化过程中发生错误。\n", "        IOError: 如果文件写入过程中发生 I/O 错误。\n", "    \"\"\"\n", "    with open(name + '.pickle', 'wb') as f:\n", "        pickle.dump(obj, f, pickle.HIGHEST_PROTOCOL)\n", "\n", "\n", "def load_obj(name: str) -> object:\n", "    \"\"\"\n", "    加载指定名称的 pickle 文件并返回其内容。\n", "\n", "    此函数会打开一个以 `.pickle` 为扩展名的文件，并使用 `pickle` 模块加载其内容。\n", "\n", "    Args:\n", "        name (str): 不带扩展名的文件名称。\n", "\n", "    Returns:\n", "        object: 从 pickle 文件中加载的 Python 对象。\n", "\n", "    Raises:\n", "        FileNotFoundError: 如果指定的文件不存在。\n", "        pickle.UnpicklingError: 如果文件内容无法被正确反序列化。\n", "    \"\"\"\n", "    with open(name + '.pickle', 'rb') as f:\n", "        return pickle.load(f)\n", "\n", "\n", "def wait_get(url: str, max_retries: int = 10) -> \"Response\":\n", "    \"\"\"\n", "    发送带有重试机制的 GET 请求，直到成功或达到最大重试次数。\n", "    此函数会根据服务器返回的 `Retry-After` 头信息进行等待，并在遇到 401 状态码时重新初始化配置。\n", "\n", "    Args:\n", "        url (str): 目标 URL。\n", "        max_retries (int, optional): 最大重试次数，默认为 10。\n", "\n", "    Returns:\n", "        Response: 请求的响应对象。\n", "    \"\"\"\n", "    retries = 0\n", "    while retries < max_retries:\n", "        while True:\n", "            simulation_progress = sess.get(url)\n", "            if simulation_progress.headers.get(\"Retry-After\", 0) == 0:\n", "                break\n", "            time.sleep(float(simulation_progress.headers[\"Retry-After\"]))\n", "        if simulation_progress.status_code < 400:\n", "            break\n", "        else:\n", "            time.sleep(2 ** retries)\n", "            retries += 1\n", "    return simulation_progress"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <b>六. 定义获取alpha_pnl的函数</b>"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2025-05-21T06:54:26.590017Z", "start_time": "2025-05-21T06:54:26.571624Z"}}, "outputs": [], "source": ["def _get_alpha_pnl(alpha_id: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    获取指定 alpha 的 PnL数据，并返回一个包含日期和 PnL 的 DataFrame。\n", "\n", "    此函数通过调用 WorldQuant Brain API 获取指定 alpha 的 PnL 数据，\n", "    并将其转换为 pandas DataFrame 格式，方便后续数据处理。\n", "\n", "    Args:\n", "        alpha_id (str): Alpha 的唯一标识符。\n", "\n", "    Returns:\n", "        pd.DataFrame: 包含日期和对应 PnL 数据的 DataFrame，列名为 'Date' 和 alpha_id。\n", "    \"\"\"\n", "    pnl = wait_get(\"https://api.worldquantbrain.com/alphas/\" + alpha_id + \"/recordsets/pnl\").json()\n", "    df = pd.DataFrame(pnl['records'], columns=[item['name'] for item in pnl['schema']['properties']])\n", "    df = df.rename(columns={'date':'Date', 'pnl':alpha_id})\n", "    df = df[['Date', alpha_id]]\n", "    return df\n", "\n", "\n", "def get_alpha_pnls(\n", "    alphas: list[dict], \n", "    alpha_pnls: Optional[pd.DataFrame] = None, \n", "    alpha_ids: Optional[dict[str, list]] = None\n", ") -> <PERSON><PERSON>[dict[str, list], pd.DataFrame]:\n", "    \"\"\"\n", "    获取 alpha 的 PnL 数据，并按区域分类 alpha 的 ID。\n", "\n", "    Args:\n", "        alphas (list[dict]): 包含 alpha 信息的列表，每个元素是一个字典，包含 alpha 的 ID 和设置等信息。\n", "        alpha_pnls (Optional[pd.DataFrame], 可选): 已有的 alpha PnL 数据，默认为空的 DataFrame。\n", "        alpha_ids (Optional[dict[str, list]], 可选): 按区域分类的 alpha ID 字典，默认为空字典。\n", "\n", "    Returns:\n", "        Tuple[dict[str, list], pd.DataFrame]: \n", "            - 按区域分类的 alpha ID 字典。\n", "            - 包含所有 alpha 的 PnL 数据的 DataFrame。\n", "    \"\"\"\n", "    # 如果alpha_ids字典没有传入，则生成一个默认值为list()的字典存储alpha_id以及一个dataframe存储alpha_pnls\n", "    if alpha_ids is None:\n", "        alpha_ids = defaultdict(list)\n", "    if alpha_pnls is None:\n", "        alpha_pnls = pd.DataFrame()\n", "    # 筛选新增的alpha_id\n", "    new_alphas = [item for item in alphas if item['id'] not in alpha_pnls.columns]\n", "    if not new_alphas:\n", "        return alpha_ids, alpha_pnls\n", "    # 在alpha_ids字典中的对应region增加新增的alpha_id\n", "    for item_alpha in new_alphas:\n", "        alpha_ids[item_alpha['settings']['region']].append(item_alpha['id'])\n", "\n", "    # 使用线程池并发对多个alpha_ids批量抓取PnL数据\n", "    fetch_pnl_func = lambda alpha_id: _get_alpha_pnl(alpha_id).set_index('Date')\n", "    with ThreadPoolExecutor(max_workers=10) as executor:\n", "        results = executor.map(fetch_pnl_func, [item['id'] for item in new_alphas])\n", "    # 把获取的alpha_pnls和已有的PnL数据合并为一个dataframe，并按日期升序排列\n", "    alpha_pnls = pd.concat([alpha_pnls] + list(results), axis=1)\n", "    alpha_pnls.sort_index(inplace=True)\n", "\n", "    return alpha_ids, alpha_pnls\n", "\n", "\n", "def get_os_alphas(limit: int = 100, get_first: bool = False) -> List[Dict]:\n", "    \"\"\"\n", "    获取OS阶段的alpha列表。\n", "\n", "    此函数通过调用WorldQuant Brain API获取用户的alpha列表，支持分页获取，并可以选择只获取第一个结果。\n", "\n", "    Args:\n", "        limit (int, optional): 每次请求获取的alpha数量限制。默认为100。\n", "        get_first (bool, optional): 是否只获取第一次请求的alpha结果。如果为True，则只请求一次。默认为False。\n", "\n", "    Returns:\n", "        List[Dict]: 包含alpha信息的字典列表，每个字典表示一个alpha。\n", "    \"\"\"\n", "    fetched_alphas = []\n", "    offset = 0\n", "    retries = 0\n", "    total_alphas = 100\n", "    while len(fetched_alphas) < total_alphas:\n", "        print(f\"Fetching alphas from offset {offset} to {offset + limit}\")\n", "        url = f\"https://api.worldquantbrain.com/users/self/alphas?stage=OS&limit={limit}&offset={offset}&order=-dateSubmitted\"\n", "        res = wait_get(url).json()\n", "        if offset == 0:\n", "            total_alphas = res['count']\n", "        alphas = res[\"results\"]\n", "        fetched_alphas.extend(alphas)\n", "        if len(alphas) < limit:\n", "            break\n", "        offset += limit\n", "        if get_first:\n", "            break\n", "    return fetched_alphas[:total_alphas]\n", "\n", "\n", "def download_data(flag_increment=True):\n", "    \"\"\"\n", "    下载数据并保存到指定路径。\n", "\n", "    此函数会检查数据是否已经存在，如果不存在，则从 API 下载数据并保存到指定路径。\n", "\n", "    Args:\n", "        flag_increment (bool): 是否使用增量下载，默认为 True。\n", "    \"\"\"\n", "    # 根据是否增量下载给变量赋值\n", "    if flag_increment:\n", "        try:\n", "            os_alpha_ids = load_obj(str(cfg.data_path / 'os_alpha_ids'))\n", "            os_alpha_pnls = load_obj(str(cfg.data_path / 'os_alpha_pnls'))\n", "            ppac_alpha_ids = load_obj(str(cfg.data_path / 'ppac_alpha_ids'))\n", "            exist_alpha = [alpha for ids in os_alpha_ids.values() for alpha in ids]\n", "        except Exception as e:\n", "            logging.error(f\"Failed to load existing data: {e}\")\n", "            os_alpha_ids = None\n", "            os_alpha_pnls = None\n", "            exist_alpha = []\n", "            ppac_alpha_ids = []\n", "    else:\n", "        os_alpha_ids = None\n", "        os_alpha_pnls = None\n", "        exist_alpha = []\n", "        ppac_alpha_ids = []\n", "    # 增量/全量获取已提交alpha信息\n", "    if os_alpha_ids is None:\n", "        alphas = get_os_alphas(limit=100, get_first=False)\n", "    else:\n", "        alphas = get_os_alphas(limit=30, get_first=True)  # 可以根据实际情况调整limit\n", "    # 筛选新增alpha_id的数据\n", "    alphas = [item for item in alphas if item['id'] not in exist_alpha]\n", "    # 获取符合ppac theme的alpha_ids\n", "    ppac_alpha_ids += [item['id'] for item in alphas for item_match in item['classifications'] if item_match['name'] == 'Power Pool Alpha']\n", "    # 获取全量的alpha_pnls以及全量alpha_ids和ppac主题alpha_ids，并保存为pickle文件\n", "    os_alpha_ids, os_alpha_pnls = get_alpha_pnls(alphas, alpha_pnls=os_alpha_pnls, alpha_ids=os_alpha_ids)\n", "    save_obj(os_alpha_ids, str(cfg.data_path / 'os_alpha_ids'))\n", "    save_obj(os_alpha_pnls, str(cfg.data_path / 'os_alpha_pnls'))\n", "    save_obj(ppac_alpha_ids, str(cfg.data_path / 'ppac_alpha_ids'))\n", "    print(f'新下载的alpha数量: {len(alphas)}, 目前总共alpha数量: {os_alpha_pnls.shape[1]}')\n", "\n", "\n", "def load_data(tag=None):\n", "    \"\"\"\n", "    加载数据。\n", "\n", "    此函数会检查数据是否已经存在，如果不存在，则从 API 下载数据并保存到指定路径。\n", "\n", "    Args:\n", "        tag (str): 数据标记，默认为 None。\n", "    \"\"\"\n", "\n", "    # 读取pickle文件\n", "    os_alpha_ids = load_obj(str(cfg.data_path / 'os_alpha_ids'))\n", "    os_alpha_pnls = load_obj(str(cfg.data_path / 'os_alpha_pnls'))\n", "    ppac_alpha_ids = load_obj(str(cfg.data_path / 'ppac_alpha_ids'))\n", "\n", "    # 根据tag筛选alpha_pnls\n", "    if tag=='PPAC':\n", "        for item in os_alpha_ids:\n", "            os_alpha_ids[item] = [alpha for alpha in os_alpha_ids[item] if alpha in ppac_alpha_ids]\n", "    elif tag=='SelfCorr':\n", "        for item in os_alpha_ids:\n", "            os_alpha_ids[item] = [alpha for alpha in os_alpha_ids[item] if alpha not in ppac_alpha_ids]\n", "    else:\n", "        os_alpha_ids = os_alpha_ids\n", "\n", "    exist_alpha = [alpha for ids in os_alpha_ids.values() for alpha in ids]\n", "    os_alpha_pnls = os_alpha_pnls[exist_alpha]\n", "\n", "    # 计算已提交alpha的日度收益率并截取近4年数据\n", "    os_alpha_rets = os_alpha_pnls - os_alpha_pnls.ffill().shift(1)\n", "    os_alpha_rets = os_alpha_rets[pd.to_datetime(os_alpha_rets.index)>pd.to_datetime(os_alpha_rets.index).max() - pd.DateOffset(years=4)]\n", "    return os_alpha_ids, os_alpha_rets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <b>七. 定义计算相关性函数</b>"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def calc_self_corr(\n", "    alpha_id: str,\n", "    os_alpha_rets: pd.DataFrame | None = None,\n", "    os_alpha_ids: dict[str, str] | None = None,\n", "    alpha_result: dict | None = None,\n", "    return_alpha_pnls: bool = False,\n", "    alpha_pnls: pd.DataFrame | None = None\n", ") -> float | tuple[float, pd.DataFrame]:\n", "    \"\"\"\n", "    计算指定 alpha 与其他 alpha 的最大自相关性。\n", "\n", "    Args:\n", "        alpha_id (str): 目标 alpha 的唯一标识符。\n", "        os_alpha_rets (pd.DataFrame | None, optional): 其他 alpha 的收益率数据，默认为 None。\n", "        os_alpha_ids (dict[str, str] | None, optional): 其他 alpha 的标识符映射，默认为 None。\n", "        alpha_result (dict | None, optional): 目标 alpha 的详细信息，默认为 None。\n", "        return_alpha_pnls (bool, optional): 是否返回 alpha 的 PnL 数据，默认为 False。\n", "        alpha_pnls (pd.DataFrame | None, optional): 目标 alpha 的 PnL 数据，默认为 None。\n", "\n", "    Returns:\n", "        float | tuple[float, pd.DataFrame]: 如果 `return_alpha_pnls` 为 False，返回最大自相关性值；\n", "            如果 `return_alpha_pnls` 为 True，返回包含最大自相关性值和 alpha PnL 数据的元组。\n", "    \"\"\"\n", "    # 按需获取alpha_result\n", "    if alpha_result is None:\n", "        alpha_result = wait_get(f\"https://api.worldquantbrain.com/alphas/{alpha_id}\").json()\n", "\n", "    # 按需获取alpha_pnls\n", "    if alpha_pnls is not None:\n", "        if len(alpha_pnls) == 0:\n", "            alpha_pnls = None\n", "    if alpha_pnls is None:\n", "        _, alpha_pnls = get_alpha_pnls([alpha_result])\n", "        alpha_pnls = alpha_pnls[alpha_id]\n", "\n", "    # 计算因子的日度收益率\n", "    alpha_rets = alpha_pnls - alpha_pnls.ffill().shift(1)\n", "    # 截取最近4年数据\n", "    alpha_rets = alpha_rets[pd.to_datetime(alpha_rets.index)>pd.to_datetime(alpha_rets.index).max() - pd.DateOffset(years=4)]\n", "    # 打印结果到控制台\n", "    print(os_alpha_rets[os_alpha_ids[alpha_result['settings']['region']]].corrwith(alpha_rets).sort_values(ascending=False).round(4))\n", "    # 按需决定是否需要保存到csv文件\n", "    # os_alpha_rets[os_alpha_ids[alpha_result['settings']['region']]].corrwith(alpha_rets).sort_values(ascending=False).round(4).to_csv(str(cfg.data_path / 'os_alpha_corr.csv'))\n", "    # 返回最大值\n", "    self_corr = os_alpha_rets[os_alpha_ids[alpha_result['settings']['region']]].corrwith(alpha_rets).max()\n", "    if np.isnan(self_corr):\n", "        self_corr = 0\n", "    if return_alpha_pnls:\n", "        return self_corr, alpha_pnls\n", "    else:\n", "        return self_corr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <b>八. 调用函数计算自相关性</b>"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2025-05-21T06:54:28.138378Z", "start_time": "2025-05-21T06:54:27.017622Z"}}, "outputs": [], "source": ["# 登录\n", "global sess\n", "sess = sign_in(cfg.username, cfg.password)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2025-05-21T06:54:29.859537Z", "start_time": "2025-05-21T06:54:28.156474Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fetching alphas from offset 0 to 30\n", "新下载的alpha数量: 7, 目前总共alpha数量: 786\n"]}], "source": ["# 增量下载数据\n", "download_data(flag_increment=True)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2025-05-21T06:54:32.570060Z", "start_time": "2025-05-21T06:54:30.116986Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7ZeYq7O    0.5112\n", "A0llYKX    0.4517\n", "rjOAqv9    0.4477\n", "e9zzmpO    0.4231\n", "6rxeLr7    0.4148\n", "            ...  \n", "7Za5XG2   -0.1968\n", "GdQKWAG   -0.2149\n", "a1QZLm9   -0.2188\n", "QGNdPgW   -0.2336\n", "2OOJgv8   -0.2435\n", "Length: 224, dtype: float64\n"]}, {"data": {"text/plain": ["np.float64(0.5112330270153354)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 计算相关性\n", "alpha_id = 'zArN9G1'\n", "os_alpha_ids, os_alpha_rets = load_data(tag='SelfCorr')  # 加载数据， 如果需要使用不同的标签，可以传入 tag 参数， 例如 tag='PPAC' 或 tag='SelfCorr'\n", "calc_self_corr(\n", "    alpha_id=alpha_id,\n", "    os_alpha_rets=os_alpha_rets,\n", "    os_alpha_ids=os_alpha_ids,\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2025-05-21T06:54:33.405575Z", "start_time": "2025-05-21T06:54:32.593787Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["L8ZQv1M    0.4527\n", "QWmAnRW    0.4047\n", "xbEjoWb    0.4041\n", "LZ0PeYm    0.3896\n", "lKYbkQN    0.3625\n", "            ...  \n", "NVE010p   -0.1117\n", "aa0mPVR   -0.1244\n", "oE21k62   -0.1245\n", "w9v5xZp   -0.1431\n", "d9WOkOX   -0.1958\n", "Length: 87, dtype: float64\n"]}, {"data": {"text/plain": ["np.float64(0.45271395078142107)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["alpha_id = 'zArN9G1'\n", "os_alpha_ids, os_alpha_rets = load_data(tag='PPAC')\n", "calc_self_corr(\n", "    alpha_id=alpha_id,\n", "    os_alpha_rets=os_alpha_rets,\n", "    os_alpha_ids=os_alpha_ids,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <b>七. 检验结果</b>\n", "\n", "![](https://pic1.imgdb.cn/item/682d8a1858cb8da5c801dac8.jpg)\n", "![](https://pic1.imgdb.cn/item/682d3d5158cb8da5c80020fa.jpg)\n"]}], "metadata": {"kernelspec": {"display_name": "Quant", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}