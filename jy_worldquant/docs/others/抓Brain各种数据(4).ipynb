{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 抓Brain平台数据专用\n", "\n", "- 抓Operators\n", "- 抓所有datasets和datafields\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## login 账户"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from os import environ\n", "import os\n", "from time import sleep\n", "import time\n", "import json\n", "import pandas as pd\n", "\n", "\n", "def login():\n", "    \n", "    username = \"\"\n", "    password = \"\"\n", " \n", "    # Create a session to persistently store the headers\n", "    s = requests.Session()\n", " \n", "    # Save credentials into session\n", "    s.auth = (username, password)\n", " \n", "    # Send a POST request to the /authentication API\n", "    response = s.post('https://api.worldquantbrain.com/authentication')\n", "    print(response.content)\n", "    return s  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"ZS59763\"},\"token\":{\"expiry\":43200.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"CONSULTANT\",\"MULTI_SIMULATION\",\"PROD_ALPHAS\",\"REFERRAL\",\"<PERSON>UPER_ALPHA\",\"VISUALIZAT<PERSON>\",\"WORKDAY\"]}'\n"]}], "source": ["import pandas as pd\n", "\n", "user_id = \"\"\n", "s = login()\n", "res = s.get(\"https://api.worldquantbrain.com/operators\")\n", "df = pd.<PERSON><PERSON><PERSON><PERSON>(res.json())[['name']]\n", "df.to_csv(f\"{user_id}_operators.csv\", index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 抓 Operators\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"ZS59763\"},\"token\":{\"expiry\":43200.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"CONSULTANT\",\"MULTI_SIMULATION\",\"PROD_ALPHAS\",\"REFERRAL\",\"<PERSON>UPER_ALPHA\",\"VISUALIZAT<PERSON>\",\"WORKDAY\"]}'\n"]}], "source": ["s=login()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# create a function to get all operators, return is a list of dict, url is https://api.worldquantbrain.com/operators\n", "def get_operators(s):\n", "    response = s.get('https://api.worldquantbrain.com/operators')\n", "    operators = response.json()\n", "    return operators\n", "\n", "# get_operators(s)\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"ZS59763\"},\"token\":{\"expiry\":43200.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"CONSULTANT\",\"MULTI_SIMULATION\",\"PROD_ALPHAS\",\"REFERRAL\",\"<PERSON>UPER_ALPHA\",\"VISUALIZAT<PERSON>\",\"WORKDAY\"]}'\n"]}, {"data": {"text/plain": ["152"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["s=login()\n", "len(get_operators(s))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# create a function to get details of a operator, return is json, url is https://api.worldquantbrain.com/datasetsoperators/{operator_id}. \n", "def get_operator_details(s, document_url):\n", "    response = s.get(f'https://api.worldquantbrain.com{document_url}')\n", "    operator = response.json()\n", "    return operator\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"ZS59763\"},\"token\":{\"expiry\":43200.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"CONSULTANT\",\"MULTI_SIMULATION\",\"PROD_ALPHAS\",\"REFERRAL\",\"<PERSON>UPER_ALPHA\",\"VISUALIZAT<PERSON>\",\"WORKDAY\"]}'\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>category</th>\n", "      <th>scope</th>\n", "      <th>definition</th>\n", "      <th>description</th>\n", "      <th>documentation</th>\n", "      <th>level</th>\n", "      <th>details</th>\n", "      <th>content</th>\n", "      <th>lastModified</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>reduce_skewness</td>\n", "      <td>Reduce</td>\n", "      <td>[COMBO]</td>\n", "      <td>reduce_skewness(input)</td>\n", "      <td>Skewness of values in the array *** Takes an i...</td>\n", "      <td>None</td>\n", "      <td>ALL</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>reduce_count</td>\n", "      <td>Reduce</td>\n", "      <td>[COMBO]</td>\n", "      <td>reduce_count(input, threshold)</td>\n", "      <td>Count the number of element of d(..., :) &gt; thr...</td>\n", "      <td>None</td>\n", "      <td>ALL</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>reduce_kurtosis</td>\n", "      <td>Reduce</td>\n", "      <td>[COMBO]</td>\n", "      <td>reduce_kurtosis(input)</td>\n", "      <td>Kurtosis of values in the array ***Takes an in...</td>\n", "      <td>None</td>\n", "      <td>ALL</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>reduce_choose</td>\n", "      <td>Reduce</td>\n", "      <td>[COMBO]</td>\n", "      <td>reduce_choose(input, nth, ignoreNan=true)</td>\n", "      <td>Choose the 'nth' element in the array, return ...</td>\n", "      <td>None</td>\n", "      <td>ALL</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>reduce_stddev</td>\n", "      <td>Reduce</td>\n", "      <td>[COMBO]</td>\n", "      <td>reduce_stddev(input, threshold=0)</td>\n", "      <td>Standard deviation of values in the array. Thr...</td>\n", "      <td>None</td>\n", "      <td>ALL</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                name category    scope  \\\n", "147  reduce_skewness   Reduce  [COMBO]   \n", "148     reduce_count   Reduce  [COMBO]   \n", "149  reduce_kurtosis   Reduce  [COMBO]   \n", "150    reduce_choose   Reduce  [COMBO]   \n", "151    reduce_stddev   Reduce  [COMBO]   \n", "\n", "                                    definition  \\\n", "147                     reduce_skewness(input)   \n", "148             reduce_count(input, threshold)   \n", "149                     reduce_kurtosis(input)   \n", "150  reduce_choose(input, nth, ignoreNan=true)   \n", "151          reduce_stddev(input, threshold=0)   \n", "\n", "                                           description documentation level  \\\n", "147  Skewness of values in the array *** Takes an i...          None   ALL   \n", "148  Count the number of element of d(..., :) > thr...          None   ALL   \n", "149  Kurtosis of values in the array ***Takes an in...          None   ALL   \n", "150  Choose the 'nth' element in the array, return ...          None   ALL   \n", "151  Standard deviation of values in the array. Thr...          None   ALL   \n", "\n", "    details content lastModified  \n", "147    None    None         None  \n", "148    None    None         None  \n", "149    None    None         None  \n", "150    None    None         None  \n", "151    None    None         None  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["s=login()\n", "\n", "# Get all Operators, in list of dict\n", "list_of_operators = get_operators(s)\n", "\n", "# convert the list of dict to a pandas dataframe\n", "df_operators = pd.DataFrame(list_of_operators)\n", "\n", "# loop df_operator, \"documentation\" column is not None, get the details of the operator, convert to a dataframe using get_operator(s, document_url), then merge with df_operators. \n", "# all columns in the details should be added to df_operators\n", "\n", "# add 'details', 'lastModified', 'content' to df_operators\n", "\n", "df_operators['details'] = None\n", "df_operators['content'] = None\n", "df_operators['lastModified'] = None\n", "\n", "for index, row in df_operators.iterrows():\n", "    if pd.notnull(row['documentation']) and row['documentation'] != 'None':\n", "        detail_operator = get_operator_details(s, row['documentation'])\n", "        df_operators.at[index, 'details'] = detail_operator\n", "        df_operators.at[index, 'content'] = detail_operator.get('content')\n", "        df_operators.at[index, 'lastModified'] = detail_operator.get('lastModified')\n", "        \n", "\n", "df_operators.tail()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Save all the details of the operators to a csv file\n", "df_operators.to_csv('operators_2025-master.csv', index=False)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 抓Datafields\n", "\n", "多个region，settings的datasets"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# OPTIONS url = “https://api.worldquantbrain.com/simulations” 获得json格式返回值。\n", "def options_simulations(s):\n", "    response = s.options('https://api.worldquantbrain.com/simulations')\n", "    return response.json()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"ZS59763\"},\"token\":{\"expiry\":43200.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"CONSULTANT\",\"MULTI_SIMULATION\",\"PROD_ALPHAS\",\"REFERRAL\",\"<PERSON>UPER_ALPHA\",\"VISUALIZAT<PERSON>\",\"WORKDAY\"]}'\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOP3000'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOP1000'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOP500'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOP200'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOPSP500'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOP3000'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOP1000'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOP500'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOP200'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOPSP500'}\n", "{'region': 'GLB', 'delay': 1, 'universe': 'TOP3000'}\n", "{'region': 'GLB', 'delay': 1, 'universe': 'MINVOL1M'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'TOP2500'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'TOP1200'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'TOP800'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'TOP400'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'TOP2500'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'TOP1200'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'TOP800'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'TOP400'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'ASI', 'delay': 1, 'universe': 'MINVOL1M'}\n", "{'region': 'ASI', 'delay': 1, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'CHN', 'delay': 0, 'universe': 'TOP2000U'}\n", "{'region': 'CHN', 'delay': 1, 'universe': 'TOP2000U'}\n", "{'region': 'JPN', 'delay': 1, 'universe': 'TOP1600'}\n", "{'region': 'JPN', 'delay': 1, 'universe': 'TOP1200'}\n", "{'region': 'AMR', 'delay': 1, 'universe': 'TOP600'}\n", "{'region': 'AMR', 'delay': 0, 'universe': 'TOP600'}\n"]}], "source": ["\n", "s=login()\n", "settings = options_simulations(s)\n", "settings\n", "\n", "# Function to get all combinations\n", "def get_combinations(settings):\n", "\n", "    config = settings['actions']['POST']['settings']['children']\n", "    regions = config['region']['choices']['instrumentType']['EQUITY']\n", "    delays = config['delay']['choices']['instrumentType']['EQUITY']\n", "    universes = config['universe']['choices']['instrumentType']['EQUITY']\n", "\n", "    combinations = []\n", "    for region in regions:\n", "        region_value = region['value']\n", "        region_delays = delays['region'].get(region_value, [])\n", "        region_universes = universes['region'].get(region_value, [])\n", "        \n", "        for delay in region_delays:\n", "            for universe in region_universes:\n", "                combinations.append({\n", "                    'region': region_value,\n", "                    'delay': delay['value'],\n", "                    'universe': universe['value']\n", "                })\n", "    return combinations\n", "\n", "# Get and print all combinations\n", "all_combinations = get_combinations(settings)\n", "for combo in all_combinations:\n", "    print(combo)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["\n", "from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type\n", "import requests\n", "@retry(\n", "    stop=stop_after_attempt(5),  # 增加重试次数到5次\n", "    wait=wait_exponential(multiplier=30, min=60, max=300),  # 调整等待时间\n", "    retry=retry_if_exception_type(requests.exceptions.RequestException)  # 只在特定异常时重试\n", ")\n", "def get_datafields(s, instrumentType, region, delay, universe):\n", "    timeout=180\n", "    try:\n", "        response = s.get(\n", "            f'https://api.worldquantbrain.com/data-fields?instrumentType=EQUITY&region={region}&delay={delay}&universe={universe}',\n", "            timeout=timeout\n", "        )\n", "        response.raise_for_status()  # Raises an HTTPError for bad responses\n", "        datafields = response.json()\n", "        return datafields\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"An error occurred: {e}\")\n", "        raise  # Re-raise the exception to trigger a retry\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["datafields_USA_1_TOP3000.csv saved - 50527\n", "datafields_USA_1_TOP1000.csv saved - 50527\n", "datafields_USA_1_TOP500.csv saved - 50527\n", "datafields_USA_1_TOP200.csv saved - 50527\n", "datafields_USA_1_ILLIQUID_MINVOL1M.csv saved - 37823\n", "datafields_USA_1_TOPSP500.csv saved - 42979\n", "datafields_USA_0_TOP3000.csv saved - 10360\n", "datafields_USA_0_TOP1000.csv saved - 10360\n", "datafields_USA_0_TOP500.csv saved - 10360\n", "datafields_USA_0_TOP200.csv saved - 10360\n", "datafields_USA_0_ILLIQUID_MINVOL1M.csv saved - 6219\n", "datafields_USA_0_TOPSP500.csv saved - 8378\n", "datafields_GLB_1_TOP3000.csv saved - 12465\n", "datafields_GLB_1_MINVOL1M.csv saved - 12465\n", "datafields_EUR_1_TOP2500.csv saved - 23138\n", "datafields_EUR_1_TOP1200.csv saved - 23140\n", "datafields_EUR_1_TOP800.csv saved - 23140\n", "datafields_EUR_1_TOP400.csv saved - 23140\n", "datafields_EUR_1_ILLIQUID_MINVOL1M.csv saved - 17528\n", "datafields_EUR_0_TOP2500.csv saved - 3703\n", "datafields_EUR_0_TOP1200.csv saved - 3703\n", "datafields_EUR_0_TOP800.csv saved - 3703\n", "datafields_EUR_0_TOP400.csv saved - 3703\n", "datafields_EUR_0_ILLIQUID_MINVOL1M.csv saved - 2260\n", "datafields_ASI_1_MINVOL1M.csv saved - 10614\n", "datafields_ASI_1_ILLIQUID_MINVOL1M.csv saved - 3660\n", "datafields_CHN_0_TOP2000U.csv saved - 3419\n", "datafields_CHN_1_TOP2000U.csv saved - 12869\n", "datafields_JPN_1_TOP1600.csv saved - 8117\n", "datafields_JPN_1_TOP1200.csv saved - 8117\n", "datafields_AMR_1_TOP600.csv saved - 12312\n", "datafields_AMR_0_TOP600.csv saved - 892\n"]}], "source": ["ddf=pd.DataFrame()\n", "# loop all_combinations, get the datafields for each combination, then save to a csv file.\n", "for combo in all_combinations:\n", "    try:\n", "        datafields = get_datafields(s, 'EQUITY', combo['region'], combo['delay'], combo['universe'])\n", "        df_datafields = pd.json_normalize(datafields['results'])\n", "        df_datafields=pd.DataFrame(df_datafields)\n", "        ddf=pd.concat([ddf,df_datafields],axis=0)\n", "        # save to csv\n", "        #df_datafields.to_excel(f'datafields_{combo[\"region\"]}_{combo[\"delay\"]}_{combo[\"universe\"]}.xlsx')\n", "        print(f'datafields_{combo[\"region\"]}_{combo[\"delay\"]}_{combo[\"universe\"]}.csv saved - ' + str(len(df_datafields)))\n", "    except Exception as e:\n", "        print(f\"Failed to get datafields for {combo}: {e}\")\n", "ddf.to_csv('alldatausable.csv')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["ddf.to_csv('data.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'user_package'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01muser_package\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmachine_lib\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m login\n\u001b[0;32m      5\u001b[0m user_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mZS59763\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m      6\u001b[0m s \u001b[38;5;241m=\u001b[39m login()\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'user_package'"]}], "source": ["import pandas as pd\n", "\n", "from user_package.machine_lib import login\n", "\n", "user_id = \"\"\n", "s = login()\n", "res = s.get(\"https://api.worldquantbrain.com/operators\")\n", "df = pd.<PERSON><PERSON><PERSON><PERSON>(res.json())[['name']]\n", "df.to_csv(f\"{user_id}_operators.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}