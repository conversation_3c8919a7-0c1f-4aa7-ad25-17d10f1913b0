{"cells": [{"cell_type": "markdown", "id": "759bf383", "metadata": {}, "source": ["## 1.双层比较模版（时间维度+横截面维度）\n", "\n", "https://support.worldquantbrain.com/hc/en-us/community/posts/24497520676119-Machine-Alpha-%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%861-%E4%BB%80%E4%B9%88%E6%98%AFAlpha%E6%A8%A1%E6%9D%BF\n"]}, {"cell_type": "code", "execution_count": null, "id": "25800ab8", "metadata": {}, "outputs": [], "source": ["<group_compare_op>(<ts_compare_op>(<company_fundamentals>, <days>), <group>)"]}, {"cell_type": "markdown", "id": "94950633", "metadata": {}, "source": ["如在行业内自身EPS收入改善明显，在行业内相对优秀的公司可能有更好表现。可表达为group_rank(ts_rank(eps,252),industry)\n", "\n", "这种模板特别适合构建相对价值或相对动量类因子，通过双层比较（时间维度+横截面维度）来识别有潜力的标的。于行业平均EPS的股票，或者过去一年内自身收益，成长率高于行业平均收益的股票，这些股票往往具有更高的收益预期，因此可能具有alpha机会。\n", "\n", "原始Alpha基于idea使用了EPS，但这一理念可以很容易地扩展到其他基本面数据，如DPS（每股股利）、CPS（每股现金流量）、BPS（每股账面价值）、EBIT（息税前利润）、销售额等。"]}, {"cell_type": "code", "execution_count": null, "id": "1f5a6e14", "metadata": {}, "outputs": [], "source": ["<group_compare_op>(<ts_compare_op>(<company_fundamentals>,<days>,<group>))"]}, {"cell_type": "markdown", "id": "cbb5e8b5", "metadata": {}, "source": ["上述公式包含以下组件：\n", "\n", "#### <company_fundamentals>：原始Alpha基于idea使用了EPS，但这一理念可以很容易地扩展到其他基本面数据，如DPS（每股股利）、CPS（每股现金流量）、BPS（每股账面价值）、EBIT（息税前利润）、销售额等。\n", "#### <ts_compare_op>：原始实现中使用了ts_rank。还有其他一些服务于类似目的的时间序列操作符，例如ts_zscore、ts_delta、ts_avg_diff等。\n", "#### <group_compare_op>：使用了group_rank。类似于<ts_compare_op>的情况，你也可以考虑group_zscore、group_neutralize来控制特定组的效应。\n", "#### <days>，<group>：你还可以更改<ts_compare_op>的回溯天数，或者<group_compare_op>的定义。 这种模块化方法使模板高度可定制。每一步都是可互换的，并且可以根据你的Alpha假设的具体细节进行调整。"]}, {"cell_type": "code", "execution_count": 2, "id": "ba5c646e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["there are total 60 alpha expressions\n", "['group_rank(ts_rank(assets, 600), subindustry)', 'group_rank(ts_rank(assets, 600), densify(pv13_h_f1_sector))', 'group_rank(ts_rank(assets, 200), subindustry)', 'group_rank(ts_rank(assets, 200), densify(pv13_h_f1_sector))', 'group_rank(ts_rank(assets_curr, 600), subindustry)']\n"]}], "source": ["# 登录获取数据略，可参见备份代码day4\n", "datafields_list_fnd6 = ['assets', 'assets_curr', 'bookvalue_ps', 'capex', 'cash']\n", "# group_compare_op = ['group_rank', 'group_zscore', 'group_neutralize']原始\n", "group_compare_op = ['group_rank', 'group_zscore', 'group_neutralize']\n", "# ts_compare_op = ['ts_rank', 'ts_zscore', 'ts_av_diff']原始\n", "ts_compare_op = ['ts_rank']\n", "# company_fundamentals = datafields_list_fnd6原始\n", "company_fundamentals = datafields_list_fnd6\n", "# days = [600, 200]原始\n", "days = [600, 200]\n", "# group = ['market', 'industry', 'subindustry', 'sector', 'densify(pv13_h_f1_sector)']原始   ###……\n", "group = ['subindustry',  'densify(pv13_h_f1_sector)']   ###……\n", "\n", "alpha_expressions = []\n", "\n", "for gco in group_compare_op:\n", "    for tco in ts_compare_op:\n", "        # for index, row in company_fundamentals.iterrows():\n", "        for cf in company_fundamentals:\n", "            for d in days:\n", "                for grp in group:\n", "                    # alpha_expressions.append(f\"{gco}({tco}({row['id']}, {d}), {grp})\")\n", "                    alpha_expressions.append(f\"{gco}({tco}({cf}, {d}), {grp})\")\n", "                    \n", "# Print or return the result_strings list\n", "print(f'there are total {len(alpha_expressions)} alpha expressions')\n", "print(alpha_expressions[:5]) \n"]}, {"cell_type": "markdown", "id": "4604efab", "metadata": {}, "source": ["## 2 时间维度上的公司排名及操作"]}, {"cell_type": "code", "execution_count": null, "id": "7ba8bc0c", "metadata": {}, "outputs": [], "source": ["time_series_operator(<profit_field>/<size_field>, <days>)"]}, {"cell_type": "markdown", "id": "c0b1fb17", "metadata": {}, "source": ["https://support.worldquantbrain.com/hc/en-us/community/posts/25066216209687-Machine-Alpha-%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%862-%E7%90%86%E8%A7%A3%E6%97%B6%E9%97%B4%E5%BA%8F%E5%88%97%E5%88%A9%E6%B6%A6%E4%B8%8E%E8%A7%84%E6%A8%A1%E6%AF%94%E8%BE%83%E6%A8%A1%E6%9D%BF"]}, {"cell_type": "markdown", "id": "d52fcea2", "metadata": {}, "source": ["时间序列利润与规模比较模板"]}, {"cell_type": "markdown", "id": "a62afc91", "metadata": {}, "source": ["标题：Fundamental"]}, {"cell_type": "markdown", "id": "d373075d", "metadata": {}, "source": ["##### profit_field是代表收入/盈余/利润的任何字段 \n", "##### size_field是代表公司规模的任何字段\n", "##### days需要使用合理的天数参数，例如周（5天）、月（20天）、季（60天）或年（250天），基本面数据建议不要使用短期数据，因为短期数据可能受到季节性因素和短期波动的影响，而长期数据则更能反映公司的基本面状况。"]}, {"cell_type": "markdown", "id": "8c50eecf", "metadata": {}, "source": ["#### 2.1登录"]}, {"cell_type": "code", "execution_count": 1, "id": "92cfbadd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"NY96758\"},\"token\":{\"expiry\":14400.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"TUTORIAL\"]}'\n"]}], "source": ["from machine_lib import * \n", "s = login()"]}, {"cell_type": "markdown", "id": "dd606387", "metadata": {}, "source": ["#### 2.2获取数据字段"]}, {"cell_type": "code", "execution_count": null, "id": "b80703e4", "metadata": {}, "outputs": [], "source": ["#获取数据字段\n", "# df = get_datafields(s, dataset_id = 'model77', region='USA', universe='TOP3000', delay=1)\n", "# df = df[df['type'] == \"MATRIX\"][\"id\"].tolist()\n", "# print(df)\n", "\n", "# df = get_datafields(s, dataset_id='fundamental17', region='GLB', universe='TOP3000', delay=1)   课程演示，权限不够\n", "\n", "# fundamental6  analyst4\n", "df = get_datafields(s, dataset_id = 'fundamental6', region='USA', universe='TOP3000', delay=1)\n", "df = df[df['type'] == \"MATRIX\"]\n", "print(len(df))\n", "df"]}, {"cell_type": "markdown", "id": "87bec12a", "metadata": {}, "source": ["#### 2.31数据预处理，筛选和提取description包含特定关键词的数据。"]}, {"cell_type": "code", "execution_count": 3, "id": "204ee28d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>ebit</td>\n", "      <td>Earnings Before Interest and Taxes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>ebitda</td>\n", "      <td>Earnings Before Interest</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>eps</td>\n", "      <td>Earnings Per Share (Basic) - Including Extraor...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>fnd6_aqi</td>\n", "      <td>Acquisitions - Income Contribution</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>fnd6_ci</td>\n", "      <td>Comprehensive Income - Total</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          id                                        description\n", "17      ebit                 Earnings Before Interest and Taxes\n", "18    ebitda                           Earnings Before Interest\n", "21       eps  Earnings Per Share (Basic) - Including Extraor...\n", "34  fnd6_aqi                 Acquisitions - Income Contribution\n", "42   fnd6_ci                       Comprehensive Income - Total"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#数据预处理\n", "df[df[\"description\"].str.contains(\"Profit|Income|Earning|Revenue\")][[\"id\", \"description\"]][:5]"]}, {"cell_type": "markdown", "id": "d5d8eaa7", "metadata": {}, "source": ["#### 2.32 提取包含特定关键词的行的 id 列，并转换为列表"]}, {"cell_type": "code", "execution_count": 4, "id": "e79fada5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ebit', 'ebitda', 'eps', 'fnd6_aqi', 'fnd6_ci', 'fnd6_cibegni', 'fnd6_cimii', 'fnd6_cipen', 'fnd6_citotal', 'fnd6_cptmfmq_oibdpq', 'fnd6_cptmfmq_opepsq', 'fnd6_cptnewqv1300_epsf12', 'fnd6_cptnewqv1300_epsfxq', 'fnd6_cptnewqv1300_epsx12', 'fnd6_cptnewqv1300_nopiq', 'fnd6_cptnewqv1300_oeps12', 'fnd6_cptnewqv1300_oiadpq', 'fnd6_cptnewqv1300_oibdpq', 'fnd6_cptnewqv1300_opepsq', 'fnd6_cptnewqv1300_req', 'fnd6_cshpri', 'fnd6_esubc', 'fnd6_ibmii', 'fnd6_idit', 'fnd6_itci', 'fnd6_mfma2_opeps', 'fnd6_mfma2_txach', 'fnd6_mfmq_cshprq', 'fnd6_mfmq_ibcomq', 'fnd6_mfmq_piq', 'fnd6_newa1v1300_acominc', 'fnd6_newa1v1300_cshfd', 'fnd6_newa1v1300_ebit', 'fnd6_newa1v1300_ebitda', 'fnd6_newa1v1300_epsfi', 'fnd6_newa1v1300_epsfx', 'fnd6_newa1v1300_epspi', 'fnd6_newa1v1300_epspx', 'fnd6_newa1v1300_fca', 'fnd6_newa1v1300_gp', 'fnd6_newa1v1300_ib', 'fnd6_newa1v1300_ibadj', 'fnd6_newa1v1300_ibc', 'fnd6_newa1v1300_ibcom', 'fnd6_newa2v1300_mii', 'fnd6_newa2v1300_ni', 'fnd6_newa2v1300_nopi', 'fnd6_newa2v1300_oiadp', 'fnd6_newa2v1300_oibdp', 'fnd6_newa2v1300_opeps', 'fnd6_newa2v1300_pi', 'fnd6_newa2v1300_re', 'fnd6_newa2v1300_reuna', 'fnd6_newa2v1300_spced', 'fnd6_newa2v1300_spceeps', 'fnd6_newa2v1300_txach', 'fnd6_newa2v1300_txp', 'fnd6_newa2v1300_txt', 'fnd6_newqv1300_acomincq', 'fnd6_newqv1300_aociotherq', 'fnd6_newqv1300_cibegniq', 'fnd6_newqv1300_cimiiq', 'fnd6_newqv1300_ciq', 'fnd6_newqv1300_citotalq', 'fnd6_newqv1300_csh12q', 'fnd6_newqv1300_cshprq', 'fnd6_newqv1300_epsfiq', 'fnd6_newqv1300_epspiq', 'fnd6_newqv1300_epspxq', 'fnd6_newqv1300_fcaq', 'fnd6_newqv1300_glcea12', 'fnd6_newqv1300_glced12', 'fnd6_newqv1300_glceeps12', 'fnd6_newqv1300_ibadj12', 'fnd6_newqv1300_ibadjq', 'fnd6_newqv1300_ibcomq', 'fnd6_newqv1300_ibmiiq', 'fnd6_newqv1300_ibq', 'fnd6_newqv1300_miiq', 'fnd6_newqv1300_msaq', 'fnd6_newqv1300_oepf12', 'fnd6_newqv1300_oepsxq', 'fnd6_newqv1300_piq', 'fnd6_newqv1300_reunaq', 'fnd6_newqv1300_spcedpq', 'fnd6_newqv1300_spcedq', 'fnd6_newqv1300_spceepspq', 'fnd6_newqv1300_spceepsq', 'fnd6_newqv1300_spcep12', 'fnd6_newqv1300_spcepd12', 'fnd6_newqv1300_spcepq', 'fnd6_newqv1300_spceq', 'fnd6_newqv1300_tfvceq', 'fnd6_newqv1300_txdiq', 'fnd6_newqv1300_txpq', 'fnd6_newqv1300_txtq', 'fnd6_niadj', 'fnd6_nopio', 'fnd6_oprepsx', 'fnd6_pidom', 'fnd6_pifo', 'fnd6_rea', 'fnd6_reajo', 'fnd6_recta', 'fnd6_spce', 'fnd6_tfvce', 'fnd6_txc', 'fnd6_txdi', 'fnd6_txfed', 'fnd6_txfo', 'fnd6_txo', 'fnd6_txpd', 'fnd6_txr', 'fnd6_txs', 'income', 'income_beforeextra', 'income_tax', 'operating_income', 'pretax_income', 'retained_earnings']\n", "120\n"]}], "source": ["profit_fields = df[df[\"description\"].str.contains(\"Profit|Income|Earning\")][\"id\"].values.tolist()\n", "print(profit_fields)\n", "print(len(profit_fields))"]}, {"cell_type": "markdown", "id": "dfca12fb", "metadata": {}, "source": ["#### 2.4 ALFHA  FACTORY"]}, {"cell_type": "code", "execution_count": null, "id": "8aff3c48", "metadata": {}, "outputs": [], "source": ["size_fields = [\"fnd17_mktcap\", \"shareout\"]\n", "\n", "def fundamental_factory(profit_field, size_field, ops):\n", "    \n", "    alpha_list = []\n", "    for profit_field in profit_fields:\n", "        for size_field in size_fields:\n", "            base_data = \"%s/%s\" % (profit_field, size_field)\n", "            for ts_op in ts_ops:\n", "                alpha_list += ts_factory(ts_op, base_data)\n", "    return alpha_list\n", "\n", "alpha_list = fundamental_factory(profit_fields, size_fields, ts_ops)\n", "print(len(alpha_list))\n", "for alpha in alpha_list:\n", "    print(alpha)"]}, {"cell_type": "markdown", "id": "e8e27d7e", "metadata": {}, "source": ["## 3.Sentiment"]}, {"cell_type": "markdown", "id": "2bff3c85", "metadata": {}, "source": ["时间序列情绪比较模版\n", "https://support.worldquantbrain.com/hc/en-us/community/posts/25066287753367-Machine-Alpha-%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%863-%E7%BB%A7%E7%BB%AD%E7%90%86%E8%A7%A3%E6%97%B6%E9%97%B4%E5%BA%8F%E5%88%97%E6%A8%A1%E6%9D%BF-%E4%BB%A5%E6%83%85%E7%BB%AA%E7%B1%BB%E6%95%B0%E6%8D%AE%E4%B8%BA%E4%BE%8B"]}, {"cell_type": "markdown", "id": "e4db5de4", "metadata": {}, "source": ["<time_series_operator>(<positive_sentiment> - <negative_sentiment>, days)"]}, {"cell_type": "markdown", "id": "37f3b509", "metadata": {}, "source": ["positive_sentiment = rank(<backfill_op>(<positive_sentiment, days));\n", "\n", "negative_sentiment = rank(<backfill_op>(<negative_sentiment, days));\n", "\n", "sentiment_difference = <compare_op>(positive_sentiment, negative_sentiment);\n", "\n", "<time_series_operator>(sentiment_difference, days)"]}, {"cell_type": "markdown", "id": "d04a80fd", "metadata": {}, "source": ["#### 3.1登录"]}, {"cell_type": "code", "execution_count": 1, "id": "3b0c9991", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"NY96758\"},\"token\":{\"expiry\":14400.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"TUTORIAL\"]}'\n"]}], "source": ["from machine_lib import * \n", "s = login()"]}, {"cell_type": "markdown", "id": "5e5f8982", "metadata": {}, "source": ["#### 3.21获取数据字段"]}, {"cell_type": "code", "execution_count": 11, "id": "56a2af18", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["100\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>snt1_cored1_score</td>\n", "      <td>bearish: score &lt; 5, bullish: score &gt; 5, neutra...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>snt1_d1_dtstsespe</td>\n", "      <td>Dispersion among analysts' estimates</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>snt1_d1_analystcoverage</td>\n", "      <td># of analysts providing an earnings estimate</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>snt1_d1_downtargetpercent</td>\n", "      <td>Ratio of (Number ofdown target) over (Number o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>snt1_d1_earningsrevision</td>\n", "      <td>1 month change in mean of earnings estimate / ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          id  \\\n", "0          snt1_cored1_score   \n", "1          snt1_d1_dtstsespe   \n", "2    snt1_d1_analystcoverage   \n", "3  snt1_d1_downtargetpercent   \n", "4   snt1_d1_earningsrevision   \n", "\n", "                                         description  \n", "0  bearish: score < 5, bullish: score > 5, neutra...  \n", "1               Dispersion among analysts' estimates  \n", "2       # of analysts providing an earnings estimate  \n", "3  Ratio of (Number ofdown target) over (Number o...  \n", "4  1 month change in mean of earnings estimate / ...  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["#获取数据字段\n", "# df = get_datafields(s, dataset_id = 'model77', region='USA', universe='TOP3000', delay=1)\n", "# df = df[df['type'] == \"MATRIX\"][\"id\"].tolist()\n", "# print(df)\n", "\n", "# df = get_datafields(s, dataset_id='fundamental17', region='GLB', universe='TOP3000', delay=1)   课程演示，权限不够\n", "\n", "# fundamental6  analyst4\n", "# df = get_datafields(s, dataset_id = 'fundamental6', region='USA', universe='TOP3000', delay=1)\n", "# df = df[df['type'] == \"MATRIX\"]\n", "# print(len(df))\n", "# df\n", "\n", "df = get_datafields(s, region='USA', universe='TOP3000', delay=1, search = \"sentiment\")\n", "fields = df[\"id\"].tolist()\n", "print(len(df))\n", "df[[\"id\", \"description\"]][:5]"]}, {"cell_type": "markdown", "id": "de3d0d16", "metadata": {}, "source": ["#### 3.22获取数据字段（中国区，暂时没有权限）"]}, {"cell_type": "code", "execution_count": null, "id": "fe791c37", "metadata": {}, "outputs": [], "source": ["df = get_datafields(s, dataset_id='other111', region='CHN', universe='TOP3000', delay=1)\n", "fields = df[\"id\"].tolist()"]}, {"cell_type": "markdown", "id": "084cdf2f", "metadata": {}, "source": ["#### 3.3数据预处理，筛选和提取description包含pos（正向情绪）及neg（净情绪）数据，并在ALpha  FACTORY中生成alpha表达式。"]}, {"cell_type": "code", "execution_count": 19, "id": "3441e4d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ts_delta(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_delta(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_std_dev(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_std_dev(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_arg_min(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_arg_min(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_arg_max(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_arg_max(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_min_diff(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_min_diff(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_max_diff(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_max_diff(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_returns(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_returns(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_scale(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_scale(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_skewness(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_skewness(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_av_diff(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_av_diff(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_min_max_cps(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_min_max_cps(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_min_max_diff(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_min_max_diff(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_zscore(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_zscore(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_quantile(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 252)\n", "ts_quantile(vec_avg(snI33_sefke1v1_fkw1_method_2_pos) - vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 504)\n", "ts_delta(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_delta(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_std_dev(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_std_dev(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_arg_min(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_arg_min(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_arg_max(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_arg_max(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_min_diff(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_min_diff(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_max_diff(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_max_diff(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_returns(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_returns(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_scale(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_scale(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_skewness(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_skewness(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_av_diff(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_av_diff(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_min_max_cps(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_min_max_cps(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_min_max_diff(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_min_max_diff(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_zscore(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_zscore(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n", "ts_quantile(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 252)\n", "ts_quantile(rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_pos), 120)) - rank(ts_backfill(vec_avg(snI33_sefke1v1_fkw1_method_2_neg), 120)), 504)\n"]}], "source": ["ts_ops = ['ts_delta', 'ts_std_dev', 'ts_arg_min', 'ts_arg_max', 'ts_min_diff',\n", "          'ts_max_diff', 'ts_returns', 'ts_scale', 'ts_skewness', 'ts_av_diff', 'ts_min_max_cps', 'ts_min_max_diff',\n", "          'ts_zscore', 'ts_quantile']\n", "\n", "fields = [\"snI33_sefke1v1_fkw1_method_2_pos\",\"snI33_sefke1v1_fkw1_method_2_neg\"]\n", "\n", "\n", "def sentiment_factory(fields, ops):\n", "    prefix = \"_pos\"\n", "    alpha_list = []\n", "    for field in fields:\n", "        if prefix in field:\n", "            pos = field\n", "            neg = pos.replace(\"pos\", \"neg\")\n", "            base_data = \"vec_avg(%s) - vec_avg(%s)\" % (pos, neg)\n", "\n", "            for op in ts_ops:\n", "                alpha_list += ts_factory(op, base_data)\n", "\n", "            pos_ranked = f\"rank(ts_backfill(vec_avg({pos}), 120))\"\n", "            neg_ranked = f\"rank(ts_backfill(vec_avg({neg}), 120))\"\n", "            base_data_ranked = f\"{pos_ranked} - {neg_ranked}\"\n", "            for op in ops:\n", "                alpha_list += ts_factory(op, base_data_ranked)\n", "\n", "    return alpha_list\n", "\n", "alpha_list = sentiment_factory(fields, ts_ops)\n", "for alpha in alpha_list:\n", "    print(alpha)"]}, {"cell_type": "markdown", "id": "d1b579df", "metadata": {}, "source": ["## 4.Option"]}, {"cell_type": "markdown", "id": "5e6d22a6", "metadata": {}, "source": ["从比较同一组内不同公司之间看涨期权和看跌期权的希腊字母净差值，拓展为从某个领域的专业知识出发进行创造Alpha 模板。"]}, {"cell_type": "markdown", "id": "01449c62", "metadata": {}, "source": ["https://support.worldquantbrain.com/hc/en-us/community/posts/25329053049495-Machine-Alpha-%E5%9F%BA%E7%A1%80%E7%9F%A5%E8%AF%864-%E5%A6%82%E4%BD%95%E5%88%A9%E7%94%A8%E5%AF%B9%E7%89%B9%E5%AE%9A%E6%95%B0%E6%8D%AE%E9%9B%86%E7%9A%84%E4%B8%93%E4%B8%9A%E9%A2%86%E5%9F%9F%E7%9F%A5%E8%AF%86%E5%88%9B%E9%80%A0Alpha-%E6%A8%A1%E6%9D%BF-%E4%BB%A5%E6%9C%9F%E6%9D%83%E6%95%B0%E6%8D%AE%E4%B8%BA%E4%BE%8B"]}, {"cell_type": "code", "execution_count": null, "id": "7c4e01e5", "metadata": {}, "outputs": [], "source": ["group_operator(<put_greek> - <call_greek>, <grouping_data>)"]}, {"cell_type": "markdown", "id": "da6a4505", "metadata": {}, "source": ["#### 4.1登录"]}, {"cell_type": "code", "execution_count": 7, "id": "2d1d34e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"NY96758\"},\"token\":{\"expiry\":14400.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"TUTORIAL\"]}'\n"]}], "source": ["from machine_lib import * \n", "s = login()\n"]}, {"cell_type": "markdown", "id": "3bad0765", "metadata": {}, "source": ["#### 4.2获取数据字段"]}, {"cell_type": "code", "execution_count": 9, "id": "10f224c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["74\n", "['call_breakeven_10', 'call_breakeven_1080', 'call_breakeven_120', 'call_breakeven_150', 'call_breakeven_180']\n"]}], "source": ["#获取数据字段\n", "# df = get_datafields(s, dataset_id = 'model77', region='USA', universe='TOP3000', delay=1)\n", "# df = df[df['type'] == \"MATRIX\"][\"id\"].tolist()\n", "# print(df)\n", "\n", "# df = get_datafields(s, dataset_id='fundamental17', region='GLB', universe='TOP3000', delay=1)   课程演示，权限不够\n", "\n", "# fundamental6  analyst4\n", "# df = get_datafields(s, dataset_id = 'fundamental6', region='USA', universe='TOP3000', delay=1)\n", "# df = df[df['type'] == \"MATRIX\"]\n", "# print(len(df))\n", "# df\n", "# df = get_datafields(s, dataset_id='option40', region='USA', universe='TOP3000', delay=1)\n", "df = get_datafields(s, dataset_id='option9', region='USA', universe='TOP3000', delay=1)\n", "fields = df['id'].tolist()\n", "\n", "print(len(fields))\n", "print(fields[:5])"]}, {"cell_type": "markdown", "id": "8b323535", "metadata": {}, "source": ["#### 4.3数据预处理，筛选和提取fields中包含prefixes列表特定字段的前缀。将field中将call替换为put后，进行比较，并在ALpha  FACTORY中生成alpha表达式。"]}, {"cell_type": "code", "execution_count": null, "id": "243eacdc", "metadata": {}, "outputs": [], "source": ["def option_factory(fields):\n", "    alpha_list = []\n", "    # prefixes = [\"opt40_call_delta_\", \"opt40_call_forwardprice_\", \"opt40_call_gamma_\", \"opt40_call_premium_\",\n", "    #             \"opt40_call_strikeprice_\", \"opt40_call_theta_\", \"opt40_call_vega_\", \"opt40_call_volatility_\", \"opt40_ivcall_\",\n", "    #             \"opt40_ivmean\"]\n", "    prefixes = [\"call_breakeven_\", \"call_breakeven_\"]\n", "\n", "    for prefix in prefixes:\n", "        for field in fields:\n", "            if prefix in field:\n", "                call_ = field\n", "                put_ = call_.replace(\"call\", \"put\")\n", "                alpha_list.append(\"%s - %s\" % (call_, put_))\n", "                alpha_list.append(\"rank(%s - %s)\" % (call_, put_))\n", "                alpha_list.append(\"ts_rank(%s - %s, 5)\" % (call_, put_))\n", "                alpha_list.append(\"rank(%s) - rank(%s)\" % (call_, put_))\n", "                alpha_list.append(\"ts_rank(%s, 5) - rank(%s, 5)\" % (call_, put_))\n", "\n", "    return alpha_list\n", "\n", "alpha_list = option_factory(fields)\n", "\n", "for alpha in alpha_list:\n", "    print(alpha)"]}, {"cell_type": "markdown", "id": "06fbc6b3", "metadata": {}, "source": ["## 5.<PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "id": "9b700343", "metadata": {}, "source": ["group_zscore (subtract (group_zscore (<act_data>, industry), group_zscore (<est_data>, industry)), industry) "]}, {"cell_type": "markdown", "id": "43f022e0", "metadata": {}, "source": ["#### 对实际数据（act_data）和估计数据（est_data）按行业（industry）进行比较、分组、评分。数据标准化和和行业分组同时进行。最后再对相减比较后的结果按行业进行比较、分组、Z 评分（group_zscore）。"]}, {"cell_type": "markdown", "id": "34cd93ea", "metadata": {}, "source": ["#### 5.1登录及获取数据"]}, {"cell_type": "code", "execution_count": null, "id": "0043d5ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"NY96758\"},\"token\":{\"expiry\":14400.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"TUTORIAL\"]}'\n"]}], "source": ["from machine_lib import * \n", "s = login()\n", "# fundamental6  analyst4\n", "# df = get_datafields(s, dataset_id = 'analyst4', region='USA', universe='TOP3000', delay=1)\n", "# 教学演示为7，权限不够，跳过\n", "df = get_datafields(s, dataset_id = 'analyst7', region='USA', universe='TOP3000', delay=1)\n", "datafields = df[df['type'] == \"MATRIX\"][\"id\"].tolist()\n", "print(len(datafields))"]}, {"cell_type": "code", "execution_count": 13, "id": "b5dc7350", "metadata": {}, "outputs": [], "source": ["datafields = ['est_q_sal_high_1wk_ago', 'est_12m_bps_mean', 'est_12m_bps_mean_3mth_ago','est_q_sal_highnum_4wks_ago', 'est_q_sal_lowRaisednum_1mth', 'est_q_sal_value_1mth_ago', 'est_q_sal_value_6mth_ago', 'est_q_sal_value_lastq', 'est_q_sal_value_lasty', 'est_q_sal_value_1mth', 'est_q_sal_value_4wks', 'est_q_sal_value_1wk', 'est_q_sal_raisenum_lasty', 'est_q_sal_raisenum_6mth', 'est_q_sal_raisenum_1wk_ago', 'est_q_sal_raisenum_4wks_ago', 'est_q_sal_raised_1wk', 'est_q_sal_raisednum_1mth', 'est_q_sal_raisednum_4wks', 'est_q_sal_std', 'est_q_sal_std_28d', 'est_q_sal_std_3mth_ago', 'est_q_sal_std_4wks_ago', 'act_12m_sal_value', 'act_q_sal_surprisemean', 'act_q_sal_surprisestd', 'act_q_sal_value', 'act_12m_bps_value']"]}, {"cell_type": "markdown", "id": "8717ab00", "metadata": {}, "source": ["#### 5.2 需要首先找到实际值act date 和估计值est date"]}, {"cell_type": "code", "execution_count": 14, "id": "e06b1123", "metadata": {}, "outputs": [], "source": ["est_dict = defaultdict(list)\n", "act_dict = defaultdict(list)\n", "\n", "for field in datafields:\n", "    t = field.split(\"_\")[0]\n", "    if t == \"act\":\n", "        metric = field.split(\"_\")[2]\n", "        act_dict[metric].append(field)\n", "    elif t == \"est\":\n", "        metric = field.split(\"_\")[2]\n", "        est_dict[metric].append(field)"]}, {"cell_type": "markdown", "id": "c8f65344", "metadata": {}, "source": ["输出字段的键值"]}, {"cell_type": "code", "execution_count": 15, "id": "eb27325a", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['sal', 'bps'])"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["est_dict.keys()"]}, {"cell_type": "code", "execution_count": 16, "id": "85d747e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["['est_q_sal_high_1wk_ago',\n", " 'est_q_sal_highnum_4wks_ago',\n", " 'est_q_sal_lowRaisednum_1mth',\n", " 'est_q_sal_value_1mth_ago',\n", " 'est_q_sal_value_6mth_ago',\n", " 'est_q_sal_value_lastq',\n", " 'est_q_sal_value_lasty',\n", " 'est_q_sal_value_1mth',\n", " 'est_q_sal_value_4wks',\n", " 'est_q_sal_value_1wk',\n", " 'est_q_sal_raisenum_lasty',\n", " 'est_q_sal_raisenum_6mth',\n", " 'est_q_sal_raisenum_1wk_ago',\n", " 'est_q_sal_raisenum_4wks_ago',\n", " 'est_q_sal_raised_1wk',\n", " 'est_q_sal_raisednum_1mth',\n", " 'est_q_sal_raisednum_4wks',\n", " 'est_q_sal_std',\n", " 'est_q_sal_std_28d',\n", " 'est_q_sal_std_3mth_ago',\n", " 'est_q_sal_std_4wks_ago']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["est_dict[\"sal\"]"]}, {"cell_type": "code", "execution_count": 17, "id": "cabdddb2", "metadata": {}, "outputs": [{"data": {"text/plain": ["['act_12m_sal_value',\n", " 'act_q_sal_surprisemean',\n", " 'act_q_sal_surprisestd',\n", " 'act_q_sal_value']"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["act_dict[\"sal\"]"]}, {"cell_type": "markdown", "id": "18fc573d", "metadata": {}, "source": ["#### After observation, we collect from 3 parts\n", "#### Part 1: act - est"]}, {"cell_type": "markdown", "id": "02e58f52", "metadata": {}, "source": ["#### 5.3 alpha factory"]}, {"cell_type": "markdown", "id": "1e5ed7b1", "metadata": {}, "source": ["5.3.1常规写法，需要前两个字段前缀一致，需要进行二次筛选"]}, {"cell_type": "code", "execution_count": 18, "id": "63b2fcc9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["act_q_sal_value - est_q_sal_high_1wk_ago\n", "( act_q_sal_value - est_q_sal_high_1wk_ago ) / est_q_sal_high_1wk_ago\n", "act_12m_bps_value - est_12m_bps_mean\n", "( act_12m_bps_value - est_12m_bps_mean ) / est_12m_bps_mean\n", "act_12m_bps_value - est_12m_bps_mean_3mth_ago\n", "( act_12m_bps_value - est_12m_bps_mean_3mth_ago ) / est_12m_bps_mean_3mth_ago\n"]}], "source": ["alpha_list = []\n", "\n", "est_fix = [\"high\", \"low\", \"mean\", \"median\"]\n", "act_fix = [\"value\"]\n", "for key in act_dict.keys():\n", "    for act in act_dict[key]:\n", "        act_postfix = act.split(\"_\")[3]\n", "        if act_postfix in act_fix:\n", "            act_time = act.split(\"_\")[1]\n", "            for est in est_dict[key]:\n", "                est_time = est.split(\"_\")[1]\n", "                est_postfix = est.split(\"_\")[3]\n", "                if est_time == act_time and est_postfix in est_fix:\n", "                    alpha_list.append(\"%s - %s\"%(act, est))\n", "                    # scale unit\n", "                    alpha_list.append(\"( %s - %s ) / %s\"%(act, est, est))\n", "\n", "len(alpha_list)\n", "for alpha in alpha_list:\n", "    print(alpha)"]}, {"cell_type": "markdown", "id": "9e4dbb9f", "metadata": {}, "source": ["5.3.2函数写法"]}, {"cell_type": "code", "execution_count": 21, "id": "900a156e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["act_q_sal_value - est_q_sal_high_1wk_ago\n", "(act_q_sal_value - est_q_sal_high_1wk_ago)/est_q_sal_high_1wk_ago\n", "act_12m_bps_value - est_12m_bps_mean\n", "(act_12m_bps_value - est_12m_bps_mean)/est_12m_bps_mean\n", "act_12m_bps_value - est_12m_bps_mean_3mth_ago\n", "(act_12m_bps_value - est_12m_bps_mean_3mth_ago)/est_12m_bps_mean_3mth_ago\n"]}], "source": ["def analyst_factory(act_dict, est_dict):\n", "    alpha_list = []\n", "    est_fix = [\"high\", \"low\", \"mean\", \"median\"]\n", "    act_fix = [\"value\"]\n", "    for key in act_dict.keys():\n", "        for act in act_dict[key]:\n", "            act_postfix = act.split(\"_\")[3]\n", "            if act_postfix in act_fix:\n", "                act_time = act.split(\"_\")[1]\n", "                for est in est_dict[key]:\n", "                    est_time = est.split(\"_\")[1]\n", "                    est_postfix = est.split(\"_\")[3]\n", "                    if est_time == act_time and est_postfix in est_fix:\n", "                        # scale unit\n", "                        alpha_list.append(\"%s - %s\"%(act, est))\n", "                        alpha_list.append(\"(%s - %s)/%s\"%(act, est, est))\n", "                       \n", "\n", "    return alpha_list\n", "\n", "alpha_list = analyst_factory(act_dict, est_dict)\n", "\n", "for alpha in alpha_list:\n", "    print(alpha)"]}, {"cell_type": "markdown", "id": "775f537e", "metadata": {}, "source": ["## 6.Analyst期限结构，不同时间范围进行比较，比较适用于时间序列数据，适用于analyst14 and analyst15"]}, {"cell_type": "code", "execution_count": null, "id": "b1e12b94", "metadata": {}, "outputs": [], "source": ["group_zscore(subtract(group_zscore(anl14_mean_eps_<period1>, industry), group_zscore(anl14_mean_eps_<period2>, industry)), industry)"]}, {"cell_type": "code", "execution_count": null, "id": "6c0fb3c8", "metadata": {}, "outputs": [], "source": ["df = get_datafields(s, dataset_id='analyst14', region='USA', universe='TOP3000', delay=1)\n", "\n", "for id in df[\"id\"]:\n", "    print(id)"]}, {"cell_type": "markdown", "id": "4206c59b", "metadata": {}, "source": ["测试数据"]}, {"cell_type": "code", "execution_count": 34, "id": "d5871d1d", "metadata": {}, "outputs": [], "source": ["df = ['an114_low_bvps_fy3', 'an114_mean_ntprep_fy1', 'an114_mean_ntp_fy5', 'an114_mean_ntprep_fp1', 'an114_mean_ntprep_fp2', 'an114_mean_ntprep_fp3']"]}, {"cell_type": "code", "execution_count": 35, "id": "dca25583", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ntprep\n", "fp3\n"]}], "source": ["est_dict = defaultdict(lambda: defaultdict(list))\n", "est_dict = defaultdict(list)\n", "for field in df:\n", "    if \"mean\" in field and len(field.split(\"_\")) == 4:\n", "        metric = field.split(\"_\")[2]\n", "        term = field.split(\"_\")[-1]\n", "        est_dict[metric].append(field)\n", "print(metric)\n", "print(term)"]}, {"cell_type": "code", "execution_count": 36, "id": "a8b67a3a", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['ntprep', 'ntp'])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["est_dict.keys()"]}, {"cell_type": "code", "execution_count": 37, "id": "6c2ecee1", "metadata": {}, "outputs": [{"data": {"text/plain": ["defaultdict(list,\n", "            {'ntprep': ['an114_mean_ntprep_fy1',\n", "              'an114_mean_ntprep_fp1',\n", "              'an114_mean_ntprep_fp2',\n", "              'an114_mean_ntprep_fp3'],\n", "             'ntp': ['an114_mean_ntp_fy5']})"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["est_dict"]}, {"cell_type": "code", "execution_count": null, "id": "630f55da", "metadata": {}, "outputs": [], "source": ["group_zscore(subtract(group_zscore(anl14_mean_eps_<period1>, industry), group_zscore(anl14_mean_eps_<period2>, industry)), industry)"]}, {"cell_type": "markdown", "id": "ee9fed90", "metadata": {}, "source": ["## 7.CAMP"]}, {"cell_type": "code", "execution_count": null, "id": "56b8bdc6", "metadata": {}, "outputs": [], "source": ["ts_regression(returns, group_mean(returns, ts_mean(cap, 21), sector),252, rettype=0)"]}, {"cell_type": "markdown", "id": "cd56d9e9", "metadata": {}, "source": ["#### 7.1模版拓展，将CAMP运用于其他数据"]}, {"cell_type": "code", "execution_count": null, "id": "07fd96f7", "metadata": {}, "outputs": [], "source": ["data = winsorize(ts_backfill(<data>, 63), std=4.0);\n", "data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "resid"]}, {"cell_type": "code", "execution_count": 15, "id": "6581fbea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "        data = winsorize(ts_backfill(ebit), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\n", "\n", "        data = winsorize(ts_backfill(ebitda), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\n", "\n", "        data = winsorize(ts_backfill(eps), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\n", "\n", "        data = winsorize(ts_backfill(fnd6_aqi), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\n", "\n", "        data = winsorize(ts_backfill(fnd6_ci), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\n", "\n", "        data = winsorize(ts_backfill(fnd6_cibegni), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\n", "\n", "        data = winsorize(ts_backfill(fnd6_cimii), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\n", "\n", "        data = winsorize(ts_backfill(fnd6_cipen), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\n", "\n", "        data = winsorize(ts_backfill(fnd6_citotal), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\n"]}], "source": ["fields=['ebit', 'ebitda', 'eps', 'fnd6_aqi', 'fnd6_ci', 'fnd6_cibegni', 'fnd6_cimii', 'fnd6_cipen', 'fnd6_citotal']\n", "def regression_factory(fields):\n", "    alpha_list = []\n", "\n", "    for field in fields:\n", "        alpha = \"\"\"\n", "        data = winsorize(ts_backfill({0}), 63), std=4.0);\n", "        data_gpm = group_mean(data, log(ts_mean(cap, 21)), sector);\n", "        resid = ts_regression(data, data_gpm, 252, rettype=0);\n", "        resid\"\"\".format(field)\n", "\n", "        alpha_list.append(alpha)\n", "\n", "    return alpha_list\n", "\n", "alpha_list = regression_factory(fields)\n", "for alpha in alpha_list:\n", "    print(alpha)"]}, {"cell_type": "markdown", "id": "002feb25", "metadata": {}, "source": ["7.2模版拓展，将CAMP运用于获取Beta coefficient,通过将 rettype 设置为 2，您可以获得回归的斜率。斜率是回归系数，表示因变量（目标数据）对自变量（市场数据）的敏感度。"]}, {"cell_type": "code", "execution_count": null, "id": "9ecc1af2", "metadata": {}, "outputs": [], "source": ["ts_regression(returns, group_mean(returns, ts_mean(cap, 21), 252, rettype=2))"]}, {"cell_type": "code", "execution_count": null, "id": "e795d9f1", "metadata": {}, "outputs": [], "source": ["target_data = winsorize(ts_backfill(<target_data>, 63), std=4.0);\n", "market_data = winsorize(ts_backfill(<market_data>, 63), std=4.0);\n", "target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "target_beta"]}, {"cell_type": "markdown", "id": "bc606757", "metadata": {}, "source": ["此模板捕获了个股与其各自组之间的协同变动。它通过计算回归系数（斜率）来衡量个股对市场数据的敏感度。回归系数越高，表明个股对市场数据的变动越敏感。"]}, {"cell_type": "code", "execution_count": 1, "id": "f847accd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12\n", "\n", "            target_data = winsorize(ts_backfill(ebit, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_ci, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(ebit, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_cibegni, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(ebit, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_cimii, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(ebit, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_citotal, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(ebitda, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_ci, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(ebitda, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_cibegni, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(ebitda, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_cimii, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(ebitda, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_citotal, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(eps, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_ci, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(eps, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_cibegni, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(eps, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_cimii, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n", "\n", "            target_data = winsorize(ts_backfill(eps, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill(fnd6_citotal, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\n"]}], "source": ["target_datas=['ebit','ebitda','eps']\n", "market_datas=['fnd6_ci','fnd6_cibegni','fnd6_cimii','fnd6_citotal']\n", "def regression_factory(target_datas,market_datas):\n", "    alpha_list = []\n", "\n", "    for target_data in target_datas:\n", "        for market_data in market_datas:\n", "            alpha = \"\"\"\n", "            target_data = winsorize(ts_backfill({}, 63), std=4.0);\n", "            market_data = winsorize(ts_backfill({}, 63), std=4.0);\n", "            target_beta = ts_regression(target_data, group_mean(market_data, log(ts_mean(cap, 21)), sector), 252, rettype=2);\n", "            target_beta\"\"\".format(target_data, market_data)\n", "            alpha_list.append(alpha)\n", "\n", "    return alpha_list\n", "\n", "alpha_list = regression_factory(target_datas,market_datas)\n", "print(len(alpha_list))\n", "for alpha in alpha_list:\n", "    print(alpha)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}