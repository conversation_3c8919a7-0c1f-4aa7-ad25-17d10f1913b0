-ts_regression(ts_delta(close,1),{alpha},200)

------------------------------------------------------------------------------------------------------------------------
模板方面：大部分的alpha都是通过除法模板跑出来的，大概思路就是取高Coverage的字段，跨dataset进行分子分母组合，即分子分母不能是同一数据集。

------------------------------------------------------------------------------------------------------------------------
ts_rank(winsorize(ts_backfill(pv87_qtr_matrix_net_income_normalized_estimate_median/close, 60), std=4), 120)

------------------------------------------------------------------------------------------------------------------------
dataset_id = analyst69
prefix = anl69
region = EUR
universe = TOP2500
delay = 1
neutralization = INDUSTRY

------------------------------------------------------------------------------------------------------------------------
ts_regression (ts_zscore(A,500), ts_zscore(B,500),500)

------------------------------------------------------------------------------------------------------------------------
-ts_regression(ts_delta(close,1),{alpha},200)
close可改为cap
结合group_zscore

------------------------------------------------------------------------------------------------------------------------
https://support.worldquantbrain.com/hc/en-us/community/posts/27758310278295-%E5%A6%82%E4%BD%95%E6%89%BE%E5%88%B0%E6%9B%B4%E5%A4%9Aalpha%E7%9A%84%E6%80%9D%E8%80%83-%E6%96%B0%E6%89%8B%E8%BF%9B%E9%98%B6%E7%AF%87-%E7%B3%BB%E5%88%97-%E7%AC%AC%E4%B8%89%E7%AF%87-%E8%AE%BA%E5%9D%9B%E5%86%85%E7%9A%84%E6%A8%A1%E7%89%88