<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="800" height="600" viewBox="0 0 800 600">
  <!-- 样式定义 -->
  <defs>
    <style>
      .component {
        fill: #f0f8ff;
        stroke: #4682b4;
        stroke-width: 2;
        rx: 10;
        ry: 10;
      }
      .center {
        fill: #e6f2ff;
        stroke: #4169e1;
        stroke-width: 3;
      }
      .arrow {
        fill: none;
        stroke: #708090;
        stroke-width: 2;
        marker-end: url(#arrowhead);
      }
      .label {
        font-family: 'Arial', sans-serif;
        font-size: 14px;
        text-anchor: middle;
      }
      .title {
        font-family: 'Arial', sans-serif;
        font-size: 20px;
        font-weight: bold;
        text-anchor: middle;
        fill: #2c3e50;
      }
      .component-title {
        font-family: 'Arial', sans-serif;
        font-size: 16px;
        font-weight: bold;
        text-anchor: middle;
      }
      .database {
        fill: #f5f5dc;
        stroke: #8b4513;
        stroke-width: 2;
      }
    </style>
    
    <!-- 箭头定义 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#708090"/>
    </marker>
  </defs>
  
  <!-- 图表标题 -->
  <text x="400" y="40" class="title">WorldQuant知识图谱系统架构</text>
  
  <!-- 中心组件：知识图谱管理器 -->
  <rect x="300" y="260" width="200" height="80" class="center"/>
  <text x="400" y="290" class="component-title">知识图谱管理器</text>
  <text x="400" y="315" class="label">KnowledgeGraphManager</text>
  
  <!-- 左上：实体提取器 -->
  <rect x="100" y="150" width="160" height="70" class="component"/>
  <text x="180" y="180" class="component-title">实体提取器</text>
  <text x="180" y="200" class="label">EntityExtractor</text>
  
  <!-- 右上：关系提取器 -->
  <rect x="540" y="150" width="160" height="70" class="component"/>
  <text x="620" y="180" class="component-title">关系提取器</text>
  <text x="620" y="200" class="label">RelationExtractor</text>
  
  <!-- 左中：大语言模型接口 -->
  <rect x="100" y="265" width="160" height="70" class="component"/>
  <text x="180" y="295" class="component-title">LLM接口</text>
  <text x="180" y="315" class="label">LLMInterface</text>
  
  <!-- 右中：配置管理 -->
  <rect x="540" y="265" width="160" height="70" class="component"/>
  <text x="620" y="295" class="component-title">配置管理</text>
  <text x="620" y="315" class="label">Config</text>
  
  <!-- 左下：数据处理器 -->
  <rect x="100" y="380" width="160" height="70" class="component"/>
  <text x="180" y="410" class="component-title">数据处理器</text>
  <text x="180" y="430" class="label">DataProcessor</text>
  
  <!-- 右下：图存储 -->
  <rect x="540" y="380" width="160" height="70" class="component"/>
  <text x="620" y="410" class="component-title">图存储</text>
  <text x="620" y="430" class="label">GraphStore</text>
  
  <!-- 底部：Neo4j数据库 -->
  <rect x="300" y="480" width="200" height="70" rx="5" ry="20" class="database"/>
  <text x="400" y="510" class="component-title">Neo4j数据库</text>
  <text x="400" y="530" class="label">图数据存储</text>
  
  <!-- 连接线 -->
  <!-- 实体提取器 -> 知识图谱管理器 -->
  <path d="M 260 185 L 290 260" class="arrow"/>
  
  <!-- 关系提取器 -> 知识图谱管理器 -->
  <path d="M 540 185 L 510 260" class="arrow"/>
  
  <!-- LLM接口 -> 知识图谱管理器 -->
  <path d="M 260 300 L 300 300" class="arrow"/>
  
  <!-- 配置管理 -> 知识图谱管理器 -->
  <path d="M 540 300 L 500 300" class="arrow"/>
  
  <!-- 数据处理器 -> 知识图谱管理器 -->
  <path d="M 260 400 L 300 330" class="arrow"/>
  
  <!-- 图存储 -> 知识图谱管理器 -->
  <path d="M 540 400 L 500 330" class="arrow"/>
  
  <!-- 知识图谱管理器 -> Neo4j数据库 -->
  <path d="M 400 340 L 400 480" class="arrow"/>
  
  <!-- LLM接口 -> 实体提取器 -->
  <path d="M 180 265 L 180 220" class="arrow"/>
  
  <!-- LLM接口 -> 关系提取器 -->
  <path d="M 260 275 C 350 220 450 220 540 180" class="arrow"/>
  
  <!-- 图存储 -> Neo4j数据库 -->
  <path d="M 540 415 C 500 450 450 470 400 480" class="arrow"/>
</svg> 