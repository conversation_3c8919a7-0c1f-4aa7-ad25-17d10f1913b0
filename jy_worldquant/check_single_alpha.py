#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import configparser
import time
from core import AlphaService, SubmissionService
from common_auth import BrainAuth
from common_config import CommonConfig
from db_utils import setup_database

# 提前配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('check_single_alpha.log')
    ]
)

# 4. 获取alpha列表（单数据集且无FAIL）
def get_single_dataset_alphas(alpha_service, session, start_date, end_date, alpha_num, sharpe_th, fitness_th, submission_service):
    # 只取单数据集且无FAIL的alpha
    all_alphas = alpha_service.get_alphas(
        start_date=start_date,
        end_date=end_date,
        alpha_num=alpha_num,
        usage="submit",
        sharpe_th=sharpe_th,
        fitness_th=fitness_th,
        use_region=False
    )
    filtered = []
    for alpha in all_alphas:
        alpha_id = alpha[0]
        # 获取详细信息，判断是否为单数据集
        url = f"https://api.worldquantbrain.com/alphas/{alpha_id}"
        resp = session.get(url)
        try:
            data = resp.json()
            classifications = data.get('classifications', [])
            has_single_dataset = any(
                c.get('id') == 'DATA_USAGE:SINGLE_DATA_SET' for c in classifications
            )
            checks = data.get('is', {}).get('checks', [])
            has_fail = any(chk.get('result') == 'FAIL' for chk in checks)
            if has_single_dataset and not has_fail:
                logging.info(f"找到alpha: {alpha_id}")
                filtered.append(data)
                submission_service._set_alpha_properties(session, alpha_id, name=alpha_id, tags=["single"], color="YELLOW")
        except Exception as e:
            logging.error(f"获取alpha详情失败: {alpha_id}, {e}")
    return filtered

def print_alpha_info(alpha):
    alpha_id = alpha.get('id')
    code = alpha.get('regular', {}).get('code', 'N/A')
    is_data = alpha.get('is', {})
    sharpe = is_data.get('sharpe', 'N/A')
    fitness = is_data.get('fitness', 'N/A')
    turnover = is_data.get('turnover', 'N/A')
    margin = is_data.get('margin', 'N/A')
    classifications = [c.get('name', '') for c in alpha.get('classifications', [])]
    check_results = [f"{chk.get('name', 'UNKNOWN')}: {chk.get('result', 'UNKNOWN')}" for chk in is_data.get('checks', [])]
    logging.info(f"Alpha ID: {alpha_id}")
    logging.info(f"代码: {code}")
    logging.info(f"Sharpe: {sharpe}, Fitness: {fitness}, Turnover: {turnover}, Margin: {margin}")
    logging.info(f"分类: {', '.join(classifications)}")
    logging.info(f"检查结果: {'; '.join(check_results)}")
    logging.info("-" * 120)

def main():
    logging.info("脚本开始执行...")
    # 1. 加载配置
    config = configparser.ConfigParser()
    config.read('config.ini', encoding='utf-8')
    common_config = CommonConfig.from_config(config)

    # 2. 初始化认证、数据库和服务
    session = BrainAuth.login()
    db_service = setup_database(config)

    # 3. 初始化服务
    alpha_service = AlphaService(common_config, BrainAuth, db_service)
    submission_service = SubmissionService(common_config, BrainAuth, db_service)

    logging.info("正在获取alpha列表...")
    all_filtered_alphas = get_single_dataset_alphas(
        alpha_service,
        session,
        start_date="06-25",
        end_date="06-26",
        alpha_num=20000,
        sharpe_th=1.5,
        fitness_th=1.5,
        submission_service=submission_service
    )
    logging.info(f"总共找到 {len(all_filtered_alphas)} 个符合条件的单数据集且无FAIL检查的alpha")
    logging.info("=" * 120)
    for idx, alpha in enumerate(all_filtered_alphas):
        logging.info(f"checking index {idx}")
        alpha_id = alpha['id']
        # 检查prod相关性
        while True:
            result = session.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}/correlations/prod")
            if "retry-after" in result.headers:
                time.sleep(float(result.headers["Retry-After"]))
            else:
                break
        try:
            if result.json().get("max", 0) < 0.7:
                print_alpha_info(alpha)
                submission_service._set_alpha_properties(session, alpha_id, name=alpha_id, tags=["singled"], color="BLUE")
        except Exception as e:
            logging.error(f"catch: {alpha_id}, {str(e)}")

if __name__ == '__main__':
    main()
