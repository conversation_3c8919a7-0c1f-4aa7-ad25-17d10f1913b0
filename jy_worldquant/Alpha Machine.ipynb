{"cells": [{"cell_type": "markdown", "id": "333be283-d493-4723-9ae5-cd503ac112aa", "metadata": {}, "source": ["## 1, Import Library"]}, {"cell_type": "code", "execution_count": null, "id": "fbf76117-979d-462e-b530-0a176f2f0e9b", "metadata": {}, "outputs": [], "source": ["from machine_lib import * "]}, {"cell_type": "markdown", "id": "1ef5bef8-1053-4e98-84a5-822694058aa5", "metadata": {}, "source": ["## 2, 登录\n", "<div style=\"margin-left: 20px;\">\n", "1, 在machine_lib文件的login方法中填写用户名和密码后保存，然后来到本文件Restart Kernal后重新import machine_lib后才在本文件生效\n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "2, 打印INVALID_CREDENTAIL即登录失败，打印自己的user_id信息才是登录成功。\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "52afa2f2-b36f-42da-a2bd-f336bb579b80", "metadata": {}, "outputs": [], "source": ["s = login()"]}, {"cell_type": "markdown", "id": "38e838a5-4f2d-4065-a3cd-99ee530ecbe2", "metadata": {}, "source": ["## 3, 获取数据字段\n", "<div style=\"margin-left: 20px;\">\n", "在官网Data页面中显示的为自己目前有权限的数据集，在数据集Description面板下可以看到dataset_id\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "4c455d1d-2776-4e92-b778-071900803722", "metadata": {}, "outputs": [], "source": ["df = get_datafields(s, dataset_id = 'analyst4', region='USA', universe='TOP3000', delay=1)\n", "df"]}, {"cell_type": "markdown", "id": "da2e4866", "metadata": {}, "source": ["## 4，数据字段预处理\n", "\n", "<div style=\"margin-left: 20px;\">\n", "1, matrix, vector 数据类型\n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "2, ts_backfill 回填缺失值，提高数据Coverage \n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "2, winsorize 去极值\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "23333291", "metadata": {}, "outputs": [], "source": ["pc_fields = process_datafields(df)\n", "len(pc_fields)"]}, {"cell_type": "markdown", "id": "ff2dcbb2-2d6c-4076-ac5e-80857adc78a8", "metadata": {}, "source": ["## 5, Alpha factory \n", "<div style=\"margin-left: 20px;\">\n", "在factory方法中将数据字段与操作符组装成alpha表达式\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "6b95c83a-ec99-4564-84b0-5a2c011f8555", "metadata": {}, "outputs": [], "source": ["first_order = first_order_factory(pc_fields, ts_ops)\n", "print(first_order[:10])\n", "print(len(first_order))"]}, {"cell_type": "markdown", "id": "8534645d", "metadata": {}, "source": ["## 6, 回测前载入\n", "\n", "<div style=\"margin-left: 20px;\">\n", "1, alpha表达式与初始decay配对\n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "2, random shuffle \n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "2, Load task pool数据结构\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "9e3da2b8", "metadata": {}, "outputs": [], "source": ["# 赋予alpha表达式一个初始decay\n", "init_decay = 6\n", "fo_alpha_list = []\n", "\n", "for alpha in first_order:\n", "    fo_alpha_list.append((alpha, init_decay))\n", "\n", "# 随机采样快速评估一个数据集的潜力\n", "random.shuffle(fo_alpha_list)\n", "\n", "print(\"数量: %s\"%len(fo_alpha_list))\n", "print(fo_alpha_list[:5])"]}, {"cell_type": "code", "execution_count": null, "id": "b4436351-0571-4beb-bf83-348c7dba9122", "metadata": {}, "outputs": [], "source": ["# Load alphas to task pools\n", "fo_pools = load_task_pool(fo_alpha_list, 10, 10)\n", "print(fo_pools[0])"]}, {"cell_type": "markdown", "id": "ad4cf830-441d-45f1-8e5a-17fa54082461", "metadata": {}, "source": ["## 7, 回测"]}, {"cell_type": "code", "execution_count": null, "id": "b6c7e33f", "metadata": {}, "outputs": [], "source": ["# Simulate First Order\n", "multi_simulate(fo_pools, \"SUBINDUSTRY\", \"USA\", \"TOP3000\", 0)"]}, {"cell_type": "markdown", "id": "03124465-1fe9-4820-915a-bc21795bff00", "metadata": {}, "source": ["## 8, 筛选Alpha\n", "\n", "\n", "<div style=\"margin-left: 20px;\">\n", "1, get_alpha：截取有潜力提升表现至可以提交的alpha进入下一阶\n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "2, 剪枝Prune：精减相似alpha，提高回测资源利用率\n", "</div>"]}, {"cell_type": "code", "execution_count": null, "id": "764e459c", "metadata": {}, "outputs": [], "source": ["## get promising alphas to improve in the next order\n", "fo_tracker = get_alphas(\"02-27\", \"02-28\", 1.2, 0.7, \"USA\", 100, \"track\")\n", "print(len(fo_tracker))"]}, {"cell_type": "markdown", "id": "5f44ce49", "metadata": {}, "source": ["#### Prune 剪枝"]}, {"cell_type": "code", "execution_count": null, "id": "8daae0ea", "metadata": {}, "outputs": [], "source": ["fo_layer = prune(fo_tracker, 'anl4', 5)\n", "\n", "# 剪枝后数量\n", "print(len(fo_layer))"]}, {"cell_type": "markdown", "id": "6a6ae688-d0e6-42bb-a9bd-a8d6a044b027", "metadata": {}, "source": ["## 9, 二阶提升\n", "### ts_ops(field, days) -> group_ops(ts_ops(field, days), group)"]}, {"cell_type": "code", "execution_count": null, "id": "f7037d0a", "metadata": {}, "outputs": [], "source": ["so_alpha_list = []\n", "group_ops = [\"group_neutralize\", \"group_rank\", \"group_zscore\"]\n", "\n", "for expr, decay in fo_layer:\n", "    for alpha in get_group_second_order_factory([expr], group_ops, \"USA\"):\n", "        so_alpha_list.append((alpha,decay))\n", "\n", "random.shuffle(so_alpha_list)\n", "print(len(so_alpha_list))\n", "print(so_alpha_list[:3])"]}, {"cell_type": "markdown", "id": "f20213f6", "metadata": {}, "source": ["### Simulate second order"]}, {"cell_type": "code", "execution_count": null, "id": "3fd912f9", "metadata": {}, "outputs": [], "source": ["so_pools = load_task_pool(so_alpha_list, 10, 10)\n", "multi_simulate(so_pools, 'SUBINDUSTRY', 'USA', 'TOP3000', 0)"]}, {"cell_type": "markdown", "id": "2049d949", "metadata": {}, "source": ["## 10，三阶提升\n", "group_ops(ts_ops(field, days), group) -> trade_when(entre_event, group_ops(ts_ops(field, days), group), exit_event)"]}, {"cell_type": "code", "execution_count": null, "id": "81a8250b", "metadata": {}, "outputs": [], "source": ["## get promising alphas from second order to improve in the third order\n", "so_tracker = get_alphas(\"02-27\", \"02-28\", 1.3, 0.8, \"USA\", 200, \"track\")\n", "\n", "so_layer = prune(so_tracker, 'anl4', 5)\n", "th_alpha_list = []\n", "\n", "for expr, decay in so_layer:\n", "    for alpha in trade_when_factory(\"trade_when\",expr,\"USA\"):\n", "        th_alpha_list.append((alpha,decay))\n", "\n", "random.shuffle(th_alpha_list)        \n", "print(\"三阶表达式数量:%s\"%len(th_alpha_list))"]}, {"cell_type": "markdown", "id": "f7fa679e", "metadata": {}, "source": ["### Simulate Third Order"]}, {"cell_type": "code", "execution_count": null, "id": "367d9436", "metadata": {}, "outputs": [], "source": ["# Simulate third order\n", "th_pools = load_task_pool(th_alpha_list, 10, 9)\n", "multi_simulate(th_pools, 'SUBINDUSTRY', 'USA', 'TOP3000', 0)"]}, {"cell_type": "markdown", "id": "c55f86b4-b464-44e4-ab33-ca2532128fa2", "metadata": {}, "source": ["## 11, 获取可提交的Alpha\n", "\n", "<div style=\"margin-left: 20px;\">\n", "1, 拉取sharpe,fitness达到提交要求的alpha\n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "2, Check Submission：检查其他Test是否达到要求\n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "2, view_alphas 对可以提交的alpha进行排序\n", "</div>\n"]}, {"cell_type": "code", "execution_count": null, "id": "0972b4ee-7b9d-4a6f-a7ec-e43849f54275", "metadata": {}, "outputs": [], "source": ["# 1.58 sharpe, 1 fitness, \"submit\"参数\n", "th_tracker = get_alphas(\"07-16\", \"07-20\", 1.58, 1, \"USA\", 200, \"submit\")"]}, {"cell_type": "code", "execution_count": null, "id": "ecdb7bae-6d0c-45d2-a91e-7ca789e3b92d", "metadata": {}, "outputs": [], "source": ["## 将get的alpha的id取出至stone_bag，用api check submission\n", "stone_bag = []\n", "for alpha in th_tracker:\n", "    stone_bag.append(alpha[0])\n", "print(len(stone_bag))\n", "gold_bag = []\n", "check_submission(stone_bag, gold_bag, 0)"]}, {"cell_type": "code", "execution_count": null, "id": "f54bcb30-6c07-4d59-b6ea-01508118b577", "metadata": {}, "outputs": [], "source": ["# 打印可提交的alpha信息并按sharpe排序，在网页上找到alpha手动提交\n", "view_alphas(gold_bag)"]}, {"cell_type": "markdown", "id": "802d5f47-6a65-4b93-8f00-397cbd276520", "metadata": {}, "source": ["## 12, 微调可以提交的alpha\n", "\n", "<div style=\"margin-left: 20px;\">\n", "1, 得到更好的表现\n", "</div>\n", "<div style=\"margin-left: 40px;\">\n", "调整中性化，操作符参数，Decay\n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "2, <PERSON>质量评估\n", "</div>\n", "\n", "<div style=\"margin-left: 40px;\">\n", "performance comparison，turnover，margin\n", "</div>\n", "\n", "<div style=\"margin-left: 20px;\">\n", "3, 鲁棒性评估，防止过拟合\n", "</div>\n", "\n", "<div style=\"margin-left: 40px;\">\n", "更改中性化，Rank，Binary Test...\n", "</div>"]}, {"cell_type": "markdown", "id": "1150d1a4", "metadata": {}, "source": ["### Appendix"]}, {"cell_type": "code", "execution_count": null, "id": "0b666bd2", "metadata": {}, "outputs": [], "source": ["# 模板构建Factory实例\n", "\n", "def template_factory(sent_fields, option_fields):\n", "    alpha_list = []\n", "    for sent_field in sent_fields:\n", "        for opt_field in option_fields:\n", "            alpha_list.append(\"log(1+sigmoid(ts_zscore(%s,30))*sigmoid(ts_zscore(%s,30))\"%(sent_field, opt_field))\n", "    return alpha_list \n", "\n", "opt_df = get_datafields(s, dataset_id = 'option8', region='USA', universe='TOP3000', delay=1)\n", "opt_fields = opt_df[opt_df['type'] == \"MATRIX\"][\"id\"].tolist()\n", "print(opt_fields)\n", "\n", "sent_df = get_datafields(s, dataset_id = 'sentiment1', region='USA', universe='TOP3000', delay=1)\n", "sent_fields = sent_df[sent_df['type'] == \"MATRIX\"][\"id\"].tolist()\n", "print(sent_fields)\n", "\n", "alpha_list = template_factory(sent_fields, opt_fields)\n", "print(alpha_list)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 5}