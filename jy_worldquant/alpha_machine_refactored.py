#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重构后的Alpha机器主程序
使用模块化的服务架构，提供更好的可维护性和扩展性
"""

import os
import sys
import logging
import threading
import configparser
from datetime import datetime, timed<PERSON>ta
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core import AlphaService, SimulationService, SubmissionService
from common_config import CommonConfig
from common_auth import BrainAuth
from db_utils import setup_database


class AlphaMachine:
    """重构后的Alpha机器主类"""
    
    def __init__(self, config_file: str = 'config.ini'):
        """
        初始化Alpha机器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.logger = None
        self.config_parser = None
        self.common_config = None
        self.db_service = None
        self.auth_service = None
        
        # 服务对象
        self.alpha_service = None
        self.simulation_service = None
        self.submission_service = None
        
        # 异步任务列表
        self.async_tasks = []
        
    def initialize(self) -> bool:
        """
        初始化系统组件
        
        Returns:
            是否初始化成功
        """
        try:
            # 1. 设置日志系统
            self._setup_logging()
            
            # 2. 加载配置
            self._load_configuration()
            
            # 3. 初始化数据库
            self._setup_database()
            
            # 4. 初始化认证服务
            self._setup_auth_service()
            
            # 5. 初始化业务服务
            self._setup_services()
            
            self.logger.info("Alpha机器初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Alpha机器初始化失败: {str(e)}")
            else:
                print(f"Alpha机器初始化失败: {str(e)}")
            return False
    
    def _setup_logging(self):
        """设置日志系统"""
        # 创建logs目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 设置日志文件名
        log_file = os.path.join(
            log_dir, 
            f"alpha_machine_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"
        )
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    
    def _load_configuration(self):
        """加载配置文件"""
        self.logger.info("加载配置文件")
        
        self.config_parser = configparser.ConfigParser()
        self.config_parser.read(self.config_file, encoding='utf-8')
        
        # 创建通用配置对象
        self.common_config = CommonConfig.from_config(self.config_parser)
        self.common_config.log_parameters()
    
    def _setup_database(self):
        """设置数据库服务"""
        self.logger.info("初始化数据库服务")
        
        self.db_service = setup_database(self.config_parser)
        if not self.db_service:
            self.logger.warning("数据库连接失败，将不会保存回测记录")
    
    def _setup_auth_service(self):
        """设置认证服务"""
        self.logger.info("初始化认证服务")
        self.auth_service = BrainAuth()
    
    def _setup_services(self):
        """设置业务服务"""
        self.logger.info("初始化业务服务")
        
        # Alpha服务
        self.alpha_service = AlphaService(
            config=self.common_config,
            auth_service=self.auth_service,
            db_service=self.db_service
        )
        
        # 仿真服务
        self.simulation_service = SimulationService(
            config=self.common_config,
            auth_service=self.auth_service,
            db_service=self.db_service
        )
        
        # 提交服务
        self.submission_service = SubmissionService(
            config=self.common_config,
            auth_service=self.auth_service,
            db_service=self.db_service
        )
    
    def get_execution_dates(self) -> tuple[str, str]:
        """
        获取执行日期
        
        Returns:
            (开始日期, 结束日期) 元组
        """
        if self.config_parser.getboolean('dates', 'use_dynamic_dates'):
            current_date = datetime.today()
            end_date = current_date.strftime("%m-%d")
            start_date = (current_date - timedelta(days=1)).strftime("%m-%d")
        else:
            start_date = self.config_parser.get('dates', 'start_date')
            end_date = self.config_parser.get('dates', 'end_date')
        
        self.logger.info(f"执行日期范围: {start_date} 到 {end_date}")
        return start_date, end_date
    
    def run_alpha_generation_and_simulation(self) -> bool:
        """
        运行Alpha生成和仿真流程
        
        Returns:
            是否成功
        """
        if not self.common_config.is_simulate:
            self.logger.info("跳过Alpha生成和仿真流程")
            return True
        
        self.logger.info("开始Alpha生成和仿真流程")
        
        try:
            # 1. 生成Alpha
            alpha_list = self._generate_alphas()
            if not alpha_list:
                self.logger.warning("未生成任何Alpha")
                return False
            
            # 2. 批量仿真
            self._run_batch_simulation(alpha_list)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Alpha生成和仿真流程失败: {str(e)}")
            return False
    
    def _generate_alphas(self) -> list:
        """
        生成Alpha表达式
        
        Returns:
            Alpha列表
        """
        self.logger.info("开始生成Alpha表达式")
        
        # 根据配置选择生成方式
        if hasattr(self.common_config, 'fields_file') and self.common_config.fields_file:
            # 从文件生成
            alpha_list = self.alpha_service.create_alpha_from_file(self.common_config.fields_file)
        else:
            # 从数据字段生成
            alpha_list = self.alpha_service.create_alpha_from_datafields()
        
        # 过滤已回测的Alpha
        if self.db_service:
            self.logger.info(f"生成了 {len(alpha_list)} 个Alpha，开始过滤已回测的Alpha")
            alpha_list = self.alpha_service.filter_new_alphas(alpha_list)
            self.logger.info(f"过滤后剩余 {len(alpha_list)} 个未回测的Alpha")
        
        return alpha_list
    
    def _run_batch_simulation(self, alpha_list: list):
        """
        运行批量仿真
        
        Args:
            alpha_list: Alpha列表
        """
        start_date, end_date = self.get_execution_dates()
        
        # 批量仿真
        for progress in self.simulation_service.batch_simulate(alpha_list):
            self.logger.info(f"仿真进度: {progress}%")
            
            # 在每个批次后运行提升和提交检查
            if self.common_config.is_second_promote:
                self._run_second_order_promotion(start_date, end_date)
            
            if self.common_config.is_third_promote:
                self._run_third_order_promotion(start_date, end_date)
                
                # 异步检查提交
                self._async_check_submission(start_date, end_date)
    
    def _run_second_order_promotion(self, start_date: str, end_date: str):
        """
        运行二阶提升
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        """
        self.logger.info("开始二阶提升")
        
        try:
            # 获取候选Alpha
            alpha_records = self.alpha_service.get_alphas(
                start_date=start_date,
                end_date=end_date,
                alpha_num=self.common_config.alpha_num_filter,
                usage="track",
                sharpe_th=self.common_config.second_promote_sharp,
                fitness_th=self.common_config.second_promote_fitness,
                use_region=True
            )
            
            if not alpha_records:
                self.logger.warning("没有找到符合条件的Alpha进行二阶提升")
                return
            
            # 去重
            unique_records = self.alpha_service.remove_duplicates(alpha_records)
            
            # 修剪
            pruned_alphas = self.alpha_service.prune_alphas(
                unique_records, 
                self.common_config.prefix, 
                5
            )
            
            if len(pruned_alphas) > 10000:
                pruned_alphas = pruned_alphas[:10000]
            
            # 生成二阶Alpha
            second_order_alphas = []
            for expr, decay in pruned_alphas:
                # 检查是否包含group操作
                if not any(op in expr for op in ["group_neutralize", "group_rank", "group_zscore", 
                                               "group_mean", "group_extra", "group_backfill", 
                                               "group_scale", "group_cartesian_product"]):
                    factory_alphas = self.alpha_service.alpha_factory.create_group_operations(
                        "group_neutralize", expr
                    )
                    for factory_alpha in factory_alphas:
                        second_order_alphas.append((factory_alpha, decay))
            
            # 过滤新Alpha
            second_order_alphas = self.alpha_service.filter_new_alphas(second_order_alphas)
            
            if second_order_alphas:
                if len(second_order_alphas) > 10000:
                    second_order_alphas = second_order_alphas[:10000]
                
                # 仿真二阶Alpha - 使用batch_simulate确保保存到数据库
                for progress in self.simulation_service.batch_simulate(second_order_alphas):
                    self.logger.info(f"二阶Alpha仿真进度: {progress}%")
                        
                self.logger.info(f"二阶提升完成，处理了 {len(second_order_alphas)} 个Alpha")
            else:
                self.logger.info("没有新的二阶Alpha需要处理")
                
        except Exception as e:
            self.logger.error(f"二阶提升失败: {str(e)}")
    
    def _run_third_order_promotion(self, start_date: str, end_date: str):
        """
        运行三阶提升
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        """
        self.logger.info("开始三阶提升")
        
        try:
            # 获取候选Alpha
            alpha_records = self.alpha_service.get_alphas(
                start_date=start_date,
                end_date=end_date,
                alpha_num=self.common_config.third_promote_num,
                usage="track",
                sharpe_th=self.common_config.third_promote_sharp,
                fitness_th=self.common_config.third_promote_fitness,
                use_region=True
            )
            
            if not alpha_records:
                self.logger.warning("没有找到符合条件的Alpha进行三阶提升")
                return
            
            # 过滤掉包含trade_when的Alpha
            filtered_records = [
                record for record in alpha_records
                if "trade_when" not in str(record[1])
            ]
            
            # 去重
            unique_records = self.alpha_service.remove_duplicates(filtered_records)
            
            # 修剪
            pruned_alphas = self.alpha_service.prune_alphas(
                unique_records,
                self.common_config.prefix,
                5
            )
            
            # 生成三阶Alpha
            third_order_alphas = []
            for expr, decay in pruned_alphas:
                factory_alphas = self.alpha_service.alpha_factory.create_trade_when_operations(
                    expr, self.common_config.events
                )
                for factory_alpha in factory_alphas:
                    third_order_alphas.append((factory_alpha, decay))
            
            # 过滤新Alpha
            third_order_alphas = self.alpha_service.filter_new_alphas(third_order_alphas)
            
            if third_order_alphas:
                if len(third_order_alphas) > 10000:
                    third_order_alphas = third_order_alphas[:10000]
                
                # 仿真三阶Alpha - 使用batch_simulate确保保存到数据库
                for progress in self.simulation_service.batch_simulate(third_order_alphas):
                    self.logger.info(f"三阶Alpha仿真进度: {progress}%")
                        
                self.logger.info(f"三阶提升完成，处理了 {len(third_order_alphas)} 个Alpha")
            else:
                self.logger.info("没有新的三阶Alpha需要处理")
                
        except Exception as e:
            self.logger.error(f"三阶提升失败: {str(e)}")
    
    def _async_check_submission(self, start_date: str, end_date: str):
        """
        异步检查提交
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
        """
        def submission_task():
            try:
                eligible_alphas = self.submission_service.process_submission_workflow(
                    start_date, end_date
                )
                self.logger.info(f"异步提交检查完成，找到 {len(eligible_alphas)} 个符合条件的Alpha")
            except Exception as e:
                self.logger.error(f"异步提交检查失败: {str(e)}")
        
        # 创建并启动异步任务
        task_thread = threading.Thread(target=submission_task, daemon=True)
        task_thread.start()
        self.async_tasks.append(task_thread)
        
        self.logger.info(f"已启动异步提交检查任务: {task_thread.name}")
    
    def run_promotion_only_workflow(self) -> bool:
        """
        仅运行提升工作流程（不进行Alpha生成和仿真）
        
        Returns:
            是否成功
        """
        self.logger.info("开始仅提升工作流程")
        
        try:
            start_date, end_date = self.get_execution_dates()
            
            # 二阶提升
            if self.common_config.is_second_promote:
                self._run_second_order_promotion(start_date, end_date)
            
            # 三阶提升
            if self.common_config.is_third_promote:
                self._run_third_order_promotion(start_date, end_date)
            
            return True
            
        except Exception as e:
            self.logger.error(f"仅提升工作流程失败: {str(e)}")
            return False
    
    def run_final_submission_check(self):
        """运行最终提交检查"""
        self.logger.info("执行最终提交检查")
        
        try:
            start_date, end_date = self.get_execution_dates()
            eligible_alphas = self.submission_service.process_submission_workflow(
                start_date, end_date
            )
            self.logger.info(f"最终提交检查完成，找到 {len(eligible_alphas)} 个符合条件的Alpha")
            
        except Exception as e:
            self.logger.error(f"最终提交检查失败: {str(e)}")
    
    def wait_for_async_tasks(self):
        """等待异步任务完成"""
        if self.async_tasks:
            self.logger.info(f"等待 {len(self.async_tasks)} 个异步任务完成")
            for task in self.async_tasks:
                task.join()
            self.logger.info("所有异步任务已完成")
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("开始清理资源")
        
        # 等待异步任务完成
        self.wait_for_async_tasks()
        
        # 关闭数据库连接
        if self.db_service:
            # 检查是否是异步数据库服务
            if hasattr(self.db_service, 'wait_for_completion'):
                self.logger.info("等待异步数据库操作完成")
                self.db_service.wait_for_completion()
            
            self.db_service.close()
            self.logger.info("数据库连接已关闭")
        
        self.logger.info("资源清理完成")
    
    def run(self) -> bool:
        """
        运行完整的Alpha机器流程
        
        Returns:
            是否成功
        """
        if not self.initialize():
            return False
        
        try:
            # 根据配置决定运行流程
            if self.common_config.is_simulate:
                # 完整流程：生成、仿真、提升
                success = self.run_alpha_generation_and_simulation()
            else:
                # 仅提升流程
                success = self.run_promotion_only_workflow()
            
            if success:
                # 最终提交检查
                self.run_final_submission_check()
            
            return success
            
        except Exception as e:
            self.logger.error(f"Alpha机器运行失败: {str(e)}")
            return False
            
        finally:
            self.cleanup()


def main():
    """主函数"""
    # 创建Alpha机器实例
    alpha_machine = AlphaMachine()
    
    # 运行Alpha机器
    success = alpha_machine.run()
    
    if success:
        print("Alpha机器运行成功完成")
        return 0
    else:
        print("Alpha机器运行失败")
        return 1


if __name__ == '__main__':
    sys.exit(main()) 