---
description:
globs:
alwaysApply: false
---
# Alpha机器系统API使用指南

## 主程序API

### AlphaMachine类
主程序入口：[alpha_machine_refactored.py](mdc:alpha_machine_refactored.py)

```python
from alpha_machine_refactored import AlphaMachine

# 创建实例
alpha_machine = AlphaMachine('config.ini')

# 初始化系统
if alpha_machine.initialize():
    # 运行完整流程
    success = alpha_machine.run()
```

**主要方法:**
- `initialize()` - 初始化系统组件
- `run()` - 运行完整的Alpha机器流程
- `run_alpha_generation_and_simulation()` - 仅运行生成和仿真
- `run_promotion_only_workflow()` - 仅运行提升流程
- `cleanup()` - 清理资源

## 核心服务API

### AlphaService - Alpha管理服务
位置：[core/alpha_service.py](mdc:core/alpha_service.py)

#### 初始化
```python
from core import AlphaService

alpha_service = AlphaService(
    config=common_config,
    auth_service=auth_service,
    db_service=db_service  # 可选
)
```

#### Alpha生成
```python
# 从数据字段生成Alpha
alpha_list = alpha_service.create_alpha_from_datafields()

# 从文件生成Alpha
alpha_list = alpha_service.create_alpha_from_file('data_fields.txt')

# 从模板生成Alpha
alpha_list = alpha_service.create_alpha_from_template(template_list)
```

#### Alpha获取和筛选
```python
# 获取Alpha列表
alphas = alpha_service.get_alphas(
    start_date="05-24",
    end_date="05-30",
    alpha_num=100,
    usage="track",
    sharpe_th=1.2,
    fitness_th=0.7
)

# 过滤新Alpha（数据库去重）
filtered_alphas = alpha_service.filter_new_alphas(alpha_list)

# 去除重复Alpha
unique_alphas = alpha_service.remove_duplicates(alpha_list)

# 修剪Alpha列表
pruned_alphas = alpha_service.prune_alphas(
    alpha_list, 
    prefix="winsorize", 
    max_length=5
)
```

### SimulationService - 仿真服务
位置：[core/simulation_service.py](mdc:core/simulation_service.py)

#### 初始化
```python
from core import SimulationService

simulation_service = SimulationService(
    config=common_config,
    auth_service=auth_service,
    db_service=db_service  # 可选
)
```

#### 批量仿真
```python
# 批量仿真（推荐）
for progress in simulation_service.batch_simulate(alpha_list):
    print(f"仿真进度: {progress}%")

# 单次仿真
success = simulation_service.simulate_alphas(alpha_list)
```

#### 任务管理
```python
# 获取任务管理器
task_manager = simulation_service.task_manager

# 检查任务状态
active_tasks = task_manager.get_active_task_count()
print(f"当前活跃任务数: {active_tasks}")
```

### SubmissionService - 提交服务
位置：[core/submission_service.py](mdc:core/submission_service.py)

#### 初始化
```python
from core import SubmissionService

submission_service = SubmissionService(
    config=common_config,
    auth_service=auth_service,
    db_service=db_service  # 可选
)
```

#### 提交检查
```python
# 处理提交工作流
eligible_alphas = submission_service.process_submission_workflow(
    start_date="05-24",
    end_date="05-30"
)

# 检查Alpha提交状态
status = submission_service.check_submission_status(alpha_ids)

# 检查Alpha相关性
correlation_matrix = submission_service.check_alpha_correlation(alpha_ids)
```

## 配置管理API

### CommonConfig类
位置：[common_config.py](mdc:common_config.py)

```python
import configparser
from common_config import CommonConfig

# 从配置文件创建
config_parser = configparser.ConfigParser()
config_parser.read('config.ini', encoding='utf-8')
config = CommonConfig.from_config(config_parser)

# 访问配置属性
print(f"数据集ID: {config.dataset_id}")
print(f"批量大小: {config.batch_size}")
print(f"是否仿真: {config.is_simulate}")
```

**主要配置属性:**
- `dataset_id` - 数据集ID
- `region` - 地区
- `universe` - 股票池
- `batch_size` - 批量大小
- `is_simulate` - 是否执行仿真
- `is_second_promote` - 是否二阶提升
- `is_third_promote` - 是否三阶提升

## 数据库API

### 数据库服务
位置：[db_utils.py](mdc:db_utils.py)

#### 初始化
```python
from db_utils import setup_database

# 设置数据库
db_service = setup_database(config_parser)
```

#### Alpha操作
```python
# 检查Alpha是否存在
exists = db_service.is_alpha_in_db(alpha_expr, decay, config)

# 保存Alpha到数据库
success = db_service.save_alpha_to_db(alpha_expr, decay, config)

# 批量过滤新Alpha
new_alphas = db_service.filter_new_alpha_list(alpha_list, config)
```

#### 统计查询
```python
# 获取数据库统计信息
stats = db_service.get_statistics()
print(f"总Alpha数量: {stats['total_alphas']}")
print(f"已提交数量: {stats['submitted_count']}")

# 获取Alpha历史记录
history = db_service.get_alpha_history(
    start_date="2024-05-24",
    end_date="2024-05-30",
    limit=100
)
```

## Alpha工厂API

### AlphaFactory类
位置：[core/alpha_factory.py](mdc:core/alpha_factory.py)

```python
from core.alpha_factory import AlphaFactory

# 创建工厂实例
factory = AlphaFactory()

# 生成group操作Alpha
group_alphas = factory.create_group_operations(
    operation="group_neutralize",
    base_expr="ts_mean(close, 20)"
)

# 生成trade_when操作Alpha
trade_alphas = factory.create_trade_when_operations(
    base_expr="group_neutralize(ts_mean(close, 20), industry)",
    events=["open_events", "earnings_events"]
)

# 生成时间序列操作Alpha
ts_alphas = factory.create_time_series_operations(
    base_expr="close",
    operations=["ts_mean", "ts_std_dev", "ts_rank"]
)
```

## 认证服务API

### BrainAuth类
位置：[common_auth.py](mdc:common_auth.py)

```python
from common_auth import BrainAuth

# 创建认证服务
auth_service = BrainAuth()

# 检查认证状态
if auth_service.is_authenticated():
    print("已认证")
else:
    print("需要重新认证")

# 获取认证头
headers = auth_service.get_auth_headers()
```

## 错误处理

### 常见异常类型
```python
try:
    # Alpha机器操作
    result = alpha_machine.run()
except BrainAPIException as e:
    print(f"Brain API错误: {e}")
except DatabaseException as e:
    print(f"数据库错误: {e}")
except ConfigurationException as e:
    print(f"配置错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

### 重试机制
```python
import time

def retry_operation(operation, max_retries=3, delay=1):
    """通用重试机制"""
    for attempt in range(max_retries):
        try:
            return operation()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            print(f"操作失败，重试 {attempt + 1}/{max_retries}")
            time.sleep(delay * (2 ** attempt))  # 指数退避
```

## 使用示例

### 完整工作流示例
```python
#!/usr/bin/env python3
import configparser
from alpha_machine_refactored import AlphaMachine

def main():
    # 创建Alpha机器
    alpha_machine = AlphaMachine('config.ini')
    
    # 运行完整流程
    if alpha_machine.initialize():
        success = alpha_machine.run()
        if success:
            print("Alpha机器运行成功")
        else:
            print("Alpha机器运行失败")
    else:
        print("Alpha机器初始化失败")

if __name__ == '__main__':
    main()
```

### 自定义服务示例
```python
from core import AlphaService, SimulationService
from common_config import CommonConfig
from common_auth import BrainAuth
from db_utils import setup_database

def custom_workflow():
    # 初始化组件
    config_parser = configparser.ConfigParser()
    config_parser.read('config.ini', encoding='utf-8')
    
    config = CommonConfig.from_config(config_parser)
    auth_service = BrainAuth()
    db_service = setup_database(config_parser)
    
    # 创建服务
    alpha_service = AlphaService(config, auth_service, db_service)
    simulation_service = SimulationService(config, auth_service, db_service)
    
    # 生成和仿真Alpha
    alpha_list = alpha_service.create_alpha_from_datafields()
    alpha_list = alpha_service.filter_new_alphas(alpha_list)
    
    for progress in simulation_service.batch_simulate(alpha_list):
        print(f"进度: {progress}%")

if __name__ == '__main__':
    custom_workflow()
```

## 性能优化建议

### 批量操作
- 使用 `batch_simulate()` 而不是单个Alpha仿真
- 合理设置 `batch_size` 参数（默认1000）
- 使用数据库批量过滤避免重复检查

### 并发控制
- 默认最大并发数为8，可根据系统性能调整
- 使用任务管理器监控并发状态
- 避免过度并发导致系统负载过高

### 内存管理
- 及时释放大型Alpha列表
- 使用生成器处理大量数据
- 分批处理避免内存溢出
