---
description: 
globs: 
alwaysApply: false
---
# Alpha机器系统代码标准

本项目遵循以下代码风格和最佳实践，专门针对Alpha机器系统的特点进行优化。

## Python代码风格

- 遵循PEP 8编码规范
- 使用4个空格进行缩进
- 行长度限制在120个字符以内
- 使用清晰的变量和函数命名
- 为所有函数和类添加文档字符串

## 文件组织

### 核心模块结构
- `core/` - 核心服务模块
  - `alpha_service.py` - Alpha管理服务
  - `simulation_service.py` - 仿真服务
  - `submission_service.py` - 提交服务
  - `base_service.py` - 服务基类
  - `alpha_factory.py` - Alpha工厂

### 通用组件
- [common_config.py](mdc:common_config.py) - 配置管理
- [common_auth.py](mdc:common_auth.py) - 认证服务
- [db_utils.py](mdc:db_utils.py) - 数据库工具

### 目录约定
- `data/` - 数据文件和SQLite数据库
- `logs/` - 系统日志文件
- `docs/` - 文档和教程

## 命名约定

### Alpha机器特定约定
- Alpha表达式变量：`alpha_expr`, `alpha_list`
- 配置对象：`config`, `common_config`
- 服务对象：`alpha_service`, `simulation_service`, `submission_service`
- 数据库对象：`db_service`

### 通用约定
- 类名使用驼峰命名法（如`AlphaService`）
- 函数和变量名使用小写字母加下划线（如`create_alpha_from_datafields`）
- 常量使用全大写（如`MAX_CONCURRENT_TASKS`）
- 私有方法和变量以下划线开头（如`_generate_alphas`）

## 服务类规范

### 服务基类继承
所有服务必须继承自 [core/base_service.py](mdc:core/base_service.py)：

```python
from .base_service import BaseService

class YourService(BaseService):
    def __init__(self, config, auth_service, db_service=None):
        super().__init__(config, auth_service, db_service)
    
    def get_service_name(self):
        return "YourService"
```

### 服务方法约定
- 公共方法：提供给外部调用的接口
- 私有方法：以`_`开头，内部实现细节
- 异步方法：返回生成器或使用异步框架

## Alpha处理规范

### Alpha表达式处理
```python
# 正确：使用元组存储Alpha和decay
alpha_list = [(alpha_expr, decay), ...]

# 正确：批量处理Alpha
for batch in self._create_batches(alpha_list, batch_size):
    self._process_batch(batch)
```

### 数据库过滤
```python
# 必须：在生成Alpha后进行数据库过滤
alpha_list = self.alpha_service.create_alpha_from_datafields()
alpha_list = self.alpha_service.filter_new_alphas(alpha_list)
```

## 错误处理规范

### Alpha机器特定错误处理
```python
try:
    # Alpha相关操作
    result = self.alpha_service.get_alphas(...)
except BrainAPIException as e:
    self.logger.error(f"Brain API错误: {str(e)}")
    # 重试或降级处理
except DatabaseException as e:
    self.logger.error(f"数据库错误: {str(e)}")
    # 数据库恢复逻辑
except Exception as e:
    self.logger.error(f"未知错误: {str(e)}")
    # 通用错误处理
```

### 重试机制
```python
def retry_brain_operation(self, operation, max_retries=3):
    """重试Brain平台操作"""
    for attempt in range(max_retries):
        try:
            return operation()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            self.logger.warning(f"操作失败，重试 {attempt + 1}/{max_retries}")
            time.sleep(2 ** attempt)  # 指数退避
```

## 日志规范

### 日志级别使用
```python
# INFO: 正常流程信息
self.logger.info(f"开始处理 {len(alpha_list)} 个Alpha")

# WARNING: 警告但不影响执行
self.logger.warning("数据库连接失败，将不会保存记录")

# ERROR: 错误但程序可以继续
self.logger.error(f"Alpha仿真失败: {str(e)}")

# DEBUG: 调试信息
self.logger.debug(f"Alpha表达式: {alpha_expr}")
```

### 日志格式约定
- 包含操作类型和数量
- 提供足够的上下文信息
- 避免敏感信息泄露

## 配置管理规范

### 配置访问
```python
# 正确：通过CommonConfig对象访问配置
dataset_id = self.config.dataset_id
batch_size = self.config.batch_size

# 错误：直接访问配置解析器
# dataset_id = config_parser.get('setting', 'dataset_id')
```

### 配置验证
```python
def validate_config(self):
    """验证配置参数"""
    if self.config.batch_size <= 0:
        raise ValueError("batch_size必须大于0")
    
    if not self.config.dataset_id:
        raise ValueError("dataset_id不能为空")
```

## 数据库操作规范

### 事务处理
```python
# 正确：使用事务处理批量操作
with self.db_service.get_connection() as conn:
    cursor = conn.cursor()
    for alpha_expr, decay in alpha_list:
        self.db_service.save_alpha_to_db(alpha_expr, decay, self.config)
    conn.commit()
```

### 查询优化
```python
# 正确：使用参数化查询
cursor.execute(
    "SELECT * FROM alpha_history WHERE alpha_hash = ? AND dataset_id = ?",
    (alpha_hash, dataset_id)
)

# 错误：字符串拼接（SQL注入风险）
# cursor.execute(f"SELECT * FROM alpha_history WHERE alpha_hash = '{alpha_hash}'")
```

## 性能优化规范

### 批量处理
```python
# 正确：使用生成器处理大量数据
def batch_simulate(self, alpha_list):
    for i in range(0, len(alpha_list), self.config.batch_size):
        batch = alpha_list[i:i + self.config.batch_size]
        yield from self._process_batch(batch)
```

### 并发控制
```python
# 正确：使用任务管理器控制并发
with ThreadPoolExecutor(max_workers=self.config.max_concurrent_tasks) as executor:
    futures = [executor.submit(self._simulate_alpha, alpha) for alpha in batch]
    for future in as_completed(futures):
        result = future.result()
```

## 测试规范

### 单元测试结构
```python
def test_alpha_service():
    """测试Alpha服务功能"""
    # 准备测试数据
    config = create_test_config()
    service = AlphaService(config, mock_auth, mock_db)
    
    # 执行测试
    result = service.create_alpha_from_datafields()
    
    # 验证结果
    assert len(result) > 0
    assert all(isinstance(item, tuple) for item in result)
```

### 集成测试
```python
def test_complete_workflow():
    """测试完整工作流程"""
    alpha_machine = AlphaMachine('test_config.ini')
    success = alpha_machine.run()
    assert success is True
```

## 版本控制规范

### 提交信息格式
```
类型(范围): 简短描述

详细描述（可选）

相关Issue: #123
```

### 分支策略
- `main` - 主分支，稳定版本
- `develop` - 开发分支
- `feature/xxx` - 功能分支
- `hotfix/xxx` - 热修复分支

### 代码审查要点
- 服务类是否正确继承基类
- 是否进行了适当的错误处理
- 数据库操作是否安全
- 日志信息是否充分
- 配置访问是否规范
