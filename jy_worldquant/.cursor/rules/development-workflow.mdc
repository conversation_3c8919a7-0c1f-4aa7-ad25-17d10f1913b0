---
description: 
globs: 
alwaysApply: false
---
# 开发工作流

本项目是WorldQuant Alpha研究系统，用于开发和管理量化交易策略。以下是开发和使用的主要工作流程。

## 环境设置

1. 确保已安装所有依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 配置文件设置在 [config.ini](mdc:config.ini) 中完成。

## 开发流程

### 启动与停止

- 使用 [start.sh](mdc:start.sh) 启动系统
- 使用 [stop.sh](mdc:stop.sh) 停止系统
- 使用 [tailf.sh](mdc:tailf.sh) 查看日志

### 核心库使用

- [machine_lib.py](mdc:machine_lib.py) 提供了主要的机器学习功能
- [machine_lib_concurrent.py](mdc:machine_lib_concurrent.py) 提供了并发版本的功能

### 自动执行

[auto_ex_new.py](mdc:auto_ex_new.py) 是自动执行模块，可以用于：
- 自动运行Alpha策略
- 执行回测
- 提交结果到WorldQuant平台

## 数据处理

数据处理主要通过以下几个步骤：
1. 数据加载 - 从数据源获取数据
2. 数据清洗 - 处理缺失值和异常值
3. 特征工程 - 创建新特征
4. 模型训练 - 使用机器学习模型
5. 结果评估 - 评估模型性能

详细的实现可以参考 [process_fields.py](mdc:process_fields.py) 和 [db_utils.py](mdc:db_utils.py)。
