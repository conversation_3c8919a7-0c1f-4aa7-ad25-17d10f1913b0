---
description:
globs:
alwaysApply: false
---
# Alpha机器系统开发指南

## 开发环境设置

### 必需依赖
- Python 3.8+
- WorldQuant Brain账户
- SQLite3 (通常随Python安装)

### 项目初始化
```bash
# 克隆项目
git clone <repository-url>
cd jy_worldquant

# 安装依赖
pip install -r requirements.txt

# 配置系统
cp config.ini.example config.ini
# 编辑config.ini设置你的参数
```

## 代码结构和约定

### 服务基类
所有服务都继承自 [core/base_service.py](mdc:core/base_service.py)

```python
from core.base_service import BaseService

class YourService(BaseService):
    def get_service_name(self):
        return "YourService"
    
    def your_method(self):
        self.logger.info("使用继承的logger")
        # 实现你的逻辑
```

### 配置管理
使用 [common_config.py](mdc:common_config.py) 中的 `CommonConfig` 类

```python
from common_config import CommonConfig

# 从配置解析器创建配置对象
config = CommonConfig.from_config(config_parser)

# 访问配置
dataset_id = config.dataset_id
batch_size = config.batch_size
```

### 数据库操作
使用 [db_utils.py](mdc:db_utils.py) 中的工具函数

```python
from db_utils import setup_database

# 设置数据库连接
db_service = setup_database(config_parser)

# 检查Alpha是否存在
exists = db_service.is_alpha_in_db(alpha_expr, decay, config)

# 保存Alpha到数据库
success = db_service.save_alpha_to_db(alpha_expr, decay, config)
```

## 添加新服务

### 1. 创建服务类
在 `core/` 目录下创建新的服务文件：

```python
# core/your_service.py
from .base_service import BaseService

class YourService(BaseService):
    def __init__(self, config, auth_service, db_service=None):
        super().__init__(config, auth_service, db_service)
    
    def get_service_name(self):
        return "YourService"
    
    def your_business_logic(self):
        # 实现业务逻辑
        pass
```

### 2. 更新服务初始化
在 [core/__init__.py](mdc:core/__init__.py) 中导出新服务：

```python
from .your_service import YourService

__all__ = [
    'AlphaService',
    'SimulationService', 
    'SubmissionService',
    'YourService'  # 添加新服务
]
```

### 3. 在主程序中使用
在 [alpha_machine_refactored.py](mdc:alpha_machine_refactored.py) 中集成：

```python
def _setup_services(self):
    # ... 现有服务初始化 ...
    
    # 添加新服务
    self.your_service = YourService(
        config=self.common_config,
        auth_service=self.auth_service,
        db_service=self.db_service
    )
```

## Alpha工厂扩展

### 添加新的Alpha操作
在 [core/alpha_factory.py](mdc:core/alpha_factory.py) 中添加新方法：

```python
def create_your_operations(self, base_expr, your_params):
    """
    创建你的自定义Alpha操作
    
    Args:
        base_expr: 基础Alpha表达式
        your_params: 你的参数
        
    Returns:
        生成的Alpha列表
    """
    alphas = []
    # 实现你的Alpha生成逻辑
    return alphas
```

## 测试和调试

### 单元测试
创建测试文件来验证功能：

```python
#!/usr/bin/env python3
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core import YourService
from common_config import CommonConfig

def test_your_service():
    # 设置测试环境
    config = CommonConfig.from_config(config_parser)
    service = YourService(config, auth_service, db_service)
    
    # 执行测试
    result = service.your_business_logic()
    assert result is not None
    
    print("测试通过")

if __name__ == '__main__':
    test_your_service()
```

### 日志调试
系统使用标准Python logging模块：

```python
# 在服务中使用继承的logger
self.logger.info("信息日志")
self.logger.warning("警告日志") 
self.logger.error("错误日志")
self.logger.debug("调试日志")

# 启用调试级别日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 配置扩展

### 添加新配置项
1. 在 [config.ini](mdc:config.ini) 中添加配置：
```ini
[your_section]
your_param = your_value
```

2. 在 [common_config.py](mdc:common_config.py) 中添加属性：
```python
@classmethod
def from_config(cls, config_parser):
    # ... 现有代码 ...
    
    # 添加新配置
    your_param = config_parser.get('your_section', 'your_param', fallback='default_value')
    
    return cls(
        # ... 现有参数 ...
        your_param=your_param
    )
```

## 数据库扩展

### 添加新表
在 [db_utils.py](mdc:db_utils.py) 中添加表创建逻辑：

```python
def create_your_table(cursor):
    """创建你的新表"""
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS your_table (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            your_field TEXT NOT NULL,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

def setup_database(config_parser):
    # ... 现有代码 ...
    
    # 创建新表
    create_your_table(cursor)
    
    # ... 其余代码 ...
```

## 性能优化建议

### 1. 批量处理
- 使用批量操作减少API调用
- 合理设置batch_size参数

### 2. 并发控制
- 遵循系统的并发限制
- 使用任务管理器进行并发控制

### 3. 内存管理
- 及时释放大对象
- 使用生成器处理大量数据

### 4. 数据库优化
- 使用事务处理批量操作
- 合理使用索引

## 错误处理最佳实践

### 1. 异常捕获
```python
try:
    # 业务逻辑
    result = risky_operation()
except SpecificException as e:
    self.logger.error(f"特定错误: {str(e)}")
    # 处理特定错误
except Exception as e:
    self.logger.error(f"未知错误: {str(e)}")
    # 通用错误处理
```

### 2. 重试机制
```python
import time

def retry_operation(func, max_retries=3, delay=1):
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            time.sleep(delay)
```

## 代码提交规范

### 提交信息格式
```
类型(范围): 简短描述

详细描述（可选）

相关Issue: #123
```

### 类型说明
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动
