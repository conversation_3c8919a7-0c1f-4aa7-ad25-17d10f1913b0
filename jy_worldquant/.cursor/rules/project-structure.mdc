---
description: 
globs: 
alwaysApply: false
---
# 项目结构

这是一个WorldQuant Alpha机器系统，专为WorldQuant Brain平台设计的自动化Alpha因子生成、回测和提交工具。

## 主程序入口

- [alpha_machine_refactored.py](mdc:alpha_machine_refactored.py) - 重构后的Alpha机器主程序，采用模块化架构

## 核心服务模块

### 服务层 (core/)
- [core/alpha_service.py](mdc:core/alpha_service.py) - Alpha因子生成、获取、过滤和管理服务
- [core/simulation_service.py](mdc:core/simulation_service.py) - Alpha回测仿真和任务管理服务
- [core/submission_service.py](mdc:core/submission_service.py) - Alpha提交检查和相关性分析服务
- [core/base_service.py](mdc:core/base_service.py) - 服务基类，提供通用功能
- [core/__init__.py](mdc:core/__init__.py) - 核心服务模块初始化

### Alpha工厂
- [core/alpha_factory.py](mdc:core/alpha_factory.py) - Alpha表达式生成工厂

## 通用组件

- [common_config.py](mdc:common_config.py) - 通用配置管理
- [common_auth.py](mdc:common_auth.py) - WorldQuant Brain平台认证
- [db_utils.py](mdc:db_utils.py) - SQLite数据库工具和Alpha历史记录管理

## 传统模块（向后兼容）

- [machine_lib.py](mdc:machine_lib.py) - 传统机器学习库
- [machine_lib_concurrent.py](mdc:machine_lib_concurrent.py) - 并发版本的机器学习库
- [auto_ex_new.py](mdc:auto_ex_new.py) - 传统自动执行工具

## 配置文件

- [config.ini](mdc:config.ini) - 主配置文件，包含所有系统参数
- [config_check.ini](mdc:config_check.ini) - 配置检查文件

## 重要目录

- `core/` - 核心服务模块
- `data/` - 数据文件和SQLite数据库
- `logs/` - 系统日志目录
- `docs/` - 文档和教程
- `llm/` - 大语言模型相关功能（知识图谱系统）

## 启动脚本

- [start.sh](mdc:start.sh) - 系统启动脚本
- [stop.sh](mdc:stop.sh) - 系统停止脚本
- [tailf.sh](mdc:tailf.sh) - 日志实时查看工具

## 示例和教程

- [Alpha Machine.ipynb](mdc:Alpha Machine.ipynb) - Jupyter笔记本教程
- [README.md](mdc:README.md) - 完整的系统文档和使用指南
