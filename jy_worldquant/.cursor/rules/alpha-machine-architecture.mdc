---
description:
globs:
alwaysApply: false
---
# Alpha机器系统架构

## 系统概述

Alpha机器系统采用模块化服务架构，主要由以下组件构成：

```
AlphaMachine (主程序)
├── AlphaService (Alpha服务)
├── SimulationService (仿真服务)
├── SubmissionService (提交服务)
├── DatabaseService (数据库服务)
└── AuthService (认证服务)
```

## 核心服务详解

### 1. AlphaService - Alpha管理服务
位置: [core/alpha_service.py](mdc:core/alpha_service.py)

**主要功能:**
- Alpha因子生成 (`create_alpha_from_datafields`, `create_alpha_from_file`)
- Alpha获取和筛选 (`get_alphas`)
- 重复过滤 (`remove_duplicates`, `filter_new_alphas`)
- Alpha修剪 (`prune_alphas`)

**关键方法:**
- `create_alpha_from_datafields()` - 基于数据字段生成Alpha
- `get_alphas()` - 从Brain平台获取Alpha列表
- `filter_new_alphas()` - 过滤数据库中已存在的Alpha

### 2. SimulationService - 仿真服务
位置: [core/simulation_service.py](mdc:core/simulation_service.py)

**主要功能:**
- 批量Alpha仿真 (`batch_simulate`)
- 并发任务管理 (`SimulationTaskManager`)
- 进度监控和状态跟踪

**关键组件:**
- `batch_simulate()` - 批量仿真主方法
- `SimulationTaskManager` - 任务管理器，处理并发控制
- `_multi_simulate()` - 多线程仿真执行

### 3. SubmissionService - 提交服务
位置: [core/submission_service.py](mdc:core/submission_service.py)

**主要功能:**
- Alpha提交检查 (`check_submission_status`)
- 相关性分析 (`check_alpha_correlation`)
- 提交工作流管理 (`process_submission_workflow`)

### 4. DatabaseService - 数据库服务
位置: [db_utils.py](mdc:db_utils.py)

**主要功能:**
- Alpha历史记录存储
- 重复Alpha过滤
- 统计信息查询

**核心表结构:**
```sql
alpha_history (
    alpha_hash,     -- Alpha哈希值（用于快速查找）
    alpha,          -- Alpha表达式
    dataset_id,     -- 数据集ID
    region,         -- 地区
    sharpe,         -- Sharpe比率
    fitness,        -- 适应度
    is_submitted,   -- 是否已提交
    is_checked      -- 是否已检查
)
```

## 工作流程

### 1. 完整流程 (is_simulate = True)
```
1. 初始化系统 → 2. 生成Alpha → 3. 数据库过滤 → 4. 批量仿真 
→ 5. 二阶提升 → 6. 三阶提升 → 7. 提交检查
```

### 2. 仅提升流程 (is_simulate = False)
```
1. 初始化系统 → 2. 二阶提升 → 3. 三阶提升 → 4. 提交检查
```

## Alpha提升机制

### 一阶Alpha (基础因子)
- 基于数据字段直接生成
- 使用时间序列操作 (ts_mean, ts_std_dev, rank等)

### 二阶Alpha (组合因子)
- 在一阶Alpha基础上添加group操作
- 支持: group_neutralize, group_rank, group_zscore等

### 三阶Alpha (事件驱动因子)
- 在二阶Alpha基础上添加trade_when条件
- 支持各种事件触发机制

## 配置管理

主配置文件: [config.ini](mdc:config.ini)

**关键配置段:**
- `[dates]` - 日期配置
- `[setting]` - 回测参数
- `[others]` - 流程控制和筛选参数
- `[database]` - 数据库配置

## 错误处理和重试机制

- **网络重试**: 自动重试失败的API请求
- **任务恢复**: 仿真任务失败时的恢复机制
- **数据库事务**: 确保数据一致性
- **日志记录**: 详细的错误日志和调试信息

## 性能优化

- **并发控制**: 最大8个并发仿真任务
- **批量处理**: 默认1000个Alpha为一批
- **数据库索引**: 基于alpha_hash的快速查询
- **内存管理**: 分批处理大量Alpha避免内存溢出
