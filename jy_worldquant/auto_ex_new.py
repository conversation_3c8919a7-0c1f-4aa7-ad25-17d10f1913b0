# !/usr/bin/env python3
# -*- coding: utf-8 -*-

from machine_lib_concurrent import *
from datetime import datetime, timedelta
import configparser
import logging
import os
import threading
import sqlite3
import hashlib
from db_utils import (setup_database, is_alpha_in_db, save_alpha_to_db, 
                     mark_alpha_as_submitted, filter_new_alpha_list)
from common_config import CommonConfig

# 配置日志记录
def setup_logging():
    # 创建logs目录（如果不存在）
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 设置日志文件名（使用当前时间）
    log_file = os.path.join(log_dir, f"alpha_machine_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log")
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    
    logging.info("日志系统初始化完成")
    return log_file

# 读取配置文件
def load_config():
    config = configparser.ConfigParser()
    config.read('config.ini', encoding='utf-8')
    return config

# 1 获取当前执行时间（根据配置决定是否使用动态日期）
def get_current_time(config):
    logging.info("1 获取时间")
    if config.getboolean('dates', 'use_dynamic_dates'):
        current_date = datetime.today()
        end_date = current_date.strftime("%m-%d")
        start_date = (current_date - timedelta(days=1)).strftime("%m-%d")
    else:
        start_date = config.get('dates', 'start_date')
        end_date = config.get('dates', 'end_date')

    logging.info(f"start:{start_date}, end:{end_date}")
    return start_date, end_date

# 初始化记录文件
def log_dataset_id(dataset_id):
    with open('dataset_id_history.txt', 'a') as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}] Dataset ID used: {dataset_id}\n")

# 初始化记录文件
def log_parameters(dataset_id, region, universe, delay):
    with open('dataset_id_history.txt', 'a') as f:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        f.write(f"[{timestamp}] Dataset ID: {dataset_id}, Region: {region}, Universe: {universe}, Delay: {delay}\n")

# 3 登陆
def start_login():
    logging.info("3 登陆")
    s = login()
    return s


def create_alpha(login_response, common_config):
    """创建alpha表达式列表
    
    Args:
        login_response: 登录返回的response
        common_config: CommonConfig对象，包含所有配置参数
        
    Returns:
        alpha表达式列表
    """
    df = get_datafields(login_response, 
                      dataset_id=common_config.dataset_id, 
                      region=common_config.region, 
                      universe=common_config.universe, 
                      delay=common_config.delay, 
                      coverage=common_config.coverage)
    log_parameters(common_config.dataset_id, common_config.region, common_config.universe, common_config.delay)  # 记录参数到文件
    # logging.info(df)

    # 4 数据字段预处理
    logging.info("4 数据字段预处理")
    pc_fields = process_datafields(df)

    # 5 Alpha Factory
    logging.info("5 Alpha Factory")
    first_order = first_order_factory(pc_fields, ts_ops)
    logging.info("first_order: %s" % first_order[:1])

    # 6 回测前载入
    logging.info("6 回测前载入")
    # 赋予alpha表达式一个初始decay
    init_decay = common_config.decay
    fo_alpha_list = []

    for alpha in first_order:
        fo_alpha_list.append((alpha, init_decay))

    # 随机采样快速评估一个数据集的潜力
    # random.shuffle(fo_alpha_list)

    logging.info("数量: %s" % len(fo_alpha_list))
    logging.info(fo_alpha_list[:1])

    return fo_alpha_list


def create_alpha_by_template(login_response, common_config):
    """创建alpha表达式列表（基于模板）
    
    Args:
        login_response: 登录返回的response
        common_config: CommonConfig对象，包含所有配置参数
        
    Returns:
        alpha表达式列表
    """
    df = get_datafields(
        login_response, 
        dataset_id=common_config.dataset_id, 
        region=common_config.region, 
        universe=common_config.universe, 
        delay=common_config.delay, 
        coverage=common_config.coverage
    )
    log_parameters(common_config.dataset_id, common_config.region, common_config.universe, common_config.delay)  # 记录参数到文件

    datafields = []
    datafields += df[df['type'] == "MATRIX"]["id"].tolist()
    datafields += get_vec_fields(df[df['type'] == "VECTOR"]["id"].tolist())

    alpha_list = []

    alpha_template = """
        mean_EP = ts_mean({field}, 252);
        std_EP = ts_std_dev({field}, 252);
        upper_band = mean_EP + 2 * std_EP;
        lower_band = mean_EP - 2 * std_EP;
        signal = ({field} > upper_band) ? -1 : ({field} < lower_band) ? 1 : 0;
        alpha = group_neutralize(signal, industry);
        """

    # 用每个field生成一个alpha表达式
    for datafield in datafields:
        expr = alpha_template.format(field=datafield)
        alpha_list.append((expr, common_config.decay))

    return alpha_list


def create_alpha_new(login_response, common_config):
    # 4 数据字段预处理
    logging.info("4 数据字段预处理")

    datafields = []
    try:
        # 从文件中逐行读取数据字段
        with open(f'data/others/{common_config.fields_file}', 'r') as f:
            for line in f:
                # 去除行尾的空白字符并添加到列表中
                field = line.strip()
                if field:  # 确保不添加空行
                    datafields.append(field)
        logging.info(f"从{common_config.fields_file}加载了 {len(datafields)} 个数据字段")
    except Exception as e:
        logging.error(f"读取数据字段文件时出错: {e}")
        return []

    pc_fields = ["winsorize(ts_backfill(%s, 120), std=4)"%field for field in datafields]
    # pc_fields = ["%s"%field for field in datafields]

    # 5 Alpha Factory
    logging.info("5 Alpha Factory")
    first_order = first_order_factory(pc_fields, ts_ops)
    logging.info(first_order[:1])

    # 6 回测前载入
    logging.info("6 回测前载入")
    # 赋予alpha表达式一个初始decay
    init_decay = common_config.decay
    fo_alpha_list = []

    for alpha in first_order:
        fo_alpha_list.append((alpha, init_decay))

    # 随机采样快速评估一个数据集的潜力
    # random.shuffle(fo_alpha_list)

    logging.info("数量: %s" % len(fo_alpha_list))
    logging.info(fo_alpha_list[:1])

    return fo_alpha_list

def create_alpha_by_factors(login_response, common_config):
    # 4 数据字段预处理
    logging.info("4 数据字段预处理")

    alpha_list = []
    try:
        # 从文件中逐行读取数据字段
        with open(f'data/others/{common_config.fields_file}', 'r') as f:
            for line in f:
                # 去除行尾的空白字符并添加到列表中
                field = line.strip()
                if field:  # 确保不添加空行
                    alpha_list.append(field)
        logging.info(f"从{common_config.fields_file}加载了 {len(alpha_list)} 个数据字段")
    except Exception as e:
        logging.error(f"读取数据字段文件时出错: {e}")
        return []

    logging.info(alpha_list[:1])

    # 6 回测前载入
    logging.info("6 回测前载入")
    # 赋予alpha表达式一个初始decay
    init_decay = common_config.decay
    fo_alpha_list = []

    for alpha in alpha_list:
        fo_alpha_list.append((alpha, init_decay))

    # 随机采样快速评估一个数据集的潜力
    # random.shuffle(fo_alpha_list)

    logging.info("数量: %s" % len(fo_alpha_list))
    logging.info(fo_alpha_list[:1])

    return fo_alpha_list

# 7 回测
def simulate(fo_pools, common_config):
    """回测alpha
    
    Args:
        fo_pools: alpha池
        common_config: CommonConfig对象，包含回测参数
    """
    logging.info("7 回测")

    multi_simulate(fo_pools, common_config.get_setting_params())

# 7.1 批量回测（新增）
def batch_simulate(alpha_list, common_config, db_conn=None):
    """每次回测指定数量的alpha，并将结果保存到数据库
    
    Args:
        alpha_list: alpha列表
        common_config: CommonConfig对象，包含回测参数
        db_conn: 数据库连接，可选
        
    Yields:
        回测进度百分比
    """
    logging.info(f"7.1 批量回测 {common_config.batch_size}个alpha")
    
    # 过滤已回测的alpha
    if db_conn:
        # 使用数据库参数过滤
        alpha_list = filter_new_alpha_list(
            db_conn, 
            alpha_list, 
            common_config
        )
    
    for i in range(0, len(alpha_list), common_config.batch_size):
        batch = alpha_list[i:i+common_config.batch_size]
        if not batch:  # 如果没有新alpha需要回测，则跳过
            continue
            
        logging.info(f"进行第{i//common_config.batch_size + 1}批回测，数量: {len(batch)}")
        
        pools = load_task_pool(batch, 10, 8)

        multi_simulate(pools, common_config)
        
        # 将本批次的alpha保存到数据库
        if db_conn:
            for alpha_item in batch:
                alpha_expr = str(alpha_item[0])
                decay = alpha_item[1]
                # 保存到数据库
                save_alpha_to_db(
                    db_conn, 
                    alpha_expr, 
                    decay,
                    common_config
                )
        
        # 返回当前批次完成百分比
        progress = min(100, int((i + common_config.batch_size) / len(alpha_list) * 100))
        logging.info(f"回测进度: {progress}%")
        
        yield progress

# 8 筛选Alpha
def filter_alpha(start_date, end_date, common_config):
    """筛选Alpha
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        common_config: CommonConfig对象，包含筛选参数
        
    Returns:
        筛选后的alpha列表
    """
    logging.info("8 筛选Alpha")
    # 从common_config中获取筛选参数
    alpha_num = common_config.alpha_num_filter
    region = common_config.region
    second_promote_sharp = common_config.second_promote_sharp
    second_promote_fitness = common_config.second_promote_fitness
    prefix = common_config.prefix
    
    ## get promising alphas to improve in the next order
    fo_tracker = get_alphas(start_date, end_date, region, alpha_num, "track", 
                          second_promote_sharp, second_promote_fitness, margin_th=0)
    logging.info("fo_tracker len: %s" %len(fo_tracker))
    logging.info(fo_tracker[:1])
    
    filter_new_alpha_list = []
    if common_config.is_filter_pnl:
        filter_new_alpha_list = filter_alpha_by_pnl(fo_tracker)
        logging.info("filter_new_alpha_list len: %s" %len(filter_new_alpha_list))
    else:
        filter_new_alpha_list = fo_tracker


    # 过滤重复的alpha
    logging.info("开始过滤重复的alpha")
    # 使用字典来存储唯一的alpha，键为alpha表达式的字符串表示
    unique_dict = {}
    
    for item in filter_new_alpha_list:
        # 获取alpha表达式
        expr_str = str(item[1])  # 将表达式转换为字符串
        
        # 如果表达式不在字典中，则添加
        if expr_str not in unique_dict:
            unique_dict[expr_str] = item
    
    # 将唯一的alpha添加到新列表中
    unique_fo_tracker = list(unique_dict.values())
    logging.info(f"unique_fo_tracker: {unique_fo_tracker[:2]}")
    
    duplicate_count = len(fo_tracker) - len(unique_fo_tracker)
    logging.info(f"共过滤掉 {duplicate_count} 个重复的alpha")
    logging.info(f"去重后的数量: {len(unique_fo_tracker)}")


    # Prune剪枝
    fo_layer = prune(unique_fo_tracker, prefix, 5)
    # 剪枝后数量
    logging.info("筛选后的数量：%s" %len(fo_layer))
    logging.info(fo_layer[:1])

    if len(fo_layer) > 10000:
        logging.info(f"alpha数量超过10000，保留前10000个")
        fo_layer = fo_layer[:10000]
    
    return fo_layer


# 9 二阶提升
def second_level_promote(fo_layer, common_config, db_conn=None):
    """二阶提升
    
    Args:
        fo_layer: 一阶alpha层
        common_config: CommonConfig对象，包含回测参数
        db_conn: 数据库连接，可选
    """
    logging.info("9 二阶提升")
    so_alpha_list = []
    group_ops = ["group_neutralize", "group_rank", "group_zscore", "group_mean", 
                 "group_extra", "group_backfill", "group_scale", "group_cartesian_product"]

    filter_fo_layer = [
        (expr, decay)
        for expr, decay in fo_layer
        if not any(operation in expr for operation in group_ops)
    ]

    for expr, decay in filter_fo_layer:   
        for alpha in get_group_second_order_factory([expr], group_ops, common_config.region):
            so_alpha_list.append((alpha, decay))

    # 检查每个生成的二阶alpha是否已经回测过
    if db_conn:
        so_alpha_list = filter_new_alpha_list(
            db_conn, 
            so_alpha_list,
            common_config,
        )         

    logging.info("二阶提升的数量：%s" %len(so_alpha_list))
    if so_alpha_list:
        logging.info(so_alpha_list[:1])

    if len(so_alpha_list) > 10000:
        so_alpha_list = so_alpha_list[:10000]

    if so_alpha_list:  # 只有当有alpha需要回测时才执行
        so_pools = load_task_pool(so_alpha_list, 10, 10)
        multi_simulate(so_pools, common_config)
        
        # 将本批次的alpha保存到数据库
        if db_conn:
            for alpha_item in so_alpha_list:
                save_alpha_to_db(
                    db_conn, 
                    alpha_item[0], 
                    alpha_item[1], 
                    common_config
                )
    else:
        logging.info("没有新的二阶alpha需要回测")


# 10 三阶提升
def third_level_promote(start_date, end_date, common_config, db_conn=None):
    """三阶提升
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        common_config: CommonConfig对象，包含回测参数
        db_conn: 数据库连接，可选
    """
    logging.info("10 三阶提升")
    ## get promising alphas from second order to improve in the third order
    so_tracker = get_alphas(start_date, end_date, common_config.region, common_config.third_promote_num, "track", 
                          common_config.third_promote_sharp, common_config.third_promote_fitness, margin_th=0)

    filter_so_tracker = [
        item
        for item in so_tracker
        if "trade_when" not in str(item[1])  # 直接检查字符串是否包含特定关键词
    ]
    
    # 过滤重复的alpha
    logging.info("开始过滤三阶提升中重复的alpha")
    # 使用字典来存储唯一的alpha，键为alpha表达式的字符串表示
    unique_dict = {}
    
    for item in filter_so_tracker:
        # 获取alpha表达式
        expr_str = str(item[1])  # 将表达式转换为字符串
        
        # 如果表达式不在字典中，则添加
        if expr_str not in unique_dict:
            unique_dict[expr_str] = item
    
    # 将唯一的alpha添加到新列表中
    unique_filter_so_tracker = list(unique_dict.values())
    
    duplicate_count = len(filter_so_tracker) - len(unique_filter_so_tracker)
    logging.info(f"三阶提升共过滤掉 {duplicate_count} 个重复的alpha")
    logging.info(f"三阶提升去重后的数量: {len(unique_filter_so_tracker)}")

    so_layer = prune(unique_filter_so_tracker, common_config.prefix, 5)
    th_alpha_list = []

    for item in so_layer:
        expr, decay = item[0], item[1]
            
        for alpha in trade_when_factory("trade_when", expr, common_config.region, events=common_config.events):
            th_alpha_list.append((alpha, decay))

    logging.info("th_alpha_list size: %s" % len(th_alpha_list))
    logging.info("th_alpha_list: %s" % th_alpha_list[:1])

    # 检查每个生成的三阶alpha是否已经回测过
    if db_conn:
        th_alpha_list = filter_new_alpha_list(
            db_conn, 
            th_alpha_list, 
            common_config
        )

    logging.info("三阶提升的数量: %s" % len(th_alpha_list))
    if th_alpha_list:
        logging.info(th_alpha_list[:1])

    if len(th_alpha_list) > 10000:
        th_alpha_list = th_alpha_list[:10000]

    if th_alpha_list:  # 只有当有alpha需要回测时才执行
        # Simulate third order
        th_pools = load_task_pool(th_alpha_list, 10, 10)
        # 使用**操作符直接传递参数字典
        multi_simulate(th_pools, common_config)
        
        # 将本批次的alpha保存到数据库
        if db_conn:
            for alpha_item in th_alpha_list:
                alpha_expr = str(alpha_item[0])
                decay = alpha_item[1]
                save_alpha_to_db(
                    db_conn, 
                    alpha_expr, 
                    decay,
                    common_config
                )
    else:
        logging.info("没有新的三阶alpha需要回测")


# 11 获取可提交的Alpha
def get_submit_alpha(start_date, end_date, common_config, config_parser=None):
    """获取可提交的Alpha
    Args:
        start_date: 开始日期
        end_date: 结束日期
        common_config: CommonConfig对象，包含提交参数
        config_parser: 配置对象，用于新建数据库连接
    """
    logging.info("11 获取可提交的Alpha")
    # 线程内新建数据库连接
    db_conn = setup_database(config_parser) if config_parser else None
    try:
        # 使用common_config参数
        th_tracker = get_alphas(
            start_date, 
            end_date, 
            common_config.region, 
            common_config.alpha_num_submit, 
            "submit", 
            sharpe_th=common_config.submit_sharp_th, 
            fitness_th=common_config.submit_fitness_th, 
            margin_th=common_config.submit_margin_th
        )

        ## 将get的alpha的id取出至stone_bag，用api check submission
        stone_bag = []
        for alpha in th_tracker:
            stone_bag.append(alpha[0])
            # 如果有数据库连接，更新sharpe和fitness
            if db_conn:
                alpha_expr = str(alpha[1])  # alpha表达式
                decay = alpha[-1]
                # 更新数据库
                save_alpha_to_db(
                    db_conn, 
                    alpha_expr, 
                    decay,
                    common_config
                )
        
        logging.info(f"count stone_bag: {len(stone_bag)}")

        gold_bag = []
        check_submission(stone_bag, gold_bag, 0)
        
        # 如果有数据库连接，标记已提交的alpha
        if db_conn and gold_bag:
            for alpha_id in gold_bag:
                alpha_expr = str(alpha_id)  # 需要根据实际情况修改
                mark_alpha_as_submitted(db_conn, alpha_expr)

        # 打印可提交的alpha信息并按sharpe排序，在网页上找到alpha手动提交
        logging.info("12 打印可提交的Alpha")
        view_alphas(gold_bag)
    finally:
        if db_conn:
            db_conn.close()
            logging.info("异步线程数据库连接已关闭")

# 11.1 异步执行get_submit_alpha（新增）
def async_get_submit_alpha(start_date, end_date, common_config, config_parser=None):
    """在新线程中异步执行get_submit_alpha
    Args:
        start_date: 开始日期
        end_date: 结束日期
        common_config: CommonConfig对象，包含提交参数
        config_parser: 配置对象，用于新建数据库连接
    Returns:
        线程对象
    """
    thread = threading.Thread(
        target=get_submit_alpha,
        args=(start_date, end_date, common_config, config_parser)
    )
    thread.daemon = True  # 设置为守护线程，主程序退出时线程会自动结束
    thread.start()
    logging.info(f"已启动异步提交alpha评估线程 {thread.name}")
    return thread


if __name__ == '__main__':
    # 设置日志
    log_file = setup_logging()
    logging.info(f"日志文件路径: {log_file}")
    
    # 加载配置
    config_parser = load_config()
    # 获取日期参数
    start_date, end_date = get_current_time(config_parser)
    
    # 使用CommonConfig类从配置文件创建配置对象
    common_config = CommonConfig.from_config(config_parser)
    # 记录所有参数
    common_config.log_parameters()
    
    # 设置数据库连接
    db_conn = setup_database(config_parser)
    if not db_conn:
        logging.warning("数据库连接失败，将不会保存回测记录")

    # 登陆
    login_response = start_login()

    fo_alpha_list = []
    
    # 修改后的循环回测流程
    if common_config.is_simulate:
        # 构建alpha
        fo_alpha_list = create_alpha_new(login_response, common_config)
        logging.info("fo_alpha_list: %s" % fo_alpha_list[:1])
        
        # 使用配置中的批次大小
        logging.info(f"使用配置的批次大小: {common_config.batch_size}")
        
        # 存储所有已启动的提交线程
        submit_threads = []
        
        # 批次回测并在每批次后进行二阶三阶提升
        for progress in batch_simulate(fo_alpha_list, common_config, db_conn):
            logging.info(f"完成批次回测，当前进度: {progress}%")
            
            if common_config.is_second_promote:
                logging.info("当前批次回测后进行二阶提升")
                fo_layer = filter_alpha(start_date, end_date, common_config)
                if len(fo_layer) > 0:
                    second_level_promote(fo_layer, common_config, db_conn)
                else:
                    logging.warning("没有可提升的alpha进行二阶提升")
            
            if common_config.is_third_promote and common_config.is_second_promote:
                logging.info("当前批次二阶提升后进行三阶提升")
                third_level_promote(start_date, end_date, common_config, db_conn)
                
                # 完成三阶提升后异步执行get_submit_alpha
                submit_thread = async_get_submit_alpha(start_date, end_date, common_config, config_parser)
                submit_threads.append(submit_thread)
    else:
        logging.info("跳过回测流程")
        
        # 单独执行二阶提升
        if common_config.is_second_promote:
            fo_layer = filter_alpha(start_date, end_date, common_config)
            if len(fo_layer) > 0:
                second_level_promote(fo_layer, common_config, db_conn)
            else:
                logging.warning("没有可提升的alpha进行二阶提升")
        
        # 单独执行三阶提升
        if common_config.is_third_promote:
            third_level_promote(start_date, end_date, common_config, db_conn)
    
    # 最后在主线程中再执行一次get_submit_alpha，汇总所有结果
    logging.info("所有批次处理完成，执行最终提交alpha评估")
    get_submit_alpha(start_date, end_date, common_config, config_parser)
    
    # 关闭数据库连接
    if db_conn:
        db_conn.close()
        logging.info("数据库连接已关闭")











