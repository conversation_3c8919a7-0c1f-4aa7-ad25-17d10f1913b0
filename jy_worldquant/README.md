# WorldQuant Alpha机器系统

## 项目简介

WorldQuant Alpha机器系统是一个专为WorldQuant Brain平台设计的自动化Alpha因子生成、回测和提交工具。该系统采用模块化架构，支持多阶Alpha提升、智能数据库过滤、批量仿真和自动提交检查，大幅提升量化研究效率。

## 版本信息

- **当前版本**: v2.0.0
- **最新更新**: 2024年12月
- **兼容性**: Python 3.8+ | WorldQuant Brain Platform

## 系统特性

- **🚀 模块化架构**：采用服务化设计，易于维护和扩展
- **🔄 多阶Alpha提升**：支持一阶、二阶、三阶Alpha自动生成和优化
- **💾 智能数据库过滤**：自动过滤已回测的Alpha，避免重复计算
- **⚡ 批量并发仿真**：支持大规模Alpha批量回测，提高效率
- **🎯 自动提交检查**：智能筛选符合条件的Alpha并检查提交状态
- **📊 实时进度监控**：提供详细的执行进度和状态反馈
- **🛡️ 异常处理机制**：完善的错误处理和重试机制

## 🆕 新功能亮点

- **🧠 知识图谱系统**：集成大语言模型驱动的Alpha知识图谱，提供智能因子推荐和分析
- **🔬 稳健性分析**：全面的Alpha稳健性测试和评估框架，支持多维度风险分析
- **🛠️ 实用工具集**：包含时间处理、数据分析、系统管理等完整工具集
- **📈 数据集分析**：支持多数据集特征分析和对比，优化因子选择策略
- **🎯 单Alpha检查**：支持单个Alpha的详细分析和验证
- **📋 天才排行榜**：集成WorldQuant天才排行榜分析功能

## 系统架构

系统由以下核心组件构成：

```
┌─────────────────────────────────────────────────────────────┐
│                    Alpha机器主程序                           │
│                 (AlphaMachine)                              │
└─────────────────┬───────────────────────────────────────────┘
                  │
    ┌─────────────┼─────────────┬─────────────┐
    │             │             │             │
┌───▼───┐    ┌───▼───┐    ┌───▼───┐    ┌───▼───┐
│Alpha  │    │仿真   │    │提交   │    │知识   │
│服务   │    │服务   │    │服务   │    │图谱   │
└───┬───┘    └───┬───┘    └───┬───┘    └───┬───┘
    │            │            │            │
┌───▼───┐    ┌───▼───┐    ┌───▼───┐    ┌───▼───┐
│Alpha  │    │任务   │    │相关性 │    │LLM    │
│工厂   │    │管理器 │    │检查器 │    │引擎   │
└───────┘    └───────┘    └───────┘    └───────┘
    │            │            │            │
┌───▼─────────────▼─────────────▼─────────────▼───┐
│               通用服务层                         │
│ (认证服务、数据库服务、配置管理、工具集)         │
└─────────────────────────────────────────────────┘
```

### 核心服务

- **AlphaService**：Alpha因子生成、获取、过滤和管理
- **SimulationService**：Alpha回测仿真和任务管理
- **SubmissionService**：Alpha提交检查和相关性分析
- **DatabaseService**：Alpha历史记录存储和查询
- **AuthService**：WorldQuant Brain平台认证
- **KnowledgeGraphService**：基于LLM的Alpha知识图谱系统

## 项目结构

### 主程序入口

- **[alpha_machine_refactored.py](alpha_machine_refactored.py)** - 重构后的Alpha机器主程序，采用模块化架构

### 核心服务模块 (core/)

- **[core/alpha_service.py](core/alpha_service.py)** - Alpha因子生成、获取、过滤和管理服务
- **[core/simulation_service.py](core/simulation_service.py)** - Alpha回测仿真和任务管理服务
- **[core/submission_service.py](core/submission_service.py)** - Alpha提交检查和相关性分析服务
- **[core/base_service.py](core/base_service.py)** - 服务基类，提供通用功能
- **[core/alpha_factory.py](core/alpha_factory.py)** - Alpha表达式生成工厂
- **[core/__init__.py](core/__init__.py)** - 核心服务模块初始化

### 知识图谱系统 (llm/)

- **大语言模型集成** - 支持多种LLM后端的Alpha智能分析
- **知识图谱构建** - 自动构建Alpha因子关系图谱
- **智能推荐系统** - 基于历史数据的因子推荐

### 实用工具集 (utils/)

- **[utils/time_utils.py](utils/time_utils.py)** - 时间处理和占位符替换工具
- **[utils/__init__.py](utils/__init__.py)** - 工具模块初始化
- **[utils/README.md](utils/README.md)** - 工具模块详细文档

### 通用组件

- **[common_config.py](common_config.py)** - 通用配置管理
- **[common_auth.py](common_auth.py)** - WorldQuant Brain平台认证
- **[db_utils.py](db_utils.py)** - SQLite数据库工具和Alpha历史记录管理

### 分析工具

- **[check_alpha_robust.py](check_alpha_robust.py)** - Alpha稳健性分析工具
- **[check_single_alpha.py](check_single_alpha.py)** - 单个Alpha详细检查工具
- **[analyze_datasets.py](analyze_datasets.py)** - 数据集特征分析工具
- **[analyze_genius_rank.py](analyze_genius_rank.py)** - 天才排行榜分析工具
- **[view_submittable_alpha.py](view_submittable_alpha.py)** - 可提交Alpha查看工具
- **[generate_alpha_factors.py](generate_alpha_factors.py)** - Alpha因子生成工具

### 传统模块（向后兼容）

- **[machine_lib.py](machine_lib.py)** - 传统机器学习库
- **[machine_lib_concurrent.py](machine_lib_concurrent.py)** - 并发版本的机器学习库
- **[auto_ex_new.py](auto_ex_new.py)** - 传统自动执行工具

### 配置文件

- **[config.ini](config.ini)** - 主配置文件，包含所有系统参数
- **[config_check.ini](config_check.ini)** - 配置检查文件
- **[SETUP_CREDENTIALS.md](SETUP_CREDENTIALS.md)** - 凭据设置指南

### 启动脚本

- **[start.sh](start.sh)** - 系统启动脚本
- **[stop.sh](stop.sh)** - 系统停止脚本
- **[tailf.sh](tailf.sh)** - 日志实时查看工具
- **[start_check.sh](start_check.sh)** - 检查模式启动脚本
- **[stop_check.sh](stop_check.sh)** - 检查模式停止脚本

### 示例和教程

- **[Alpha Machine.ipynb](Alpha Machine.ipynb)** - Jupyter笔记本教程
- **[README.md](README.md)** - 完整的系统文档和使用指南

### 重要目录

- **data/** - 数据文件和SQLite数据库
- **logs/** - 系统日志目录
- **docs/** - 文档和教程
- **robustness_results/** - 稳健性分析结果

## 主要功能

### 1. Alpha生成与管理

- **数据字段Alpha生成**：基于数据集字段自动生成一阶Alpha
- **文件Alpha生成**：从指定文件读取数据字段生成Alpha
- **模板Alpha生成**：基于预定义模板生成Alpha
- **Alpha去重过滤**：自动识别和过滤重复的Alpha表达式
- **数据库过滤**：避免重复回测已存在的Alpha

### 2. 多阶Alpha提升

#### 一阶Alpha（基础因子）
```python
# 示例：基于数据字段的一阶Alpha
ts_mean(close, 20)
ts_std_dev(volume, 10)
rank(returns)
```

#### 二阶Alpha（组合因子）
```python
# 示例：添加group操作的二阶Alpha
group_neutralize(ts_mean(close, 20), industry)
group_rank(ts_std_dev(volume, 10), sector)
group_zscore(rank(returns), market)
```

#### 三阶Alpha（事件驱动因子）
```python
# 示例：添加trade_when条件的三阶Alpha
trade_when(group_neutralize(ts_mean(close, 20), industry), open_events)
trade_when(group_rank(ts_std_dev(volume, 10), sector), earnings_events)
```

### 3. 智能仿真系统

- **批量仿真**：支持大规模Alpha批量回测
- **并发控制**：智能任务调度和并发管理
- **进度监控**：实时显示仿真进度和状态
- **异常处理**：自动重试和错误恢复机制
- **资源优化**：动态调整并发数和检查间隔

### 4. 稳健性分析系统

- **多维度评估**：从统计稳健性、风险调整收益等角度评估Alpha
- **时间序列分析**：评估Alpha在不同时间窗口的表现
- **相关性分析**：检测Alpha之间的相关性和冗余度
- **自动报告生成**：生成详细的稳健性分析报告

### 5. 数据库管理

- **Alpha历史记录**：存储所有回测过的Alpha信息
- **智能过滤**：基于Alpha哈希值快速过滤重复项
- **统计分析**：提供数据库统计信息和分析
- **状态管理**：跟踪Alpha的提交和检查状态

## 安装与配置

### 系统要求

- **Python**: 3.8+ (推荐 Python 3.9+)
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最低 8GB RAM (推荐 16GB+)
- **存储**: 最低 10GB 可用空间
- **网络**: 稳定的互联网连接
- **WorldQuant Brain账户**: 有效的平台访问权限

### 快速开始

#### 1. 环境准备

```bash
# 克隆项目仓库
git clone <repository-url>
cd jy_worldquant

# 创建虚拟环境 (推荐)
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate  # Windows
```

#### 2. 依赖安装

```bash
# 安装核心依赖
pip install -r requirements.txt

# 或使用自动安装脚本
python install_deps.py
```

#### 3. 凭据配置

```bash
# 复制凭据模板
cp brain_credentials.txt.example brain_credentials.txt

# 编辑凭据文件，填入你的WorldQuant Brain账户信息
# 详见 SETUP_CREDENTIALS.md
```

#### 4. 系统配置

编辑 `config.ini` 文件：

```ini
[dates]
use_dynamic_dates = False
start_date = 12-01
end_date = 12-07

[setting]
dataset_id = model26
prefix = winsorize
region = USA
universe = TOP3000
delay = 0
decay = 6
instrument_type = EQUITY
neutralization = INDUSTRY
truncation = 0.08
pasteurization = ON
unit_handling = VERIFY
nan_handling = ON
language = FASTEXPR
visualization = false
max_trade = OFF

[others]
events = open_events
batch_size = 1000
alpha_num_filter = 2000
third_promote_num = 200
alpha_num_submit = 500
second_promote_sharp = 1.2
second_promote_fitness = 0.7
third_promote_sharp = 1.3
third_promote_fitness = 0.8
submit_sharp_th = 1.58
submit_fitness_th = 1
submit_margin_th = 1
is_simulate = True
is_second_promote = True
is_third_promote = True
fields_file = USA_1_TOP3000_MODEL.txt

[database]
db_file = data/worldquant.db
enabled = True
store_all_alphas = True
timeout = 10
```

#### 5. 首次运行

```bash
# 测试配置
python test.py

# 运行完整流程
python alpha_machine_refactored.py

# 或使用启动脚本
./start.sh
```

## 使用指南

### 基本使用

#### 完整流程执行

```bash
# 运行完整的Alpha生成、仿真、提升流程
python alpha_machine_refactored.py
```

#### 分阶段执行

```bash
# 仅运行Alpha生成和仿真
python alpha_machine_refactored.py --simulate-only

# 仅运行提升流程（跳过生成）
python alpha_machine_refactored.py --promote-only

# 仅运行提交检查
python alpha_machine_refactored.py --submit-only
```

#### 使用启动脚本

```bash
# 后台运行
./start.sh

# 查看实时日志
./tailf.sh

# 停止系统
./stop.sh
```

### 专项工具使用

#### 稳健性分析

```bash
# 分析特定Alpha的稳健性
python check_alpha_robust.py --alpha "ts_mean(close, 20)" --days 30

# 批量稳健性分析
python check_alpha_robust.py --file alpha_list.txt --output robustness_results/
```

#### 单Alpha检查

```bash
# 检查单个Alpha的详细信息
python check_single_alpha.py --alpha "rank(returns)" --dataset model26
```

#### 数据集分析

```bash
# 分析数据集特征
python analyze_datasets.py --dataset model26 --region USA

# 对比多个数据集
python analyze_datasets.py --compare model26,model30 --region USA
```

#### 天才排行榜分析

```bash
# 获取最新排行榜数据
python analyze_genius_rank.py --top 100

# 分析特定用户表现
python analyze_genius_rank.py --user your_username
```

### 编程接口

#### 基本使用

```python
from alpha_machine_refactored import AlphaMachine

# 创建Alpha机器实例
alpha_machine = AlphaMachine('config.ini')

# 初始化系统
if alpha_machine.initialize():
    # 运行完整流程
    success = alpha_machine.run()
    print(f"执行结果: {'成功' if success else '失败'}")
```

#### 服务使用示例

##### Alpha服务

```python
from core import AlphaService
from common_config import CommonConfig
from common_auth import BrainAuth

# 初始化服务
config = CommonConfig.from_config(config_parser)
auth_service = BrainAuth()
alpha_service = AlphaService(config, auth_service, db_service)

# 生成Alpha
alpha_list = alpha_service.create_alpha_from_datafields()

# 过滤新Alpha
filtered_alphas = alpha_service.filter_new_alphas(alpha_list)

# 获取Alpha列表
alphas = alpha_service.get_alphas(
    start_date="12-01",
    end_date="12-07",
    alpha_num=100,
    usage="track",
    sharpe_th=1.2,
    fitness_th=0.7
)
```

##### 仿真服务

```python
from core import SimulationService

# 初始化仿真服务
simulation_service = SimulationService(config, auth_service, db_service)

# 批量仿真
for progress in simulation_service.batch_simulate(alpha_list):
    print(f"仿真进度: {progress}%")

# 单次仿真
success = simulation_service.simulate_alphas(alpha_list)
```

##### 提交服务

```python
from core import SubmissionService

# 初始化提交服务
submission_service = SubmissionService(config, auth_service, db_service)

# 处理提交工作流
eligible_alphas = submission_service.process_submission_workflow(
    start_date="12-01",
    end_date="12-07"
)

# 检查Alpha相关性
correlation_matrix = submission_service.check_alpha_correlation(alpha_ids)
```

## 配置说明

### 日期配置
- `use_dynamic_dates`: 是否使用动态日期（当天和前一天）
- `start_date/end_date`: 固定日期范围（MM-DD格式）

### 回测配置
- `dataset_id`: 数据集ID
- `region`: 地区（USA, EUR, CHN等）
- `universe`: 股票池（TOP3000, TOP1000等）
- `delay`: 延迟天数
- `decay`: 衰减参数
- `neutralization`: 中性化方式（INDUSTRY, SECTOR等）

### 流程控制
- `is_simulate`: 是否执行Alpha生成和仿真
- `is_second_promote`: 是否执行二阶提升
- `is_third_promote`: 是否执行三阶提升
- `batch_size`: 批量仿真大小

### 筛选参数
- `alpha_num_filter`: 一阶Alpha筛选数量
- `third_promote_num`: 三阶提升Alpha数量
- `second_promote_sharp/fitness`: 二阶提升阈值
- `third_promote_sharp/fitness`: 三阶提升阈值
- `submit_sharp_th/fitness_th/margin_th`: 提交阈值

## 数据库说明

系统使用SQLite数据库存储Alpha历史记录，具有以下特点：

- **轻量级**：无需额外数据库服务器
- **高效**：基于哈希值的快速查询
- **可靠**：事务支持和错误恢复
- **统计**：提供丰富的统计信息

### 数据库表结构

```sql
CREATE TABLE alpha_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    alpha_hash TEXT NOT NULL,
    alpha TEXT NOT NULL,
    dataset_id TEXT,
    region TEXT,
    universe TEXT,
    delay INTEGER,
    sharpe REAL,
    fitness REAL,
    instrument_type TEXT,
    decay INTEGER,
    unit_handling TEXT,
    nan_handling TEXT,
    language TEXT,
    visualization TEXT,
    max_trade INTEGER,
    last_test_time TIMESTAMP,
    neutralization TEXT,
    is_submitted INTEGER DEFAULT 0,
    is_checked INTEGER DEFAULT 0
);
```

### 数据库操作

```python
from db_utils import setup_database

# 设置数据库
db_service = setup_database(config_parser)

# 检查Alpha是否存在
exists = db_service.is_alpha_in_db(alpha_expr, decay, config)

# 保存Alpha
success = db_service.save_alpha_to_db(alpha_expr, decay, config)

# 获取统计信息
stats = db_service.get_statistics()
```

## 日志系统

系统提供详细的日志记录，包括：

- **执行进度**：实时显示各阶段进度
- **错误信息**：详细的错误堆栈和处理建议
- **性能统计**：执行时间和资源使用情况
- **数据库操作**：Alpha保存和过滤记录

日志文件位置：`logs/alpha_machine_YYYY-MM-DD_HH-MM-SS.log`

## 性能优化

### 并发控制
- 默认最大并发任务数：8
- 任务检查间隔：5秒
- 自动重试机制：最多3次

### 批量处理
- 默认批量大小：1000
- 支持动态调整批量大小
- 内存优化的数据处理

### 数据库优化
- 基于哈希值的快速查询
- 索引优化
- 批量操作支持

## 故障排除

### 常见问题

#### 1. 认证失败

**问题**: `AuthenticationError: Invalid credentials`

**解决方案**:
```bash
# 检查凭据文件
cat brain_credentials.txt

# 验证网络连接
ping api.worldquant.com

# 重新配置凭据
cp brain_credentials.txt.example brain_credentials.txt
# 编辑文件填入正确信息
```

#### 2. 数据库错误

**问题**: `DatabaseError: Permission denied`

**解决方案**:
```bash
# 检查数据库文件权限
ls -la data/worldquant.db

# 创建数据目录
mkdir -p data/

# 重置数据库
rm data/worldquant.db
python -c "from db_utils import setup_database; setup_database()"
```

#### 3. 仿真超时

**问题**: `SimulationTimeout: Request timeout after 300s`

**解决方案**:
```ini
# 调整config.ini中的并发设置
[others]
batch_size = 500  # 减少批量大小
max_concurrent = 4  # 减少并发数
check_interval = 10  # 增加检查间隔
```

#### 4. 内存不足

**问题**: `MemoryError: Cannot allocate memory`

**解决方案**:
```bash
# 监控内存使用
top -p $(pgrep -f alpha_machine)

# 调整批量大小
sed -i 's/batch_size = 1000/batch_size = 200/' config.ini

# 启用垃圾回收
export PYTHONOPTIMIZE=1
```

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

使用测试模式：
```bash
# 运行测试
python test.py --verbose

# 检查配置
python -c "from common_config import CommonConfig; print(CommonConfig.validate())"
```

## 扩展开发

### 自定义Alpha工厂

```python
from core.alpha_factory import AlphaFactory

class CustomAlphaFactory(AlphaFactory):
    def create_custom_operations(self, base_expr):
        """自定义Alpha生成逻辑"""
        custom_operations = [
            "ts_corr({}, returns, 20)".format(base_expr),
            "ts_regression({}, volume, 15)".format(base_expr),
        ]
        return custom_operations
    
    def create_sector_specific_alphas(self, sector):
        """针对特定行业的Alpha生成"""
        if sector == "TECH":
            return ["rank(close / ts_mean(close, 20))"]
        elif sector == "FINANCE":
            return ["group_neutralize(returns, industry)"]
        return []
```

### 自定义服务

```python
from core.base_service import BaseService

class CustomAnalysisService(BaseService):
    def get_service_name(self):
        return "CustomAnalysisService"
    
    def analyze_alpha_performance(self, alpha_list):
        """自定义Alpha性能分析"""
        results = []
        for alpha in alpha_list:
            # 实现自定义分析逻辑
            analysis = self.perform_custom_analysis(alpha)
            results.append(analysis)
        return results
    
    def perform_custom_analysis(self, alpha):
        """具体分析实现"""
        # 自定义分析逻辑
        return {
            'alpha': alpha,
            'custom_score': self.calculate_custom_score(alpha),
            'risk_metrics': self.calculate_risk_metrics(alpha)
        }
```

### 自定义工具模块

```python
# utils/custom_tools.py
from utils.time_utils import replace_time_placeholders

class CustomToolManager:
    def __init__(self, config):
        self.config = config
    
    def generate_report(self, data):
        """生成自定义报告"""
        template = """
        报告生成时间: [DATETIME]
        用户: [USER_NAME]
        数据摘要: {summary}
        """
        
        summary = self.summarize_data(data)
        report = template.format(summary=summary)
        return replace_time_placeholders(report)
    
    def summarize_data(self, data):
        """数据摘要生成"""
        # 实现数据摘要逻辑
        return "自定义数据摘要"
```

## 版本历史

### v2.0.0 (2024-12)
- 🆕 新增知识图谱系统
- 🆕 新增稳健性分析框架
- 🆕 新增实用工具集
- 🔧 重构核心架构
- 📈 性能优化和稳定性提升

### v1.5.0 (2024-06)
- 🔄 三阶Alpha提升功能
- 💾 智能数据库过滤
- ⚡ 批量并发仿真优化

### v1.0.0 (2023-12)
- 🚀 初始版本发布
- 📊 基础Alpha生成和回测功能
- 🎯 自动提交检查系统

## 许可证

本项目采用MIT许可证。详见 [LICENSE](LICENSE) 文件。

```
MIT License

Copyright (c) 2023 WorldQuant知识图谱系统

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 贡献指南

我们欢迎社区贡献！请遵循以下准则：

### 贡献流程

1. **Fork 项目**
   ```bash
   git clone https://github.com/your-username/jy_worldquant.git
   cd jy_worldquant
   ```

2. **创建特性分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **开发和测试**
   ```bash
   # 进行开发
   # 运行测试
   python -m pytest tests/
   ```

4. **提交更改**
   ```bash
   git add .
   git commit -m "Add: your feature description"
   git push origin feature/your-feature-name
   ```

5. **创建 Pull Request**
   - 提供清晰的PR描述
   - 包含相关的测试
   - 确保代码通过CI检查

### 代码规范

- 遵循PEP 8 Python代码风格
- 使用有意义的变量和函数名
- 添加适当的注释和文档字符串
- 保持函数简洁，单一职责原则

### 测试要求

- 为新功能编写单元测试
- 确保测试覆盖率不低于80%
- 测试命名清晰，描述测试场景

### 文档更新

- 更新相关的README部分
- 添加API文档（如适用）
- 更新版本历史

## 联系方式

如有问题或建议，请通过以下方式联系：

- **GitHub Issues**: [提交Issue](https://github.com/nhoodboy/jy_worldquant/issues)
- **邮件联系**: <EMAIL>
- **技术讨论**: 加入我们的技术讨论群

## 致谢

感谢以下项目和社区的支持：

- [WorldQuant Brain](https://platform.worldquant.com) - 提供Alpha回测平台
- [Python社区](https://python.org) - 优秀的编程语言和生态
- 所有贡献者和使用者的反馈和建议

---

**免责声明**: 本项目仅供学习和研究使用，投资有风险，请谨慎决策。 