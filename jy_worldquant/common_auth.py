import logging
import requests
import json
from urllib.parse import urljoin
from os.path import expanduser

class BrainAuth:
    """
    WorldQuant BRAIN认证类，处理与WorldQuant BRAIN平台的登录认证
    """
    
    @staticmethod
    def load_credentials():
        """
        从凭证文件加载用户名和密码
        
        Returns:
            tuple: 包含用户名和密码的元组
        """
        try:
            credentials_file = expanduser('brain_credentials.txt')
            with open(credentials_file) as f:
                credentials = json.load(f)
            # 提取用户名和密码
            username, password = credentials[0], credentials[1]
            
            if not username or not password:
                logging.error("凭证文件中的用户名或密码为空")
                logging.error("请在 brain_credentials.txt 文件中填入正确的用户名和密码")
                logging.error("文件格式应为: [\"your_username\", \"your_password\"]")
                return "", ""
                
            return username, password
        except FileNotFoundError:
            logging.error("未找到凭证文件 brain_credentials.txt")
            logging.error("请创建 brain_credentials.txt 文件并填入您的WorldQuant BRAIN凭证")
            logging.error("文件格式应为: [\"your_username\", \"your_password\"]")
            logging.error("您可以参考 brain_credentials.txt.example 文件")
            return "", ""
        except (json.JSONDecodeError, IndexError, KeyError) as e:
            logging.error(f"凭证文件格式错误: {str(e)}")
            logging.error("文件格式应为: [\"your_username\", \"your_password\"]")
            return "", ""
        except Exception as e:
            logging.error(f"加载凭证文件时出错: {str(e)}")
            return "", ""
    
    @staticmethod
    def login():
        """
        标准登录方法，创建会话并使用保存的凭证进行认证
        
        Returns:
            requests.Session: 已认证的会话对象
        """
        # 加载凭证
        username, password = BrainAuth.load_credentials()
        
        if not username or not password:
            logging.error("无法获取有效的用户名和密码，登录失败")
            return None
        
        # 创建会话以持久存储头信息
        s = requests.Session()
        
        # 保存凭证到会话
        s.auth = (username, password)
        
        try:
            # 发送POST请求到认证API
            response = s.post('https://api.worldquantbrain.com/authentication', timeout=30)
            
            if response.status_code == 201:
                logging.info("登录成功")
                logging.info(f"响应内容: {response.content.decode('utf-8')}")
                return s
            elif response.status_code == 401:
                logging.error("登录失败：用户名或密码错误 (401 Unauthorized)")
                logging.error("请检查 brain_credentials.txt 文件中的凭证是否正确")
                return None
            else:
                logging.error(f"登录失败：HTTP {response.status_code}")
                logging.error(f"响应内容: {response.content.decode('utf-8')}")
                return None
                
        except requests.exceptions.Timeout:
            logging.error("登录请求超时，请检查网络连接")
            return None
        except requests.exceptions.RequestException as e:
            logging.error(f"登录请求失败: {str(e)}")
            return None
    
    @staticmethod
    def login_hk():
        """
        香港区域登录方法，支持生物识别认证
        
        Returns:
            requests.Session: 已认证的会话对象
        """
        # 加载凭证
        username, password = BrainAuth.load_credentials()
        
        # 创建会话以持久存储头信息
        s = requests.Session()
        
        # 保存凭证到会话
        s.auth = (username, password)
        
        # 发送POST请求到认证API
        response = s.post('https://api.worldquantbrain.com/authentication')
        
        if response.status_code == requests.codes.unauthorized:
            # 检查是否需要生物识别
            if response.headers.get("WWW-Authenticate") == "persona":
                logging.info(
                    "Complete biometrics authentication by scanning your face. Follow the link: \n"
                    + urljoin(response.url, response.headers["Location"]) + "\n"
                )
                input("Press any key after you complete the biometrics authentication.")
                
                # 在生物识别后重试认证
                biometrics_response = s.post(urljoin(response.url, response.headers["Location"]))
                
                while biometrics_response.status_code != 201:
                    input("Biometrics authentication is not complete. Please try again and press any key when completed.")
                    biometrics_response = s.post(urljoin(response.url, response.headers["Location"]))
                    
                logging.info("Biometrics authentication completed.")
            else:
                logging.error("\nIncorrect username or password. Please check your credentials.\n")
        else:
            logging.info("Logged in successfully.")
        
        return s 