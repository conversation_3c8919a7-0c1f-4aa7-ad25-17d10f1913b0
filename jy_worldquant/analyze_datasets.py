#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析WorldQuant数据集统计脚本
"""

import pandas as pd

# 输入文件
INPUT_FILE = "worldquant_datasets_consolidated.csv"

def analyze_datasets():
    """分析整合后的数据集文件，统计不同类别数量"""
    print(f"正在分析文件: {INPUT_FILE}")
    
    # 读取CSV文件
    df = pd.read_csv(INPUT_FILE)
    
    # 打印基本信息
    print(f"总数据集数量: {len(df)}")
    print(f"唯一ID数量: {df['id'].nunique()}")
    
    # 统计不同类别的数量
    category_counts = df['category_name'].value_counts()
    
    print("\n按类别统计:")
    print("-" * 50)
    for category, count in category_counts.items():
        print(f"{category}: {count}")
    
    # 保存类别统计到CSV
    category_df = pd.DataFrame({
        'category': category_counts.index,
        'count': category_counts.values
    })
    category_df.to_csv('dataset_categories.csv', index=False)
    print(f"\n类别统计已保存到 dataset_categories.csv")

if __name__ == "__main__":
    analyze_datasets() 