# !/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import time
from os.path import expanduser
import requests
import pandas as pd
import matplotlib.pyplot as plt
import logging

# Load credentials
with open(expanduser('brain_credentials.txt')) as f:
    credentials = json.load(f)

# Extract username and password from list
username, password = credentials

s = requests.Session()
s.auth = (username, password)
response = s.post('https://api.worldquantbrain.com/authentication')
logging.info(response.content)

# API的基本信息
base_url = "https://api.worldquantbrain.com/consultant/boards/genius"

# 1. 遍历分页 API，收集所有用户数据
def fetch_all_data(s):
    offset = 0
    limit = 30
    all_data = []
    while True:
        response = s.get(f"{base_url}?limit={limit}&offset={offset}&order%3D-fieldAvg&aggregate=user",)
        data = response.json()

        if "results" in data:
            all_data.extend(data["results"])
            offset += limit
            logging.info(f"Fetched {offset} records")
            if offset >= data["count"]:
                break
        else:
            logging.error("Error fetching data")
            break

        time.sleep(1)

    # 保存为CSV文件
    df = pd.DataFrame(all_data)
    df.to_csv("all_users_data.csv", index=False)
    logging.info("Data saved to all_users_data.csv")
    return df


# 2. 获取自己的用户数据中的leaderboard部分
def fetch_self_data(s):
    response = s.get("https://api.worldquantbrain.com/users/self/consultant/summary")
    self_data = response.json().get("leaderboard", {})
    return self_data


# 3. 绘制直方图并高亮自己所在位置
def plot_histogram(df, self_data, col):
    # 获取 fieldAvg 数据
    field_avg_data = df[col].dropna()

    # 绘制直方图
    plt.figure(figsize=(10, 6))
    n, bins, patches = plt.hist(field_avg_data, bins=20, color="blue", edgecolor="black", alpha=0.7)

    # 确定自己在哪个柱子中
    self_field_avg = self_data.get("fieldAvg")
    for i in range(len(bins) - 1):
        if bins[i] <= self_field_avg < bins[i + 1]:
            patches[i].set_facecolor("red")
            break

    # 图表设置
    plt.xlabel(f"{col}")
    plt.ylabel("Frequency")
    plt.title(f"Distribution of {col} with Self Highlighted")
    plt.show()


# 执行步骤
df = fetch_all_data(s)
self_data = fetch_self_data(s)
plot_histogram(df, self_data, "alphaCount")
plot_histogram(df, self_data, "fieldAvg")