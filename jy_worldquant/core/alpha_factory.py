#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Alpha工厂类，负责生成各种类型的Alpha表达式

该模块提供了丰富的分组配置功能，支持多种具有经济学意义的分组：

基础分组 (base):
- 市场、行业分类分组
- 市值和资产规模分组
- 基础波动性和流动性分组

扩展分组类别:
- profitability: 盈利能力分组（ROE, ROA, 净利润率等）
- valuation: 估值分组（P/E, P/B, EV/EBITDA等）
- financial_health: 财务健康度分组（流动比率, 负债率等）
- growth: 成长性分组（销售增长, EPS增长等）
- momentum: 动量因子分组（价格动量, 收益率排名等）
- liquidity: 流动性分组（换手率, 成交量等）
- risk: 风险特征分组（波动性, Beta等）
- technical: 技术指标分组（RSI, 布林带等）
- industry_relative: 行业相对表现分组
- composite: 综合指标分组

使用示例:
    # 基础用法
    factory = AlphaFactory(config)
    first_order = factory.create_first_order_alphas(fields)
    second_order = factory.create_second_order_alphas(first_order)
    
    # 使用特定分组类别
    categories = ['base', 'valuation', 'momentum']
    custom_second_order = factory.create_second_order_alphas_with_custom_groups(
        first_order, categories
    )
    
    # 查看可用分组类别
    available_categories = factory.get_available_group_categories()
"""

import logging
from typing import List, Dict, Any
import pandas as pd
from itertools import product
from .group_config import GroupConfig


class AlphaFactory:
    """Alpha表达式工厂类"""
    
    def __init__(self, config):
        """
        初始化Alpha工厂
        
        Args:
            config: CommonConfig 对象
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 分组配置管理器
        self.group_config = GroupConfig()
        
        # 基础操作符
        self.basic_ops = ["reverse", "inverse", "rank", "zscore", "quantile", "normalize"]
        
        # 时序操作符
        self.ts_ops = [
            "ts_rank", "ts_zscore", "ts_delta", "ts_sum", "ts_delay", 
            "ts_std_dev", "ts_mean", "ts_arg_min", "ts_arg_max", "ts_scale", 
            "ts_quantile", "ts_entropy", "ts_av_diff", "ts_min_max_diff", 
            "ts_min_max_cps", "ts_count_nans", "ts_min_diff", 
            "ts_decay_linear", "ts_regression", "ts_skewness", "ts_target_tvr_decay", "ts_product"
        ]
        
        # 分组操作符
        self.group_ops = [
            "group_neutralize", "group_rank", "group_zscore", "group_mean", 
            "group_extra", "group_backfill", "group_scale", "group_cartesian_product"
        ]
        
        # 向量操作符
        self.vec_ops = ["vec_avg", "vec_sum"]
        
        # 时间窗口
        self.days = [5, 22, 66, 120, 240]
        
        # 时序操作模板
        self.ts_op_templates = {
            "ts_zscore": lambda f: [f"ts_zscore({f}, {d})" for d in self.days],
            "ts_product": lambda f: [f"ts_product({f}, {d})" for d in self.days],
            "ts_std_dev": lambda f: [f"ts_std_dev({f}, {d})" for d in self.days],
            "last_diff_value": lambda f: [f"last_diff_value({f}, {d})" for d in self.days],
            "ts_scale": lambda f: [f"ts_scale({f}, {d}, constant=0)" for d in self.days],
            "ts_entropy": lambda f: [f"ts_entropy({f}, {d})" for d in self.days],
            "ts_sum": lambda f: [f"ts_sum({f}, {d})" for d in self.days],
            "ts_av_diff": lambda f: [f"ts_av_diff({f}, {d})" for d in self.days],
            "ts_mean": lambda f: [f"ts_mean({f}, {d})" for d in self.days],
            "ts_min_max_diff": lambda f: [f"ts_min_max_diff({f}, {d}, f=0.5)" for d in self.days],
            "ts_arg_max": lambda f: [f"ts_arg_max({f}, {d})" for d in self.days],
            "ts_min_max_cps": lambda f: [f"ts_min_max_cps({f}, {d}, f=2)" for d in self.days],
            "ts_rank": lambda f: [f"ts_rank({f}, {d}, constant=0)" for d in self.days],
            "ts_delay": lambda f: [f"ts_delay({f}, {d})" for d in self.days],
            "ts_quantile": lambda f: [f"ts_quantile({f}, {d}, driver='gaussian')" for d in self.days],
            "ts_count_nans": lambda f: [f"ts_count_nans({f}, {d})" for d in self.days],
            "ts_min_diff": lambda f: [f"ts_min_diff({f}, {d})" for d in self.days],
            "ts_decay_linear": lambda f: [f"ts_decay_linear({f}, {d}, dense=false)" for d in self.days],
            "ts_arg_min": lambda f: [f"ts_arg_min({f}, {d})" for d in self.days],
            "ts_regression": lambda f: [f"ts_regression({f}, {f}, {d}, lag=0, rettype=0)" for d in self.days],
            "ts_skewness": lambda f: [f"ts_skewness({f}, {d})" for d in self.days],
            "ts_delta": lambda f: [f"ts_delta({f}, {d})" for d in self.days],
            "ts_target_tvr_decay": lambda f: [f"ts_target_tvr_decay({f}, lambda_min=0, lambda_max=1, target_tvr=0.1)"],
            "days_from_last_change": lambda f: [f"days_from_last_change({f})"],
        }
    
    def process_datafields(self, df: pd.DataFrame) -> List[str]:
        """
        处理数据字段DataFrame，提取并预处理字段
        
        Args:
            df: 数据字段DataFrame
            
        Returns:
            预处理后的字段列表
        """
        datafields = []
        
        # 提取MATRIX类型字段
        matrix_fields = df[df['type'] == "MATRIX"]["id"].tolist()
        datafields.extend(matrix_fields)
        
        # 提取VECTOR类型字段并处理
        vector_fields = df[df['type'] == "VECTOR"]["id"].tolist()
        datafields.extend(self.get_vec_fields(vector_fields))
        
        # 预处理字段
        return self.preprocess_fields(datafields)
    
    def extract_datafields(self, df: pd.DataFrame) -> List[str]:
        """
        从DataFrame中提取原始数据字段（不做预处理）
        
        Args:
            df: 数据字段DataFrame
            
        Returns:
            原始字段列表
        """
        datafields = []
        
        # 提取MATRIX类型字段
        matrix_fields = df[df['type'] == "MATRIX"]["id"].tolist()
        datafields.extend(matrix_fields)
        
        # 提取VECTOR类型字段并处理
        vector_fields = df[df['type'] == "VECTOR"]["id"].tolist()
        datafields.extend(self.get_vec_fields(vector_fields))
        
        return datafields
    
    def get_vec_fields(self, fields: List[str]) -> List[str]:
        """
        生成向量字段的操作表达式
        
        Args:
            fields: 向量字段列表
            
        Returns:
            向量操作表达式列表
        """
        vec_fields = []
        
        for field in fields:
            for vec_op in self.vec_ops:
                if vec_op == "vec_choose":
                    vec_fields.append(f"{vec_op}({field}, nth=-1)")
                    vec_fields.append(f"{vec_op}({field}, nth=0)")
                else:
                    vec_fields.append(f"{vec_op}({field})")
        
        return vec_fields
    
    def preprocess_fields(self, datafields: List[str]) -> List[str]:
        """
        预处理数据字段，添加winsorize和ts_backfill
        
        Args:
            datafields: 原始字段列表
            
        Returns:
            预处理后的字段列表
        """
        return [f"winsorize(ts_backfill({field}, 120), std=4)" for field in datafields]
        # return [f"{field}" for field in datafields]
    
    def create_first_order_alphas(self, fields: List[str]) -> List[str]:
        """
        创建一阶Alpha表达式
        
        Args:
            fields: 字段列表
            
        Returns:
            一阶Alpha表达式列表
        """
        alpha_set = []
        all_ops = self.basic_ops + self.ts_ops
        
        for field in fields:
            # 添加原始字段
            alpha_set.append(field)
            
            # 添加各种操作符
            for op in all_ops:
                if op in self.ts_op_templates:
                    # 使用模板生成时序操作
                    alpha_set.extend(self.ts_op_templates[op](field))
                else:
                    # 基础操作符
                    alpha_set.append(f"{op}({field})")
        
        self.logger.info(f"生成了 {len(alpha_set)} 个一阶Alpha表达式")
        return alpha_set
    
    def create_second_order_alphas(self, first_order_alphas: List[str]) -> List[str]:
        """
        创建二阶Alpha表达式（group操作）
        
        Args:
            first_order_alphas: 一阶Alpha表达式列表
            
        Returns:
            二阶Alpha表达式列表
        """
        second_order = []
        
        # 过滤掉已经包含group操作的表达式
        filtered_alphas = [
            alpha for alpha in first_order_alphas
            if not any(group_op in alpha for group_op in self.group_ops)
        ]
        
        for alpha in filtered_alphas:
            for group_op in self.group_ops:
                second_order.extend(self.create_group_operations(group_op, alpha))
        
        self.logger.info(f"生成了 {len(second_order)} 个二阶Alpha表达式")
        return second_order
    
    def create_group_operations(self, op: str, field: str) -> List[str]:
        """
        创建分组操作表达式
        
        Args:
            op: 分组操作符
            field: 字段表达式
            
        Returns:
            分组操作表达式列表
        """
        # 使用基础分组
        groups = self._get_groups_for_region(['base'])
        return self._create_group_operations_with_custom_groups(op, field, groups)
    
    def create_third_order_alphas(self, second_order_alphas: List[str], 
                                 events: str = "open_events") -> List[str]:
        """
        创建三阶Alpha表达式（trade_when操作）
        
        Args:
            second_order_alphas: 二阶Alpha表达式列表
            events: 事件类型
            
        Returns:
            三阶Alpha表达式列表
        """
        third_order = []
        
        # 过滤掉已经包含trade_when的表达式
        filtered_alphas = [
            alpha for alpha in second_order_alphas
            if "trade_when" not in alpha
        ]
        
        for alpha in filtered_alphas:
            third_order.extend(self.create_trade_when_operations(alpha, events))
        
        self.logger.info(f"生成了 {len(third_order)} 个三阶Alpha表达式")
        return third_order
    
    def create_trade_when_operations(self, field: str, events: str = "open_events") -> List[str]:
        """
        创建trade_when操作表达式
        
        Args:
            field: 字段表达式
            events: 事件类型
            
        Returns:
            trade_when表达式列表
        """
        output = []
        
        # 获取事件列表
        apply_events = self._get_events_for_region(events)
        
        # 通用退出事件
        exit_events = ["abs(returns) > 0.1", "-1"]
        
        # 生成trade_when表达式
        for open_event in apply_events:
            for exit_event in exit_events:
                alpha = f"trade_when({open_event}, {field}, {exit_event})"
                output.append(alpha)
        
        return output
    
    def _get_groups_for_region(self, include_categories=None) -> List[str]:
        """
        根据区域获取分组列表
        
        Args:
            include_categories: 包含的分组类别列表，如果为None则包含基础分组
            
        Returns:
            分组列表
        """
        region = self.config.region
        
        # 获取基础分组（默认情况）
        if include_categories is None:
            include_categories = ['base']
        
        # 从配置文件获取分组
        groups = self.group_config.get_all_groups(include_categories)
        
        # 获取区域特定分组
        region_groups = self.group_config.get_region_specific_groups()
        
        # GLB区域需要特殊处理资产分组
        if region == "GLB":
            # 替换assets相关的分组为GLB特定的字段
            for i, group in enumerate(groups):
                if "bucket(rank(assets)" in group:
                    groups[i] = "bucket(rank(fnd23_tot_assets),range='0.1, 1, 0.1')"
                elif "bucket(group_rank(assets, sector)" in group:
                    groups[i] = "bucket(group_rank(fnd23_tot_assets, sector),range='0.1, 1, 0.1')"
        
        # 添加区域特定分组
        if region in region_groups:
            groups.extend(region_groups[region])
        
        self.logger.info(f"为区域 {region} 加载了 {len(groups)} 个分组，包含类别: {include_categories}")
        
        return groups
    
    def _get_events_for_region(self, events: str) -> List[str]:
        """
        根据区域和事件类型获取事件列表
        
        Args:
            events: 事件类型
            
        Returns:
            事件列表
        """
        region = self.config.region
        
        # 区域特定事件
        region_events = {
            "usa_events": [
                "rank(rp_css_business) > 0.8", "ts_rank(rp_css_business, 22) > 0.8",
                "rank(vec_avg(mws82_sentiment)) > 0.8", "ts_rank(vec_avg(mws82_sentiment),22) > 0.8",
                "rank(vec_avg(nws48_ssc)) > 0.8", "ts_rank(vec_avg(nws48_ssc),22) > 0.8",
                "rank(vec_avg(mws50_ssc)) > 0.8", "ts_rank(vec_avg(mws50_ssc),22) > 0.8",
                "ts_rank(vec_sum(scl12_alltype_buzzvec),22) > 0.9", "pcr_oi_270 < 1", "pcr_oi_270 > 1"
            ],
            "asi_events": [
                "rank(vec_avg(mws38_score)) > 0.8", "ts_rank(vec_avg(mws38_score),22) > 0.8"
            ],
            "eur_events": [
                "rank(rp_css_business) > 0.8", "ts_rank(rp_css_business, 22) > 0.8",
                "rank(vec_avg(oth429_research_reports_fundamental_keywords_4_method_2_pos)) > 0.8",
                "ts_rank(vec_avg(oth429_research_reports_fundamental_keywords_4_method_2_pos),22) > 0.8",
                "rank(vec_avg(mws84_sentiment)) > 0.8", "ts_rank(vec_avg(mws84_sentiment),22) > 0.8",
                "rank(vec_avg(mws85_sentiment)) > 0.8", "ts_rank(vec_avg(mws85_sentiment),22) > 0.8",
                "rank(mdl110_analyst_sentiment) > 0.8", "ts_rank(mdl110_analyst_sentiment, 22) > 0.8",
                "rank(vec_avg(nws3_scores_posnormscr)) > 0.8",
                "ts_rank(vec_avg(nws3_scores_posnormscr),22) > 0.8",
                "rank(vec_avg(mws36_sentiment_words_positive)) > 0.8",
                "ts_rank(vec_avg(mws36_sentiment_words_positive),22) > 0.8"
            ],
            "glb_events": [
                "rank(vec_avg(mdl109_news_sent_1m)) > 0.8",
                "ts_rank(vec_avg(mdl109_news_sent_1m),22) > 0.8",
                "rank(vec_avg(nws20_ssc)) > 0.8", "ts_rank(vec_avg(nws20_ssc),22) > 0.8",
                "vec_avg(nws20_ssc) > 0", "rank(vec_avg(nws20_bee)) > 0.8",
                "ts_rank(vec_avg(nws20_bee),22) > 0.8", "rank(vec_avg(nws20_qmb)) > 0.8",
                "ts_rank(vec_avg(nws20_qmb),22) > 0.8"
            ],
            "chn_events": [
                "rank(vec_avg(oth111_xueqiunaturaldaybasicdivisionstat_senti_conform)) > 0.8",
                "ts_rank(vec_avg(oth111_xueqiunaturaldaybasicdivisionstat_senti_conform),22) > 0.8",
                "rank(vec_avg(oth111_gubanaturaldaydevicedivisionstat_senti_conform)) > 0.8",
                "ts_rank(vec_avg(oth111_gubanaturaldaydevicedivisionstat_senti_conform),22) > 0.8",
                "rank(vec_avg(oth111_baragedivisionstat_regi_senti_conform)) > 0.8",
                "ts_rank(vec_avg(oth111_baragedivisionstat_regi_senti_conform),22) > 0.8"
            ],
            "kor_events": [
                "rank(vec_avg(mdl110_analyst_sentiment)) > 0.8",
                "ts_rank(vec_avg(mdl110_analyst_sentiment),22) > 0.8",
                "rank(vec_avg(mws38_score)) > 0.8", "ts_rank(vec_avg(mws38_score),22) > 0.8"
            ],
            "twn_events": [
                "rank(vec_avg(mdl109_news_sent_1m)) > 0.8",
                "ts_rank(vec_avg(mdl109_news_sent_1m),22) > 0.8",
                "rank(rp_ess_business) > 0.8", "ts_rank(rp_ess_business,22) > 0.8"
            ]
        }
        
        # 获取事件列表
        apply_events = region_events.get(events, [])
        
        # 添加通用事件
        common_events = [
            "ts_arg_max(volume, 5) == 0", "ts_corr(close, volume, 20) < 0",
            "ts_corr(close, volume, 5) < 0", "ts_mean(volume,10)>ts_mean(volume,60)",
            "group_rank(ts_std_dev(returns,60), sector) > 0.7", "ts_zscore(returns,60) > 2",
            "ts_arg_min(volume, 5) > 3", "ts_std_dev(returns, 5) > ts_std_dev(returns, 20)",
            "ts_arg_max(close, 5) == 0", "ts_arg_max(close, 20) == 0",
            "ts_corr(close, volume, 5) > 0", "ts_corr(close, volume, 5) > 0.3",
            "ts_corr(close, volume, 5) > 0.5", "ts_corr(close, volume, 20) > 0",
            "ts_corr(close, volume, 20) > 0.3", "ts_corr(close, volume, 20) > 0.5",
        ]
        
        apply_events.extend(common_events)
        return apply_events
    
    def get_available_group_categories(self) -> List[str]:
        """
        获取可用的分组类别列表
        
        Returns:
            分组类别列表
        """
        return [
            'base',           # 基础分组
            'profitability',  # 盈利能力分组
            'valuation',      # 估值分组
            'financial_health', # 财务健康度分组
            'growth',         # 成长性分组
            'momentum',       # 动量因子分组
            'liquidity',      # 流动性分组
            'risk',           # 风险特征分组
            'technical',      # 技术指标分组
            'industry_relative', # 行业相对表现分组
            'composite'       # 综合指标分组
        ]
    
    def create_second_order_alphas_with_custom_groups(self, first_order_alphas: List[str], 
                                                    group_categories: List[str] = None) -> List[str]:
        """
        使用自定义分组类别创建二阶Alpha表达式
        
        Args:
            first_order_alphas: 一阶Alpha表达式列表
            group_categories: 要使用的分组类别列表，如果为None则使用基础分组
            
        Returns:
            二阶Alpha表达式列表
        """
        second_order = []
        
        # 过滤掉已经包含group操作的表达式
        filtered_alphas = [
            alpha for alpha in first_order_alphas
            if not any(group_op in alpha for group_op in self.group_ops)
        ]
        
        # 获取指定类别的分组
        custom_groups = self._get_groups_for_region(group_categories)
        
        for alpha in filtered_alphas:
            for group_op in self.group_ops:
                second_order.extend(self._create_group_operations_with_custom_groups(group_op, alpha, custom_groups))
        
        self.logger.info(f"使用自定义分组类别 {group_categories} 生成了 {len(second_order)} 个二阶Alpha表达式")
        return second_order
    
    def _create_group_operations_with_custom_groups(self, op: str, field: str, custom_groups: List[str]) -> List[str]:
        """
        使用自定义分组创建分组操作表达式
        
        Args:
            op: 分组操作符
            field: 字段表达式
            custom_groups: 自定义分组列表
            
        Returns:
            分组操作表达式列表
        """
        output = []
        vectors = ["cap"]
        
        for group in custom_groups:
            if op in ["group_mean", "group_extra"]:
                # 需要weight参数
                for vector in vectors:
                    alpha = f"{op}({field}, {vector}, densify({group}))"
                    output.append(alpha)
            elif op == "group_backfill":
                # 需要d和std参数
                d_list = [20, 60, 120]
                for d in d_list:
                    alpha = f"{op}({field}, densify({group}), {d}, std=4.0)"
                    output.append(alpha)
            elif op == "group_cartesian_product":
                # 需要两个group
                for group2 in custom_groups:
                    if group != group2:
                        alpha = f"{op}(densify({group}), densify({group2}))"
                        output.append(alpha)
            elif op in ["group_rank", "group_scale", "group_zscore", "group_neutralize"]:
                # 标准group操作
                alpha = f"{op}({field}, densify({group}))"
                output.append(alpha)
            else:
                # 默认处理
                alpha = f"{op}({field}, densify({group}))"
                output.append(alpha)
        
        return output 