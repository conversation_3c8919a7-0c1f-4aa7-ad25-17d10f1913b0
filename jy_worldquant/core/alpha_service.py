#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Alpha服务类，负责Alpha的生成、筛选和管理
"""

import random
from typing import List, Tuple, Optional, Dict, Any
import pandas as pd
from itertools import product
import datetime

from .base_service import BaseService
from .alpha_factory import AlphaFactory


class AlphaService(BaseService):
    """Alpha服务类，提供Alpha相关的所有功能"""
    
    def __init__(self, config, auth_service, db_service=None):
        """
        初始化Alpha服务
        
        Args:
            config: CommonConfig 对象
            auth_service: 认证服务对象
            db_service: 数据库服务对象（可选）
        """
        super().__init__(config, auth_service)
        self.db_service = db_service
        self.alpha_factory = AlphaFactory(config)
        
    def get_service_name(self) -> str:
        """获取服务名称"""
        return "AlphaService"
    
    def get_datasets(self) -> pd.DataFrame:
        """
        获取可用的数据集
        
        Returns:
            数据集DataFrame
        """
        url = "https://api.worldquantbrain.com/data-sets"
        params = {
            'instrumentType': self.config.instrument_type,
            'region': self.config.region,
            'delay': str(self.config.delay),
            'universe': self.config.universe
        }
        
        response = self.make_request('GET', url, params=params)
        if response:
            try:
                data = response.json()
                return pd.DataFrame(data.get('results', []))
            except ValueError as e:
                self.logger.error(f"解析数据集JSON失败: {str(e)}")
                return pd.DataFrame()
        
        return pd.DataFrame()
    
    def get_datafields(self, dataset_id: Optional[str] = None, 
                      search: str = '') -> pd.DataFrame:
        """
        获取数据字段
        
        Args:
            dataset_id: 数据集ID（可选）
            search: 搜索关键词
            
        Returns:
            数据字段DataFrame
        """
        base_url = "https://api.worldquantbrain.com/data-fields"
        
        params = {
            'instrumentType': self.config.instrument_type,
            'region': self.config.region,
            'delay': str(self.config.delay),
            'universe': self.config.universe,
            'limit': '50'
        }
        
        if dataset_id:
            params['dataset.id'] = dataset_id
        if search:
            params['search'] = search
            
        # 构建URL模板用于分页
        url_template = f"{base_url}?" + "&".join([f"{k}={v}" for k, v in params.items()]) + "&offset={offset}"
        
        all_results = self.handle_paginated_request(url_template)
        
        # 根据覆盖率过滤
        filtered_results = [
            item for item in all_results 
            if item.get('coverage', 0) >= self.config.coverage
        ]
        
        return pd.DataFrame(filtered_results)
    
    def create_alpha_from_datafields(self) -> List[Tuple[str, int]]:
        """
        从数据字段创建Alpha表达式
        
        Returns:
            Alpha表达式列表，每个元素为 (expression, decay) 元组
        """
        self.logger.info("开始从数据字段创建Alpha")
        
        # 获取数据字段
        df = self.get_datafields(dataset_id=self.config.dataset_id)
        if df.empty:
            self.logger.warning("未获取到任何数据字段")
            return []
        
        # 处理数据字段
        datafields = self.alpha_factory.process_datafields(df)
        
        # 生成一阶Alpha
        first_order_alphas = self.alpha_factory.create_first_order_alphas(datafields)
        
        # 创建alpha列表
        alpha_list = [(alpha, self.config.decay) for alpha in first_order_alphas]
        
        self.logger.info(f"生成了 {len(alpha_list)} 个Alpha表达式")
        return alpha_list
    
    def create_alpha_from_file(self, filename: str) -> List[Tuple[str, int]]:
        """
        从文件读取数据字段并创建Alpha
        
        Args:
            filename: 字段文件名
            
        Returns:
            Alpha表达式列表
        """
        self.logger.info(f"从文件 {filename} 创建Alpha")
        
        try:
            datafields = []
            with open(filename, 'r') as f:
                for line in f:
                    field = line.strip()
                    if field:
                        datafields.append(field)
            
            self.logger.info(f"从文件加载了 {len(datafields)} 个数据字段")

            if self.config.is_preprocess_alpha:
                processed_fields = self.alpha_factory.preprocess_fields(datafields)
                # 生成一阶Alpha
                first_order_alphas = self.alpha_factory.create_first_order_alphas(processed_fields)
            else:
                first_order_alphas = datafields
            
            # 创建alpha列表
            alpha_list = [(alpha, self.config.decay) for alpha in first_order_alphas]
            
            self.logger.info(f"生成了 {len(alpha_list)} 个Alpha表达式")
            return alpha_list
            
        except Exception as e:
            self.logger.error(f"从文件创建Alpha时出错: {str(e)}")
            return []
    
    def create_alpha_from_template(self, template: str) -> List[Tuple[str, int]]:
        """
        基于模板创建Alpha
        
        Args:
            template: Alpha模板
            
        Returns:
            Alpha表达式列表
        """
        self.logger.info("基于模板创建Alpha")
        
        # 获取数据字段
        df = self.get_datafields(dataset_id=self.config.dataset_id)
        if df.empty:
            self.logger.warning("未获取到任何数据字段")
            return []
        
        datafields = self.alpha_factory.extract_datafields(df)
        alpha_list = []
        
        # 用每个字段生成Alpha表达式
        for datafield in datafields:
            expr = template.format(field=datafield)
            alpha_list.append((expr, self.config.decay))
        
        self.logger.info(f"基于模板生成了 {len(alpha_list)} 个Alpha表达式")
        return alpha_list
    
    def read_alpha_factors_from_file(self, filename: str) -> List[Tuple[str, int]]:
        """
        直接从文件读取Alpha因子
        
        Args:
            filename: Alpha因子文件名
            
        Returns:
            Alpha表达式列表
        """
        self.logger.info(f"从文件 {filename} 读取Alpha因子")
        
        try:
            alpha_list = []
            with open(f'data/others/{filename}', 'r') as f:
                for line in f:
                    alpha = line.strip()
                    if alpha:
                        alpha_list.append((alpha, self.config.decay))
            
            self.logger.info(f"从文件读取了 {len(alpha_list)} 个Alpha因子")
            return alpha_list
            
        except Exception as e:
            self.logger.error(f"读取Alpha因子文件时出错: {str(e)}")
            return []
    
    def get_alphas(self, start_date: str, end_date: str,
                   alpha_num: int, usage: str,
                   sharpe_th: float = 0.0,
                   fitness_th: float = 0.0,
                   margin_th: float = 0.0,
                   use_region: bool = True) -> List[List[Any]]:
        """
        获取Alpha列表

        Args:
            start_date: 开始日期 (MM-DD)
            end_date: 结束日期 (MM-DD)
            alpha_num: Alpha数量
            usage: 使用用途 ('track' 或 'submit')
            sharpe_th: Sharpe阈值
            fitness_th: Fitness阈值
            margin_th: Margin阈值
            use_region: 是否使用region过滤

        Returns:
            Alpha信息列表
        """
        self.logger.info(f"获取Alpha列表: {start_date} 到 {end_date}，use_region={use_region}")

        output = []
        current_year = datetime.datetime.now().year

        # 构建基础查询参数
        base_params = {
            'limit': '100',
            'offset': '0',
            'status': 'UNSUBMITTED,IS_FAIL',
            'dateCreated>=': f'{current_year}-{start_date}T00:00:00-04:00',
            'dateCreated<': f'{current_year}-{end_date}T00:00:00-04:00',
            'settings.region': self.config.region,
            'hidden': 'false',
            # 'type!': 'SUPER'
        }
        if use_region:
            base_params['settings.region'] = self.config.region

        # 构建不同排序和过滤条件的查询列表
        urls = []
        
        # 正向搜索（高sharpe和fitness）
        positive_params = base_params.copy()
        positive_params.update({
            'is.fitness>': str(fitness_th),
            'is.sharpe>': str(sharpe_th),
            'order': '-is.sharpe'
        })
        urls.append(self._build_url_with_params("https://api.worldquantbrain.com/users/self/alphas", positive_params))
        
        # 负向搜索（低sharpe和fitness，仅用于track）
        if usage != "submit":
            negative_params = base_params.copy()
            negative_params.update({
                'is.fitness<': f'-{fitness_th}',
                'is.sharpe<': f'-{sharpe_th}',
                'order': 'is.sharpe'
            })
            urls.append(self._build_url_with_params("https://api.worldquantbrain.com/users/self/alphas", negative_params))
        
        self.logger.info(urls[:10])

        # 获取所有Alpha
        count = 0
        for url in urls:
            for offset in range(0, alpha_num, 100):
                # 替换URL中的offset参数
                import re
                paginated_url = re.sub(r'offset=\d+', f'offset={offset}', url)
                response = self.make_request('GET', paginated_url)
                
                if not response:
                    continue
                
                try:
                    alpha_list = response.json().get("results", [])
                    
                    for alpha_data in alpha_list:
                        count += 1
                        processed_alpha = self._process_alpha_data(alpha_data, usage, sharpe_th)
                        if processed_alpha:
                            output.append(processed_alpha)
                            
                except Exception as e:
                    self.logger.error(f"处理Alpha数据时出错: {str(e)}")
                    continue
        
        self.logger.info(f"获取到 {count} 个Alpha，有效Alpha {len(output)} 个")
        return output
    
    def _build_url_with_params(self, base_url: str, params: Dict[str, str]) -> str:
        """构建带参数的URL"""
        from urllib.parse import quote
        
        encoded_params = []
        for k, v in params.items():
            # 特殊处理status参数中的逗号 - 不需要再次编码
            original_v = v
            if k == 'status' and ',' in v:
                v = v.replace(',', '%1F')
            
            # 特殊处理包含比较操作符的参数名
            # 对于像 is.fitness> 这样的参数，我们需要将其转换为 is.fitness%3E0.7 的格式
            if '>=' in k:
                # dateCreated>= -> dateCreated%3E=
                encoded_key = k.replace('>=', '%3E=')
                encoded_value = quote(str(v), safe='-:')
                encoded_params.append(f"{encoded_key}{encoded_value}")
            elif '<=' in k:
                # 类似处理
                encoded_key = k.replace('<=', '%3C=')
                encoded_value = quote(str(v), safe='-:')
                encoded_params.append(f"{encoded_key}{encoded_value}")
            elif '>' in k:
                # is.fitness> -> is.fitness%3E0.7 (注意没有等号)
                encoded_key = k.replace('>', '%3E')
                encoded_value = quote(str(v), safe='-:')
                encoded_params.append(f"{encoded_key}{encoded_value}")
            elif '<' in k:
                # is.fitness< -> is.fitness%3C0.7 (注意没有等号)
                encoded_key = k.replace('<', '%3C')
                encoded_value = quote(str(v), safe='-:')
                encoded_params.append(f"{encoded_key}{encoded_value}")
            else:
                # 普通参数
                if k == 'status' and '%1F' in v:
                    # status参数已经手动编码了逗号，不需要再次编码
                    encoded_params.append(f"{k}={v}")
                else:
                    encoded_value = quote(str(v), safe='-:')
                    encoded_params.append(f"{k}={encoded_value}")
        
        param_string = "&".join(encoded_params)
        return f"{base_url}?{param_string}"
    
    def _process_alpha_data(self, alpha_data: Dict[str, Any], 
                           usage: str, sharpe_th: float) -> Optional[List[Any]]:
        """
        处理单个Alpha数据
        
        Args:
            alpha_data: Alpha数据字典
            usage: 使用用途
            sharpe_th: Sharpe阈值
            
        Returns:
            处理后的Alpha记录或None
        """
        try:
            alpha_id = alpha_data["id"]
            exp = alpha_data['regular']['code']
            sharpe = alpha_data["is"]["sharpe"]
            fitness = alpha_data["is"]["fitness"]
            turnover = alpha_data["is"]["turnover"]
            margin = alpha_data["is"]["margin"]
            long_count = alpha_data["is"]["longCount"]
            short_count = alpha_data["is"]["shortCount"]
            decay = alpha_data["settings"]["decay"]
            date_created = alpha_data["dateCreated"]
            tags = alpha_data.get('tags', [])
            
            # 检查是否满足条件
            if (long_count + short_count) <= 100:
                return None
            
            # 对于submit用途，检查标签
            if usage == "submit":
                if tags in [["checked"], ["submittable"]]:
                    return None
            
            # 处理负sharpe的情况
            if sharpe < -sharpe_th:
                exp = f"-{exp}"
            
            # 构建记录
            rec = [alpha_id, exp, sharpe, turnover, fitness, margin, date_created, decay]
            
            # 根据turnover调整decay
            adjusted_decay = self._calculate_adjusted_decay(turnover, decay)
            rec.append(adjusted_decay)
            
            return rec
            
        except KeyError as e:
            self.logger.error(f"Alpha数据缺少必要字段: {str(e)}")
            return None
    
    def _calculate_adjusted_decay(self, turnover: float, decay: int) -> int:
        """根据turnover调整decay值"""
        if turnover > 0.7:
            return min(512, decay * 4)
        elif turnover > 0.6:
            return min(512, decay * 3 + 3)
        elif turnover > 0.5:
            return min(512, decay * 3)
        elif turnover > 0.4:
            return min(512, decay * 2)
        elif turnover > 0.35:
            return min(512, decay + 4)
        elif turnover > 0.3:
            return min(512, decay + 2)
        else:
            return decay
    
    def prune_alphas(self, alpha_recs: List[List[Any]], 
                    prefix: str, keep_num: int = 5) -> List[Tuple[str, int]]:
        """
        修剪Alpha列表，保留每个字段的top表现者
        
        Args:
            alpha_recs: Alpha记录列表
            prefix: 数据字段前缀
            keep_num: 每个字段保留的数量
            
        Returns:
            修剪后的Alpha列表
        """
        self.logger.info(f"开始修剪Alpha，前缀: {prefix}, 保留数量: {keep_num}")
        
        from collections import defaultdict
        
        output = []
        num_dict = defaultdict(int)
        
        for rec in alpha_recs:
            exp = rec[1]
            try:
                field = exp.split(prefix)[-1].split(",")[0]
            except IndexError:
                field = exp
                
            sharpe = rec[2]
            if sharpe < 0:
                field = f"-{field}"
            
            if num_dict[field] < keep_num:
                num_dict[field] += 1
                decay = rec[-1]
                output.append([exp, decay])
        
        self.logger.info(f"修剪完成，从 {len(alpha_recs)} 个Alpha减少到 {len(output)} 个")
        return output
    
    def remove_duplicates(self, alpha_list: List[Any]) -> List[Any]:
        """
        移除重复的Alpha表达式
        
        Args:
            alpha_list: Alpha列表
            
        Returns:
            去重后的Alpha列表
        """
        self.logger.info("开始去重Alpha表达式")
        
        unique_dict = {}
        
        for item in alpha_list:
            # 获取alpha表达式
            expr_str = str(item[1]) if len(item) > 1 else str(item[0])
            
            # 如果表达式不在字典中，则添加
            if expr_str not in unique_dict:
                unique_dict[expr_str] = item
        
        unique_list = list(unique_dict.values())
        duplicate_count = len(alpha_list) - len(unique_list)
        
        self.logger.info(f"去重完成，过滤掉 {duplicate_count} 个重复Alpha")
        return unique_list
    
    def filter_new_alphas(self, alpha_list: List[Tuple[str, int]]) -> List[Tuple[str, int]]:
        """
        过滤已经回测过的Alpha
        
        Args:
            alpha_list: Alpha列表
            
        Returns:
            未回测的Alpha列表
        """
        if not self.db_service:
            return alpha_list
            
        return self.db_service.filter_new_alpha_list(alpha_list, self.config) 