#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础服务类，提供所有服务的通用功能
"""

import logging
import time
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class BaseService(ABC):
    """所有服务的基础类"""
    
    def __init__(self, config, auth_service):
        """
        初始化基础服务
        
        Args:
            config: CommonConfig 对象
            auth_service: 认证服务对象
        """
        self.config = config
        self.auth_service = auth_service
        self.logger = logging.getLogger(self.__class__.__name__)
        self._session = None
        
    def get_session(self) -> requests.Session:
        """获取HTTP会话，带有重试机制"""
        if not self._session:
            self._session = self._create_session()
        return self._session
    
    def _create_session(self) -> requests.Session:
        """创建带重试机制的HTTP会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=5,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504, 429],
            allowed_methods=["POST", "GET", "PATCH", "DELETE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        
        return session
    
    def make_request(self, method: str, url: str, **kwargs) -> Optional[requests.Response]:
        """
        发送HTTP请求，带有认证和错误处理
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 请求参数
            
        Returns:
            响应对象或None
        """
        session = self.get_session()
        
        # 确保会话已认证
        if not hasattr(session, '_authenticated'):
            session = self.auth_service.login()
            if not session:
                self.logger.error("认证失败，无法获取有效会话")
                return None
            session._authenticated = True
            self._session = session
        
        try:
            response = session.request(method, url, **kwargs)
            
            # 检查是否需要重新认证
            if response.status_code == 401:
                self.logger.info("需要重新认证")
                session = self.auth_service.login()
                if not session:
                    self.logger.error("重新认证失败")
                    return None
                session._authenticated = True
                self._session = session
                response = session.request(method, url, **kwargs)
            
            # 处理429错误（速率限制）
            if response.status_code == 429:
                retry_after = response.headers.get("Retry-After", 30)
                self.logger.warning(f"触发速率限制，等待 {retry_after} 秒")
                time.sleep(float(retry_after))
                return self.make_request(method, url, **kwargs)
                
            response.raise_for_status()
            return response
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求失败: {method} {url}, 错误: {str(e)}")
            return None
    
    def handle_paginated_request(self, url_template: str, 
                                params: Optional[Dict[str, Any]] = None,
                                limit: int = 50) -> list:
        """
        处理分页请求
        
        Args:
            url_template: URL模板，应包含{offset}占位符
            params: 查询参数
            limit: 每页限制
            
        Returns:
            所有结果的列表
        """
        all_results = []
        offset = 0
        
        while True:
            url = url_template.format(offset=offset)
            response = self.make_request('GET', url, params=params)
            
            if not response:
                break
                
            try:
                data = response.json()
                results = data.get('results', [])
                
                if not results:
                    break
                    
                all_results.extend(results)
                
                # 检查是否还有更多数据
                if len(results) < limit:
                    break
                    
                offset += limit
                
            except ValueError as e:
                self.logger.error(f"解析JSON失败: {str(e)}")
                break
                
        return all_results
    
    def sleep_with_jitter(self, base_delay: float, jitter_factor: float = 0.1):
        """
        带抖动的延时，避免请求过于规律
        
        Args:
            base_delay: 基础延时时间（秒）
            jitter_factor: 抖动因子（0-1之间的小数）
        """
        import random
        jitter = random.uniform(-jitter_factor, jitter_factor) * base_delay
        actual_delay = base_delay + jitter
        time.sleep(max(0, actual_delay))
    
    @abstractmethod
    def get_service_name(self) -> str:
        """获取服务名称"""
        pass 