#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
仿真服务类，负责Alpha的回测和仿真
"""

import time
import logging
from typing import List, Tuple, Optional, Dict, Any, Generator
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from .base_service import BaseService


class SimulationService(BaseService):
    """仿真服务类，提供Alpha回测功能"""
    
    def __init__(self, config, auth_service, db_service=None):
        """
        初始化仿真服务
        
        Args:
            config: CommonConfig 对象
            auth_service: 认证服务对象
            db_service: 数据库服务对象（可选）
        """
        super().__init__(config, auth_service)
        self.db_service = db_service
        self.max_concurrent_tasks = 8
        self.task_check_interval = 5  # 秒
        
    def get_service_name(self) -> str:
        """获取服务名称"""
        return "SimulationService"
    
    def simulate_alphas(self, alpha_list: List[Tuple[str, int]]) -> List[Tuple[str, int]]:
        """
        仿真Alpha列表
        
        Args:
            alpha_list: Alpha列表，每个元素为 (expression, decay) 元组
            
        Returns:
            成功完成仿真的Alpha列表
        """
        if not alpha_list:
            self.logger.warning("Alpha列表为空，跳过仿真")
            return []
            
        self.logger.info(f"开始仿真 {len(alpha_list)} 个Alpha")
        
        # 将Alpha分组为任务
        alpha_pools = self._load_task_pools(alpha_list, 10, self.max_concurrent_tasks)
        
        return self._multi_simulate(alpha_pools)
    
    def batch_simulate(self, alpha_list: List[Tuple[str, int]], 
                      batch_size: Optional[int] = None) -> Generator[int, None, None]:
        """
        批量仿真Alpha，返回进度
        
        Args:
            alpha_list: Alpha列表（已经过滤过的）
            batch_size: 批次大小（如果为None则使用配置中的值）
            
        Yields:
            完成百分比
        """
        batch_size = batch_size or self.config.batch_size
        self.logger.info(f"开始批量仿真，批次大小: {batch_size}")
        
        # 注意：alpha_list应该已经在调用此方法之前进行过数据库过滤
        if not alpha_list:
            self.logger.warning("Alpha列表为空，跳过批量仿真")
            return
        
        total_batches = len(alpha_list) // batch_size + (1 if len(alpha_list) % batch_size else 0)
        
        for i in range(0, len(alpha_list), batch_size):
            batch = alpha_list[i:i + batch_size]
            if not batch:
                continue
                
            batch_num = i // batch_size + 1
            self.logger.info(f"进行第 {batch_num}/{total_batches} 批仿真，数量: {len(batch)}")
            
            # 仿真当前批次，获取成功的Alpha列表
            successful_alphas = self.simulate_alphas(batch)
            if not successful_alphas:
                self.logger.warning(f"第 {batch_num} 批仿真无成功的Alpha")
            else:
                self.logger.info(f"第 {batch_num} 批仿真成功完成 {len(successful_alphas)} 个Alpha")
                # 注意：Alpha已在任务完成时异步保存到数据库，无需再次保存
                self.logger.info(f"第 {batch_num} 批次的 {len(successful_alphas)} 个成功Alpha已在任务完成时异步保存到数据库")
            
            # 计算进度
            progress = min(100, int((i + batch_size) / len(alpha_list) * 100))
            self.logger.info(f"批次 {batch_num} 完成，总进度: {progress}%")
            
            yield progress
    
    def _load_task_pools(self, alpha_list: List[Tuple[str, int]], 
                        children_limit: int, pool_limit: int) -> List[List[List[Tuple[str, int]]]]:
        """
        将Alpha列表分组为任务池
        
        Args:
            alpha_list: Alpha列表
            children_limit: 子任务限制
            pool_limit: 池限制
            
        Returns:
            任务池列表
        """
        # 分组为任务
        tasks = [
            alpha_list[i:i + children_limit] 
            for i in range(0, len(alpha_list), children_limit)
        ]
        
        # 分组为池
        pools = [
            tasks[i:i + pool_limit] 
            for i in range(0, len(tasks), pool_limit)
        ]
        
        return pools
    
    def _multi_simulate(self, alpha_pools: List[List[List[Tuple[str, int]]]]) -> List[Tuple[str, int]]:
        """
        多线程仿真Alpha池
        
        Args:
            alpha_pools: Alpha池列表
            
        Returns:
            成功完成仿真的Alpha列表
        """
        # 扁平化所有任务
        all_tasks = [task for pool in alpha_pools for task in pool]
        
        if not all_tasks:
            return []
            
        self.logger.info(f"准备仿真 {len(all_tasks)} 个任务")
        
        # 任务管理器
        task_manager = SimulationTaskManager(
            service=self,  # 传入整个服务对象而不是会话
            config=self.config,
            max_concurrent=self.max_concurrent_tasks,
            check_interval=self.task_check_interval,
            logger=self.logger
        )
        
        return task_manager.run_tasks(all_tasks)
    
    def _generate_simulation_data(self, alpha_list: List[Tuple[str, int]]) -> List[Dict[str, Any]]:
        """
        生成仿真数据
        
        Args:
            alpha_list: Alpha列表
            
        Returns:
            仿真数据列表
        """
        sim_data_list = []
        
        for alpha_expr, decay in alpha_list:
            simulation_data = {
                'type': 'REGULAR',
                'settings': {
                    'instrumentType': self.config.instrument_type,
                    'region': self.config.region,
                    'universe': self.config.universe,
                    'delay': self.config.delay,
                    'decay': decay,
                    'neutralization': self.config.neutralization,
                    'truncation': self.config.truncation,
                    'pasteurization': self.config.pasteurization,
                    'testPeriod': self.config.test_period,
                    'unitHandling': self.config.unit_handling,
                    'nanHandling': self.config.nan_handling,
                    'language': self.config.language,
                    'visualization': False,
                    'maxTrade': self.config.max_trade,
                },
                'regular': alpha_expr
            }
            sim_data_list.append(simulation_data)
        
        return sim_data_list


class SimulationTaskManager:
    """仿真任务管理器"""
    
    def __init__(self, service, config, max_concurrent=8, check_interval=5, logger=None):
        """
        初始化任务管理器
        
        Args:
            service: SimulationService 对象
            config: 配置对象
            max_concurrent: 最大并发数
            check_interval: 检查间隔（秒）
            logger: 日志记录器
        """
        self.service = service
        self.config = config
        self.max_concurrent = max_concurrent
        self.check_interval = check_interval
        self.logger = logger or logging.getLogger(__name__)
        self.running_tasks = []
        self.successful_alphas = []  # 存储成功完成仿真的Alpha
        self.lock = threading.Lock()
        
    def run_tasks(self, all_tasks: List[List[Tuple[str, int]]]) -> List[Tuple[str, int]]:
        """
        运行所有任务
        
        Args:
            all_tasks: 所有任务列表
            
        Returns:
            成功完成仿真的Alpha列表
        """
        current_index = 0
        
        # 初始填充任务
        while current_index < min(self.max_concurrent, len(all_tasks)):
            self._add_new_task(all_tasks, current_index)
            current_index += 1
        
        # 动态监控循环
        while self.running_tasks:
            time.sleep(self.check_interval)
            
            with self.lock:
                completed_indices = []
                
                for i, task_info in enumerate(self.running_tasks):
                    if self._check_task_status(task_info):
                        completed_indices.append(i)
                        
                        # 添加新任务
                        if current_index < len(all_tasks):
                            self._add_new_task(all_tasks, current_index)
                            current_index += 1
                
                # 移除完成的任务
                for i in reversed(completed_indices):
                    self.running_tasks.pop(i)
        
        self.logger.info(f"所有仿真任务完成，成功完成 {len(self.successful_alphas)} 个Alpha")
        return self.successful_alphas
    
    def _add_new_task(self, all_tasks: List[List[Tuple[str, int]]], task_index: int) -> bool:
        """
        添加新任务
        
        Args:
            all_tasks: 所有任务列表
            task_index: 任务索引
            
        Returns:
            是否成功添加
        """
        if task_index >= len(all_tasks):
            return False
            
        task = all_tasks[task_index]
        sim_data = self._generate_simulation_data(task)
        
        progress_url = self._send_simulation_task(sim_data, task_index)
        if progress_url:
            task_info = {
                "progress_url": progress_url,
                "task": task,
                "next_check": time.time() + self.check_interval,
                "index": task_index,
                "start_time": time.time(),
                "retry_count": 0
            }
            self.running_tasks.append(task_info)
            return True
        
        return False
    
    def _check_task_status(self, task_info: Dict[str, Any]) -> bool:
        """
        检查任务状态
        
        Args:
            task_info: 任务信息
            
        Returns:
            任务是否完成
        """
        current_time = time.time()
        
        # 检查是否到了检查时间
        if task_info["next_check"] > current_time:
            return False
            
        progress_url = task_info["progress_url"]
        index = task_info["index"]
        start_time = task_info["start_time"]
        
        try:
            response = self.service.make_request('GET', progress_url, timeout=120)
            
            if not response:
                self.logger.error(f"任务 {index} 状态检查失败：无响应")
                task_info["retry_count"] += 1
                if task_info["retry_count"] > 3:
                    return True
                task_info["next_check"] = current_time + self.check_interval * 2
                return False
            
            # 处理重试延迟
            retry_after = response.headers.get("Retry-After", 0)
            if retry_after != 0:
                wait_time = min(max(20, float(retry_after)), 300)
                task_info["next_check"] = current_time + wait_time
                
                elapsed_time = current_time - start_time
                if elapsed_time > 300:
                    self.logger.info(f"任务 {index} 已耗时 {int(elapsed_time)}s，等待 {wait_time}s")
                
                # 超时处理
                if elapsed_time > 2000:
                    self.logger.warning(f"任务 {index} 超时，丢弃")
                    self.service.make_request('DELETE', progress_url)
                    return True
                
                return False
            
            # 解析响应
            try:
                response_data = response.json()
                status = response_data.get("status", "UNKNOWN")
                
                if status == "COMPLETE":
                    self.logger.info(f"✅ 任务 {index} 完成，耗时 {int(current_time - start_time)}s")
                    
                    # 将成功完成的Alpha添加到成功列表中（保持向后兼容）
                    self.successful_alphas.extend(task_info["task"])
                    
                    # 立即异步保存Alpha到数据库
                    if self.service.db_service:
                        for alpha_expr, decay in task_info["task"]:
                            try:
                                # 异步保存到数据库
                                future = self.service.db_service.save_alpha_to_db(
                                    alpha_expr, decay, self.config, check_status="COMPLETE", async_mode=True
                                )
                                self.logger.debug(f"🔄 Alpha {alpha_expr[:50]}... 已提交异步保存到数据库")
                            except Exception as e:
                                self.logger.error(f"❌ Alpha {alpha_expr[:50]}... 异步保存失败: {str(e)}")
                    else:
                        self.logger.warning(f"⚠️ 任务 {index} 完成但无数据库服务，跳过保存")
                    
                    return True
                elif status == "ERROR":
                    # 获取失败的Alpha表达式
                    failed_alphas = task_info["task"]
                    self.logger.error(f"❌ 任务 {index} 失败，包含的Alpha表达式：")
                    for alpha_expr, decay in failed_alphas:
                        self.logger.error(f"   - Alpha: {alpha_expr}, Decay: {decay}")
                    
                    self.logger.warning(f"❌ 任务 {index} 失败{progress_url}: {response.text}")
                    
                    # 检查children字段并查询详细信息
                    children = response_data.get("children", [])
                    if children:
                        self.logger.info(f"正在查询任务 {index} 的 {len(children)} 个子任务详情...")
                        self._query_children_details(children, index)
                    
                    return True
                else:
                    # 任务仍在运行
                    task_info["next_check"] = current_time + self.check_interval
                    return False
                    
            except ValueError as e:
                self.logger.error(f"任务 {index} 响应解析失败: {str(e)}")
                task_info["next_check"] = current_time + self.check_interval
                return False
                
        except Exception as e:
            self.logger.error(f"任务 {index} 状态检查异常: {str(e)}")
            task_info["retry_count"] += 1
            
            # 超过重试次数则放弃任务
            if task_info["retry_count"] > 3:
                self.logger.error(f"任务 {index} 重试次数超限，放弃")
                return True
            
            task_info["next_check"] = current_time + self.check_interval * 2
            return False
    
    def _send_simulation_task(self, sim_data: List[Dict[str, Any]], task_index: int) -> Optional[str]:
        """
        发送仿真任务
        
        Args:
            sim_data: 仿真数据
            task_index: 任务索引
            
        Returns:
            进度URL或None
        """
        retry_count = 0
        max_retries = 3
        
        while retry_count < max_retries:
            try:
                response = self.service.make_request(
                    'POST',
                    'https://api.worldquantbrain.com/simulations',
                    json=sim_data,
                    timeout=(30, 120)
                )
                
                if not response:
                    raise Exception("请求失败，响应为空")
                
                location = response.headers.get('Location')
                if location:
                    self.logger.info(f"任务 {task_index} 提交成功")
                    return location
                else:
                    self.logger.warning(f"任务 {task_index} 提交成功但无Location头")
                    return None
                    
            except Exception as e:
                retry_count += 1
                self.logger.error(f"任务 {task_index} 提交失败 (重试 {retry_count}/{max_retries}): {str(e)}")
                
                if retry_count < max_retries:
                    time.sleep(60)  # 等待后重试
        
        self.logger.error(f"任务 {task_index} 最终提交失败")
        return None
    
    def _generate_simulation_data(self, alpha_list: List[Tuple[str, int]]) -> List[Dict[str, Any]]:
        """
        生成仿真数据
        
        Args:
            alpha_list: Alpha列表
            
        Returns:
            仿真数据列表
        """
        sim_data_list = []
        
        for alpha_expr, decay in alpha_list:
            simulation_data = {
                'type': 'REGULAR',
                'settings': {
                    'instrumentType': self.config.instrument_type,
                    'region': self.config.region,
                    'universe': self.config.universe,
                    'delay': self.config.delay,
                    'decay': decay,
                    'neutralization': self.config.neutralization,
                    'truncation': self.config.truncation,
                    'pasteurization': self.config.pasteurization,
                    'testPeriod': self.config.test_period,
                    'unitHandling': self.config.unit_handling,
                    'nanHandling': self.config.nan_handling,
                    'language': self.config.language,
                    'visualization': False,
                    'maxTrade': self.config.max_trade,
                },
                'regular': alpha_expr
            }
            sim_data_list.append(simulation_data)
        
        return sim_data_list
    
    def _query_children_details(self, children: List[str], task_index: int) -> None:
        """
        查询子任务详情
        
        Args:
            children: 子任务ID列表
            task_index: 父任务索引
        """
        # 获取对应的alpha列表信息（需要从当前任务信息中获取）
        current_task_info = None
        for task_info in self.running_tasks:
            if task_info["index"] == task_index:
                current_task_info = task_info
                break
        
        if not current_task_info:
            self.logger.warning(f"无法找到任务 {task_index} 的信息，跳过子任务详情查询")
            return
        
        task_alphas = current_task_info["task"]  # List[Tuple[str, int]]
        
        for i, child_id in enumerate(children):
            try:
                child_url = f"https://api.worldquantbrain.com/simulations/{child_id}"
                response = self.service.make_request('GET', child_url, timeout=30)
                
                if response:
                    self.logger.info(f"📄 任务 {task_index} 子任务 {i+1}/{len(children)} (ID: {child_id}):")
                    try:
                        child_data = response.json()
                        # 格式化打印重要信息
                        status = child_data.get("status", "UNKNOWN")
                        error_msg = child_data.get("message", "无错误信息")
                        
                        self.logger.info(f"   状态: {status}")
                        if status == "ERROR":
                            self.logger.error(f"   错误信息: {error_msg}")
                            
                            # 保存失败的Alpha到数据库
                            if i < len(task_alphas) and self.service.db_service:
                                alpha_expr, decay = task_alphas[i]
                                try:
                                    # 异步保存失败的Alpha到数据库，状态为ERROR
                                    future = self.service.db_service.save_alpha_to_db(
                                        alpha_expr, decay, self.config, check_status="ERROR", async_mode=True
                                    )
                                    self.logger.debug(f"🔄 失败的Alpha {alpha_expr}... 已提交异步保存到数据库 (状态: ERROR)")
                                except Exception as e:
                                    self.logger.error(f"❌ 失败的Alpha {alpha_expr}... 异步保存失败: {str(e)}")
                            elif not self.service.db_service:
                                self.logger.warning(f"⚠️ 失败的Alpha无法保存：无数据库服务")
                        
                        # 打印完整响应（用于调试）
                        self.logger.debug(f"   完整响应: {response.text}")
                        
                    except ValueError:
                        self.logger.warning(f"   响应解析失败: {response.text}")
                else:
                    self.logger.error(f"   子任务 {child_id} 请求失败：无响应")
                    
            except Exception as e:
                self.logger.error(f"   子任务 {child_id} 查询异常: {str(e)}")
            
            # 避免请求过快
            time.sleep(0.5) 