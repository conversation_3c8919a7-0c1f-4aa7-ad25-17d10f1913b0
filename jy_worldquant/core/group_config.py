#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分组配置文件，定义所有类型的分组表达式
包括基础分组和具有经济学意义的分组
"""


class GroupConfig:
    """分组配置类"""
    
    @staticmethod
    def get_base_groups():
        """
        获取基础分组列表
        
        Returns:
            基础分组表达式列表
        """
        return [
            # 基础分类分组
            "market", 
            "sector", 
            "industry", 
            "subindustry",
            
            # 市值和资产规模分组
            "bucket(rank(cap), range='0.1, 1, 0.1')",
            "bucket(rank(assets), range='0.1, 1, 0.1')",
            "bucket(group_rank(assets, sector), range='0.1, 1, 0.1')",
            "bucket(group_rank(cap, sector), range='0.1, 1, 0.1')",
            
            # 波动性和流动性分组
            "bucket(rank(ts_std_dev(returns,20)), range='0.1, 1, 0.1')",
            "bucket(rank(close*volume), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_profitability_groups():
        """
        获取盈利能力分组
        
        Returns:
            盈利能力分组表达式列表
        """
        return [
            # 净利润率分组
            "bucket(rank(divide(netincome, sales)), range='0.1, 1, 0.1')",
            
            # ROE分组  
            "bucket(rank(divide(netincome, equity)), range='0.1, 1, 0.1')",
            
            # ROA分组
            "bucket(rank(divide(netincome, assets)), range='0.1, 1, 0.1')",
            
            # EBITDA利润率分组
            "bucket(rank(divide(ebitda, sales)), range='0.1, 1, 0.1')",
            
            # 毛利率分组
            "bucket(rank(divide(subtract(sales, cogs), sales)), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_valuation_groups():
        """
        获取估值分组
        
        Returns:
            估值分组表达式列表
        """
        return [
            # P/E比率分组
            "bucket(rank(divide(cap, netincome)), range='0.1, 1, 0.1')",
            
            # P/B比率分组  
            "bucket(rank(divide(cap, equity)), range='0.1, 1, 0.1')",
            
            # EV/EBITDA分组
            "bucket(rank(divide(enterprise_value, ebitda)), range='0.1, 1, 0.1')",
            
            # P/S比率分组
            "bucket(rank(divide(cap, sales)), range='0.1, 1, 0.1')",
            
            # 每股账面价值分组
            "bucket(rank(bookvalue_ps), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_financial_health_groups():
        """
        获取财务健康度分组
        
        Returns:
            财务健康度分组表达式列表
        """
        return [
            # 流动比率分组
            "bucket(rank(current_ratio), range='0.1, 1, 0.1')",
            
            # 负债权益比分组
            "bucket(rank(divide(debt, equity)), range='0.1, 1, 0.1')",
            
            # 资产负债率分组
            "bucket(rank(divide(debt, assets)), range='0.1, 1, 0.1')",
            
            # 经营现金流健康度分组
            "bucket(rank(divide(cashflow_op, cap)), range='0.1, 1, 0.1')",
            
            # 现金比率分组
            "bucket(rank(divide(cash, assets)), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_growth_groups():
        """
        获取成长性分组
        
        Returns:
            成长性分组表达式列表
        """
        return [
            # 销售增长率分组
            "bucket(rank(ts_delta(sales, 252)), range='0.1, 1, 0.1')",
            
            # EPS增长分组
            "bucket(rank(ts_delta(eps, 252)), range='0.1, 1, 0.1')",
            
            # 资产增长分组
            "bucket(rank(ts_delta(assets, 252)), range='0.1, 1, 0.1')",
            
            # 净利润增长分组
            "bucket(rank(ts_delta(netincome, 252)), range='0.1, 1, 0.1')",
            
            # EBITDA增长分组
            "bucket(rank(ts_delta(ebitda, 252)), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_momentum_groups():
        """
        获取动量因子分组
        
        Returns:
            动量因子分组表达式列表
        """
        return [
            # 短期价格动量分组
            "bucket(rank(ts_delta(close, 22)), range='0.1, 1, 0.1')",
            
            # 中期价格动量分组
            "bucket(rank(ts_delta(close, 66)), range='0.1, 1, 0.1')",
            
            # 长期价格动量分组  
            "bucket(rank(ts_delta(close, 252)), range='0.1, 1, 0.1')",
            
            # 收益率排名分组
            "bucket(rank(ts_rank(returns, 22)), range='0.1, 1, 0.1')",
            
            # 价格相对强度分组
            "bucket(rank(ts_rank(close, 66)), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_liquidity_groups():
        """
        获取流动性和交易活跃度分组
        
        Returns:
            流动性分组表达式列表
        """
        return [
            # 换手率分组
            "bucket(rank(divide(volume, cap)), range='0.1, 1, 0.1')",
            
            # 平均交易量分组
            "bucket(rank(adv20), range='0.1, 1, 0.1')",
            
            # 相对交易量分组
            "bucket(group_rank(adv20, sector), range='0.1, 1, 0.1')",
            
            # 成交额分组
            "bucket(rank(multiply(close, volume)), range='0.1, 1, 0.1')",
            
            # 交易活跃度相对排名
            "bucket(group_rank(volume, sector), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_risk_groups():
        """
        获取风险特征分组
        
        Returns:
            风险特征分组表达式列表
        """
        return [
            # 收益波动性分组
            "bucket(rank(ts_std_dev(returns, 66)), range='0.1, 1, 0.1')",
            
            # 价格波动性分组
            "bucket(rank(ts_std_dev(close, 22)), range='0.1, 1, 0.1')",
            
            # 最大回撤分组
            "bucket(rank(ts_min_diff(close, 22)), range='0.1, 1, 0.1')",
            
            # 相对波动性分组
            "bucket(group_rank(ts_std_dev(returns, 66), sector), range='0.1, 1, 0.1')",
            
            # Beta分组
            "bucket(rank(ts_regression(returns, returns, 66, lag=0, rettype=0)), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_technical_groups():
        """
        获取技术指标分组
        
        Returns:
            技术指标分组表达式列表
        """
        return [
            # RSI分组
            "bucket(rank(ts_rank(close, 14)), range='0.1, 1, 0.1')",
            
            # 价格相对位置分组
            "bucket(rank(divide(subtract(close, ts_min(close, 22)), subtract(ts_max(close, 22), ts_min(close, 22)))), range='0.1, 1, 0.1')",
            
            # 移动平均相对位置
            "bucket(rank(divide(close, ts_mean(close, 22))), range='0.1, 1, 0.1')",
            
            # 布林带位置
            "bucket(rank(ts_zscore(close, 22)), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_industry_relative_groups():
        """
        获取行业相对表现分组
        
        Returns:
            行业相对表现分组表达式列表
        """
        return [
            # 行业相对收益分组
            "bucket(group_rank(returns, sector), range='0.1, 1, 0.1')",
            
            # 行业相对估值分组
            "bucket(group_rank(divide(cap, netincome), sector), range='0.1, 1, 0.1')",
            
            # 行业相对盈利能力分组
            "bucket(group_rank(divide(netincome, sales), sector), range='0.1, 1, 0.1')",
            
            # 行业相对成长性分组
            "bucket(group_rank(ts_delta(sales, 252), sector), range='0.1, 1, 0.1')",
            
            # 行业相对市值分组
            "bucket(group_rank(cap, industry), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_composite_groups():
        """
        获取综合指标分组
        
        Returns:
            综合指标分组表达式列表
        """
        return [
            # 综合规模指标分组
            "bucket(rank(multiply(cap, adv20)), range='0.1, 1, 0.1')",
            
            # 质量因子分组（ROE + 负债率）
            "bucket(rank(subtract(divide(netincome, equity), divide(debt, equity))), range='0.1, 1, 0.1')",
            
            # 价值动量综合分组
            "bucket(rank(multiply(divide(cap, netincome), ts_delta(close, 66))), range='0.1, 1, 0.1')",
            
            # 员工生产力分组（如果有员工数据）
            "bucket(rank(divide(sales, employee)), range='0.1, 1, 0.1')",
        ]
    
    @staticmethod
    def get_region_specific_groups():
        """
        获取区域特定分组
        
        Returns:
            区域特定分组字典
        """
        return {
            "CHN": [
                'sta1_top3000c30', 'sta1_top3000c20', 'sta1_top3000c10',
                'sta1_top3000c2', 'sta1_top3000c5'
            ],
            "USA": [
                'pv13_h_min2_3000_sector', 'pv13_r2_min20_3000_sector',
                'pv13_r2_min2_3000_sector', 'pv13_h_min2_focused_pureplay_3000_sector',
                'sta1_top3000c50', 'sta1_allc20', 'sta1_allc10', 'sta1_top3000c20', 'sta1_allc5',
                'sta2_top3000_fact3_c50', 'sta2_top3000_fact4_c20', 'sta2_top3000_fact4_c10',
                'mdl10_group_name'
            ],
            "HKG": [
                'pv13_10_f3_g2_minvol_1m_sector', 'pv13_10_minvol_1m_sector', 'pv13_20_minvol_1m_sector',
                'pv13_2_minvol_1m_sector', 'pv13_5_minvol_1m_sector', 'pv13_1l_scibr', 'pv13_3l_scibr',
                'pv13_2l_scibr', 'pv13_4l_scibr', 'pv13_5l_scibr',
                'sta1_allc50', 'sta1_allc5', 'sta1_allxjp_513_c20', 'sta1_top2000xjp_513_c5',
                'sta2_all_xjp_513_all_fact4_c10', 'sta2_top2000_xjp_513_top2000_fact3_c10',
                'sta2_allfactor_xjp_513_13', 'sta2_top2000_xjp_513_top2000_fact3_c20'
            ],
            "EUR": [
                'pv13_5_sector', 'pv13_2_sector', 'pv13_v3_3l_scibr', 'pv13_v3_2l_scibr', 'pv13_2l_scibr',
                'pv13_52_sector', 'pv13_v3_6l_scibr', 'pv13_v3_4l_scibr', 'pv13_v3_1l_scibr',
                'sta1_allc10', 'sta1_allc2', 'sta1_top1200c2', 'sta1_allc20', 'sta1_top1200c10',
                'sta2_top1200_fact3_c50', 'sta2_top1200_fact3_c20', 'sta2_top1200_fact4_c50'
            ],
            "GLB": [
                'pv13_2_sector', 'pv13_10_sector', 'pv13_3l_scibr', 'pv13_2l_scibr', 'pv13_1l_scibr',
                'pv13_52_minvol_1m_all_delay_1_sector', 'pv13_52_minvol_1m_sector',
                'sta1_allc20', 'sta1_allc10', 'sta1_allc50', 'sta1_allc5'
            ]
        }
    
    @staticmethod
    def get_all_groups(include_categories=None):
        """
        获取所有分组
        
        Args:
            include_categories: 包含的分组类别列表，如果为None则包含所有
            
        Returns:
            所有分组表达式列表
        """
        all_categories = {
            'base': GroupConfig.get_base_groups(),
            'profitability': GroupConfig.get_profitability_groups(),
            'valuation': GroupConfig.get_valuation_groups(),
            'financial_health': GroupConfig.get_financial_health_groups(),
            'growth': GroupConfig.get_growth_groups(),
            'momentum': GroupConfig.get_momentum_groups(),
            'liquidity': GroupConfig.get_liquidity_groups(),
            'risk': GroupConfig.get_risk_groups(),
            'technical': GroupConfig.get_technical_groups(),
            'industry_relative': GroupConfig.get_industry_relative_groups(),
            'composite': GroupConfig.get_composite_groups()
        }
        
        if include_categories is None:
            # 包含所有分组
            groups = []
            for category_groups in all_categories.values():
                groups.extend(category_groups)
            return groups
        else:
            # 只包含指定的分组类别
            groups = []
            for category in include_categories:
                if category in all_categories:
                    groups.extend(all_categories[category])
            return groups 