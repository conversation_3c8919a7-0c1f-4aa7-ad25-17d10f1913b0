#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
提交服务类，负责Alpha的检查和提交
"""

import time
import json
import pandas as pd
from typing import List, Tuple, Optional, Dict, Any
from collections import defaultdict

from .base_service import BaseService


class SubmissionService(BaseService):
    """提交服务类，提供Alpha检查和提交功能"""
    
    def __init__(self, config, auth_service, db_service=None):
        """
        初始化提交服务
        
        Args:
            config: CommonConfig 对象
            auth_service: 认证服务对象
            db_service: 数据库服务对象（可选）
        """
        super().__init__(config, auth_service)
        self.db_service = db_service
        
    def get_service_name(self) -> str:
        """获取服务名称"""
        return "SubmissionService"
    
    def check_submission_eligibility(self, alpha_ids: List[str], 
                                   start_index: int = 0) -> List[Tuple[str, float]]:
        """
        检查Alpha提交资格
        
        Args:
            alpha_ids: Alpha ID列表
            start_index: 开始检查的索引
            
        Returns:
            符合提交条件的Alpha列表，元组格式为(alpha_id, correlation)
        """
        self.logger.info(f"开始检查 {len(alpha_ids)} 个Alpha的提交资格")
        
        eligible_alphas = []
        nan_error_counts = defaultdict(int)
        session = self.get_session()
        
        for idx, alpha_id in enumerate(alpha_ids):
            if idx < start_index:
                continue
                
            if idx % 5 == 0:
                self.logger.info(f"检查进度: {idx}/{len(alpha_ids)}")
                
            if idx % 200 == 0:
                # 定期刷新session
                session = self.auth_service.login()
                
            # 检查相关性
            correlation = self._check_alpha_correlation(session, alpha_id)
            
            # 设置初始标签
            self._set_alpha_properties(session, alpha_id, name=alpha_id, tags=["checked"], color="YELLOW")
            
            if correlation == "sleep":
                time.sleep(100)
                session = self.auth_service.login()
                alpha_ids.append(alpha_id)  # 重新添加到队列
                continue
            elif correlation != correlation:  # NaN检查
                self.logger.warning(f"Alpha {alpha_id} 相关性检查返回NaN")
                nan_error_counts[alpha_id] += 1
                
                if nan_error_counts[alpha_id] <= 2:
                    self.logger.info(f"NaN错误第{nan_error_counts[alpha_id]}次，重试alpha: {alpha_id}")
                    time.sleep(100)
                    alpha_ids.append(alpha_id)  # 重新添加到队列
                else:
                    self.logger.info(f"Alpha {alpha_id} 已超过2次NaN错误限制，丢弃")
                    self._set_alpha_properties(session, alpha_id, tags=["checked"], color="BLUE")
                continue
            elif correlation == "fail":
                self._set_alpha_properties(session, alpha_id, tags=["checked"], color="RED")
                continue
            elif correlation == "error":
                self._set_alpha_properties(session, alpha_id, tags=["checked"], color="BLUE")
                continue
            else:
                # 检查通过
                self.logger.info(f"Alpha {alpha_id} 检查通过，相关性: {correlation}")
                eligible_alphas.append((alpha_id, correlation))
                self._set_alpha_properties(session, alpha_id, tags=["submittable"], color="GREEN")
                
                # 更新数据库
                if self.db_service:
                    self.db_service.mark_alpha_as_checked(alpha_id, self.config.decay, self.config)
        
        self.logger.info(f"检查完成，{len(eligible_alphas)} 个Alpha符合提交条件")
        return eligible_alphas
    
    def view_eligible_alphas(self, eligible_alphas: List[Tuple[str, float]]):
        """
        查看和显示符合条件的Alpha信息
        
        Args:
            eligible_alphas: 符合条件的Alpha列表
        """
        self.logger.info("获取符合提交条件的Alpha详细信息")
        
        session = self.get_session()
        alpha_details = []
        
        for alpha_id, correlation in eligible_alphas:
            alpha_info = self._get_alpha_details(session, alpha_id)
            if alpha_info:
                info = [
                    alpha_info[0],  # alpha_id
                    alpha_info[2],  # sharpe
                    alpha_info[3],  # turnover
                    alpha_info[4],  # fitness
                    alpha_info[5],  # margin
                    alpha_info[6],  # dateCreated
                    alpha_info[1],  # expression
                    correlation     # correlation
                ]
                alpha_details.append(info)
        
        # 按sharpe排序
        alpha_details.sort(reverse=True, key=lambda x: x[1])
        
        self.logger.info("符合提交条件的Alpha详情（按Sharpe排序）：")
        for info in alpha_details:
            self.logger.info(f"ID: {info[0]}, Sharpe: {info[1]:.4f}, "
                           f"Fitness: {info[3]:.4f}, Correlation: {info[7]:.4f}")
    
    def _check_alpha_correlation(self, session, alpha_id: str) -> any:
        """
        检查Alpha的相关性
        
        Args:
            session: HTTP会话
            alpha_id: Alpha ID
            
        Returns:
            相关性值、"sleep"、"fail"、"error"或None
        """
        url = f"https://api.worldquantbrain.com/alphas/{alpha_id}/check"
        
        # 等待检查结果
        while True:
            response = self.make_request('GET', url)
            if not response:
                return "error"
                
            if "retry-after" in response.headers:
                time.sleep(float(response.headers["Retry-After"]))
            else:
                break
        
        try:
            response_data = response.json()
            
            # 检查是否登录
            if response_data.get("is", 0) == 0:
                self.logger.info("会话已过期，需要重新登录")
                return "sleep"
            
            # 解析检查结果
            checks_list = response_data.get("is", {}).get("checks", [])
            if not checks_list:
                return "error"
                
            checks_df = pd.DataFrame(checks_list)
            
            # 查找PROD_CORRELATION检查
            correlation_checks = checks_df[checks_df['name'] == "PROD_CORRELATION"]
            if correlation_checks.empty:
                return "error"
                
            correlation_value = correlation_checks['value'].values[0]
            
            # 检查是否有失败项
            if any(checks_df["result"] == "FAIL"):
                return "fail"
            else:
                return correlation_value
                
        except Exception as e:
            self.logger.error(f"解析Alpha {alpha_id} 检查结果时出错: {str(e)}")
            return "error"
    
    def _set_alpha_properties(self, session, alpha_id: str, 
                             name: Optional[str] = None,
                             color: Optional[str] = None,
                             selection_desc: str = "None",
                             combo_desc: str = "None",
                             tags: Optional[List[str]] = None):
        """
        设置Alpha属性
        
        Args:
            session: HTTP会话
            alpha_id: Alpha ID
            name: Alpha名称
            color: 颜色标记
            selection_desc: 选择描述
            combo_desc: 组合描述
            tags: 标签列表
        """
        url = f"https://api.worldquantbrain.com/alphas/{alpha_id}"
        
        params = {
            "color": color,
            "name": name,
            "tags": tags,
            "category": None,
            "regular": {"description": None},
            "combo": {"description": combo_desc},
            "selection": {"description": selection_desc},
        }
        
        response = self.make_request('PATCH', url, json=params)
        if not response:
            self.logger.warning(f"设置Alpha {alpha_id} 属性失败")
    
    def _get_alpha_details(self, session, alpha_id: str) -> Optional[List[Any]]:
        """
        获取Alpha详细信息
        
        Args:
            session: HTTP会话
            alpha_id: Alpha ID
            
        Returns:
            Alpha信息列表或None
        """
        url = f"https://api.worldquantbrain.com/alphas/{alpha_id}"
        
        # 等待响应
        while True:
            response = self.make_request('GET', url)
            if not response:
                return None
                
            if "retry-after" in response.headers:
                time.sleep(float(response.headers["Retry-After"]))
            else:
                break
        
        try:
            alpha_data = response.json()
            
            return [
                alpha_id,
                alpha_data['regular']['code'],      # expression
                alpha_data["is"]["sharpe"],         # sharpe
                alpha_data["is"]["turnover"],       # turnover
                alpha_data["is"]["fitness"],        # fitness
                alpha_data["is"]["margin"],         # margin
                alpha_data["dateCreated"],          # dateCreated
                alpha_data["settings"]["decay"]     # decay
            ]
            
        except Exception as e:
            self.logger.error(f"获取Alpha {alpha_id} 详情时出错: {str(e)}")
            return None
    
    def mark_alphas_as_submitted(self, alpha_ids: List[str]):
        """
        标记Alpha为已提交状态
        
        Args:
            alpha_ids: Alpha ID列表
        """
        if not self.db_service:
            self.logger.warning("无数据库服务，无法标记Alpha为已提交")
            return
            
        for alpha_id in alpha_ids:
            self.db_service.mark_alpha_as_submitted(alpha_id, self.config.decay, self.config)
        
        self.logger.info(f"已标记 {len(alpha_ids)} 个Alpha为已提交状态")
    
    def get_submission_candidates(self, start_date: str, end_date: str) -> List[str]:
        """
        获取提交候选Alpha列表
        
        Args:
            start_date: 开始日期 (MM-DD)
            end_date: 结束日期 (MM-DD)
            
        Returns:
            Alpha ID列表
        """
        from .alpha_service import AlphaService
        
        # 创建Alpha服务实例
        alpha_service = AlphaService(self.config, self.auth_service, self.db_service)
        
        # 获取符合提交条件的Alpha
        alpha_records = alpha_service.get_alphas(
            start_date=start_date,
            end_date=end_date,
            alpha_num=self.config.alpha_num_submit,
            usage="submit",
            sharpe_th=self.config.submit_sharp_th,
            fitness_th=self.config.submit_fitness_th,
            margin_th=self.config.submit_margin_th,
            use_region=True
        )
        
        # 提取Alpha ID
        alpha_ids = [record[0] for record in alpha_records]
        
        # 更新数据库中的sharpe和fitness信息
        if self.db_service:
            for record in alpha_records:
                alpha_expr = str(record[1])
                decay = record[-1]
                self.db_service.save_alpha_to_db(alpha_expr, decay, self.config)
        
        self.logger.info(f"获得 {len(alpha_ids)} 个提交候选Alpha")
        return alpha_ids
    
    def process_submission_workflow(self, start_date: str, end_date: str) -> List[Tuple[str, float]]:
        """
        完整的提交工作流程
        
        Args:
            start_date: 开始日期 (MM-DD)
            end_date: 结束日期 (MM-DD)
            
        Returns:
            符合提交条件的Alpha列表
        """
        self.logger.info("开始执行完整的提交工作流程")
        
        # 1. 获取提交候选Alpha
        candidate_alphas = self.get_submission_candidates(start_date, end_date)
        
        if not candidate_alphas:
            self.logger.warning("未找到任何提交候选Alpha")
            return []
        
        # 2. 检查提交资格
        eligible_alphas = self.check_submission_eligibility(candidate_alphas)
        
        if not eligible_alphas:
            self.logger.warning("未找到任何符合提交条件的Alpha")
            return []
        
        # 3. 显示符合条件的Alpha
        self.view_eligible_alphas(eligible_alphas)
        
        # 4. 标记为已提交（如果有数据库）
        alpha_ids = [alpha_id for alpha_id, _ in eligible_alphas]
        self.mark_alphas_as_submitted(alpha_ids)
        
        self.logger.info(f"提交工作流程完成，找到 {len(eligible_alphas)} 个符合条件的Alpha")
        return eligible_alphas 