import os
import time
import json
import logging
from pathlib import Path
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import sys
import pandas as pd
import requests
from datetime import datetime

# 添加项目根目录到路径，以便导入common_auth
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common_auth import BrainAuth

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorldQuantForumScraper:
    """
    WorldQuant平台中文论坛爬虫类
    用于爬取WorldQuant中文论坛上的所有帖子
    """
    
    def __init__(self):
        self.base_url = "https://support.worldquantbrain.com/hc/en-us/community/topics/18910956638743-%E9%A1%BE%E9%97%AE%E4%B8%93%E5%B1%9E%E4%B8%AD%E6%96%87%E8%AE%BA%E5%9D%9B"
        self.data_dir = Path("data/worldquant_forum")
        self.setup_directories()
        self.setup_driver()
        
    def setup_directories(self):
        """创建必要的目录结构"""
        self.data_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"数据保存目录：{self.data_dir}")
        
    def setup_driver(self):
        """配置Selenium WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        # 增加用户代理以模拟正常浏览器
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        # 禁用JavaScript限制
        chrome_options.add_argument("--disable-web-security")
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.info("Chrome WebDriver初始化完成")
    
    def login(self):
        """使用common_auth登录WorldQuant平台"""
        try:
            logger.info("开始登录WorldQuant平台...")
            self.session = BrainAuth.login()
            
            # 确保登录成功，检查cookies
            cookies = self.session.cookies.get_dict()
            if not cookies:
                logger.error("登录失败：没有获取到cookies")
                return False
                
            logger.info(f"成功获取到 {len(cookies)} 个cookies")
            
            # 先访问主页，然后再添加cookies
            try:
                self.driver.set_page_load_timeout(60)  # 设置页面加载超时时间为60秒
                self.driver.get("https://platform.worldquantbrain.com")
                time.sleep(3)  # 等待页面加载
            except Exception as e:
                logger.warning(f"访问主页超时: {str(e)}")
                try:
                    self.driver.execute_script("window.stop();")  # 停止页面加载
                except:
                    pass
            
            # 清除任何现有cookies
            self.driver.delete_all_cookies()
            logger.info("已清除浏览器现有cookies")
            
            # 添加cookies到WebDriver
            added_cookies = 0
            for name, value in cookies.items():
                try:
                    cookie = {
                        'name': name,
                        'value': value,
                        'domain': '.worldquantbrain.com'  # 使用通配符域名
                    }
                    self.driver.add_cookie(cookie)
                    added_cookies += 1
                except Exception as cookie_error:
                    logger.warning(f"添加cookie '{name}'失败: {str(cookie_error)}")
            
            logger.info(f"成功添加了 {added_cookies}/{len(cookies)} 个cookies")
            
            # 刷新页面以应用cookies
            try:
                self.driver.refresh()
                time.sleep(3)  # 等待刷新完成
            except Exception as e:
                logger.warning(f"刷新页面超时: {str(e)}")
                try:
                    self.driver.execute_script("window.stop();")  # 停止页面加载
                except:
                    pass
            
            # 检查是否已登录
            try:
                page_source = self.driver.page_source.lower()
                if "login" in page_source and "password" in page_source:
                    logger.warning("可能未成功登录，页面仍然包含登录元素")
                    # 尝试手动执行API请求
                    try:
                        logger.info("尝试通过Selenium执行登录请求...")
                        username, password = BrainAuth.load_credentials()
                        login_script = f"""
                        fetch('https://api.worldquantbrain.com/authentication', {{
                            method: 'POST',
                            headers: {{'Content-Type': 'application/json'}},
                            credentials: 'include',
                            body: JSON.stringify({{username: '{username}', password: '{password}'}})
                        }})
                        .then(response => console.log('Login response:', response.status));
                        """
                        self.driver.execute_script(login_script)
                        time.sleep(5)  # 等待登录请求完成
                        self.driver.refresh()
                        time.sleep(3)
                    except Exception as script_error:
                        logger.error(f"执行登录脚本失败: {str(script_error)}")
            except Exception as e:
                logger.warning(f"检查登录状态时出错: {str(e)}")
            
            logger.info("已完成登录流程")
            
            # 为了确保同样的cookies对Zendesk论坛生效，尝试为support.worldquantbrain.com域名设置cookies
            try:
                logger.info("尝试为support.worldquantbrain.com域名添加cookies...")
                self.driver.get("https://support.worldquantbrain.com")
                time.sleep(3)
                
                # 添加Zendesk特定cookies或从请求中提取
                zendesk_response = self.session.get("https://support.worldquantbrain.com")
                if zendesk_response.status_code == 200:
                    zendesk_cookies = zendesk_response.cookies.get_dict()
                    logger.info(f"从Zendesk获取到 {len(zendesk_cookies)} 个cookies")
                    
                    for name, value in zendesk_cookies.items():
                        try:
                            cookie = {
                                'name': name,
                                'value': value,
                                'domain': '.worldquantbrain.com'
                            }
                            self.driver.add_cookie(cookie)
                        except Exception as e:
                            logger.warning(f"添加Zendesk cookie '{name}'失败: {str(e)}")
                
                # 刷新页面以应用cookies
                self.driver.refresh()
                time.sleep(3)
            except Exception as e:
                logger.warning(f"为Zendesk设置cookies时出错: {str(e)}")
            
            return True
        except Exception as e:
            logger.error(f"登录过程中出错: {str(e)}")
            return False

    def handle_cookie_consent(self):
        """处理cookie同意弹窗"""
        try:
            # 尝试多种可能的选择器来定位cookie弹窗的接受按钮
            cookie_selectors = [
                "button.cookie-consent__button",
                "button[class*='cookie-consent']",
                "button[class*='cookie']",
                "a[class*='cookie-consent']",
                ".cookie-consent__modal button",
                "div[class*='cookie'] button",
                "button[data-testid*='cookie']",
                "button[aria-label*='cookie']",
                "#accept-cookies",
                "#cookie-accept",
                ".cookie-banner button",
                "button:contains('Accept')",
                "button:contains('接受')",
                "button:contains('同意')",
                "button:contains('I accept')",
                "button:contains('Agree')"
            ]
            
            for selector in cookie_selectors:
                try:
                    # 等待cookie弹窗出现
                    logger.info(f"尝试定位cookie弹窗按钮: {selector}")
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        if element.is_displayed():
                            # 尝试点击按钮
                            logger.info(f"找到cookie弹窗按钮，尝试点击")
                            element.click()
                            time.sleep(1)
                            logger.info("已点击cookie同意按钮")
                            return True
                except Exception as e:
                    logger.warning(f"尝试选择器 {selector} 失败: {str(e)}")
            
            # 如果上面的方法都失败了，尝试使用JavaScript直接关闭弹窗
            logger.info("尝试使用JavaScript关闭cookie弹窗")
            js_scripts = [
                "document.querySelector('.cookie-consent__modal').style.display='none';",
                "document.querySelector('.cookie-consent__modal-wrapper').style.display='none';",
                "document.querySelector('div[class*=\"cookie\"]').style.display='none';",
                "document.querySelectorAll('div[class*=\"cookie\"]').forEach(el => el.style.display='none');",
                "document.querySelectorAll('.cookie-consent__modal, .cookie-banner, #cookie-banner').forEach(el => el.remove());"
            ]
            
            for script in js_scripts:
                try:
                    self.driver.execute_script(script)
                    logger.info("已执行JavaScript尝试关闭cookie弹窗")
                    time.sleep(1)
                except Exception as e:
                    logger.warning(f"执行JavaScript {script} 失败: {str(e)}")
            
            return False
        except Exception as e:
            logger.warning(f"处理cookie同意弹窗时出错: {str(e)}")
            return False
            
    def get_post_urls(self, page_url=None):
        """获取所有帖子的URL"""
        try:
            if page_url is None:
                page_url = self.base_url
                
            logger.info(f"获取帖子列表页: {page_url}")
            
            self.driver.get(page_url)
            time.sleep(3)  # 等待页面加载
            
            # 处理cookie弹窗
            self.handle_cookie_consent()
            
            # 等待页面加载完成
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 保存页面源码供调试
            html_file = self.data_dir / "forum_list_page.html"
            with open(html_file, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            logger.info(f"已保存论坛列表页面源码到 {html_file}")
            
            # 解析页面内容获取帖子链接
            soup = BeautifulSoup(self.driver.page_source, "html.parser")
            
            # 查找帖子链接 (这里的选择器可能需要根据实际页面结构调整)
            post_links = []
            
            # 尝试多种可能的帖子链接选择器
            post_selectors = [
                "ul.community-post-list a.community-post-title-link",  # 可能的选择器1
                "a.post-title",  # 可能的选择器2
                ".community-post-list a[href*='/posts/']",  # 可能的选择器3
                "a[href*='/posts/']",  # 更通用的选择器
                ".post-list a",  # 更通用的选择器
                "article a.title",  # 更通用的选择器
                ".article-list a",  # Zendesk常用选择器
                ".article-list-item a",  # Zendesk常用选择器 
                ".article-list h3 a"  # Zendesk常用选择器
            ]
            
            for selector in post_selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个帖子链接")
                    for element in elements:
                        href = element.get("href")
                        if href:
                            # 如果链接是相对URL，则转换为绝对URL
                            if href.startswith("/"):
                                href = f"https://support.worldquantbrain.com{href}"
                            post_links.append(href)
                    break
            
            if not post_links:
                # 如果上面的选择器都失败了，尝试更通用的方法
                logger.warning("使用预定义选择器未找到帖子链接，尝试通用方法")
                all_links = soup.find_all("a")
                for link in all_links:
                    href = link.get("href")
                    if href and ("/posts/" in href or "/community/" in href or "/articles/" in href):
                        # 如果链接是相对URL，则转换为绝对URL
                        if href.startswith("/"):
                            href = f"https://support.worldquantbrain.com{href}"
                        post_links.append(href)
            
            # 检查是否有下一页
            next_page_url = None
            next_page_selectors = [
                "a.pagination-next",
                "a.next",
                "a[rel='next']",
                ".pagination a:contains('Next')",
                ".pagination a:contains('下一页')"
            ]
            
            for selector in next_page_selectors:
                next_elements = soup.select(selector)
                if next_elements:
                    next_href = next_elements[0].get("href")
                    if next_href:
                        # 如果链接是相对URL，则转换为绝对URL
                        if next_href.startswith("/"):
                            next_href = f"https://support.worldquantbrain.com{next_href}"
                        next_page_url = next_href
                        logger.info(f"找到下一页链接: {next_page_url}")
                        break
            
            logger.info(f"当前页面找到 {len(post_links)} 个帖子链接")
            
            # 如果有下一页，递归获取下一页的帖子链接
            if next_page_url:
                next_page_links = self.get_post_urls(next_page_url)
                post_links.extend(next_page_links)
            
            # 去重
            post_links = list(set(post_links))
            logger.info(f"总共找到 {len(post_links)} 个帖子链接")
            
            # 保存帖子链接
            links_file = self.data_dir / "post_links.json"
            with open(links_file, "w", encoding="utf-8") as f:
                json.dump(post_links, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存帖子链接到 {links_file}")
            
            return post_links
        except Exception as e:
            logger.error(f"获取帖子链接失败: {str(e)}")
            return []
            
    def parse_post(self, url):
        """解析单个帖子的内容"""
        try:
            logger.info(f"解析帖子: {url}")
            
            self.driver.get(url)
            time.sleep(3)  # 等待页面加载
            
            # 处理cookie弹窗
            self.handle_cookie_consent()
            
            # 等待页面加载完成
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 解析帖子ID
            post_id = url.split("/")[-1]
            if not post_id.isdigit():
                post_id = f"post_{int(time.time())}"
            
            # 保存页面源码供调试
            post_dir = self.data_dir / "posts"
            post_dir.mkdir(exist_ok=True)
            html_file = post_dir / f"{post_id}.html"
            with open(html_file, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            logger.info(f"已保存帖子页面源码到 {html_file}")
            
            # 解析页面内容获取帖子信息
            soup = BeautifulSoup(self.driver.page_source, "html.parser")
            
            # 查找帖子标题
            title = ""
            title_selectors = [
                "h1.community-post-title",
                "h1.post-title",
                "h1",
                ".community-post-title",
                ".post-title",
                ".article-title",
                ".article-header h1"
            ]
            
            for selector in title_selectors:
                elements = soup.select(selector)
                if elements:
                    title = elements[0].get_text().strip()
                    logger.info(f"使用选择器 '{selector}' 找到帖子标题: {title}")
                    break
            
            # 查找帖子作者
            author = ""
            author_selectors = [
                ".community-post-meta .community-post-meta-item a",
                ".post-author a",
                ".author-name",
                ".user-name",
                ".community-post-header .name",
                ".article-author",
                ".article-meta .meta-data", 
                ".meta-data [data-test-id='article-author']"
            ]
            
            for selector in author_selectors:
                elements = soup.select(selector)
                if elements:
                    author = elements[0].get_text().strip()
                    logger.info(f"使用选择器 '{selector}' 找到帖子作者: {author}")
                    break
            
            # 查找发布日期
            date = ""
            date_selectors = [
                ".community-post-meta time",
                ".post-date",
                "time",
                ".created-at",
                ".publication-date",
                ".article-meta .meta-data time",
                ".meta-data"
            ]
            
            for selector in date_selectors:
                elements = soup.select(selector)
                if elements:
                    date_element = elements[0]
                    date_str = date_element.get_text().strip()
                    date_attr = date_element.get("datetime")
                    date = date_attr if date_attr else date_str
                    logger.info(f"使用选择器 '{selector}' 找到发布日期: {date}")
                    break
            
            # 查找帖子内容
            content = ""
            content_selectors = [
                ".community-post-body",
                ".post-content",
                ".post-body",
                ".content",
                "article .description",
                ".article-body",
                ".article-content"
            ]
            
            for selector in content_selectors:
                elements = soup.select(selector)
                if elements:
                    content = elements[0].get_text().strip()
                    logger.info(f"使用选择器 '{selector}' 找到帖子内容 ({len(content)} 字符)")
                    break
            
            # 查找评论
            comments = []
            comments_selectors = [
                ".community-comment",
                ".comment",
                ".community-comment-list .community-comment",
                ".comments-list .comment",
                ".article-comment",
                ".article-comments .comment"
            ]
            
            for selector in comments_selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个评论")
                    for element in elements:
                        comment_author = ""
                        comment_date = ""
                        comment_content = ""
                        
                        # 尝试提取评论作者
                        author_elements = element.select(".community-comment-meta .name, .author-name, .user-name, .comment-meta .name")
                        if author_elements:
                            comment_author = author_elements[0].get_text().strip()
                        
                        # 尝试提取评论日期
                        date_elements = element.select(".community-comment-meta time, .date, time, .created-at, .comment-meta time")
                        if date_elements:
                            date_element = date_elements[0]
                            date_str = date_element.get_text().strip()
                            date_attr = date_element.get("datetime")
                            comment_date = date_attr if date_attr else date_str
                        
                        # 尝试提取评论内容
                        content_elements = element.select(".community-comment-body, .comment-body, .content, .description, .comment-content")
                        if content_elements:
                            comment_content = content_elements[0].get_text().strip()
                        
                        comments.append({
                            "author": comment_author,
                            "date": comment_date,
                            "content": comment_content
                        })
                    
                    break
            
            # 组织帖子数据
            post_data = {
                "id": post_id,
                "url": url,
                "title": title,
                "author": author,
                "date": date,
                "content": content,
                "comments": comments,
                "scrape_time": datetime.now().isoformat()
            }
            
            # 保存帖子数据
            json_file = post_dir / f"{post_id}.json"
            with open(json_file, "w", encoding="utf-8") as f:
                json.dump(post_data, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存帖子数据到 {json_file}")
            
            return post_data
        except Exception as e:
            logger.error(f"解析帖子失败: {str(e)}")
            return {
                "id": url.split("/")[-1],
                "url": url,
                "title": "",
                "author": "",
                "date": "",
                "content": "",
                "comments": [],
                "error": str(e),
                "scrape_time": datetime.now().isoformat()
            }
    
    def run(self):
        """运行爬虫的主函数"""
        try:
            logger.info("开始运行论坛爬虫...")
            
            # 先登录
            if not self.login():
                logger.error("登录失败，爬虫终止")
                return []
            
            # 获取所有帖子链接
            post_urls = self.get_post_urls()
            
            if not post_urls:
                logger.error("未找到任何帖子链接，爬虫终止")
                return []
            
            # 解析每个帖子
            all_posts = []
            for i, url in enumerate(post_urls):
                logger.info(f"解析帖子 {i+1}/{len(post_urls)}: {url}")
                post_data = self.parse_post(url)
                all_posts.append(post_data)
                
                # 避免请求过于频繁
                time.sleep(3)
            
            # 保存所有帖子数据
            all_posts_file = self.data_dir / "all_posts.json"
            with open(all_posts_file, "w", encoding="utf-8") as f:
                json.dump(all_posts, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存所有帖子数据到 {all_posts_file}")
            
            # 转换为CSV格式
            try:
                # 准备CSV数据 (不包含评论，因为评论是嵌套结构)
                csv_data = []
                for post in all_posts:
                    item = {
                        "id": post.get("id", ""),
                        "url": post.get("url", ""),
                        "title": post.get("title", ""),
                        "author": post.get("author", ""),
                        "date": post.get("date", ""),
                        "content": post.get("content", ""),
                        "comment_count": len(post.get("comments", [])),
                        "scrape_time": post.get("scrape_time", "")
                    }
                    csv_data.append(item)
                
                if csv_data:
                    df = pd.DataFrame(csv_data)
                    csv_file = self.data_dir / "all_posts.csv"
                    df.to_csv(csv_file, index=False, encoding="utf-8")
                    logger.info(f"已保存CSV格式数据到 {csv_file}")
            except Exception as e:
                logger.error(f"转换为CSV格式失败: {str(e)}")
            
            # 创建评论CSV
            try:
                # 准备评论CSV数据
                comments_data = []
                for post in all_posts:
                    post_id = post.get("id", "")
                    post_title = post.get("title", "")
                    post_url = post.get("url", "")
                    
                    for i, comment in enumerate(post.get("comments", [])):
                        item = {
                            "post_id": post_id,
                            "post_title": post_title,
                            "post_url": post_url,
                            "comment_id": f"{post_id}_{i+1}",
                            "author": comment.get("author", ""),
                            "date": comment.get("date", ""),
                            "content": comment.get("content", "")
                        }
                        comments_data.append(item)
                
                if comments_data:
                    df = pd.DataFrame(comments_data)
                    csv_file = self.data_dir / "all_comments.csv"
                    df.to_csv(csv_file, index=False, encoding="utf-8")
                    logger.info(f"已保存评论CSV格式数据到 {csv_file}")
            except Exception as e:
                logger.error(f"转换评论为CSV格式失败: {str(e)}")
            
            logger.info(f"爬虫任务完成，共爬取 {len(all_posts)} 个帖子")
            return all_posts
        except Exception as e:
            logger.error(f"爬虫运行出错: {str(e)}")
            return []
        finally:
            self.driver.quit()
            logger.info("浏览器已关闭")

if __name__ == "__main__":
    scraper = WorldQuantForumScraper()
    posts = scraper.run()
    print(f"已爬取 {len(posts)} 个帖子") 