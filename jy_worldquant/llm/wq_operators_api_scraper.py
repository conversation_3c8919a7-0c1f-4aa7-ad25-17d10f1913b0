import os
import json
import logging
from pathlib import Path
import sys
import pandas as pd

# 添加项目根目录到路径，以便导入common_auth
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common_auth import BrainAuth

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorldQuantAPIOperatorScraper:
    """
    WorldQuant平台操作符API爬虫类
    专门用于通过API获取操作符信息并生成CSV文件
    """
    
    def __init__(self):
        self.data_dir = Path("data/worldquant_operators")
        self.setup_directories()
        
    def setup_directories(self):
        """创建必要的目录结构"""
        self.data_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"数据保存目录：{self.data_dir}")
        
    def login(self):
        """使用common_auth登录WorldQuant平台"""
        try:
            logger.info("开始登录WorldQuant平台...")
            self.session = BrainAuth.login()
            
            # 确保登录成功，检查cookies
            cookies = self.session.cookies.get_dict()
            if not cookies:
                logger.error("登录失败：没有获取到cookies")
                return False
                
            logger.info(f"成功获取到 {len(cookies)} 个cookies")
            logger.info("登录成功")
            return True
        except Exception as e:
            logger.error(f"登录过程中出错: {str(e)}")
            return False
            
    def extract_operators_api(self):
        """通过API获取操作符信息"""
        try:
            logger.info("尝试通过API获取操作符信息...")
            
            # 可能的API端点
            api_endpoints = [
                "https://api.worldquantbrain.com/learn/operators",
                "https://api.worldquantbrain.com/operators",
                "https://platform.worldquantbrain.com/api/operators",
                "https://api.worldquantbrain.com/operators-catalog"
            ]
            
            for endpoint in api_endpoints:
                try:
                    logger.info(f"尝试API端点: {endpoint}")
                    headers = {
                        "accept": "application/json;version=2.0",
                        "accept-language": "zh-CN,zh;q=0.9"
                    }
                    response = self.session.get(endpoint, headers=headers)
                    
                    if response.status_code == 200:
                        logger.info(f"API端点 {endpoint} 返回成功")
                        
                        # 尝试解析为JSON
                        try:
                            api_data = response.json()
                            # 保存原始API数据作为备份
                            api_file = self.data_dir / f"api_{endpoint.split('/')[-1]}.json"
                            with open(api_file, "w", encoding="utf-8") as f:
                                json.dump(api_data, f, ensure_ascii=False, indent=2)
                            logger.info(f"API数据已保存到 {api_file}")
                            return api_data
                        except ValueError as json_error:
                            logger.warning(f"API响应不是有效的JSON格式: {str(json_error)}")
                            # 保存为文本文件
                            text_file = self.data_dir / f"api_{endpoint.split('/')[-1]}.txt"
                            with open(text_file, "w", encoding="utf-8") as f:
                                f.write(response.text)
                            logger.info(f"API响应已保存到 {text_file}")
                    else:
                        logger.warning(f"API端点 {endpoint} 返回状态码: {response.status_code}")
                except Exception as e:
                    logger.warning(f"API端点 {endpoint} 请求失败: {str(e)}")
            
            logger.warning("所有API端点尝试失败")
            return None
        except Exception as e:
            logger.error(f"通过API获取操作符信息失败: {str(e)}")
            return None
    
    def process_api_data(self, api_data):
        """处理API返回的数据，转换为标准格式"""
        try:
            logger.info("开始处理API数据...")
            api_operators = []
            
            if api_data and isinstance(api_data, list):
                logger.info(f"API返回了 {len(api_data)} 个操作符")
                # 处理API返回的操作符数据
                for op in api_data:
                    if isinstance(op, dict) and "name" in op:
                        # 处理scope字段，确保它是字符串格式
                        scope = op.get("scope", [])
                        if isinstance(scope, list):
                            scope_str = str(scope)  # 转换为字符串格式
                        else:
                            scope_str = str(scope)
                            
                        api_operators.append({
                            "name": op.get("name", ""),
                            "description": op.get("description", ""),
                            "category": op.get("category", ""),
                            "definition": op.get("definition", ""),
                            "scope": scope_str,
                            "level": op.get("level", "")
                        })
                
                logger.info(f"成功处理了 {len(api_operators)} 个操作符")
                return api_operators
            else:
                logger.warning("API数据格式不符合预期")
                return []
        except Exception as e:
            logger.error(f"处理API数据失败: {str(e)}")
            return []
    
    def save_to_csv(self, operators):
        """将操作符数据保存为CSV文件"""
        try:
            if not operators:
                logger.warning("没有操作符数据可保存")
                return False
                
            logger.info(f"开始保存 {len(operators)} 个操作符到CSV文件...")
            
            # 转换为DataFrame
            df = pd.DataFrame(operators)
            
            # 保存为CSV文件
            csv_file = self.data_dir / "api_operators.csv"
            df.to_csv(csv_file, index=False, encoding="utf-8")
            logger.info(f"已成功保存CSV文件到: {csv_file}")
            
            # 显示数据统计信息
            logger.info(f"CSV文件包含 {len(df)} 行数据，{len(df.columns)} 列")
            logger.info(f"列名: {list(df.columns)}")
            
            return True
        except Exception as e:
            logger.error(f"保存CSV文件失败: {str(e)}")
            return False
    
    def run(self):
        """运行API爬虫的主函数"""
        try:
            logger.info("开始运行WorldQuant操作符API爬虫...")
            
            # 登录
            if not self.login():
                logger.error("登录失败，爬虫终止")
                return False
                
            # 通过API获取操作符数据
            api_data = self.extract_operators_api()
            
            if not api_data:
                logger.error("未能获取到API数据，爬虫终止")
                return False
            
            # 处理API数据
            operators = self.process_api_data(api_data)
            
            if not operators:
                logger.error("API数据处理失败，爬虫终止")
                return False
            
            # 保存为CSV文件
            if self.save_to_csv(operators):
                logger.info(f"爬虫运行成功！共获取 {len(operators)} 个操作符")
                return True
            else:
                logger.error("CSV文件保存失败")
                return False
                
        except Exception as e:
            logger.error(f"爬虫运行出错: {str(e)}")
            return False

def main():
    """主函数"""
    scraper = WorldQuantAPIOperatorScraper()
    success = scraper.run()
    
    if success:
        print("✅ API爬虫运行成功！")
        print(f"📁 数据已保存到: {scraper.data_dir}/api_operators.csv")
    else:
        print("❌ API爬虫运行失败！")
        print("请检查日志获取详细错误信息")

if __name__ == "__main__":
    main() 