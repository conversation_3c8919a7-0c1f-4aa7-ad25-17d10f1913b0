import os
import csv

input_root = "data/worldquant_datasets/by_category"
output_file = "all_merged.csv"

first_header = None
rows = []

for category in os.listdir(input_root):
    category_path = os.path.join(input_root, category)
    if not os.path.isdir(category_path):
        continue
    for fname in os.listdir(category_path):
        if not fname.lower().endswith('.csv'):
            continue
        file_path = os.path.join(category_path, fname)
        if not os.path.isfile(file_path):
            continue
        with open(file_path, "r", encoding="utf-8") as csvfile:
            reader = csv.reader(csvfile)
            try:
                header = next(reader)
                if first_header is None:
                    first_header = header
            except StopIteration:
                continue  # 空文件
            for row in reader:
                rows.append(row)

with open(output_file, "w", encoding="utf-8", newline='') as outcsv:
    writer = csv.writer(outcsv)
    if first_header:
        writer.writerow(first_header)
    for row in rows:
        writer.writerow(row)

print(f"合并完成，输出文件：{output_file}") 