import itertools
import os

# 价格字段 (P)
P = ["close", "open", "high", "low", "pv103_c_price", "pv103_o_price", "pv103_ctwp_mean", "pv103_ivwp_mean", 
     "pv104_lstp_mean", "pv104_lolp_mean", "pv104_lilp_last_all", "pv103_lstp_mean", "pv103_mtrd_mean"]

# 每股收益字段 (E)
E = ["pv87_2_eps_af_matrix_all_mean", "pv87_2_eps_af_matrix_all_median", "pv87_2_eps_af_matrix_all_high", "pv87_2_eps_qf_matrix_all_low", 
     "pv87_2_epsr_af_matrix_all_mean", "pv87_2_epsr_af_matrix_all_median", "pv87_2_epsr_qf_matrix_all_mean", "pv87_2_epsr_qf_matrix_all_median",
     "pv87_2_eps_af_matrix_p1_mean", "pv87_2_eps_af_matrix_p1_median", "pv87_2_eps_qf_matrix_p1_mean", "pv87_2_eps_qf_matrix_p1_median"]

# 增长率字段 (G)
G = ["pv87_2_eps_af_matrix_all_chngratio_mean", "pv87_2_eps_af_matrix_all_chngratio_median", "pv87_2_eps_af_matrix_p1_chngratio_mean", 
     "pv87_2_eps_af_matrix_p1_chngratio_median", "pv87_2_eps_qf_matrix_all_chngratio_mean", "pv87_2_eps_qf_matrix_all_chngratio_median", 
     "pv87_2_eps_qf_matrix_p1_chngratio_mean", "pv87_2_eps_qf_matrix_p1_chngratio_median", "pv87_2_sales_af_matrix_all_chngratio_mean",
     "pv87_2_sales_af_matrix_p1_chngratio_mean", "pv87_2_sales_qf_matrix_all_chngratio_mean", "pv87_2_bps_af_matrix_all_chngratio_mean",
     "pv87_2_capex_af_matrix_p1_chngratio_mean", "pv87_2_capex_af_matrix_p1_chngratio_mean"]

# 组内比较操作
group_compare_op = ["group_mean", "group_rank", "group_extra", "group_backfill", "group_scale", "group_zscore", "group_neutralize"]

# 跨截面比较操作 (从api_operators.csv中选择适合PEG计算的操作符)
cs_compare_op = [
    "divide",      # P/E / G (标准PEG比率)
    "subtract",    # P/E - G (差值方法)
    "add",         # P/E + G (加和方法)
    "multiply",    # P/E * G (乘积方法)
    "log",         # log(P/E / G) (对数方法)
    "abs",         # abs(P/E - G) (绝对差值)
    "greater",     # P/E > G (逻辑比较)
    "less",        # P/E < G (逻辑比较)
    "max",         # max(P/E, G) (最大值)
    "min"          # min(P/E, G) (最小值)
]

# 分组字段
group = ["currency", "exchange", "market", "industry", "country", "sector", "subindustry"]

# Alpha表达式模板
alpha_template = "<group_compare_op>(<cs_compare_op>(divide(<P>, <E>), <G>), <group>)"

def generate_alpha_expressions():
    """
    生成基于PEG比率的Alpha表达式
    """
    expressions = []
    
    # 选择核心字段组合以避免组合爆炸
    # 选择最具代表性的P, E, G字段
    core_P = ["close", "open", "pv103_ctwp_mean", "pv104_lstp_mean"]  # 4个核心价格字段
    core_E = ["pv87_2_eps_af_matrix_all_mean", "pv87_2_eps_af_matrix_p1_mean", 
              "pv87_2_epsr_af_matrix_all_mean", "pv87_2_eps_qf_matrix_all_mean"]  # 4个核心EPS字段
    core_G = ["pv87_2_eps_af_matrix_all_chngratio_mean", "pv87_2_eps_af_matrix_p1_chngratio_mean",
              "pv87_2_sales_af_matrix_all_chngratio_mean", "pv87_2_eps_qf_matrix_all_chngratio_mean"]  # 4个核心增长率字段
    
    # 选择核心操作符
    core_cs_ops = ["divide", "subtract", "log", "greater", "less"]  # 5个核心跨截面操作
    core_group_ops = ["group_zscore", "group_rank", "group_neutralize", "group_scale"]  # 4个核心组操作
    core_groups = ["industry", "sector", "market", "country"]  # 4个核心分组
    
    print("开始生成Alpha表达式...")
    
    # 生成所有组合
    for p in core_P:
        for e in core_E:
            for g in core_G:
                for cs_op in core_cs_ops:
                    for group_op in core_group_ops:
                        for grp in core_groups:
                            # 构建P/E部分
                            pe_ratio = f"divide({p}, {e})"
                            
                            # 构建跨截面比较部分
                            if cs_op == "divide":
                                cs_part = f"divide({pe_ratio}, {g})"
                            elif cs_op == "subtract":
                                cs_part = f"subtract({pe_ratio}, {g})"
                            elif cs_op == "log":
                                cs_part = f"log(divide({pe_ratio}, {g}))"
                            elif cs_op == "greater":
                                cs_part = f"greater({pe_ratio}, {g})"
                            elif cs_op == "less":
                                cs_part = f"less({pe_ratio}, {g})"
                            else:
                                cs_part = f"{cs_op}({pe_ratio}, {g})"
                            
                            # 构建完整表达式
                            expression = f"{group_op}({cs_part}, {grp})"
                            expressions.append(expression)
    
    print(f"总共生成了 {len(expressions)} 个Alpha表达式")
    return expressions

def save_expressions_to_file(expressions, filename="data/others/alpha_expressions_1.txt"):
    """
    将表达式保存到文件
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    
    with open(filename, 'w', encoding='utf-8') as f:
        for expr in expressions:
            f.write(expr + '\n')
    
    print(f"表达式已保存到 {filename}")

def main():
    """
    主函数：生成并保存Alpha表达式
    """
    print("=== WorldQuant Alpha机器 - PEG比率Alpha表达式生成器 ===")
    print()
    
    # 打印配置信息
    print("配置信息:")
    print(f"价格字段 (P): {len(P)} 个")
    print(f"EPS字段 (E): {len(E)} 个") 
    print(f"增长率字段 (G): {len(G)} 个")
    print(f"跨截面操作 (cs_compare_op): {len(cs_compare_op)} 个")
    print(f"组内操作 (group_compare_op): {len(group_compare_op)} 个")
    print(f"分组字段 (group): {len(group)} 个")
    print()
    
    # 生成表达式
    expressions = generate_alpha_expressions()
    
    # 保存到文件
    save_expressions_to_file(expressions)
    
    # 显示前10个表达式作为示例
    print("\n前10个生成的Alpha表达式示例:")
    print("-" * 80)
    for i, expr in enumerate(expressions[:10], 1):
        print(f"{i:2d}. {expr}")
    
    print(f"\n... (还有 {len(expressions)-10} 个表达式)")
    print("\n✅ Alpha表达式生成完成！")

if __name__ == "__main__":
    main()








