# WorldQuant平台文档爬虫

这个脚本用于爬取WorldQuant平台的文档内容，并将其保存到本地。

## 功能

- 自动登录WorldQuant平台
- 爬取文档页面上的所有分类和文章
- 保存HTML和文本格式的文章内容
- 生成包含元数据的JSON文件

## 前置条件

确保已安装以下Python包：
- requests
- selenium
- beautifulsoup4
- webdriver-manager

可以使用以下命令安装：
```bash
pip install -r ../requirements.txt
```

## 凭证设置

在项目根目录下创建`brain_credentials.txt`文件，包含JSON格式的用户名和密码：
```json
["your_username", "your_password"]
```

## 使用方法

1. 确保已配置好凭证
2. 运行爬虫脚本：
```bash
python wq_docs_scraper.py
```

## 输出

爬取的内容将保存在`data/worldquant_docs`目录下：
- 每个分类都有独立的子目录
- 文章内容以HTML和TXT格式保存
- 每个分类有一个metadata.json文件
- 根目录有一个all_articles.json汇总文件

## 注意事项

- 网站内容是动态加载的，爬虫通过Selenium模拟浏览器行为
- 如果遇到验证码或登录问题，可能需要手动登录
- 爬虫默认使用无头模式运行，如需查看浏览器操作，可移除"--headless"参数 