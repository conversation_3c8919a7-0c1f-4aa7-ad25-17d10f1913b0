# WorldQuant操作符API爬虫

这是一个简化版的WorldQuant操作符爬虫，专门用于通过API获取操作符信息并生成CSV文件。

## 功能特点

- ✅ 只通过API获取数据，无需浏览器
- ✅ 自动登录WorldQuant平台
- ✅ 生成标准的CSV格式文件
- ✅ 完整的错误处理和日志记录
- ✅ 轻量级，运行速度快

## 使用方法

### 1. 直接运行
```bash
python llm/wq_operators_api_scraper.py
```

### 2. 作为模块导入
```python
from llm.wq_operators_api_scraper import WorldQuantAPIOperatorScraper

scraper = WorldQuantAPIOperatorScraper()
success = scraper.run()
```

## 输出文件

运行成功后，会在 `data/worldquant_operators/` 目录下生成：

- `api_operators.csv` - 主要的CSV数据文件
- `api_operators.json` - 原始API数据备份（可选）

## CSV文件格式

生成的CSV文件包含以下列：

| 列名 | 描述 |
|------|------|
| name | 操作符名称 |
| description | 操作符描述 |
| category | 操作符分类 |
| definition | 操作符定义 |
| scope | 适用范围 |
| level | 级别 |

## 依赖要求

- pandas
- requests（通过common_auth）
- 有效的WorldQuant账户凭据

## 注意事项

1. 确保已正确配置WorldQuant登录凭据
2. 需要网络连接访问WorldQuant API
3. 如果API端点发生变化，可能需要更新脚本中的端点列表

## 错误处理

脚本包含完整的错误处理机制：
- 登录失败会自动终止
- API请求失败会尝试多个端点
- 数据处理错误会记录详细日志
- 文件保存失败会给出明确提示 