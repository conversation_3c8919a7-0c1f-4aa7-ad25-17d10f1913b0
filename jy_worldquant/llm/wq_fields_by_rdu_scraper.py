import os
import time
import json
import logging
import pandas as pd
from pathlib import Path
import sys
import csv

# 添加项目根目录到路径，以便导入common_auth
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common_auth import BrainAuth

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorldQuantFieldsByRDUScraper:
    def __init__(self):
        self.base_url = "https://api.worldquantbrain.com/data-fields"
        self.data_dir = Path("data/worldquant_fields_by_rdu")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.session = None
        self.combinations = self.load_combinations()

    def load_combinations(self):
        """从csv文件读取region/delay/universe组合"""
        csv_path = Path("data/region_delay_universe.csv")
        combinations = []
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                combinations.append((row['Region'], int(row['Delay']), row['Universe']))
        return combinations

    def load_categories(self):
        """从category.txt读取所有category"""
        txt_path = Path("data/category.txt")
        categories = []
        with open(txt_path, 'r', encoding='utf-8') as f:
            for line in f:
                cat = line.strip()
                if cat:
                    categories.append(cat)
        return categories

    def login(self):
        logger.info("登录WorldQuant平台...")
        self.session = BrainAuth.login()
        cookies = self.session.cookies.get_dict()
        if not cookies:
            logger.error("登录失败：没有获取到cookies")
            return False
        logger.info(f"成功获取到 {len(cookies)} 个cookies")
        return True

    def get_fields(self, region, delay, universe, category=None):
        """分页获取某个组合下的所有fields，可指定category"""
        all_fields = []
        page_size = 50
        offset = 0
        while True:
            params = {
                "delay": delay,
                "instrumentType": "EQUITY",
                "limit": page_size,
                "offset": offset,
                "region": region,
                "universe": universe
            }
            if category:
                params["category"] = category
            try:
                response = self.session.get(self.base_url, params=params, timeout=30)
                if response.status_code != 200:
                    logger.warning(f"API请求失败: {response.status_code}, {response.text}")
                    break
                data = response.json()
                results = data.get("results", [])
                if not results:
                    break
                all_fields.extend(results)
                if len(results) < page_size:
                    break
                offset += page_size
                time.sleep(1)
            except Exception as e:
                logger.error(f"获取fields出错: {str(e)}")
                break
        return all_fields

    def process_fields(self, fields):
        """提取关键信息"""
        processed = []
        for field in fields:
            item = {
                'id': field.get('id', ''),
                'description': field.get('description', ''),
                'type': field.get('type', ''),
                'dataset_id': field.get('dataset', {}).get('id', ''),
                'dataset_name': field.get('dataset', {}).get('name', ''),
                'category_id': field.get('category', {}).get('id', '')
            }
            processed.append(item)
        return processed

    def run(self):
        if not self.login():
            logger.error("登录失败，终止爬取")
            return
        categories = self.load_categories()
        for region, delay, universe in self.combinations:
            for category in categories:
                logger.info(f"开始爬取: {region}_{delay}_{universe}_{category}")
                fields = self.get_fields(region, delay, universe, category)
                processed = self.process_fields(fields)
                df = pd.DataFrame(processed)
                out_path = self.data_dir / f"{region}_{delay}_{universe}_{category}.csv"
                df.to_csv(out_path, index=False, encoding='utf-8')
                logger.info(f"已保存: {out_path}，共{len(df)}条记录")
        logger.info("全部爬取完成")

if __name__ == "__main__":
    scraper = WorldQuantFieldsByRDUScraper()
    scraper.run() 