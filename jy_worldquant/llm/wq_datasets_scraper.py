import os
import time
import json
import logging
from pathlib import Path
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import sys
import pandas as pd
import requests
from urllib.parse import urlparse, parse_qs

# 添加项目根目录到路径，以便导入common_auth
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common_auth import BrainAuth

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorldQuantDatasetScraper:
    """
    WorldQuant平台数据集爬虫类
    用于爬取WorldQuant平台上的数据集信息，并按Region、Delay、Universe分类
    """
    
    def __init__(self):
        self.base_url = "https://platform.worldquantbrain.com/data/data-sets"
        self.api_base_url = "https://api.worldquantbrain.com/pyramids"
        self.data_dir = Path("data/worldquant_datasets")
        self.setup_directories()
        self.setup_driver()
        
    def setup_directories(self):
        """创建必要的目录结构"""
        self.data_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"数据保存目录：{self.data_dir}")
        
    def setup_driver(self):
        """配置Selenium WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        # 增加用户代理以模拟正常浏览器
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
        # 禁用JavaScript限制
        chrome_options.add_argument("--disable-web-security")
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.info("Chrome WebDriver初始化完成")
        
    def login(self):
        """使用common_auth登录WorldQuant平台"""
        try:
            logger.info("开始登录WorldQuant平台...")
            self.session = BrainAuth.login()
            
            # 确保登录成功，检查cookies
            cookies = self.session.cookies.get_dict()
            if not cookies:
                logger.error("登录失败：没有获取到cookies")
                return False
                
            logger.info(f"成功获取到 {len(cookies)} 个cookies")
            
            # 先访问主页，然后再添加cookies
            try:
                self.driver.set_page_load_timeout(60)  # 设置页面加载超时时间为60秒
                self.driver.get("https://platform.worldquantbrain.com")
                time.sleep(3)  # 等待页面加载
            except Exception as e:
                logger.warning(f"访问主页超时: {str(e)}")
                try:
                    self.driver.execute_script("window.stop();")  # 停止页面加载
                except:
                    pass
            
            # 清除任何现有cookies
            self.driver.delete_all_cookies()
            logger.info("已清除浏览器现有cookies")
            
            # 添加cookies到WebDriver
            added_cookies = 0
            for name, value in cookies.items():
                try:
                    cookie = {
                        'name': name,
                        'value': value,
                        'domain': '.worldquantbrain.com'  # 使用通配符域名
                    }
                    self.driver.add_cookie(cookie)
                    added_cookies += 1
                except Exception as cookie_error:
                    logger.warning(f"添加cookie '{name}'失败: {str(cookie_error)}")
            
            logger.info(f"成功添加了 {added_cookies}/{len(cookies)} 个cookies")
            
            # 刷新页面以应用cookies
            try:
                self.driver.refresh()
                time.sleep(3)  # 等待刷新完成
            except Exception as e:
                logger.warning(f"刷新页面超时: {str(e)}")
                try:
                    self.driver.execute_script("window.stop();")  # 停止页面加载
                except:
                    pass
            
            # 检查是否已登录
            try:
                page_source = self.driver.page_source.lower()
                if "login" in page_source and "password" in page_source:
                    logger.warning("可能未成功登录，页面仍然包含登录元素")
                    # 尝试手动执行API请求
                    try:
                        logger.info("尝试通过Selenium执行登录请求...")
                        username, password = BrainAuth.load_credentials()
                        login_script = f"""
                        fetch('https://api.worldquantbrain.com/authentication', {{
                            method: 'POST',
                            headers: {{'Content-Type': 'application/json'}},
                            credentials: 'include',
                            body: JSON.stringify({{username: '{username}', password: '{password}'}})
                        }})
                        .then(response => console.log('Login response:', response.status));
                        """
                        self.driver.execute_script(login_script)
                        time.sleep(5)  # 等待登录请求完成
                        self.driver.refresh()
                        time.sleep(3)
                    except Exception as script_error:
                        logger.error(f"执行登录脚本失败: {str(script_error)}")
            except Exception as e:
                logger.warning(f"检查登录状态时出错: {str(e)}")
            
            logger.info("已完成登录流程")
            return True
        except Exception as e:
            logger.error(f"登录过程中出错: {str(e)}")
            return False
            
    def handle_cookie_consent(self):
        """处理cookie同意弹窗"""
        try:
            # 尝试多种可能的选择器来定位cookie弹窗的接受按钮
            cookie_selectors = [
                "button.cookie-consent__button",
                "button[class*='cookie-consent']",
                "button[class*='cookie']",
                "a[class*='cookie-consent']",
                ".cookie-consent__modal button",
                "div[class*='cookie'] button",
                "button[data-testid*='cookie']",
                "button[aria-label*='cookie']",
                "#accept-cookies",
                "#cookie-accept",
                ".cookie-banner button",
                "button:contains('Accept')",
                "button:contains('接受')",
                "button:contains('同意')",
                "button:contains('I accept')",
                "button:contains('Agree')"
            ]
            
            for selector in cookie_selectors:
                try:
                    # 等待cookie弹窗出现
                    logger.info(f"尝试定位cookie弹窗按钮: {selector}")
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    for element in elements:
                        if element.is_displayed():
                            # 尝试点击按钮
                            logger.info(f"找到cookie弹窗按钮，尝试点击")
                            element.click()
                            time.sleep(1)
                            logger.info("已点击cookie同意按钮")
                            return True
                except Exception as e:
                    logger.warning(f"尝试选择器 {selector} 失败: {str(e)}")
            
            # 如果上面的方法都失败了，尝试使用JavaScript直接关闭弹窗
            logger.info("尝试使用JavaScript关闭cookie弹窗")
            js_scripts = [
                "document.querySelector('.cookie-consent__modal').style.display='none';",
                "document.querySelector('.cookie-consent__modal-wrapper').style.display='none';",
                "document.querySelector('div[class*=\"cookie\"]').style.display='none';",
                "document.querySelectorAll('div[class*=\"cookie\"]').forEach(el => el.style.display='none');",
                "document.querySelectorAll('.cookie-consent__modal, .cookie-banner, #cookie-banner').forEach(el => el.remove());"
            ]
            
            for script in js_scripts:
                try:
                    self.driver.execute_script(script)
                    logger.info("已执行JavaScript尝试关闭cookie弹窗")
                    time.sleep(1)
                except Exception as e:
                    logger.warning(f"执行JavaScript {script} 失败: {str(e)}")
            
            return False
        except Exception as e:
            logger.warning(f"处理cookie同意弹窗时出错: {str(e)}")
            return False
            
    def get_filters(self):
        """获取可用的过滤条件（Region、Delay、Universe等）"""
        try:
            logger.info("开始获取过滤条件...")
            
            # 尝试通过API获取过滤条件
            filters = {
                "regions": [],
                "delays": [],
                "universes": [],
                "instrument_types": []
            }
            
            # 尝试通过API获取
            try:
                headers = {
                    "accept": "application/json;version=2.0",
                    "accept-language": "zh-CN,zh;q=0.9"
                }
                
                # 获取regions
                response = self.session.get("https://api.worldquantbrain.com/regions", headers=headers)
                if response.status_code == 200:
                    regions_data = response.json()
                    filters["regions"] = regions_data
                    logger.info(f"通过API获取到 {len(regions_data)} 个region")
                
                # 获取delays
                response = self.session.get("https://api.worldquantbrain.com/delays", headers=headers)
                if response.status_code == 200:
                    delays_data = response.json()
                    filters["delays"] = delays_data
                    logger.info(f"通过API获取到 {len(delays_data)} 个delay")
                
                # 获取universes
                response = self.session.get("https://api.worldquantbrain.com/universes", headers=headers)
                if response.status_code == 200:
                    universes_data = response.json()
                    filters["universes"] = universes_data
                    logger.info(f"通过API获取到 {len(universes_data)} 个universe")
                
                # 获取instrument types
                response = self.session.get("https://api.worldquantbrain.com/instrument-types", headers=headers)
                if response.status_code == 200:
                    instrument_types_data = response.json()
                    filters["instrument_types"] = instrument_types_data
                    logger.info(f"通过API获取到 {len(instrument_types_data)} 个instrument type")
                
                # 保存过滤条件
                filters_file = self.data_dir / "filters.json"
                with open(filters_file, "w", encoding="utf-8") as f:
                    json.dump(filters, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存过滤条件到 {filters_file}")
                
                return filters
            except Exception as e:
                logger.warning(f"通过API获取过滤条件失败: {str(e)}")
            
            # 如果API失败，尝试从网页提取
            logger.info("尝试从网页提取过滤条件...")
            
            try:
                # 导航到数据集页面
                self.driver.get(self.base_url)
                logger.info("已导航到数据集页面")
                
                # 处理cookie弹窗
                self.handle_cookie_consent()
                
                # 等待页面加载
                WebDriverWait(self.driver, 60).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # 等待一段时间让JavaScript内容加载
                time.sleep(5)
                
                # 保存页面截图和源码，帮助调试
                screenshot_path = self.data_dir / "filters_page_screenshot.png"
                self.driver.save_screenshot(str(screenshot_path))
                logger.info(f"过滤条件页面截图已保存至: {screenshot_path}")
                
                html_path = self.data_dir / "filters_page.html"
                with open(html_path, "w", encoding="utf-8") as f:
                    f.write(self.driver.page_source)
                logger.info(f"过滤条件页面源码已保存至: {html_path}")
                
                # 使用JavaScript提取过滤条件
                js_extract_filters = """
                function extractFilters() {
                    const filters = {
                        regions: [],
                        delays: [],
                        universes: [],
                        instrument_types: []
                    };
                    
                    // 尝试查找下拉菜单或选择器
                    const dropdowns = document.querySelectorAll('select, div[role="combobox"], div.dropdown');
                    dropdowns.forEach(dropdown => {
                        const options = dropdown.querySelectorAll('option, div[role="option"], div.option');
                        const values = Array.from(options).map(opt => opt.innerText.trim()).filter(v => v);
                        
                        // 根据内容或属性判断是哪种过滤器
                        const dropdownText = dropdown.innerText.toLowerCase();
                        if (dropdownText.includes('region')) {
                            filters.regions = values;
                        } else if (dropdownText.includes('delay')) {
                            filters.delays = values;
                        } else if (dropdownText.includes('universe')) {
                            filters.universes = values;
                        } else if (dropdownText.includes('instrument')) {
                            filters.instrument_types = values;
                        }
                    });
                    
                    return filters;
                }
                return JSON.stringify(extractFilters());
                """
                
                js_filters = self.driver.execute_script(js_extract_filters)
                web_filters = json.loads(js_filters)
                
                # 合并API和网页获取的过滤条件
                for key in filters:
                    if not filters[key] and web_filters.get(key):
                        filters[key] = web_filters[key]
                        logger.info(f"从网页提取到 {len(web_filters[key])} 个{key}")
                
                # 如果仍然没有获取到足够的过滤条件，设置默认值
                if not filters["regions"]:
                    filters["regions"] = ["NAM", "EUR", "ASI", "GLB"]
                    logger.info("使用默认region列表")
                    
                if not filters["delays"]:
                    filters["delays"] = [1, 2, 3, 4, 5, 10, 15]
                    logger.info("使用默认delay列表")
                    
                if not filters["universes"]:
                    filters["universes"] = ["TOP3000", "TOP1500", "MINVOL1M"]
                    logger.info("使用默认universe列表")
                    
                if not filters["instrument_types"]:
                    filters["instrument_types"] = ["EQUITY", "FUTURES"]
                    logger.info("使用默认instrument_type列表")
                
                # 保存最终的过滤条件
                final_filters_file = self.data_dir / "final_filters.json"
                with open(final_filters_file, "w", encoding="utf-8") as f:
                    json.dump(filters, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存最终过滤条件到 {final_filters_file}")
                
                return filters
            except Exception as e:
                logger.error(f"从网页提取过滤条件失败: {str(e)}")
                
                # 使用默认过滤条件
                filters = {
                    "regions": ["NAM", "EUR", "ASI", "GLB"],
                    "delays": [1, 2, 3, 4, 5, 10, 15],
                    "universes": ["TOP3000", "TOP1500", "MINVOL1M"],
                    "instrument_types": ["EQUITY", "FUTURES"]
                }
                logger.info("使用默认过滤条件")
                
                return filters
        except Exception as e:
            logger.error(f"获取过滤条件失败: {str(e)}")
            
            # 使用默认过滤条件
            filters = {
                "regions": ["NAM", "EUR", "ASI", "GLB"],
                "delays": [1, 2, 3, 4, 5, 10, 15],
                "universes": ["TOP3000", "TOP1500", "MINVOL1M"],
                "instrument_types": ["EQUITY", "FUTURES"]
            }
            logger.info("使用默认过滤条件")
            
            return filters
            
    def fetch_datasets_api(self, region, delay, universe, instrument_type="EQUITY"):
        """通过API获取特定过滤条件下的数据集"""
        try:
            logger.info(f"尝试通过API获取数据集 [region={region}, delay={delay}, universe={universe}, instrument_type={instrument_type}]")
            
            # 尝试不同的API端点
            api_endpoints = [
                f"{self.api_base_url}?region={region}&delay={delay}&universe={universe}&instrumentType={instrument_type}",
                f"https://api.worldquantbrain.com/data-sets?region={region}&delay={delay}&universe={universe}&instrumentType={instrument_type}",
                f"https://api.worldquantbrain.com/datasets?region={region}&delay={delay}&universe={universe}&instrumentType={instrument_type}"
            ]
            
            headers = {
                "accept": "application/json;version=2.0",
                "accept-language": "zh-CN,zh;q=0.9"
            }
            
            for endpoint in api_endpoints:
                try:
                    logger.info(f"尝试API端点: {endpoint}")
                    response = self.session.get(endpoint, headers=headers)
                    
                    # 创建目录结构，确保即使API失败也能保存响应
                    save_dir = self.data_dir / f"region_{region}" / f"delay_{delay}" / f"universe_{universe}"
                    save_dir.mkdir(parents=True, exist_ok=True)
                    
                    # 保存原始响应
                    raw_file = save_dir / f"{instrument_type.lower()}_{endpoint.split('/')[-1].split('?')[0]}_response.txt"
                    with open(raw_file, "w", encoding="utf-8") as f:
                        f.write(response.text)
                    logger.info(f"已保存API响应到 {raw_file}")
                    
                    if response.status_code == 200:
                        try:
                            response_data = response.json()
                            
                            # 检查是否为错误响应（有些API返回200但包含错误信息）
                            if isinstance(response_data, dict) and "detail" in response_data:
                                logger.warning(f"API返回错误: {response_data['detail']}")
                                continue
                            
                            # 处理可能的响应格式：1) 包含count和results的字典 2) 直接的数据集列表
                            datasets = []
                            if isinstance(response_data, dict) and "results" in response_data:
                                datasets = response_data.get("results", [])
                                count = response_data.get("count", 0)
                                logger.info(f"API返回了包含 {count} 个数据集的结果集")
                            elif isinstance(response_data, list):
                                datasets = response_data
                                logger.info(f"API直接返回了 {len(datasets)} 个数据集")
                            else:
                                # 如果响应是字典但不含results字段，可能是单个数据集
                                if isinstance(response_data, dict):
                                    datasets = [response_data]
                                    logger.info("API返回了单个数据集")
                            
                            # 检查是否为空列表或无效响应
                            if not datasets:
                                logger.warning("API返回空数据集")
                                continue
                            
                            logger.info(f"成功获取到 {len(datasets)} 个数据集")
                            
                            # 保存数据集数据
                            json_file = save_dir / f"{instrument_type.lower()}_datasets.json"
                            with open(json_file, "w", encoding="utf-8") as f:
                                json.dump(datasets, f, ensure_ascii=False, indent=2)
                            logger.info(f"已保存数据集到 {json_file}")
                            
                            # 转换为CSV格式
                            try:
                                # 提取主要字段，避免嵌套结构
                                csv_data = []
                                for ds in datasets:
                                    if not isinstance(ds, dict):
                                        continue
                                        
                                    item = {
                                        "id": ds.get("id", ""),
                                        "name": ds.get("name", ""),
                                        "description": ds.get("description", ""),
                                        "region": region,
                                        "delay": delay,
                                        "universe": universe,
                                        "instrument_type": instrument_type
                                    }
                                    
                                    # 添加类别和子类别
                                    if "category" in ds and isinstance(ds["category"], dict):
                                        item["category_id"] = ds["category"].get("id", "")
                                        item["category_name"] = ds["category"].get("name", "")
                                    
                                    if "subcategory" in ds and isinstance(ds["subcategory"], dict):
                                        item["subcategory_id"] = ds["subcategory"].get("id", "")
                                        item["subcategory_name"] = ds["subcategory"].get("name", "")
                                    
                                    # 添加其他顶级字段，但跳过复杂的嵌套结构
                                    for key, value in ds.items():
                                        if key not in item and key not in ["category", "subcategory", "themes", "researchPapers"] and not isinstance(value, (dict, list)):
                                            item[key] = value
                                    
                                    csv_data.append(item)
                                
                                if csv_data:
                                    df = pd.DataFrame(csv_data)
                                    csv_file = save_dir / f"{instrument_type.lower()}_datasets.csv"
                                    df.to_csv(csv_file, index=False, encoding="utf-8")
                                    logger.info(f"已保存CSV格式数据集到 {csv_file}")
                            except Exception as e:
                                logger.error(f"转换为CSV格式失败: {str(e)}")
                            
                            return datasets
                        except ValueError as json_err:
                            logger.warning(f"解析JSON失败: {str(json_err)}")
                    else:
                        logger.warning(f"API请求失败，状态码: {response.status_code}, 响应: {response.text[:100]}")
                except Exception as e:
                    logger.warning(f"API端点 {endpoint} 请求失败: {str(e)}")
            
            # 尝试解析HTML页面
            try:
                logger.info("尝试从HTML页面获取数据...")
                url = f"{self.base_url}?region={region}&delay={delay}&universe={universe}&instrumentType={instrument_type}"
                response = self.session.get(url, headers={"User-Agent": "Mozilla/5.0"})
                
                if response.status_code == 200:
                    # 保存HTML响应
                    html_file = save_dir / f"{instrument_type.lower()}_page.html"
                    with open(html_file, "w", encoding="utf-8") as f:
                        f.write(response.text)
                    logger.info(f"已保存HTML页面到 {html_file}")
                    
                    # 使用BeautifulSoup解析HTML
                    soup = BeautifulSoup(response.text, "html.parser")
                    
                    # 寻找可能包含数据集的表格
                    tables = soup.find_all("table")
                    if tables:
                        html_datasets = []
                        
                        for table in tables:
                            rows = table.find_all("tr")
                            for row in rows[1:]:  # 跳过表头
                                cells = row.find_all("td")
                                if len(cells) >= 2:
                                    dataset = {
                                        "name": cells[0].text.strip(),
                                        "description": cells[1].text.strip() if len(cells) > 1 else "",
                                        "region": region,
                                        "delay": delay,
                                        "universe": universe,
                                        "instrument_type": instrument_type
                                    }
                                    html_datasets.append(dataset)
                        
                        if html_datasets:
                            # 保存从HTML提取的数据集
                            html_json_file = save_dir / f"{instrument_type.lower()}_html_datasets.json"
                            with open(html_json_file, "w", encoding="utf-8") as f:
                                json.dump(html_datasets, f, ensure_ascii=False, indent=2)
                            logger.info(f"已保存从HTML提取的数据集到 {html_json_file}")
                            
                            return html_datasets
            except Exception as e:
                logger.error(f"从HTML获取数据集失败: {str(e)}")
            
            logger.warning("所有API端点尝试失败")
            return []
        except Exception as e:
            logger.error(f"通过API获取数据集失败: {str(e)}")
            return []
            
    def fetch_datasets_web(self, region, delay, universe, instrument_type="EQUITY"):
        """通过网页获取特定过滤条件下的数据集"""
        try:
            logger.info(f"尝试通过网页获取数据集 [region={region}, delay={delay}, universe={universe}, instrument_type={instrument_type}]")
            
            # 构建URL
            url = f"{self.base_url}?region={region}&delay={delay}&universe={universe}&instrumentType={instrument_type}"
            
            try:
                # 访问数据集页面
                self.driver.get(url)
                logger.info(f"已导航到数据集页面: {url}")
                
                # 处理cookie弹窗
                self.handle_cookie_consent()
                
                # 等待页面加载
                WebDriverWait(self.driver, 60).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # 等待一段时间让JavaScript内容加载
                time.sleep(5)
                
                # 创建目录结构
                save_dir = self.data_dir / f"region_{region}" / f"delay_{delay}" / f"universe_{universe}"
                save_dir.mkdir(parents=True, exist_ok=True)
                
                # 保存页面截图和源码，帮助调试
                screenshot_path = save_dir / f"{instrument_type.lower()}_page_screenshot.png"
                self.driver.save_screenshot(str(screenshot_path))
                logger.info(f"数据集页面截图已保存至: {screenshot_path}")
                
                html_path = save_dir / f"{instrument_type.lower()}_page.html"
                with open(html_path, "w", encoding="utf-8") as f:
                    f.write(self.driver.page_source)
                logger.info(f"数据集页面源码已保存至: {html_path}")
                
                # 使用JavaScript提取数据集信息
                js_extract_datasets = """
                function extractDatasets() {
                    // 尝试提取表格数据
                    const datasets = [];
                    const tableRows = document.querySelectorAll('table tr, div[role="row"], div[class*="row"]');
                    
                    tableRows.forEach((row, index) => {
                        // 跳过表头行
                        if (index === 0) return;
                        
                        const cells = row.querySelectorAll('td, th, div[role="cell"], div[class*="cell"]');
                        if (cells.length > 1) {
                            const dataset = {
                                name: cells[0]?.innerText?.trim() || '',
                                description: cells[1]?.innerText?.trim() || ''
                            };
                            
                            // 提取更多可能的字段
                            if (cells.length > 2) dataset.field3 = cells[2]?.innerText?.trim() || '';
                            if (cells.length > 3) dataset.field4 = cells[3]?.innerText?.trim() || '';
                            if (cells.length > 4) dataset.field5 = cells[4]?.innerText?.trim() || '';
                            
                            datasets.push(dataset);
                        }
                    });
                    
                    // 如果没有找到表格数据，尝试查找其他可能包含数据集的元素
                    if (datasets.length === 0) {
                        const items = document.querySelectorAll('div[class*="dataset"], div[class*="item"], div[class*="card"]');
                        items.forEach(item => {
                            const nameElem = item.querySelector('h3, h4, div[class*="title"]');
                            const descElem = item.querySelector('p, div[class*="description"]');
                            
                            if (nameElem) {
                                const dataset = {
                                    name: nameElem.innerText.trim(),
                                    description: descElem ? descElem.innerText.trim() : ''
                                };
                                datasets.push(dataset);
                            }
                        });
                    }
                    
                    return datasets;
                }
                return JSON.stringify(extractDatasets());
                """
                
                js_datasets = self.driver.execute_script(js_extract_datasets)
                datasets = json.loads(js_datasets)
                
                if datasets:
                    logger.info(f"从网页提取到 {len(datasets)} 个数据集")
                    
                    # 添加过滤条件信息
                    for ds in datasets:
                        ds["region"] = region
                        ds["delay"] = delay
                        ds["universe"] = universe
                        ds["instrument_type"] = instrument_type
                    
                    # 保存数据集数据
                    json_file = save_dir / f"{instrument_type.lower()}_web_datasets.json"
                    with open(json_file, "w", encoding="utf-8") as f:
                        json.dump(datasets, f, ensure_ascii=False, indent=2)
                    logger.info(f"已保存网页提取的数据集到 {json_file}")
                    
                    # 尝试转换为CSV格式
                    try:
                        df = pd.DataFrame(datasets)
                        csv_file = save_dir / f"{instrument_type.lower()}_web_datasets.csv"
                        df.to_csv(csv_file, index=False, encoding="utf-8")
                        logger.info(f"已保存CSV格式网页数据集到 {csv_file}")
                    except Exception as e:
                        logger.error(f"转换为CSV格式失败: {str(e)}")
                    
                    return datasets
                else:
                    logger.warning("未从网页提取到任何数据集")
                    return []
            except Exception as e:
                logger.error(f"通过网页获取数据集失败: {str(e)}")
                return []
        except Exception as e:
            logger.error(f"通过网页获取数据集失败: {str(e)}")
            return []
            
    def fetch_all_datasets(self):
        """获取所有数据集信息"""
        try:
            logger.info("开始获取所有数据集信息...")
            
            # 获取过滤条件
            filters = self.get_filters()
            
            # 创建用于存储所有数据集的字典
            all_datasets = {}
            total_count = 0
            
            # 遍历所有过滤条件组合
            for region in filters["regions"]:
                for delay in filters["delays"]:
                    for universe in filters["universes"]:
                        for instrument_type in filters["instrument_types"]:
                            # 生成组合的键名
                            key = f"{region}_{delay}_{universe}_{instrument_type}"
                            
                            # 先尝试通过API获取
                            api_datasets = self.fetch_datasets_api(region, delay, universe, instrument_type)
                            
                            # 如果API获取失败，尝试通过网页获取
                            if not api_datasets:
                                web_datasets = self.fetch_datasets_web(region, delay, universe, instrument_type)
                                if web_datasets:
                                    all_datasets[key] = web_datasets
                                    total_count += len(web_datasets)
                            else:
                                all_datasets[key] = api_datasets
                                total_count += len(api_datasets)
            
            logger.info(f"已获取所有数据集，共 {total_count} 个")
            
            # 保存所有数据集的摘要信息
            summary = {
                "total_count": total_count,
                "filter_combinations": len(all_datasets),
                "combinations": {key: len(datasets) for key, datasets in all_datasets.items()}
            }
            
            summary_file = self.data_dir / "datasets_summary.json"
            with open(summary_file, "w", encoding="utf-8") as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存数据集摘要信息到 {summary_file}")
            
            return all_datasets
        except Exception as e:
            logger.error(f"获取所有数据集失败: {str(e)}")
            return {}
    
    def run(self):
        """运行爬虫的主函数"""
        try:
            if not self.login():
                logger.error("登录失败，爬虫终止")
                return
            
            # 直接使用指定的组合参数
            logger.info("使用指定的过滤条件组合...")
            
            # 用户提供的所有组合
            combinations = [
                # Region, Delay, Universe
                ("USA", 1, "TOP3000"),
                ("USA", 1, "TOP1000"),
                ("USA", 1, "TOP500"),
                ("USA", 1, "TOP200"),
                ("USA", 1, "ILLIQUID_MINVOL1M"),
                ("USA", 1, "TOPSP500"),
                ("USA", 0, "TOP3000"),
                ("USA", 0, "TOP1000"),
                ("USA", 0, "TOP500"),
                ("USA", 0, "TOP200"),
                ("USA", 0, "ILLIQUID_MINVOL1M"),
                ("USA", 0, "TOPSP500"),
                ("GLB", 1, "MINVOL1M"),
                ("GLB", 1, "TOP3000"),
                ("EUR", 1, "TOP2500"),
                ("EUR", 1, "TOP1200"),
                ("EUR", 1, "TOP800"),
                ("EUR", 1, "TOP400"),
                ("EUR", 1, "ILLIQUID_MINVOL1M"),
                ("EUR", 0, "TOP2500"),
                ("EUR", 0, "TOP1200"),
                ("EUR", 0, "TOP800"),
                ("EUR", 0, "TOP400"),
                ("EUR", 0, "ILLIQUID_MINVOL1M"),
                ("ASI", 1, "MINVOL1M"),
                ("ASI", 1, "ILLIQUID_MINVOL1M"),
                ("CHN", 1, "TOP2000U"),
                ("CHN", 0, "TOP2000U")
            ]
            
            # 从组合中提取唯一的regions、delays和universes
            unique_regions = list(set(combo[0] for combo in combinations))
            unique_delays = list(set(combo[1] for combo in combinations))
            unique_universes = list(set(combo[2] for combo in combinations))
            
            # 为filters.json创建包含所有可能值的字典
            all_filters = {
                "regions": unique_regions,
                "delays": unique_delays,
                "universes": unique_universes,
                "instrument_types": ["EQUITY"]
            }
            
            # 保存预设的过滤条件
            filters_dir = self.data_dir / "filters"
            filters_dir.mkdir(parents=True, exist_ok=True)
            preset_filters_file = filters_dir / "all_filters.json"
            with open(preset_filters_file, "w", encoding="utf-8") as f:
                json.dump(all_filters, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存所有过滤条件到 {preset_filters_file}")
            
            # 创建按类别组织的目录结构
            categories_dir = self.data_dir / "by_category"
            categories_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建用于存储所有数据集的字典
            all_datasets = {}
            total_count = 0
            
            # 遍历所有指定的组合
            instrument_type = "EQUITY"  # 固定使用EQUITY
            for region, delay, universe in combinations:
                # 生成组合的键名
                key = f"{region}_{delay}_{universe}_{instrument_type}"
                
                # 通过API获取
                logger.info(f"尝试获取组合: [region={region}, delay={delay}, universe={universe}]")
                api_datasets = self.fetch_datasets_api(region, delay, universe, instrument_type)
                
                if api_datasets:
                    all_datasets[key] = api_datasets
                    total_count += len(api_datasets)
                    logger.info(f"成功获取 {region}/{delay}/{universe}/{instrument_type} 的数据集: {len(api_datasets)} 个")
                    
                    # 按类别组织数据集
                    self.organize_datasets_by_category(api_datasets, categories_dir, region, delay, universe, instrument_type)
                else:
                    logger.warning(f"未找到 {region}/{delay}/{universe}/{instrument_type} 的数据集")
            
            logger.info(f"最终获取数据集总数: {total_count}")
            
            # 保存所有数据集的摘要信息
            summary = {
                "total_count": total_count,
                "filter_combinations": len(all_datasets),
                "combinations": {key: len(datasets) for key, datasets in all_datasets.items()}
            }
            
            summary_file = self.data_dir / "datasets_summary.json"
            with open(summary_file, "w", encoding="utf-8") as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存数据集摘要信息到 {summary_file}")
            
            logger.info("爬虫任务完成")
            return all_datasets
        except Exception as e:
            logger.error(f"爬虫运行出错: {str(e)}")
            # 紧急情况下尝试保存当前页面
            try:
                self.driver.save_screenshot(str(self.data_dir / "error_screenshot.png"))
                with open(self.data_dir / "error_page.html", "w", encoding="utf-8") as f:
                    f.write(self.driver.page_source)
                logger.info("已保存错误页面截图和HTML源码")
            except:
                pass
            return {}
        finally:
            self.driver.quit()
            logger.info("浏览器已关闭")
            
    def organize_datasets_by_category(self, datasets, base_dir, region, delay, universe, instrument_type):
        """按类别组织数据集"""
        try:
            logger.info(f"开始按类别组织数据集...")
            
            # 按类别统计数据
            category_datasets = {}
            
            for ds in datasets:
                if not isinstance(ds, dict):
                    continue
                
                # 提取类别
                category = "未分类"
                if "category" in ds and isinstance(ds["category"], dict) and "name" in ds["category"]:
                    category = ds["category"]["name"]
                
                # 初始化类别列表
                if category not in category_datasets:
                    category_datasets[category] = []
                
                # 添加到对应类别
                category_datasets[category].append(ds)
            
            # 按类别保存数据集
            for category, ds_list in category_datasets.items():
                # 创建类别目录
                category_dir = base_dir / category
                category_dir.mkdir(parents=True, exist_ok=True)
                
                # 保存原始数据
                json_file = category_dir / f"{region}_{delay}_{universe}_{instrument_type.lower()}.json"
                with open(json_file, "w", encoding="utf-8") as f:
                    json.dump(ds_list, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存 {category} 类别的 {len(ds_list)} 个数据集到 {json_file}")
                
                # 保存CSV格式数据
                try:
                    # 提取主要字段，避免嵌套结构
                    csv_data = []
                    for ds in ds_list:
                        item = {
                            "id": ds.get("id", ""),
                            "name": ds.get("name", ""),
                            "description": ds.get("description", ""),
                            "region": region,
                            "delay": delay,
                            "universe": universe,
                            "instrument_type": instrument_type,
                            "category": category
                        }
                        
                        # 添加子类别
                        if "subcategory" in ds and isinstance(ds["subcategory"], dict):
                            item["subcategory_id"] = ds["subcategory"].get("id", "")
                            item["subcategory_name"] = ds["subcategory"].get("name", "")
                        
                        # 添加其他顶级字段，但跳过复杂的嵌套结构
                        for key, value in ds.items():
                            if key not in item and key not in ["category", "subcategory", "themes", "researchPapers"] and not isinstance(value, (dict, list)):
                                item[key] = value
                        
                        csv_data.append(item)
                    
                    if csv_data:
                        df = pd.DataFrame(csv_data)
                        csv_file = category_dir / f"{region}_{delay}_{universe}_{instrument_type.lower()}.csv"
                        df.to_csv(csv_file, index=False, encoding="utf-8")
                        logger.info(f"已保存 {category} 类别的CSV格式数据到 {csv_file}")
                except Exception as e:
                    logger.error(f"转换 {category} 类别为CSV格式失败: {str(e)}")
            
            # 按子类别进一步组织
            subcategory_datasets = {}
            
            for ds in datasets:
                if not isinstance(ds, dict):
                    continue
                
                # 提取类别和子类别
                category = "未分类"
                subcategory = "未分类"
                
                if "category" in ds and isinstance(ds["category"], dict) and "name" in ds["category"]:
                    category = ds["category"]["name"]
                
                if "subcategory" in ds and isinstance(ds["subcategory"], dict) and "name" in ds["subcategory"]:
                    subcategory = ds["subcategory"]["name"]
                
                # 组合键名
                key = f"{category}/{subcategory}"
                
                # 初始化子类别列表
                if key not in subcategory_datasets:
                    subcategory_datasets[key] = []
                
                # 添加到对应子类别
                subcategory_datasets[key].append(ds)
            
            # 按子类别保存数据集
            for key, ds_list in subcategory_datasets.items():
                # 创建子类别目录
                parts = key.split("/")
                category = parts[0]
                subcategory = parts[1] if len(parts) > 1 else "未分类"
                
                subcategory_dir = base_dir / category / subcategory
                subcategory_dir.mkdir(parents=True, exist_ok=True)
                
                # 保存原始数据
                json_file = subcategory_dir / f"{region}_{delay}_{universe}_{instrument_type.lower()}.json"
                with open(json_file, "w", encoding="utf-8") as f:
                    json.dump(ds_list, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存 {category}/{subcategory} 子类别的 {len(ds_list)} 个数据集到 {json_file}")
                
                # 保存CSV格式数据
                try:
                    # 提取主要字段，避免嵌套结构
                    csv_data = []
                    for ds in ds_list:
                        item = {
                            "id": ds.get("id", ""),
                            "name": ds.get("name", ""),
                            "description": ds.get("description", ""),
                            "region": region,
                            "delay": delay,
                            "universe": universe,
                            "instrument_type": instrument_type,
                            "category": category,
                            "subcategory": subcategory
                        }
                        
                        # 添加其他顶级字段，但跳过复杂的嵌套结构
                        for key, value in ds.items():
                            if key not in item and key not in ["category", "subcategory", "themes", "researchPapers"] and not isinstance(value, (dict, list)):
                                item[key] = value
                        
                        csv_data.append(item)
                    
                    if csv_data:
                        df = pd.DataFrame(csv_data)
                        csv_file = subcategory_dir / f"{region}_{delay}_{universe}_{instrument_type.lower()}.csv"
                        df.to_csv(csv_file, index=False, encoding="utf-8")
                        logger.info(f"已保存 {category}/{subcategory} 子类别的CSV格式数据到 {csv_file}")
                except Exception as e:
                    logger.error(f"转换 {category}/{subcategory} 子类别为CSV格式失败: {str(e)}")
            
            logger.info(f"数据集按类别和子类别组织完成")
        except Exception as e:
            logger.error(f"按类别组织数据集失败: {str(e)}")

if __name__ == "__main__":
    scraper = WorldQuantDatasetScraper()
    datasets = scraper.run()
    if datasets:
        total_count = sum(len(ds_list) for ds_list in datasets.values())
        print(f"已爬取 {total_count} 个数据集")
    else:
        print("爬取失败或未找到数据集") 