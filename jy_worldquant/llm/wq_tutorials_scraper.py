import os
import time
import json
import logging
import requests
from pathlib import Path
import sys
from datetime import datetime
import math
import html

# 添加项目根目录到路径，以便导入common_auth
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common_auth import BrainAuth

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WorldQuantTutorialsScraper:
    """
    WorldQuant平台教程爬虫类
    用于爬取WorldQuant平台上的所有教程文章并保存到本地
    """
    
    def __init__(self):
        self.tutorials_url = "https://api.worldquantbrain.com/tutorials"
        self.tutorial_page_url = "https://api.worldquantbrain.com/tutorial-pages/{}"
        self.data_dir = Path("data/worldquant_tutorials")
        self.max_retries = 5  # 最大重试次数
        self.retry_delay = 5  # 重试等待时间（秒）
        self.setup_directories()
        self.session = None
        
    def setup_directories(self):
        """创建必要的目录结构"""
        self.data_dir.mkdir(parents=True, exist_ok=True)
        # 创建调试目录
        (self.data_dir / "debug").mkdir(exist_ok=True)
        # 创建原始JSON目录
        (self.data_dir / "raw").mkdir(exist_ok=True)
        # 创建HTML目录
        (self.data_dir / "html").mkdir(exist_ok=True)
        # 创建Markdown目录
        (self.data_dir / "markdown").mkdir(exist_ok=True)
        logger.info(f"数据保存目录：{self.data_dir}")
        
    def login(self):
        """使用common_auth登录WorldQuant平台"""
        try:
            logger.info("开始登录WorldQuant平台...")
            self.session = BrainAuth.login()
            
            # 确保登录成功，检查cookies
            cookies = self.session.cookies.get_dict()
            if not cookies:
                logger.error("登录失败：没有获取到cookies")
                return False
                
            logger.info(f"成功获取到 {len(cookies)} 个cookies")
            
            # 测试API是否可访问
            try:
                logger.info("测试API访问")
                response = self.session.get(self.tutorials_url, timeout=30)
                if response.status_code == 200:
                    logger.info("API测试成功")
                    # 保存响应以供参考
                    with open(self.data_dir / "debug" / "api_test_response.json", "w", encoding="utf-8") as f:
                        json.dump(response.json(), f, ensure_ascii=False, indent=2)
                else:
                    logger.warning(f"API测试返回非200状态码: {response.status_code}, {response.text}")
            except Exception as e:
                logger.warning(f"API测试失败: {str(e)}")
            
            return True
        except Exception as e:
            logger.error(f"登录过程中出错: {str(e)}")
            return False
    
    def get_tutorials_list(self):
        """获取所有教程列表"""
        try:
            logger.info("获取教程列表")
            
            retry_count = 0
            while retry_count < self.max_retries:
                try:
                    response = self.session.get(self.tutorials_url, timeout=30)
                    
                    # 保存原始响应以便调试
                    debug_file = self.data_dir / "debug" / "tutorials_list_response.txt"
                    with open(debug_file, "w", encoding="utf-8") as f:
                        f.write(f"Status code: {response.status_code}\n")
                        f.write(f"Headers: {dict(response.headers)}\n")
                        f.write(f"Content: {response.text}\n")
                    
                    if response.status_code != 200:
                        error_msg = f"获取教程列表失败: 状态码={response.status_code}, 响应内容={response.text}"
                        logger.error(error_msg)
                        
                        # 根据错误类型采取相应措施
                        if response.status_code == 401 or response.status_code == 403:
                            # 身份验证问题，重新登录
                            logger.info("尝试重新登录")
                            if self.login():
                                logger.info("重新登录成功")
                            else:
                                logger.error("重新登录失败")
                                return None
                        else:
                            # 其他错误，常规重试
                            retry_count += 1
                            retry_delay = self.retry_delay * (retry_count + 1)
                            logger.warning(f"请求失败，等待 {retry_delay} 秒后重试 ({retry_count}/{self.max_retries})")
                            time.sleep(retry_delay)
                            continue
                    else:
                        # 成功获取数据
                        try:
                            data = response.json()
                            
                            # 保存原始响应内容
                            raw_file = self.data_dir / "raw" / "tutorials_list.json"
                            with open(raw_file, "w", encoding="utf-8") as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)
                                
                            logger.info(f"成功获取教程列表，共 {len(data.get('results', []))} 个教程系列")
                            return data
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析错误: {str(e)}, 响应内容: {response.text[:200]}...")
                            retry_count += 1
                            time.sleep(self.retry_delay)
                            continue
                except requests.exceptions.RequestException as e:
                    logger.error(f"请求异常: {str(e)}")
                    retry_count += 1
                    retry_delay = self.retry_delay * (retry_count + 1)
                    logger.warning(f"网络请求失败，等待 {retry_delay} 秒后重试 ({retry_count}/{self.max_retries})")
                    time.sleep(retry_delay)
            
            logger.error(f"获取教程列表失败，已达到最大重试次数")
            return None
        except Exception as e:
            logger.error(f"获取教程列表时出错: {str(e)}")
            return None
    
    def get_tutorial_page(self, page_id):
        """获取单个教程页面内容"""
        try:
            url = self.tutorial_page_url.format(page_id)
            logger.info(f"获取教程页面: ID={page_id}")
            
            retry_count = 0
            while retry_count < self.max_retries:
                try:
                    response = self.session.get(url, timeout=30)
                    
                    # 保存原始响应以便调试
                    debug_file = self.data_dir / "debug" / f"page_response_{page_id}.txt"
                    with open(debug_file, "w", encoding="utf-8") as f:
                        f.write(f"Status code: {response.status_code}\n")
                        f.write(f"Headers: {dict(response.headers)}\n")
                        f.write(f"Content: {response.text}\n")
                    
                    if response.status_code != 200:
                        logger.error(f"获取教程页面失败: ID={page_id}, 状态码={response.status_code}, 响应内容={response.text}")
                        
                        # 根据错误类型采取相应措施
                        if response.status_code == 401 or response.status_code == 403:
                            # 身份验证问题，重新登录
                            logger.info("尝试重新登录")
                            if self.login():
                                logger.info("重新登录成功")
                            else:
                                logger.error("重新登录失败")
                                return None
                        elif response.status_code == 404:
                            # 页面不存在
                            logger.warning(f"教程页面不存在: ID={page_id}")
                            return None
                        else:
                            # 其他错误，常规重试
                            retry_count += 1
                            retry_delay = self.retry_delay * (retry_count + 1)
                            logger.warning(f"请求失败，等待 {retry_delay} 秒后重试 ({retry_count}/{self.max_retries})")
                            time.sleep(retry_delay)
                            continue
                    else:
                        # 成功获取数据
                        try:
                            data = response.json()
                            
                            # 保存原始响应内容
                            raw_dir = self.data_dir / "raw" / "pages"
                            raw_dir.mkdir(exist_ok=True)
                            raw_file = raw_dir / f"{page_id}.json"
                            with open(raw_file, "w", encoding="utf-8") as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)
                            
                            logger.info(f"成功获取教程页面: ID={page_id}, 标题={data.get('title', '')}")
                            return data
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析错误: {str(e)}, 响应内容: {response.text[:200]}...")
                            retry_count += 1
                            time.sleep(self.retry_delay)
                            continue
                except requests.exceptions.RequestException as e:
                    logger.error(f"请求异常: {str(e)}")
                    retry_count += 1
                    retry_delay = self.retry_delay * (retry_count + 1)
                    logger.warning(f"网络请求失败，等待 {retry_delay} 秒后重试 ({retry_count}/{self.max_retries})")
                    time.sleep(retry_delay)
            
            logger.error(f"获取教程页面失败，已达到最大重试次数")
            return None
        except Exception as e:
            logger.error(f"获取教程页面时出错: ID={page_id}, 错误={str(e)}")
            return None
    
    def content_to_html(self, page_data):
        """将教程页面内容转换为HTML格式"""
        try:
            if not page_data or "content" not in page_data:
                logger.warning(f"无效的页面数据，无法转换为HTML")
                return None
            
            html_content = [f"<h1>{page_data['title']}</h1>"]
            html_content.append(f"<p>最后修改时间: {page_data['lastModified']}</p>")
            html_content.append(f"<p>ID: {page_data['id']}</p>")
            html_content.append("<hr>")
            
            for item in page_data["content"]:
                item_type = item.get("type", "")
                item_value = item.get("value", "")
                
                if item_type == "TEXT":
                    html_content.append(item_value)
                elif item_type == "HEADING":
                    level = item_value.get("level", "1")
                    content = item_value.get("content", "")
                    html_content.append(f"<h{level}>{content}</h{level}>")
                elif item_type == "IMAGE":
                    url = item_value.get("url", "")
                    title = item_value.get("title", "")
                    width = item_value.get("width", "")
                    height = item_value.get("height", "")
                    html_content.append(f'<img src="{url}" alt="{title}" title="{title}" width="{width}" height="{height}">')
                elif item_type == "EQUATION":
                    html_content.append(f'<div class="equation">{item_value}</div>')
                elif item_type == "SIMULATION_EXAMPLE":
                    html_content.append("<div class='simulation-example'>")
                    html_content.append("<h3>模拟示例</h3>")
                    settings = item_value.get("settings", {})
                    html_content.append("<ul>")
                    for key, value in settings.items():
                        html_content.append(f"<li>{key}: {value}</li>")
                    html_content.append("</ul>")
                    
                    if "regular" in item_value:
                        html_content.append(f"<pre><code>{item_value['regular']}</code></pre>")
                    
                    html_content.append("</div>")
                else:
                    html_content.append(f"<div class='unknown-type'>{item_type}: {json.dumps(item_value, ensure_ascii=False)}</div>")
            
            return "\n".join(html_content)
        except Exception as e:
            logger.error(f"转换页面为HTML时出错: {str(e)}")
            return None
    
    def content_to_markdown(self, page_data):
        """将教程页面内容转换为Markdown格式"""
        try:
            if not page_data or "content" not in page_data:
                logger.warning(f"无效的页面数据，无法转换为Markdown")
                return None
            
            md_content = [f"# {page_data['title']}"]
            md_content.append(f"最后修改时间: {page_data['lastModified']}")
            md_content.append(f"ID: {page_data['id']}")
            md_content.append("---")
            
            for item in page_data["content"]:
                item_type = item.get("type", "")
                item_value = item.get("value", "")
                
                if item_type == "TEXT":
                    # 去除HTML标签
                    text = item_value.replace("<p>", "").replace("</p>", "\n\n")
                    # 保留链接
                    text = text.replace("<a href=\"", "[").replace("\">", "](").replace("</a>", ")")
                    # 保留斜体
                    text = text.replace("<i>", "*").replace("</i>", "*")
                    md_content.append(text)
                elif item_type == "HEADING":
                    level = item_value.get("level", "1")
                    content = item_value.get("content", "")
                    md_content.append(f"{'#' * int(level)} {content}")
                elif item_type == "IMAGE":
                    url = item_value.get("url", "")
                    title = item_value.get("title", "")
                    md_content.append(f"![{title}]({url})")
                elif item_type == "EQUATION":
                    md_content.append(f"$$\n{item_value}\n$$")
                elif item_type == "SIMULATION_EXAMPLE":
                    md_content.append("### 模拟示例")
                    settings = item_value.get("settings", {})
                    for key, value in settings.items():
                        md_content.append(f"- {key}: {value}")
                    
                    if "regular" in item_value:
                        md_content.append(f"```\n{item_value['regular']}\n```")
                else:
                    md_content.append(f"*{item_type}*: {json.dumps(item_value, ensure_ascii=False)}")
                
                # 添加空行
                md_content.append("")
            
            return "\n".join(md_content)
        except Exception as e:
            logger.error(f"转换页面为Markdown时出错: {str(e)}")
            return None
    
    def save_tutorial_page(self, page_data):
        """保存教程页面内容到文件"""
        try:
            if not page_data or "id" not in page_data:
                logger.warning(f"无效的页面数据，跳过保存")
                return False
            
            page_id = page_data["id"]
            title = page_data["title"]
            
            # 保存为HTML
            html_content = self.content_to_html(page_data)
            if html_content:
                html_file = self.data_dir / "html" / f"{page_id}.html"
                with open(html_file, "w", encoding="utf-8") as f:
                    f.write(html_content)
                logger.info(f"已保存HTML文件: {html_file}")
            
            # 保存为Markdown
            md_content = self.content_to_markdown(page_data)
            if md_content:
                md_file = self.data_dir / "markdown" / f"{page_id}.md"
                with open(md_file, "w", encoding="utf-8") as f:
                    f.write(md_content)
                logger.info(f"已保存Markdown文件: {md_file}")
            
            return True
        except Exception as e:
            logger.error(f"保存教程页面时出错: {str(e)}")
            return False
    
    def create_index_file(self, tutorials_data):
        """创建索引文件"""
        try:
            if not tutorials_data or "results" not in tutorials_data:
                logger.warning(f"无效的教程数据，无法创建索引")
                return False
            
            # 创建HTML索引
            html_index = ["<html><head><title>WorldQuant教程索引</title></head><body>"]
            html_index.append("<h1>WorldQuant教程索引</h1>")
            
            # 创建Markdown索引
            md_index = ["# WorldQuant教程索引"]
            
            for tutorial in tutorials_data["results"]:
                tutorial_id = tutorial.get("id", "")
                tutorial_title = tutorial.get("title", "")
                tutorial_category = tutorial.get("category", "")
                
                html_index.append(f"<h2>{tutorial_title} ({tutorial_category})</h2>")
                html_index.append("<ul>")
                
                md_index.append(f"## {tutorial_title} ({tutorial_category})")
                md_index.append("")
                
                for page in tutorial.get("pages", []):
                    page_id = page.get("id", "")
                    page_title = page.get("title", "")
                    
                    html_index.append(f'<li><a href="html/{page_id}.html">{page_title}</a></li>')
                    md_index.append(f"- [{page_title}](markdown/{page_id}.md)")
                
                html_index.append("</ul>")
                md_index.append("")
            
            html_index.append("</body></html>")
            
            # 保存HTML索引
            with open(self.data_dir / "index.html", "w", encoding="utf-8") as f:
                f.write("\n".join(html_index))
            
            # 保存Markdown索引
            with open(self.data_dir / "index.md", "w", encoding="utf-8") as f:
                f.write("\n".join(md_index))
            
            logger.info(f"已创建索引文件")
            return True
        except Exception as e:
            logger.error(f"创建索引文件时出错: {str(e)}")
            return False
    
    def run(self):
        """运行爬虫的主函数"""
        try:
            logger.info("开始运行WorldQuant教程爬虫...")
            
            # 登录平台
            if not self.login():
                logger.error("登录失败，爬虫终止")
                return False
            
            # 获取教程列表
            tutorials_data = self.get_tutorials_list()
            if not tutorials_data:
                logger.error("获取教程列表失败，爬虫终止")
                return False
            
            total_tutorials = len(tutorials_data.get("results", []))
            total_pages = sum(len(tutorial.get("pages", [])) for tutorial in tutorials_data.get("results", []))
            
            logger.info(f"开始爬取 {total_tutorials} 个教程系列，共 {total_pages} 个页面")
            
            processed_pages = 0
            failed_pages = []
            
            # 遍历教程列表
            for tutorial in tutorials_data.get("results", []):
                tutorial_id = tutorial.get("id", "")
                tutorial_title = tutorial.get("title", "")
                
                logger.info(f"处理教程系列: {tutorial_title} (ID: {tutorial_id})")
                
                # 遍历页面
                for page in tutorial.get("pages", []):
                    page_id = page.get("id", "")
                    page_title = page.get("title", "")
                    
                    logger.info(f"爬取页面: {page_title} (ID: {page_id})")
                    
                    # 获取页面内容
                    page_data = self.get_tutorial_page(page_id)
                    if not page_data:
                        logger.error(f"获取页面内容失败: {page_id}")
                        failed_pages.append(page_id)
                        continue
                    
                    # 保存页面
                    if self.save_tutorial_page(page_data):
                        processed_pages += 1
                        logger.info(f"处理进度: {processed_pages}/{total_pages}")
                    else:
                        logger.error(f"保存页面失败: {page_id}")
                        failed_pages.append(page_id)
                    
                    # 随机延迟避免频繁请求
                    time.sleep(1 + (hash(page_id) % 3))
            
            # 创建索引文件
            self.create_index_file(tutorials_data)
            
            if failed_pages:
                logger.warning(f"爬虫任务完成，但有 {len(failed_pages)} 个页面失败: {failed_pages}")
            else:
                logger.info(f"爬虫任务完成，成功爬取 {processed_pages} 个页面")
                
            return True
        except Exception as e:
            logger.error(f"爬虫运行出错: {str(e)}")
            return False

if __name__ == "__main__":
    scraper = WorldQuantTutorialsScraper()
    scraper.run() 