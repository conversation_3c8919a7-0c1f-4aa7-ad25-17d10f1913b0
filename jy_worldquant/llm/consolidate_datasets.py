#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
脚本用于收集并整合WorldQuant所有区域数据集信息
生成包含id、name、description和category字段的CSV文件
"""

import os
import pandas as pd
import glob

# 输出文件路径
OUTPUT_FILE = "worldquant_datasets_consolidated.csv"

# 数据集根目录
DATASETS_ROOT = "data/worldquant_datasets"

# 要提取的字段
FIELDS_TO_KEEP = ["id", "name", "description", "category_name"]

def consolidate_datasets():
    """
    收集并整合所有区域的数据集信息
    """
    print("开始收集数据集信息...")
    
    # 用于存储所有数据集记录
    all_records = []
    
    # 查找所有区域目录
    region_dirs = glob.glob(os.path.join(DATASETS_ROOT, "region_*"))
    
    for region_dir in region_dirs:
        region_name = os.path.basename(region_dir)
        print(f"处理区域: {region_name}")
        
        # 查找该区域下的所有延迟目录
        delay_dirs = glob.glob(os.path.join(region_dir, "delay_*"))
        
        for delay_dir in delay_dirs:
            delay_value = os.path.basename(delay_dir)
            print(f"  处理延迟: {delay_value}")
            
            # 查找该延迟下的所有宇宙目录
            universe_dirs = glob.glob(os.path.join(delay_dir, "universe_*"))
            
            for universe_dir in universe_dirs:
                universe_name = os.path.basename(universe_dir)
                print(f"    处理宇宙: {universe_name}")
                
                # 查找CSV文件
                csv_files = glob.glob(os.path.join(universe_dir, "*_datasets.csv"))
                
                for csv_file in csv_files:
                    instrument_type = os.path.basename(csv_file).split("_")[0]
                    print(f"      处理文件: {os.path.basename(csv_file)} (类型: {instrument_type})")
                    
                    try:
                        # 读取CSV文件
                        df = pd.read_csv(csv_file)
                        
                        # 过滤需要的字段，如果某些字段不存在则跳过
                        if not all(field in df.columns for field in FIELDS_TO_KEEP):
                            missing_fields = [field for field in FIELDS_TO_KEEP if field not in df.columns]
                            print(f"        警告: 文件缺少字段 {missing_fields}，尝试继续处理...")
                            continue
                            
                        # 只保留需要的字段
                        df = df[FIELDS_TO_KEEP]
                        
                        # 将数据添加到总列表中
                        all_records.append(df)
                        
                        print(f"        成功添加 {len(df)} 条记录")
                        
                    except Exception as e:
                        print(f"        处理文件 {csv_file} 时出错: {e}")
    
    # 合并所有DataFrame
    print("合并所有数据集...")
    if not all_records:
        print("错误: 没有找到有效的数据集记录")
        return
        
    combined_df = pd.concat(all_records, ignore_index=True)
    
    # 去重（基于id字段）
    print("进行数据去重...")
    deduplicated_df = combined_df.drop_duplicates(subset=['id'])
    
    print(f"共收集到 {len(deduplicated_df)} 条唯一数据集记录")
    
    # 写入CSV文件
    print(f"写入到文件 {OUTPUT_FILE}...")
    deduplicated_df.to_csv(OUTPUT_FILE, index=False, quoting=1)  # quoting=1使用QUOTE_ALL模式
    
    print("数据集整合完成!")

if __name__ == "__main__":
    consolidate_datasets() 