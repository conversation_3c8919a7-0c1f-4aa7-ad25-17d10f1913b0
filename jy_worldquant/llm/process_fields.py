#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import csv
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def process_field_file(file_path):
    """处理单个字段文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if not isinstance(data, list):
                logging.warning(f"文件 {file_path} 不是有效的字段列表")
                return []
            
            fields_data = []
            for field in data:
                try:
                    field_info = {
                        'id': field.get('id', ''),
                        'description': field.get('description', ''),
                        'type': field.get('type', ''),
                        'dataset_id': field.get('dataset', {}).get('id', ''),
                        'dataset_name': field.get('dataset', {}).get('name', ''),
                        'category_id': field.get('category', {}).get('id', '')
                    }
                    fields_data.append(field_info)
                except Exception as e:
                    logging.error(f"处理字段时出错: {str(e)}")
                    continue
            
            return fields_data
    except Exception as e:
        logging.error(f"读取文件 {file_path} 时出错: {str(e)}")
        return []

def main():
    # 基础目录
    base_dir = Path('data/worldquant_fields/categories')
    output_file = 'data/worldquant_fields/all_fields.csv'
    
    # CSV 表头
    headers = ['id', 'description', 'type', 'dataset_id', 'dataset_name', 'category_id']
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 打开CSV文件准备写入
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers)
        writer.writeheader()
        
        # 遍历所有类别目录
        for category_dir in base_dir.iterdir():
            if not category_dir.is_dir() or category_dir.name.startswith('.'):
                continue
                
            logging.info(f"处理类别: {category_dir.name}")
            
            # 遍历类别目录中的所有JSON文件
            for json_file in category_dir.glob('*.json'):
                if json_file.name.startswith('_') or json_file.name.endswith('.bak'):
                    continue
                    
                logging.info(f"处理文件: {json_file.name}")
                
                # 处理文件并写入CSV
                fields_data = process_field_file(json_file)
                for field_info in fields_data:
                    writer.writerow(field_info)
    
    logging.info(f"处理完成，输出文件: {output_file}")

if __name__ == '__main__':
    main() 