from typing import Dict, List, Any, Optional, Set, Union, Tuple
import re
import json
import os
from collections import Counter
import logging

from llm.utils.logger import Logger
from llm.models.entities import BaseEntity
from llm.models.relations import Relation

# 初始化日志记录器
logger = Logger.get_logger(__name__)

class TextAnalyzer:
    """文本分析器基类，用于分析文本提取实体和关系"""
    
    def __init__(self, 
                config: Optional[Dict[str, Any]] = None,
                entity_types: Optional[List[str]] = None,
                relation_types: Optional[List[str]] = None):
        """
        初始化文本分析器
        
        Args:
            config: 配置参数
            entity_types: 要提取的实体类型列表
            relation_types: 要提取的关系类型列表
        """
        self.config = config or {}
        self.entity_types = entity_types or []
        self.relation_types = relation_types or []
        
        # 保存提取的实体和关系
        self.entities: List[BaseEntity] = []
        self.relations: List[Relation] = []
        
        # 初始化统计信息
        self.stats = {
            "processed_texts": 0,
            "extracted_entities": 0,
            "extracted_relations": 0,
            "entity_types_count": Counter(),
            "relation_types_count": Counter()
        }
        
        logger.info(f"TextAnalyzer initialized with {len(self.entity_types)} entity types "
                  f"and {len(self.relation_types)} relation types")
    
    def analyze(self, text: str) -> Tuple[List[BaseEntity], List[Relation]]:
        """
        分析文本并提取实体和关系
        
        Args:
            text: 要分析的文本内容
            
        Returns:
            Tuple[List[BaseEntity], List[Relation]]: 提取的实体和关系列表
        """
        self.stats["processed_texts"] += 1
        
        # 提取实体
        extracted_entities = self.extract_entities(text)
        self.entities.extend(extracted_entities)
        self.stats["extracted_entities"] += len(extracted_entities)
        
        # 更新实体类型统计
        for entity in extracted_entities:
            self.stats["entity_types_count"][entity.type] += 1
        
        # 提取关系
        extracted_relations = self.extract_relations(text, extracted_entities)
        self.relations.extend(extracted_relations)
        self.stats["extracted_relations"] += len(extracted_relations)
        
        # 更新关系类型统计
        for relation in extracted_relations:
            self.stats["relation_types_count"][relation.type] += 1
        
        logger.debug(f"Analyzed text with {len(text)} characters, found "
                   f"{len(extracted_entities)} entities and {len(extracted_relations)} relations")
        
        return extracted_entities, extracted_relations
    
    def extract_entities(self, text: str) -> List[BaseEntity]:
        """
        从文本中提取实体
        
        Args:
            text: 要分析的文本内容
            
        Returns:
            List[BaseEntity]: 提取的实体列表
        """
        # 这是一个抽象方法，子类需要实现具体的实体提取逻辑
        raise NotImplementedError("Subclasses must implement extract_entities method")
    
    def extract_relations(self, text: str, entities: List[BaseEntity]) -> List[Relation]:
        """
        基于提取的实体从文本中提取关系
        
        Args:
            text: 要分析的文本内容
            entities: 已提取的实体列表
            
        Returns:
            List[Relation]: 提取的关系列表
        """
        # 这是一个抽象方法，子类需要实现具体的关系提取逻辑
        raise NotImplementedError("Subclasses must implement extract_relations method")
    
    def find_entity_by_id(self, entity_id: str) -> Optional[BaseEntity]:
        """
        通过ID查找实体
        
        Args:
            entity_id: 实体ID
            
        Returns:
            Optional[BaseEntity]: 找到的实体或None
        """
        for entity in self.entities:
            if entity.id == entity_id:
                return entity
        return None
    
    def find_entities_by_type(self, entity_type: str) -> List[BaseEntity]:
        """
        通过类型查找实体
        
        Args:
            entity_type: 实体类型
            
        Returns:
            List[BaseEntity]: 找到的实体列表
        """
        return [entity for entity in self.entities if entity.type == entity_type]
    
    def find_relation_by_id(self, relation_id: str) -> Optional[Relation]:
        """
        通过ID查找关系
        
        Args:
            relation_id: 关系ID
            
        Returns:
            Optional[Relation]: 找到的关系或None
        """
        for relation in self.relations:
            if relation.id == relation_id:
                return relation
        return None
    
    def find_relations_by_type(self, relation_type: str) -> List[Relation]:
        """
        通过类型查找关系
        
        Args:
            relation_type: 关系类型
            
        Returns:
            List[Relation]: 找到的关系列表
        """
        return [relation for relation in self.relations if relation.type == relation_type]
    
    def find_relations_by_entity(self, entity_id: str) -> List[Relation]:
        """
        查找与指定实体相关的所有关系
        
        Args:
            entity_id: 实体ID
            
        Returns:
            List[Relation]: 找到的关系列表
        """
        return [
            relation for relation in self.relations 
            if relation.source_id == entity_id or relation.target_id == entity_id
        ]
    
    def clear(self) -> None:
        """
        清除所有提取的实体和关系
        """
        self.entities = []
        self.relations = []
        logger.info("Cleared all extracted entities and relations")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        return self.stats
    
    def save_results(self, output_file: str) -> None:
        """
        保存提取结果到文件
        
        Args:
            output_file: 输出文件路径
        """
        # 确保输出目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        
        # 将实体和关系转换为字典
        entity_dicts = [entity.to_dict() for entity in self.entities]
        relation_dicts = [relation.to_dict() for relation in self.relations]
        
        # 构建结果字典
        results = {
            "entities": entity_dicts,
            "relations": relation_dicts,
            "stats": self.stats
        }
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved results to {output_file}: "
                  f"{len(entity_dicts)} entities, {len(relation_dicts)} relations")
    
    def load_results(self, input_file: str) -> None:
        """
        从文件加载提取结果
        
        Args:
            input_file: 输入文件路径
        """
        if not os.path.exists(input_file):
            logger.error(f"Input file {input_file} does not exist")
            return
        
        try:
            # 读取文件
            with open(input_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 解析实体和关系
            self.entities = [BaseEntity.from_dict(entity_dict) for entity_dict in results.get("entities", [])]
            self.relations = [Relation.from_dict(relation_dict) for relation_dict in results.get("relations", [])]
            
            # 加载统计信息
            if "stats" in results:
                self.stats = results["stats"]
                # 转换Counter对象
                if isinstance(self.stats.get("entity_types_count"), dict):
                    self.stats["entity_types_count"] = Counter(self.stats["entity_types_count"])
                if isinstance(self.stats.get("relation_types_count"), dict):
                    self.stats["relation_types_count"] = Counter(self.stats["relation_types_count"])
            
            logger.info(f"Loaded results from {input_file}: "
                      f"{len(self.entities)} entities, {len(self.relations)} relations")
        
        except Exception as e:
            logger.error(f"Error loading results from {input_file}: {e}")
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"TextAnalyzer(entities={len(self.entities)}, "
                f"relations={len(self.relations)}, "
                f"processed_texts={self.stats['processed_texts']})")
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"TextAnalyzer(entity_types={self.entity_types}, "
                f"relation_types={self.relation_types}, "
                f"entities={len(self.entities)}, "
                f"relations={len(self.relations)}, "
                f"stats={self.stats})")


class RuleBasedTextAnalyzer(TextAnalyzer):
    """基于规则的文本分析器"""
    
    def __init__(self, 
                config: Optional[Dict[str, Any]] = None,
                entity_types: Optional[List[str]] = None,
                relation_types: Optional[List[str]] = None,
                entity_patterns: Optional[Dict[str, List[str]]] = None,
                relation_patterns: Optional[Dict[str, List[Dict[str, str]]]] = None):
        """
        初始化基于规则的文本分析器
        
        Args:
            config: 配置参数
            entity_types: 要提取的实体类型列表
            relation_types: 要提取的关系类型列表
            entity_patterns: 实体提取模式，格式为 {entity_type: [pattern1, pattern2, ...]}
            relation_patterns: 关系提取模式，格式为 {relation_type: [{source_type, target_type, pattern}, ...]}
        """
        super().__init__(config, entity_types, relation_types)
        self.entity_patterns = entity_patterns or {}
        self.relation_patterns = relation_patterns or {}
        
        # 编译正则表达式
        self.compiled_entity_patterns = {}
        for entity_type, patterns in self.entity_patterns.items():
            self.compiled_entity_patterns[entity_type] = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        
        logger.info(f"RuleBasedTextAnalyzer initialized with {sum(len(patterns) for patterns in self.entity_patterns.values())} "
                  f"entity patterns and {sum(len(patterns) for patterns in self.relation_patterns.values())} relation patterns")
    
    def extract_entities(self, text: str) -> List[BaseEntity]:
        """
        使用正则表达式从文本中提取实体
        
        Args:
            text: 要分析的文本内容
            
        Returns:
            List[BaseEntity]: 提取的实体列表
        """
        entities = []
        
        # 对每种实体类型应用对应的正则表达式
        for entity_type, compiled_patterns in self.compiled_entity_patterns.items():
            for pattern in compiled_patterns:
                for match in pattern.finditer(text):
                    # 创建实体
                    entity = BaseEntity(
                        type=entity_type,
                        properties={
                            "name": match.group(0),
                            "text": match.group(0),
                            "start": match.start(),
                            "end": match.end()
                        }
                    )
                    entities.append(entity)
        
        # 去重
        unique_entities = []
        seen_texts = set()
        for entity in entities:
            entity_text = entity.get_property("text")
            if entity_text not in seen_texts:
                seen_texts.add(entity_text)
                unique_entities.append(entity)
        
        logger.debug(f"Extracted {len(unique_entities)} unique entities from text")
        return unique_entities
    
    def extract_relations(self, text: str, entities: List[BaseEntity]) -> List[Relation]:
        """
        基于提取的实体和模式从文本中提取关系
        
        Args:
            text: 要分析的文本内容
            entities: 已提取的实体列表
            
        Returns:
            List[Relation]: 提取的关系列表
        """
        relations = []
        
        # 建立实体索引，按类型分组
        entity_by_type = {}
        for entity in entities:
            if entity.type not in entity_by_type:
                entity_by_type[entity.type] = []
            entity_by_type[entity.type].append(entity)
        
        # 对每种关系类型应用对应的关系提取规则
        for relation_type, patterns in self.relation_patterns.items():
            for pattern_dict in patterns:
                source_type = pattern_dict.get("source_type")
                target_type = pattern_dict.get("target_type")
                pattern = pattern_dict.get("pattern")
                
                if not (source_type and target_type and pattern):
                    continue
                
                # 获取源类型和目标类型的实体
                source_entities = entity_by_type.get(source_type, [])
                target_entities = entity_by_type.get(target_type, [])
                
                # 编译模式
                compiled_pattern = re.compile(pattern, re.IGNORECASE)
                
                # 查找匹配
                for match in compiled_pattern.finditer(text):
                    # 找到匹配范围内的实体
                    match_start, match_end = match.span()
                    
                    # 查找范围内的源实体和目标实体
                    sources_in_range = []
                    targets_in_range = []
                    
                    for entity in source_entities:
                        entity_start = entity.get_property("start")
                        entity_end = entity.get_property("end")
                        if entity_start is not None and entity_end is not None:
                            if match_start <= entity_start and entity_end <= match_end:
                                sources_in_range.append(entity)
                    
                    for entity in target_entities:
                        entity_start = entity.get_property("start")
                        entity_end = entity.get_property("end")
                        if entity_start is not None and entity_end is not None:
                            if match_start <= entity_start and entity_end <= match_end:
                                targets_in_range.append(entity)
                    
                    # 为每对源实体和目标实体创建关系
                    for source in sources_in_range:
                        for target in targets_in_range:
                            if source.id != target.id:  # 避免自关系
                                relation = Relation(
                                    source_id=source.id,
                                    target_id=target.id,
                                    type=relation_type,
                                    properties={
                                        "text": match.group(0),
                                        "start": match_start,
                                        "end": match_end
                                    }
                                )
                                relations.append(relation)
        
        logger.debug(f"Extracted {len(relations)} relations from text")
        return relations


class LLMBasedTextAnalyzer(TextAnalyzer):
    """基于LLM的文本分析器"""
    
    def __init__(self, 
                config: Optional[Dict[str, Any]] = None,
                entity_types: Optional[List[str]] = None,
                relation_types: Optional[List[str]] = None,
                model_name: Optional[str] = None,
                prompt_template: Optional[str] = None):
        """
        初始化基于LLM的文本分析器
        
        Args:
            config: 配置参数
            entity_types: 要提取的实体类型列表
            relation_types: 要提取的关系类型列表
            model_name: 使用的LLM模型名称
            prompt_template: 提示模板
        """
        super().__init__(config, entity_types, relation_types)
        self.model_name = model_name or "gpt-3.5-turbo"
        self.prompt_template = prompt_template or (
            "请从以下文本中提取{entity_types}类型的实体和{relation_types}类型的关系。\n"
            "文本内容：{text}\n"
            "输出格式为JSON，包含entities和relations两个列表。"
        )
        
        # 此处应该添加实际的LLM调用客户端初始化代码
        # 例如：self.llm_client = OpenAIClient(api_key=config.get("openai_api_key"))
        
        logger.info(f"LLMBasedTextAnalyzer initialized with model {self.model_name}")
    
    def extract_entities(self, text: str) -> List[BaseEntity]:
        """
        使用LLM从文本中提取实体
        
        Args:
            text: 要分析的文本内容
            
        Returns:
            List[BaseEntity]: 提取的实体列表
        """
        # 在实际实现中，这里应该调用LLM来提取实体
        # 下面的代码是伪代码，实际使用时应替换为具体的LLM API调用
        
        logger.info(f"Using LLM model {self.model_name} to extract entities")
        
        # 构建提示
        prompt = self.prompt_template.format(
            entity_types=", ".join(self.entity_types),
            relation_types=", ".join(self.relation_types),
            text=text[:1000]  # 限制文本长度
        )
        
        # 此处应该调用LLM API
        # result = self.llm_client.call(prompt)
        
        # 解析结果（伪代码）
        # parsed_result = json.loads(result)
        # entity_dicts = parsed_result.get("entities", [])
        
        # 将结果转换为实体对象
        entities = []
        # for entity_dict in entity_dicts:
        #     entity = BaseEntity.from_dict(entity_dict)
        #     entities.append(entity)
        
        return entities
    
    def extract_relations(self, text: str, entities: List[BaseEntity]) -> List[Relation]:
        """
        使用LLM从文本中提取关系
        
        Args:
            text: 要分析的文本内容
            entities: 已提取的实体列表
            
        Returns:
            List[Relation]: 提取的关系列表
        """
        # 在实际实现中，这里应该使用与extract_entities相同的LLM调用结果
        # 因为通常会一次调用同时提取实体和关系
        
        # 解析结果（伪代码）
        # parsed_result = json.loads(result)
        # relation_dicts = parsed_result.get("relations", [])
        
        # 将结果转换为关系对象
        relations = []
        # for relation_dict in relation_dicts:
        #     relation = Relation.from_dict(relation_dict)
        #     relations.append(relation)
        
        return relations 