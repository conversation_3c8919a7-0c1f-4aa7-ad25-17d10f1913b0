import os
import csv
from collections import defaultdict

input_dir = "data/worldquant_fields_by_rdu"

# 用于存储每个前缀下所有field
group_fields = defaultdict(list)

for filename in os.listdir(input_dir):
    if not filename.endswith(".csv"):
        continue
    input_path = os.path.join(input_dir, filename)
    # 获取前缀：下划线分割前3个
    prefix = "_".join(filename.split("_")[:3])
    with open(input_path, encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            # type字段严格等于GROUP
            if row.get("type", "").strip() == "GROUP":
                field = row.get("id", "").strip()
                if field:
                    group_fields[prefix].append(field)

# 写入文件
for prefix, fields in group_fields.items():
    output_path = os.path.join(input_dir, f"{prefix}.txt")
    with open(output_path, "w", encoding="utf-8") as out:
        for field in fields:
            out.write(field + "\n") 