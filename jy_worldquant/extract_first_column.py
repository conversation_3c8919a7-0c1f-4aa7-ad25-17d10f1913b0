import pandas as pd
import os

def extract_first_column(file_path):
    """
    Extracts the first column from a CSV file and prints its contents.

    Args:
        file_path (str): The path to the CSV file.
    """
    if not os.path.exists(file_path):
        print(f"Error: File not found at {file_path}")
        return

    try:
        # Read the CSV file
        df = pd.read_csv(file_path)

        # Check if the dataframe is empty
        if df.empty:
            print("The CSV file is empty.")
            return

        # Get the first column (header is 'id')
        first_column_name = df.columns[0]
        first_column_data = df[first_column_name]

        # Print the contents of the first column
        print(f"Contents of the first column ('{first_column_name}'):")
        for item in first_column_data:
            print(item)

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # The path to the CSV file provided by the user
    csv_file_path = "data/worldquant_fields_by_rdu/USA_0_TOP3000_analyst.csv"
    extract_first_column(csv_file_path) 