# 时间工具模块使用文档

## 概述

时间工具模块 (`time_utils.py`) 提供了准确的时间获取、格式化和占位符替换功能，解决了项目中时间占位符获取不是当前时间的问题。

## 主要功能

### 1. 时间获取

```python
from utils.time_utils import TimeUtils

# 获取当前日期时间 (YYYY-MM-DD_HH:MM:SS)
current_datetime = TimeUtils.get_current_datetime()
print(current_datetime)  # 输出: 2025-05-28_14:05:30

# 获取当前日期 (YYYY-MM-DD)
current_date = TimeUtils.get_current_date()
print(current_date)  # 输出: 2025-05-28

# 获取当前时间 (HH:MM:SS)
current_time = TimeUtils.get_current_time()
print(current_time)  # 输出: 14:05:30

# 获取用户名
user_name = TimeUtils.get_user_name()
print(user_name)  # 输出: JayYe
```

### 2. 占位符替换

```python
from utils.time_utils import replace_time_placeholders

# 基本占位符替换
text = "创建于：[DATETIME]，用户：[USER_NAME]"
result = replace_time_placeholders(text)
print(result)  # 输出: 创建于：2025-05-28_14:05:30，用户：JayYe

# 自定义占位符
custom_placeholders = {
    '[PROJECT_NAME]': 'WorldQuant Alpha机器',
    '[VERSION]': '1.0.0'
}
text = "项目：[PROJECT_NAME] v[VERSION]，创建时间：[DATETIME]"
result = replace_time_placeholders(text, custom_placeholders)
print(result)  # 输出: 项目：WorldQuant Alpha机器 v1.0.0，创建时间：2025-05-28_14:05:30
```

### 3. 任务文件管理

```python
from utils.time_utils import (
    create_task_identifier, 
    generate_task_file_path, 
    create_task_file_content
)

# 创建任务标识符
task_desc = "将更改类的数据库操作改为异步执行"
identifier = create_task_identifier(task_desc)
print(identifier)  # 输出: async-database-operations

# 生成任务文件路径
file_path = generate_task_file_path(task_desc, task_number=1)
print(file_path)  # 输出: .tasks/2025-05-28_1_async-database-operations.md

# 创建完整的任务文件内容
content = create_task_file_content(
    task_description="修复时间占位符问题",
    project_overview="WorldQuant Alpha机器系统",
    task_number=2,
    yolo_mode="Ask"
)
print(content)  # 输出完整的任务文件模板，所有占位符已替换为真实时间
```

## 支持的占位符

| 占位符 | 描述 | 示例输出 |
|--------|------|----------|
| `[DATETIME]` | 当前日期和时间 | 2025-05-28_14:05:30 |
| `[DATE]` | 当前日期 | 2025-05-28 |
| `[TIME]` | 当前时间 | 14:05:30 |
| `[USER_NAME]` | 当前系统用户名 | JayYe |
| `[MAIN_BRANCH]` | 主分支名称 | main |

## 使用场景

### 1. 创建任务文件

```python
from utils.time_utils import create_task_file_content

# 自动生成带有正确时间的任务文件
task_content = create_task_file_content(
    task_description="实现新功能",
    project_overview="项目描述"
)

# 写入文件
with open(".tasks/new_task.md", "w", encoding="utf-8") as f:
    f.write(task_content)
```

### 2. 日志记录

```python
from utils.time_utils import replace_time_placeholders

# 生成带时间戳的日志
log_template = "[DATETIME] - [USER_NAME] - 操作完成"
log_entry = replace_time_placeholders(log_template)
print(log_entry)  # 输出: 2025-05-28_14:05:30 - JayYe - 操作完成
```

### 3. 文件命名

```python
from utils.time_utils import TimeUtils

# 生成带时间戳的文件名
filename = f"backup_{TimeUtils.get_current_datetime()}.sql"
print(filename)  # 输出: backup_2025-05-28_14:05:30.sql
```

## 注意事项

1. **时区**: 所有时间都基于系统本地时间
2. **格式**: 时间格式固定为 YYYY-MM-DD_HH:MM:SS
3. **线程安全**: 所有函数都是线程安全的
4. **性能**: 每次调用都会获取当前时间，确保时间的准确性

## 测试

运行测试脚本验证功能：

```bash
python utils/time_utils.py
```

输出示例：
```
时间工具测试:
当前日期时间: 2025-05-28_14:05:30
当前日期: 2025-05-28
当前时间: 14:05:30
用户名: JayYe
占位符替换测试: 创建于：2025-05-28_14:05:30，用户：JayYe
任务标识符: async-database-operations
任务文件路径: .tasks/2025-05-28_1_async-database-operations.md
``` 