#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
时间工具模块：处理时间获取、格式化和占位符替换
"""

import re
import os
import getpass
from datetime import datetime
from typing import Dict, Optional


class TimeUtils:
    """时间工具类"""
    
    @staticmethod
    def get_current_datetime() -> str:
        """
        获取当前日期和时间
        
        Returns:
            格式为YYYY-MM-DD_HH:MM:SS的字符串
        """
        return datetime.now().strftime("%Y-%m-%d_%H:%M:%S")
    
    @staticmethod
    def get_current_date() -> str:
        """
        获取当前日期
        
        Returns:
            格式为YYYY-MM-DD的字符串
        """
        return datetime.now().strftime("%Y-%m-%d")
    
    @staticmethod
    def get_current_time() -> str:
        """
        获取当前时间
        
        Returns:
            格式为HH:MM:SS的字符串
        """
        return datetime.now().strftime("%H:%M:%S")
    
    @staticmethod
    def get_user_name() -> str:
        """
        获取当前系统用户名
        
        Returns:
            用户名字符串
        """
        try:
            return getpass.getuser()
        except Exception:
            return "unknown"
    
    @staticmethod
    def get_task_date_and_number(task_number: int = 1) -> str:
        """
        获取任务日期和编号
        
        Args:
            task_number: 任务编号，默认为1
            
        Returns:
            格式为YYYY-MM-DD_n的字符串
        """
        current_date = datetime.now().strftime("%Y-%m-%d")
        return f"{current_date}_{task_number}"
    
    @staticmethod
    def get_task_file_name(task_number: int = 1) -> str:
        """
        获取任务文件名
        
        Args:
            task_number: 任务编号，默认为1
            
        Returns:
            格式为YYYY-MM-DD_n的字符串
        """
        return TimeUtils.get_task_date_and_number(task_number)
    
    @staticmethod
    def get_all_placeholders() -> Dict[str, str]:
        """
        获取所有时间相关占位符的当前值
        
        Returns:
            包含所有占位符和对应值的字典
        """
        return {
            '[DATETIME]': TimeUtils.get_current_datetime(),
            '[DATE]': TimeUtils.get_current_date(),
            '[TIME]': TimeUtils.get_current_time(),
            '[USER_NAME]': TimeUtils.get_user_name(),
            '[MAIN_BRANCH]': 'main'
        }


def replace_time_placeholders(text: str, custom_placeholders: Optional[Dict[str, str]] = None) -> str:
    """
    替换文本中的时间占位符
    
    Args:
        text: 包含占位符的文本
        custom_placeholders: 自定义占位符字典，会覆盖默认值
        
    Returns:
        替换后的文本
    """
    # 获取默认占位符
    placeholders = TimeUtils.get_all_placeholders()
    
    # 合并自定义占位符
    if custom_placeholders:
        placeholders.update(custom_placeholders)
    
    # 替换占位符
    result = text
    for placeholder, value in placeholders.items():
        result = result.replace(placeholder, value)
    
    return result


def create_task_identifier(task_description: str) -> str:
    """
    从任务描述创建任务标识符
    
    Args:
        task_description: 任务描述
        
    Returns:
        任务标识符（小写，用连字符分隔）
    """
    # 移除特殊字符，保留字母、数字和空格
    clean_desc = re.sub(r'[^\w\s\u4e00-\u9fff]', '', task_description)
    
    # 将中文和空格替换为连字符
    identifier = re.sub(r'[\s\u4e00-\u9fff]+', '-', clean_desc.strip())
    
    # 转换为小写并移除多余的连字符
    identifier = re.sub(r'-+', '-', identifier.lower()).strip('-')
    
    # 限制长度
    if len(identifier) > 50:
        identifier = identifier[:50].rstrip('-')
    
    return identifier or 'task'


def generate_task_file_path(task_description: str, task_number: int = 1) -> str:
    """
    生成任务文件路径
    
    Args:
        task_description: 任务描述
        task_number: 任务编号
        
    Returns:
        完整的任务文件路径
    """
    task_file_name = TimeUtils.get_task_file_name(task_number)
    task_identifier = create_task_identifier(task_description)
    
    return f".tasks/{task_file_name}_{task_identifier}.md"


def create_task_file_content(task_description: str, project_overview: str = "", 
                           task_number: int = 1, yolo_mode: str = "Ask") -> str:
    """
    创建任务文件内容
    
    Args:
        task_description: 任务描述
        project_overview: 项目概览
        task_number: 任务编号
        yolo_mode: Yolo模式设置
        
    Returns:
        完整的任务文件内容
    """
    task_file_name = TimeUtils.get_task_file_name(task_number)
    task_identifier = create_task_identifier(task_description)
    task_branch = f"task/{task_identifier}_{TimeUtils.get_task_date_and_number(task_number)}"
    
    template = """# 背景
文件名：[TASK_FILE_NAME]
创建于：[DATETIME]
创建者：[USER_NAME]
主分支：[MAIN_BRANCH]
任务分支：[TASK_BRANCH]
Yolo模式：[YOLO_MODE]

# 任务描述
[TASK_DESCRIPTION]

# 项目概览
[PROJECT_OVERVIEW]

⚠️ 警告：永远不要修改此部分 ⚠️
核心RIPER-5协议规则：
- 必须在每个响应开头声明当前模式
- 在EXECUTE模式中必须100%忠实遵循计划
- 在REVIEW模式中必须标记即使最小的偏差
- 未经明确许可不能在模式之间转换
- 必须将分析深度与问题重要性相匹配
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析
[待分析内容]

# 提议的解决方案
[待提议解决方案]

# 当前执行步骤："1. 研究分析"

# 任务进度
[待记录变更历史]

# 最终审查
[完成后的总结]"""

    # 自定义占位符
    custom_placeholders = {
        '[TASK_FILE_NAME]': f"{task_file_name}_{task_identifier}.md",
        '[TASK_BRANCH]': task_branch,
        '[YOLO_MODE]': yolo_mode,
        '[TASK_DESCRIPTION]': task_description,
        '[PROJECT_OVERVIEW]': project_overview or "项目概览待补充"
    }
    
    return replace_time_placeholders(template, custom_placeholders)


if __name__ == "__main__":
    # 测试功能
    print("时间工具测试:")
    print(f"当前日期时间: {TimeUtils.get_current_datetime()}")
    print(f"当前日期: {TimeUtils.get_current_date()}")
    print(f"当前时间: {TimeUtils.get_current_time()}")
    print(f"用户名: {TimeUtils.get_user_name()}")
    
    # 测试占位符替换
    test_text = "创建于：[DATETIME]，用户：[USER_NAME]"
    result = replace_time_placeholders(test_text)
    print(f"占位符替换测试: {result}")
    
    # 测试任务标识符生成
    task_desc = "将更改类的数据库操作改为异步执行"
    identifier = create_task_identifier(task_desc)
    print(f"任务标识符: {identifier}")
    
    # 测试任务文件路径生成
    file_path = generate_task_file_path(task_desc)
    print(f"任务文件路径: {file_path}")