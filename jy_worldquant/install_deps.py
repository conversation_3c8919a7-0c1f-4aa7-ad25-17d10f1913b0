import subprocess
import sys

def install_package(package):
    subprocess.check_call([sys.executable, "-m", "pip", "install", package])

packages = [
    "matplotlib",
    "seaborn",
    "neo4j>=5.7.0",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",
    "requests>=2.28.0",
    "pandas>=1.5.3",
    "numpy>=1.24.0",
    "colorlog>=6.7.0",
    "tqdm>=4.65.0",
    "rich>=13.4.2",
    "nltk>=3.8.1",
    "spacy>=3.5.3",
    "transformers>=4.30.0",
    "torch>=2.0.0",
    "flask>=2.3.2",
    "flask-cors>=4.0.0",
    "pydantic>=2.0.0",
    "jsonschema>=4.18.0",
    "pytest>=7.3.1",
    "black>=23.3.0",
    "isort>=5.12.0",
    "mypy>=1.3.0"
]

for package in packages:
    try:
        print(f"Installing {package}...")
        install_package(package)
    except Exception as e:
        print(f"Error installing {package}: {str(e)}") 