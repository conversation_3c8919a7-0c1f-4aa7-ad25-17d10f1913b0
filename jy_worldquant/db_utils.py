#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库工具模块：处理alpha回测数据的存储和检索
"""

import sqlite3
import hashlib
import logging
import os
from datetime import datetime
import queue
import threading
from concurrent.futures import ThreadPoolExecutor, Future
from typing import Optional, Any, Callable

class DatabaseService:
    """数据库服务类，包装数据库操作"""
    
    def __init__(self, connection):
        """
        初始化数据库服务
        
        Args:
            connection: 数据库连接对象
        """
        self.connection = connection
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def filter_new_alpha_list(self, alpha_tuple_list, common_config):
        """
        过滤已经回测过的Alpha
        
        Args:
            alpha_tuple_list: Alpha元组列表
            common_config: 通用配置对象
            
        Returns:
            未回测的Alpha列表
        """
        return filter_new_alpha_list(self.connection, alpha_tuple_list, common_config)
    
    def save_alpha_to_db(self, alpha, decay, common_config, check_status='COMPLETE'):
        """
        保存Alpha到数据库
        
        Args:
            alpha: Alpha表达式
            decay: 衰减值
            common_config: 通用配置对象
            check_status: 检查状态，默认为'COMPLETE'
            
        Returns:
            是否保存成功
        """
        return save_alpha_to_db(self.connection, alpha, decay, common_config, check_status)
    
    def is_alpha_in_db(self, alpha, decay, common_config):
        """
        检查Alpha是否已在数据库中
        
        Args:
            alpha: Alpha表达式
            decay: 衰减值
            common_config: 通用配置对象
            
        Returns:
            是否存在
        """
        return is_alpha_in_db(self.connection, alpha, decay, common_config)
    
    def mark_alpha_as_submitted(self, alpha, decay, common_config):
        """
        标记Alpha为已提交状态
        
        Args:
            alpha: Alpha表达式
            decay: 衰减值
            common_config: 通用配置对象
            
        Returns:
            是否标记成功
        """
        return mark_alpha_as_submitted(self.connection, alpha, decay, common_config)
    
    def mark_alpha_as_checked(self, alpha, decay, common_config):
        """
        标记Alpha为已检查状态
        
        Args:
            alpha: Alpha表达式
            decay: 衰减值
            common_config: 通用配置对象
            
        Returns:
            是否标记成功
        """
        return mark_alpha_as_checked(self.connection, alpha, decay, common_config)
    
    def get_statistics(self):
        """
        获取数据库统计信息
        
        Returns:
            统计信息字典
        """
        return get_statistics(self.connection)
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.logger.info("数据库连接已关闭")


def setup_database(config):
    """设置数据库连接并创建必要的表"""
    # 检查是否启用数据库功能
    if not config.getboolean('database', 'enabled', fallback=True):
        logging.info("数据库功能已禁用")
        return None
    
    logging.info("初始化数据库连接")
    # 获取数据库文件路径
    db_file = config.get('database', 'db_file', fallback='worldquant.db')
    
    # 确保目录存在
    db_dir = os.path.dirname(db_file)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir)
    
    # 尝试连接数据库
    try:
        conn = sqlite3.connect(db_file)
        logging.info("数据库连接成功")
        
        # 创建表
        with conn:
            # 创建alpha记录表
            conn.execute("""
            CREATE TABLE IF NOT EXISTS alpha_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                alpha_hash TEXT NOT NULL,
                alpha TEXT NOT NULL,
                dataset_id TEXT,
                region TEXT,
                universe TEXT,
                delay INTEGER,
                sharpe REAL,
                fitness REAL,
                instrument_type TEXT,
                decay INTEGER,
                unit_handling TEXT,
                nan_handling TEXT,
                language TEXT,
                visualization TEXT,
                max_trade INTEGER,
                last_test_time TIMESTAMP,
                neutralization TEXT,
                is_submitted INTEGER DEFAULT 0,
                is_checked INTEGER DEFAULT 0,
                check_status TEXT DEFAULT 'COMPLETE'
            )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_alpha_hash ON alpha_history (alpha_hash)")
        
        logging.info("数据库表创建/检查完成")
        
        # 数据库迁移：检查并添加 check_status 字段
        try:
            cursor = conn.cursor()
            # 检查 check_status 字段是否存在
            cursor.execute("PRAGMA table_info(alpha_history)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'check_status' not in columns:
                logging.info("检测到旧版数据库，正在添加 check_status 字段...")
                cursor.execute("ALTER TABLE alpha_history ADD COLUMN check_status TEXT DEFAULT 'COMPLETE'")
                conn.commit()
                logging.info("check_status 字段添加成功")
            else:
                logging.info("check_status 字段已存在，跳过迁移")
                
        except Exception as e:
            logging.error(f"数据库迁移失败: {e}")
            # 迁移失败不应该阻止系统启动
        
        logging.info("数据库初始化完成")
        
        # 检查是否启用异步数据库操作
        async_enabled = config.getboolean('async_database', 'enabled', fallback=False)
        
        if async_enabled:
            logging.info("创建异步数据库服务")
            # 关闭主线程连接，异步服务会为每个线程创建独立连接
            conn.close()
            return AsyncDatabaseService(db_file, config)
        else:
            logging.info("创建同步数据库服务")
            return DatabaseService(conn)
    
    except Exception as e:
        logging.error(f"数据库连接或初始化失败: {e}")
        return None

def get_alpha_hash(alpha, decay, common_config):
    """生成alpha表达式的哈希值以便快速查找"""
    combined = alpha + str(decay) + common_config.dataset_id + common_config.region + common_config.universe + str(common_config.delay) + common_config.instrument_type + common_config.unit_handling + common_config.nan_handling + common_config.language + common_config.visualization + str(common_config.max_trade)
    return hashlib.md5(combined.encode('utf-8')).hexdigest()

def is_alpha_in_db(conn, alpha, decay, common_config):
    """检查alpha是否已经在数据库中"""
    if not conn:
        return False
    
    alpha_hash = get_alpha_hash(alpha, decay, common_config)
    try:
        cursor = conn.cursor()
        cursor.execute(
            "SELECT id FROM alpha_history WHERE alpha_hash = ?",
            (alpha_hash,)
        )
        result = cursor.fetchone()
        return result is not None
    except Exception as e:
        logging.error(f"查询数据库时出错: {e}")
        return False

def save_alpha_to_db(conn, alpha, decay, common_config, check_status='COMPLETE'):
    """将alpha保存到数据库"""
    if not conn:
        return False
    
    alpha_hash = get_alpha_hash(alpha, decay, common_config)
    current_time = datetime.now()
    
    try:
        cursor = conn.cursor()
        # 检查是否已存在
        cursor.execute(
            "SELECT id FROM alpha_history WHERE alpha_hash = ?",
            (alpha_hash,)
        )
        existing = cursor.fetchone()
        
        if existing:
            # 更新现有记录
            cursor.execute("""
            UPDATE alpha_history 
            SET last_test_time = ?, check_status = ?
            WHERE alpha_hash = ?
            """, (current_time, check_status, alpha_hash))
        else:
            # 插入新记录
            cursor.execute("""
            INSERT INTO alpha_history 
            (alpha_hash, alpha, dataset_id, region, universe, delay,
             instrument_type, decay, unit_handling, nan_handling, language, 
             visualization, max_trade, last_test_time, neutralization, is_submitted, is_checked, check_status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                alpha_hash, 
                alpha, 
                common_config.dataset_id, 
                common_config.region, 
                common_config.universe, 
                common_config.delay, 
                common_config.instrument_type, 
                common_config.decay,
                common_config.unit_handling, 
                common_config.nan_handling,
                common_config.language, 
                common_config.visualization,
                common_config.max_trade, 
                current_time, 
                common_config.neutralization, 
                0,  # is_submitted (False)
                0,  # is_checked (False)
                check_status   # check_status
            ))
        
        conn.commit()
        return True
    except Exception as e:
        logging.error(f"保存到数据库时出错: {e}")
        conn.rollback()
        return False

def mark_alpha_as_submitted(conn, alpha, decay, common_config):
    """标记alpha为已提交状态"""
    if not conn:
        return False
    
    alpha_hash = get_alpha_hash(alpha, decay, common_config)
    
    try:
        cursor = conn.cursor()
        cursor.execute("""
        UPDATE alpha_history SET is_submitted = 1
        WHERE alpha_hash = ?
        """, (alpha_hash,))
        conn.commit()
        return True
    except Exception as e:
        logging.error(f"更新alpha提交状态时出错: {e}")
        conn.rollback()
        return False

def mark_alpha_as_checked(conn, alpha, decay, common_config):
    """标记alpha为已检查状态"""
    if not conn:
        return False
    
    alpha_hash = get_alpha_hash(alpha, decay, common_config)
    
    try:
        cursor = conn.cursor()
        cursor.execute("""
        UPDATE alpha_history SET is_checked = 1
        WHERE alpha_hash = ?
        """, (alpha_hash,))
        conn.commit()
        return True
    except Exception as e:
        logging.error(f"更新alpha检查状态时出错: {e}")
        conn.rollback()
        return False

def filter_new_alpha_list(conn, alpha_tuple_list, common_config):
    if not conn:
        # 如果没有数据库连接，直接返回原始列表
        return alpha_tuple_list
    
    new_alpha_list = []
    for alpha_tuple in alpha_tuple_list:
        alpha_expr = str(alpha_tuple[0])
        decay = alpha_tuple[1]
        if not is_alpha_in_db(conn, alpha_expr, decay, common_config):
            new_alpha_list.append((alpha_expr, decay))
        
    filtered_count = len(alpha_tuple_list) - len(new_alpha_list)
    logging.info(f"已过滤掉 {filtered_count} 个已回测的alpha，剩余 {len(new_alpha_list)} 个新alpha待回测")
    return new_alpha_list
        

def get_statistics(conn):
    """获取数据库中alpha的统计信息"""
    if not conn:
        return None
    
    stats = {}
    try:
        cursor = conn.cursor()
        # 获取总数
        cursor.execute("SELECT COUNT(*) FROM alpha_history")
        stats['total_count'] = cursor.fetchone()[0]
        
        # 获取已提交数量
        cursor.execute("SELECT COUNT(*) FROM alpha_history WHERE is_submitted = 1")
        stats['submitted_count'] = cursor.fetchone()[0]
        
        # 获取按region分组的数量
        cursor.execute("SELECT region, COUNT(*) FROM alpha_history GROUP BY region")
        stats['region_counts'] = dict(cursor.fetchall())
        
        # 获取sharpe>1.58的数量
        cursor.execute("SELECT COUNT(*) FROM alpha_history WHERE sharpe > 1.58")
        stats['high_sharpe_count'] = cursor.fetchone()[0]
        
        # 获取按check_status分组的数量
        cursor.execute("SELECT check_status, COUNT(*) FROM alpha_history GROUP BY check_status")
        stats['check_status_counts'] = dict(cursor.fetchall())
        
        return stats
    except Exception as e:
        logging.error(f"获取数据库统计信息时出错: {e}")
        return None

class AsyncDatabaseService:
    """异步数据库服务类，使用线程池处理数据库操作"""
    
    def __init__(self, db_file, config=None):
        """
        初始化异步数据库服务
        
        Args:
            db_file: 数据库文件路径
            config: 配置对象，包含异步相关配置
        """
        self.db_file = db_file
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 从配置中获取参数，如果没有配置则使用默认值
        if config:
            self.thread_pool_size = config.getint('async_database', 'thread_pool_size', fallback=3)
            self.queue_max_size = config.getint('async_database', 'queue_max_size', fallback=1000)
            self.operation_timeout = config.getint('async_database', 'operation_timeout', fallback=30)
            self.shutdown_timeout = config.getint('async_database', 'shutdown_timeout', fallback=60)
        else:
            self.thread_pool_size = 3
            self.queue_max_size = 1000
            self.operation_timeout = 30
            self.shutdown_timeout = 60
        
        # 初始化线程池和队列
        self.executor = ThreadPoolExecutor(
            max_workers=self.thread_pool_size,
            thread_name_prefix="AsyncDB"
        )
        self.task_queue = queue.Queue(maxsize=self.queue_max_size)
        self.is_shutdown = False
        
        # 线程本地存储，用于管理每个线程的数据库连接
        self.thread_local = threading.local()
        
        # 跟踪所有线程连接，用于清理
        self.thread_connections = {}
        self.connections_lock = threading.Lock()
        
        self.logger.info(f"异步数据库服务已初始化，线程池大小: {self.thread_pool_size}")
    
    def _get_thread_connection(self):
        """
        获取当前线程的数据库连接
        
        Returns:
            SQLite连接对象
        """
        # 检查线程本地存储中是否已有连接
        if not hasattr(self.thread_local, 'connection') or self.thread_local.connection is None:
            try:
                # 为当前线程创建新的数据库连接
                conn = sqlite3.connect(self.db_file, timeout=10.0, check_same_thread=False)
                # 设置连接参数以提高性能
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA cache_size=1000")
                conn.execute("PRAGMA temp_store=MEMORY")
                
                self.thread_local.connection = conn
                
                # 记录连接以便后续清理
                thread_id = threading.current_thread().ident
                with self.connections_lock:
                    self.thread_connections[thread_id] = conn
                
                self.logger.debug(f"为线程 {thread_id} 创建了新的数据库连接")
                
            except Exception as e:
                self.logger.error(f"创建线程数据库连接失败: {e}")
                raise
        
        # 验证连接是否仍然有效
        try:
            self.thread_local.connection.execute("SELECT 1")
            return self.thread_local.connection
        except Exception as e:
            self.logger.warning(f"线程连接无效，重新创建: {e}")
            # 清除无效连接
            self.thread_local.connection = None
            # 递归调用重新创建连接
            return self._get_thread_connection()
    
    def _execute_db_operation(self, operation_func: Callable, *args, **kwargs) -> Any:
        """
        在线程池中执行数据库操作
        
        Args:
            operation_func: 要执行的数据库操作函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            操作结果
        """
        if self.is_shutdown:
            raise RuntimeError("异步数据库服务已关闭")
        
        try:
            # 获取当前线程的数据库连接
            conn = self._get_thread_connection()
            return operation_func(conn, *args, **kwargs)
        except Exception as e:
            self.logger.error(f"数据库操作执行失败: {e}")
            raise
    
    def _submit_async_operation(self, operation_func: Callable, *args, **kwargs) -> Future:
        """
        提交异步数据库操作
        
        Args:
            operation_func: 要执行的数据库操作函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            Future对象
        """
        if self.is_shutdown:
            raise RuntimeError("异步数据库服务已关闭")
        
        future = self.executor.submit(
            self._execute_db_operation,
            operation_func,
            *args,
            **kwargs
        )
        return future
    
    def filter_new_alpha_list(self, alpha_tuple_list, common_config, async_mode=False):
        """
        过滤已经回测过的Alpha
        
        Args:
            alpha_tuple_list: Alpha元组列表
            common_config: 通用配置对象
            async_mode: 是否异步执行
            
        Returns:
            未回测的Alpha列表或Future对象
        """
        if async_mode:
            return self._submit_async_operation(filter_new_alpha_list, alpha_tuple_list, common_config)
        else:
            # 同步执行
            return self._execute_db_operation(filter_new_alpha_list, alpha_tuple_list, common_config)
    
    def save_alpha_to_db(self, alpha, decay, common_config, check_status='COMPLETE', async_mode=False):
        """
        保存Alpha到数据库
        
        Args:
            alpha: Alpha表达式
            decay: 衰减值
            common_config: 通用配置对象
            check_status: 检查状态，默认为'COMPLETE'
            async_mode: 是否异步执行
            
        Returns:
            是否保存成功或Future对象
        """
        if async_mode:
            return self._submit_async_operation(save_alpha_to_db, alpha, decay, common_config, check_status)
        else:
            # 同步执行
            return self._execute_db_operation(save_alpha_to_db, alpha, decay, common_config, check_status)
    
    def is_alpha_in_db(self, alpha, decay, common_config, async_mode=False):
        """
        检查Alpha是否已在数据库中
        
        Args:
            alpha: Alpha表达式
            decay: 衰减值
            common_config: 通用配置对象
            async_mode: 是否异步执行
            
        Returns:
            是否存在或Future对象
        """
        if async_mode:
            return self._submit_async_operation(is_alpha_in_db, alpha, decay, common_config)
        else:
            # 同步执行
            return self._execute_db_operation(is_alpha_in_db, alpha, decay, common_config)
    
    def mark_alpha_as_submitted(self, alpha, decay, common_config, async_mode=False):
        """
        标记Alpha为已提交状态
        
        Args:
            alpha: Alpha表达式
            decay: 衰减值
            common_config: 通用配置对象
            async_mode: 是否异步执行
            
        Returns:
            是否标记成功或Future对象
        """
        if async_mode:
            return self._submit_async_operation(mark_alpha_as_submitted, alpha, decay, common_config)
        else:
            # 同步执行
            return self._execute_db_operation(mark_alpha_as_submitted, alpha, decay, common_config)
    
    def mark_alpha_as_checked(self, alpha, decay, common_config, async_mode=False):
        """
        标记Alpha为已检查状态
        
        Args:
            alpha: Alpha表达式
            decay: 衰减值
            common_config: 通用配置对象
            async_mode: 是否异步执行
            
        Returns:
            是否标记成功或Future对象
        """
        if async_mode:
            return self._submit_async_operation(mark_alpha_as_checked, alpha, decay, common_config)
        else:
            # 同步执行
            return self._execute_db_operation(mark_alpha_as_checked, alpha, decay, common_config)
    
    def get_statistics(self, async_mode=False):
        """
        获取数据库统计信息
        
        Args:
            async_mode: 是否异步执行
            
        Returns:
            统计信息字典或Future对象
        """
        if async_mode:
            return self._submit_async_operation(get_statistics)
        else:
            # 同步执行
            return self._execute_db_operation(get_statistics)
    
    def wait_for_completion(self):
        """
        等待所有异步操作完成
        
        Returns:
            是否所有操作都已完成
        """
        try:
            # 等待线程池中的所有任务完成
            self.executor.shutdown(wait=True)
            self.logger.info("所有异步数据库操作已完成")
            return True
        except Exception as e:
            self.logger.error(f"等待异步操作完成时出错: {e}")
            return False
    
    def close(self):
        """关闭异步数据库服务"""
        if self.is_shutdown:
            return
        
        self.logger.info("开始关闭异步数据库服务")
        self.is_shutdown = True
        
        try:
            # 等待所有任务完成
            self.executor.shutdown(wait=True)
            self.logger.info("线程池已关闭")
        except Exception as e:
            self.logger.error(f"关闭线程池时出错: {e}")
        
        # 关闭所有线程的数据库连接
        with self.connections_lock:
            for thread_id, conn in self.thread_connections.items():
                try:
                    if conn:
                        conn.close()
                        self.logger.debug(f"已关闭线程 {thread_id} 的数据库连接")
                except Exception as e:
                    self.logger.error(f"关闭线程 {thread_id} 数据库连接时出错: {e}")
            
            self.thread_connections.clear()
            self.logger.info("所有线程数据库连接已关闭")
        
        self.logger.info("异步数据库服务已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close() 