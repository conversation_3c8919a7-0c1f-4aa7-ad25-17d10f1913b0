#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import configparser

class CommonConfig:
    """模拟回测配置参数类，用于管理回测相关的所有参数"""
    
    def __init__(self, **kwargs):
        """初始化模拟配置对象
        
        Args:
            **kwargs: 配置参数，包括region, universe, delay等
        """
        # 基本参数
        self.region = kwargs.get('region')
        self.universe = kwargs.get('universe')
        self.delay = kwargs.get('delay')
        self.decay = kwargs.get('decay', 6)
        self.neutralization = kwargs.get('neutralization')
        self.dataset_id = kwargs.get('dataset_id')
        self.prefix = kwargs.get('prefix')
        self.events = kwargs.get('events', '')
        self.coverage = kwargs.get('coverage', 0.8)
        
        # 回测参数
        self.truncation = kwargs.get('truncation', 0.08)
        self.instrument_type = kwargs.get('instrument_type', 'EQUITY')
        self.pasteurization = kwargs.get('pasteurization', 'ON')
        self.nan_handling = kwargs.get('nan_handling', 'ON')
        self.unit_handling = kwargs.get('unit_handling', 'VERIFY')
        self.language = kwargs.get('language', 'FASTEXPR')
        self.visualization = kwargs.get('visualization', False)
        self.max_trade = kwargs.get('max_trade', 'OFF')
        self.test_period = kwargs.get('test_period', 'P0Y')

        # 提升参数
        self.alpha_num_filter = kwargs.get('alpha_num_filter')
        self.alpha_num_submit = kwargs.get('alpha_num_submit')
        self.third_promote_num = kwargs.get('third_promote_num')
        self.second_promote_sharp = kwargs.get('second_promote_sharp')
        self.second_promote_fitness = kwargs.get('second_promote_fitness')
        self.third_promote_sharp = kwargs.get('third_promote_sharp')
        self.third_promote_fitness = kwargs.get('third_promote_fitness')
        self.submit_sharp_th = kwargs.get('submit_sharp_th')
        self.submit_fitness_th = kwargs.get('submit_fitness_th')
        self.submit_margin_th = kwargs.get('submit_margin_th')
        
        # 流程控制参数
        self.is_simulate = kwargs.get('is_simulate', True)
        self.is_second_promote = kwargs.get('is_second_promote', True)
        self.is_third_promote = kwargs.get('is_third_promote', True)
        self.batch_size = kwargs.get('batch_size', 100)
        self.is_prune = kwargs.get('is_prune', False)
        self.fields_file = kwargs.get('fields_file', 'fields2.txt')
        self.is_filter_pnl = kwargs.get('is_filter_pnl', False)
        self.is_preprocess_alpha = kwargs.get('is_preprocess_alpha', True)

    @classmethod
    def from_config(cls, config):
        """从配置文件创建配置对象
        
        Args:
            config: ConfigParser对象
            
        Returns:
            SimulationConfig对象
        """
        params = {
            # 基本参数
            'dataset_id': config.get('setting', 'dataset_id'),
            'region': config.get('setting', 'region'),
            'universe': config.get('setting', 'universe'),
            'delay': config.getint('setting', 'delay'),
            'decay': config.getint('setting', 'decay', fallback=6),
            'neutralization': config.get('setting', 'neutralization'),
            'prefix': config.get('setting', 'prefix'),

            
            # 回测参数
            'truncation': config.getfloat('setting', 'truncation', fallback=0.08),
            'instrument_type': config.get('setting', 'instrument_type', fallback='EQUITY'),
            'pasteurization': config.get('setting', 'pasteurization', fallback='ON'),
            'nan_handling': config.get('setting', 'nan_handling', fallback='ON'),
            'unit_handling': config.get('setting', 'unit_handling', fallback='VERIFY'),
            'language': config.get('setting', 'language', fallback='FASTEXPR'),
            'visualization': config.get('setting', 'visualization', fallback='false'),
            'max_trade': config.get('setting', 'max_trade', fallback='OFF'),
            'test_period': config.get('setting', 'test_period', fallback='P0Y'),
            # 提升参数
            'alpha_num_filter': config.getint('others', 'alpha_num_filter'),
            'alpha_num_submit': config.getint('others', 'alpha_num_submit'),
            'third_promote_num': config.getint('others', 'third_promote_num'),
            'second_promote_sharp': config.getfloat('others', 'second_promote_sharp'),
            'second_promote_fitness': config.getfloat('others', 'second_promote_fitness'),
            'third_promote_sharp': config.getfloat('others', 'third_promote_sharp'),
            'third_promote_fitness': config.getfloat('others', 'third_promote_fitness'),
            'submit_sharp_th': config.getfloat('others', 'submit_sharp_th'),
            'submit_fitness_th': config.getfloat('others', 'submit_fitness_th'),
            'submit_margin_th': config.getfloat('others', 'submit_margin_th'),
            'events': config.get('others', 'events', fallback=''),
            'coverage': config.getfloat('others', 'coverage'),
            
            # 流程控制参数
            'is_simulate': config.getboolean('others', 'is_simulate'),
            'is_second_promote': config.getboolean('others', 'is_second_promote'),
            'is_third_promote': config.getboolean('others', 'is_third_promote'),
            'batch_size': config.getint('others', 'batch_size', fallback=100),
            'is_prune': config.getboolean('others', 'is_prune'),
            'fields_file': config.get('others', 'fields_file', fallback='fields2.txt'),
            'is_filter_pnl': config.getboolean('others', 'is_filter_pnl'),
            'is_preprocess_alpha': config.getboolean('others', 'is_preprocess_alpha'),
        }
        
        # 创建并返回配置对象
        return cls(**params)
    
    def log_parameters(self):
        """记录所有参数到日志"""
        # 记录基本参数
        logging.info(f"dataset_id: {self.dataset_id}")
        logging.info(f"region: {self.region}")
        logging.info(f"universe: {self.universe}")
        logging.info(f"delay: {self.delay}")
        logging.info(f"decay: {self.decay}")
        logging.info(f"neutralization: {self.neutralization}")
        logging.info(f"prefix: {self.prefix}")
        logging.info(f"coverage: {self.coverage}")
        
        # 记录回测参数
        logging.info(f"truncation: {self.truncation}")
        logging.info(f"instrument_type: {self.instrument_type}")
        logging.info(f"pasteurization: {self.pasteurization}")
        logging.info(f"nan_handling: {self.nan_handling}")
        logging.info(f"unit_handling: {self.unit_handling}")
        logging.info(f"language: {self.language}")
        logging.info(f"visualization: {self.visualization}")
        logging.info(f"max_trade: {self.max_trade}")
        logging.info(f"test_period: {self.test_period}")
        # 记录提升参数
        logging.info(f"events: {self.events}")
        logging.info(f"alpha_num_filter: {self.alpha_num_filter}")
        logging.info(f"alpha_num_submit: {self.alpha_num_submit}")
        logging.info(f"third_promote_num: {self.third_promote_num}")
        logging.info(f"batch_size: {self.batch_size}")
        logging.info(f"second_promote_sharp: {self.second_promote_sharp}")
        logging.info(f"second_promote_fitness: {self.second_promote_fitness}")
        logging.info(f"third_promote_sharp: {self.third_promote_sharp}")
        logging.info(f"third_promote_fitness: {self.third_promote_fitness}")
        logging.info(f"submit_sharp_th: {self.submit_sharp_th}")
        logging.info(f"submit_fitness_th: {self.submit_fitness_th}")
        logging.info(f"submit_margin_th: {self.submit_margin_th}")
        
        # 记录流程控制参数
        logging.info(f"is_simulate: {self.is_simulate}")
        logging.info(f"is_second_promote: {self.is_second_promote}")
        logging.info(f"is_third_promote: {self.is_third_promote}")
        logging.info(f"is_prune: {self.is_prune}")
        logging.info(f"fields_file: {self.fields_file}")
        logging.info(f"is_filter_pnl: {self.is_filter_pnl}")
        logging.info(f"is_preprocess_alpha: {self.is_preprocess_alpha}")

    def get_setting_params(self):
        """获取模拟回测参数字典
        
        Returns:
            包含回测所需参数的字典
        """
        return {
            'neut': self.neutralization,
            'region': self.region,
            'universe': self.universe,
            'delay': self.delay,
            'truncation': self.truncation,
            'instrument_type': self.instrument_type,
            'pasteurization': self.pasteurization,
            'nan_handling': self.nan_handling,
            'unit_handling': self.unit_handling,
            'language': self.language,
            'visualization': self.visualization,
            'maxTrade': self.max_trade,
            'testPeriod': self.test_period
        }
    
    def get_db_params(self):
        """获取数据库操作相关参数字典
        
        Returns:
            包含数据库操作所需参数的字典
        """
        return {
            'host': self.host,
            'port': self.port,
            'user': self.user,
            'password': self.password,
            'database': self.database
        }
    
    def get_other_params(self):
        """获取Alpha筛选参数字典
        
        Returns:
            包含Alpha筛选所需参数的字典
        """
        return {
            'batch_size': self.batch_size,
            'alpha_num_filter': self.alpha_num_filter,
            'third_promote_num': self.third_promote_num,
            'second_promote_sharp': self.second_promote_sharp,
            'second_promote_fitness': self.second_promote_fitness,
            'third_promote_sharp': self.third_promote_sharp,
            'third_promote_fitness': self.third_promote_fitness,
            'alpha_num_submit': self.alpha_num_submit,
            'submit_sharp_th': self.submit_sharp_th,
            'submit_fitness_th': self.submit_fitness_th,
            'submit_margin_th': self.submit_margin_th
        }
    