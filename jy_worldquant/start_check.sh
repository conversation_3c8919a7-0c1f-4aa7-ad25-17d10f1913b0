#!/bin/bash

# 配置参数
VENV_NAME="worldquant"      # Conda虚拟环境名称
LOG_DIR="logs"              # 日志目录

# 加载Conda基础配置（关键修改点）
init_conda() {
    # 尝试从常见位置加载Conda配置
    declare -a CONDA_PATHS=(
        "$HOME/anaconda3/etc/profile.d/conda.sh"
        "$HOME/miniconda3/etc/profile.d/conda.sh"
        "/opt/anaconda3/etc/profile.d/conda.sh"
    )

    for conda_path in "${CONDA_PATHS[@]}"; do
        if [ -f "$conda_path" ]; then
            source "$conda_path" >/dev/null 2>&1
            return 0
        fi
    done

    echo "❌ 错误：无法加载Conda配置，请确保Conda已正确安装"
    exit 1
}

# 检查并激活虚拟环境（核心修改）
check_venv() {
    init_conda  # 必须先初始化Conda环境

    # 获取当前Conda环境
    CURRENT_ENV=$(conda env list | grep '*' | awk '{print $1}')

    if [[ "$CURRENT_ENV" != "$VENV_NAME" ]]; then
        echo "⚠️ 正在尝试激活Conda环境: $VENV_NAME..."

        # 尝试激活环境
        if conda activate "$VENV_NAME" 2>/dev/null; then
            echo "✅ 成功激活Conda环境: $VENV_NAME"
        else
            echo "❌ 环境激活失败，可能原因："
            echo "1. 环境未创建（使用 conda create -n $VENV_NAME python=3.x 创建）"
            echo "2. 未正确安装Conda"
            echo "3. 环境路径配置错误"
            exit 1
        fi
    fi
}

# 主流程
main() {
    # 创建日志目录
    mkdir -p "$LOG_DIR" || {
        echo "❌ 无法创建日志目录: $LOG_DIR"
        exit 2
    }

    # 环境检查
    check_venv

    # 验证Python版本
    PY_VERSION=$(python -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    echo "当前Python版本: $PY_VERSION"

    # 启动程序（现在程序内部已配置好日志，不需要重定向到LOG_FILE）
    nohup python -u view_submittable_alpha.py &
    local PID=$!

    # 结果输出
    echo "============================================"
    echo "✅ 进程已启动"
    echo "PID: $PID"
    echo "日志将存储在 logs/ 目录中"
    echo "当前Conda环境: $(conda info --envs | grep '*' | awk '{print $1}')"
    echo "Python路径: $(which python)"
    echo "============================================"
}

# 执行主流程
main