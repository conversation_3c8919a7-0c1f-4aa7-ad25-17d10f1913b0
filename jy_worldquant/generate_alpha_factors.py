from machine_lib_concurrent import *
from configparser import ConfigParser

# 配置参数（可根据实际情况修改）
DATASET_ID_1 = 'fundamental65'
REGION_1 = 'USA'
UNIVERSE_1 = 'TOP3000'
DELAY_1 = 1
COVERAGE_1 = 0.5

DATASET_ID_2 = 'analyst16'
REGION_2 = 'USA'
UNIVERSE_2 = 'TOP3000'
DELAY_2 = 1
COVERAGE_2 = 0.5

# 先登录，获取session对象
s = login()
datasets_1 = ["pv1","pv103","pv13", "pv29", "pv30", "pv52", "pv53", "pv64", "pv73", "pv87", "pv96"]
datasets_2 = ["risk62", "risk70", "risk72"]
alpha_list = []
# 获取字段，传入已登录的session
for dataset in datasets_2:
    df = get_datafields(s, dataset_id=dataset, region=REGION_1, universe=UNIVERSE_1, delay=DELAY_1, coverage=COVERAGE_1)
    # fields2 = get_datafields(s, dataset_id=DATASET_ID_2, region=REGION_2, universe=UNIVERSE_2, delay=DELAY_2, coverage=COVERAGE_2)
    datafields = []
    datafields += df[df['type'] == "MATRIX"]["id"].tolist()
    # datafields += get_vec_fields(df[df['type'] == "VECTOR"]["id"].tolist())
    for field in datafields:
        alpha_template = f"zscore( ts_std_dev(abs({field}),10)/ts_std_dev({field},60) - ts_std_dev({field},10)/ts_std_dev({field}, 60))"
        alpha_list.append(alpha_template)


with open("data/others/alpha_factors_3.txt", "w", encoding="utf-8") as f:
    for expr in alpha_list:
        f.write(expr + "\n")

print(f"共生成{len(alpha_list)}个alpha表达式，已输出到alpha_factors.txt") 