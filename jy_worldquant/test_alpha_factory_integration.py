#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加路径以导入模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟配置对象
class MockConfig:
    def __init__(self):
        self.region = "USA"

def test_alpha_factory():
    print("=== Alpha Factory 完整功能测试 ===")
    
    try:
        from core.alpha_factory import AlphaFactory
        
        # 创建模拟配置
        config = MockConfig()
        
        # 初始化Alpha工厂
        factory = AlphaFactory(config)
        print("✓ AlphaFactory 初始化成功")
        
        # 测试获取可用分组类别
        categories = factory.get_available_group_categories()
        print(f"✓ 可用分组类别: {len(categories)} 个")
        print(f"  类别列表: {categories}")
        
        # 测试获取基础分组
        base_groups = factory._get_groups_for_region(['base'])
        print(f"\\n✓ 基础分组: {len(base_groups)} 个")
        print(f"  示例: {base_groups[0]}")
        
        # 测试多类别分组
        multi_groups = factory._get_groups_for_region(['base', 'valuation', 'momentum'])
        print(f"\\n✓ 多类别分组 (base+valuation+momentum): {len(multi_groups)} 个")
        
        # 测试所有分组
        all_groups = factory._get_groups_for_region(categories)
        print(f"\\n✓ 所有分组类别: {len(all_groups)} 个")
        
        # 测试GLB区域特殊处理
        config.region = "GLB"
        glb_groups = factory._get_groups_for_region(['base'])
        print(f"\\n✓ GLB区域基础分组: {len(glb_groups)} 个")
        
        # 检查是否包含fnd23_tot_assets
        fnd23_found = any("fnd23_tot_assets" in group for group in glb_groups)
        print(f"  GLB特殊字段处理: {'✓' if fnd23_found else '✗'}")
        
        print("\\n=== 所有测试通过 ===")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_alpha_factory()
    if success:
        print("\\n🎉 Alpha Factory 分组配置功能完美运行！")
    else:
        print("\\n❌ 测试失败，需要检查配置") 