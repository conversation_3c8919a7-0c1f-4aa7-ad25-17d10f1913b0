#!/bin/bash

# 配置参数
VENV_NAME="worldquant"      # Conda虚拟环境名称
PROCESS_NAME="view_submittable_alpha.py" # 要终止的进程名

# 加载Conda基础配置
init_conda() {
    declare -a CONDA_PATHS=(
        "$HOME/anaconda3/etc/profile.d/conda.sh"
        "$HOME/miniconda3/etc/profile.d/conda.sh"
        "/opt/anaconda3/etc/profile.d/conda.sh"
    )

    for conda_path in "${CONDA_PATHS[@]}"; do
        if [ -f "$conda_path" ]; then
            source "$conda_path" >/dev/null 2>&1
            return 0
        fi
    done
    echo "❌ 错误：Conda配置加载失败"
    exit 1
}

# 终止进程函数
kill_process() {
    # 获取进程PID（精确匹配进程名）
    local PID_LIST=$(pgrep -f "$PROCESS_NAME" -d ' ')

    if [ -z "$PID_LIST" ]; then
        echo "⚠️ 未找到运行中的 $PROCESS_NAME 进程"
        exit 0
    fi

    # 杀死进程
    echo "🛑 正在终止进程 (PID: $PID_LIST)..."
    kill -9 $PID_LIST 2>/dev/null

    # 验证是否终止成功
    sleep 1
    if pgrep -f "$PROCESS_NAME" >/dev/null; then
        echo "❌ 进程终止失败，请检查权限或手动处理"
        exit 1
    else
        echo "✅ 进程已终止"
    fi
}

# 主流程
main() {
    init_conda  # 初始化Conda

    # 激活目标环境（确保环境一致）
    if conda activate "$VENV_NAME" 2>/dev/null; then
        echo "ℹ️ 当前Conda环境: $VENV_NAME"
        kill_process
    else
        echo "❌ 无法激活环境 $VENV_NAME，尝试全局查找进程..."
        kill_process  # 仍尝试终止进程（可能环境未激活但进程存在）
    fi
}

main