from collections import defaultdict

my_list = [['9lRjNa9', 'group_neutralize(ts_zscore(winsorize(ts_backfill(anl4_afv4_eps_mean/fnd17_1_reptoprcexrate, 120), std=4), 22),densify(sta1_minvol1mc20))', 2.93, 0.2854, 1.84, 0.000789, '2025-05-02T14:15:21-04:00', 6], 
           ['r7WOZzo', 'group_neutralize(ts_zscore(winsorize(ts_backfill(anl4_afv4_eps_mean/fnd17_1_reptoprcexrate, 120), std=4), 22),densify(sta1_minvol1mc50))', 2.91, 0.2886, 1.78, 0.000747, '2025-05-02T14:15:20-04:00', 6]]

def prune(next_alpha_recs, prefix, keep_num):
    # prefix is the datafield prefix, fnd6, mdl175 ...
    # keep_num is the num of top sharpe same-datafield alpha
    output = []
    num_dict = defaultdict(int)
    for rec in next_alpha_recs:
        exp = rec[1]
        field = exp.split(prefix)[-1].split(",")[0]
        sharpe = rec[2]
        if sharpe < 0:
            field = "-%s"%field
        if num_dict[field] < keep_num:
            num_dict[field] += 1
            decay = rec[-1]
            exp = rec[1]
            output.append([exp,decay])
    return output

# prune(my_list, "anl4", 5)
name,description,category,definition,scope,level
winsorize,"Winsorizes x to make sure that all values in x are between the lower and upper limits, which are specified as multiple of std. Details can be found on wiki",Cross Sectional,"winsorize(x, std=4)",['REGULAR'],ALL
ts_backfill,"Backfill is the process of replacing the NAN or 0 values by a meaningful value (i.e., a first non-NaN value)",Time Series,"ts_backfill(x,lookback = d, k=1, ignore=""NAN"")",['REGULAR'],ALL

你是一名资深的量化投资研究员，请根据上面给出的operators和fields。不要使用type为VECTOR或GROUP的字段，仅使用加减乘除来组合fields，请给出200个fields组合，要求每个组合都要具有经济学意义,并且适合代入该alpha表达式中winsorize(ts_backfill({component}, 120), std=4)，共需要200个，请回答这200个组合，每行一个，不要其他多余的内容