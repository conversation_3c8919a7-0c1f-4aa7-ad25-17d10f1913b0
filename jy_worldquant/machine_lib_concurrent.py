import logging

import requests
from os import environ
from time import sleep
import time
import json
import pandas as pd
import random
import pickle
from urllib.parse import urljoin
from itertools import product
from itertools import combinations
from collections import defaultdict
import pickle
from os.path import expanduser, exists
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import os
from datetime import datetime, timedelta
from common_auth import BrainAuth
 
# 日志已在主程序中配置，这里不需要重复配置

basic_ops = ["reverse", "inverse", "rank", "zscore", "quantile", "normalize"]
 
ts_ops = ["ts_rank", "ts_zscore", "ts_delta",  "ts_sum", "ts_delay", 
          "ts_std_dev", "ts_mean",  "ts_arg_min", "ts_arg_max","ts_scale", 
          "ts_quantile", "ts_entropy", "ts_av_diff", "ts_min_max_diff", 
          "ts_min_max_cps", "ts_count_nans", "ts_min_diff", 
          "ts_decay_linear", "ts_regression", "ts_skewness", "ts_target_tvr_decay", "ts_product"]
 
ops_set = basic_ops + ts_ops

# 使用BrainAuth类替代直接加载凭证
def login():
    return BrainAuth.login()

def login_hk():
    return BrainAuth.login_hk()


def get_datasets(
    s,
    instrument_type: str = 'EQUITY',
    region: str = 'USA',
    delay: int = 1,
    universe: str = 'TOP3000'
):
    url = "https://api.worldquantbrain.com/data-sets?" +\
        f"instrumentType={instrument_type}&region={region}&delay={str(delay)}&universe={universe}"
    result = s.get(url)
    datasets_df = pd.DataFrame(result.json()['results'])
    return datasets_df


def get_datafields(
    s,
    instrument_type: str = 'EQUITY',
    region: str = 'USA',
    delay: int = 1,
    universe: str = 'TOP3000',
    dataset_id: str = '',
    search: str = '',
    coverage: float = 0.0
):
    if len(search) == 0:
        url_template = "https://api.worldquantbrain.com/data-fields?" +\
            f"&instrumentType={instrument_type}" +\
            f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" +\
            "&offset={x}"
        count = s.get(url_template.format(x=0)).json()['count']

    else:
        url_template = "https://api.worldquantbrain.com/data-fields?" +\
            f"&instrumentType={instrument_type}" +\
            f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
            f"&search={search}" +\
            "&offset={x}"
        count = 100

    datafields_list = []
    for x in range(0, count, 50):
        datafields = s.get(url_template.format(x=x))
        result = datafields.json()['results']
        datafields_list.append(datafields.json()['results'])
    datafields_list_flat = [item for sublist in datafields_list for item in sublist]

    filter_datafields_list_flat = [
        item
        for item in datafields_list_flat
        if item['coverage'] >= coverage
    ]

    datafields_df = pd.DataFrame(filter_datafields_list_flat)
    return datafields_df

def get_vec_fields(fields):

    # 请在此处添加获得权限的Vector操作符
    vec_ops = ["vec_avg", "vec_sum"]
    vec_fields = []
 
    for field in fields:
        for vec_op in vec_ops:
            if vec_op == "vec_choose":
                vec_fields.append("%s(%s, nth=-1)"%(vec_op, field))
                vec_fields.append("%s(%s, nth=0)"%(vec_op, field))
            else:
                vec_fields.append("%s(%s)"%(vec_op, field))
 
    return(vec_fields)

def process_datafields(df):
    datafields = []
    datafields += df[df['type'] == "MATRIX"]["id"].tolist()
    datafields += get_vec_fields(df[df['type'] == "VECTOR"]["id"].tolist())
    return ["winsorize(ts_backfill(%s, 120), std=4)"%field for field in datafields]
    # return ["-ts_regression(ts_delta(close,1),winsorize(ts_backfill(%s, 120), std=4),200)"%field for field in datafields]

def ts_factory(op, field):
    output = []
    #days = [3, 5, 10, 20, 60, 120, 240]
    days = [5, 22, 66, 120, 240]
    
    for day in days:
    
        alpha = "%s(%s, %d)"%(op, field, day)
        output.append(alpha)
    
    return output

def first_order_factory(fields, ops_set):
    alpha_set = []
    days = [5, 22, 66, 120, 240]
    # ts_ops特殊参数模板
    ts_op_templates = {
        # "ts_corr": lambda f: [f"ts_corr({f}, {f}, {d})" for d in days],
        "ts_zscore": lambda f: [f"ts_zscore({f}, {d})" for d in days],
        "ts_product": lambda f: [f"ts_product({f}, {d})" for d in days],
        "ts_std_dev": lambda f: [f"ts_std_dev({f}, {d})" for d in days],
        # "ts_backfill": lambda f: [f"ts_backfill({f}, {d}, k=1, ignore='NAN')" for d in days],
        "days_from_last_change": lambda f: [f"days_from_last_change({f})"],
        "last_diff_value": lambda f: [f"last_diff_value({f}, {d})" for d in days],
        "ts_scale": lambda f: [f"ts_scale({f}, {d}, constant=0)" for d in days],
        "ts_entropy": lambda f: [f"ts_entropy({f}, {d})" for d in days],
        # "ts_step": lambda f: [f"ts_step(1)"],
        "ts_sum": lambda f: [f"ts_sum({f}, {d})" for d in days],
        "ts_av_diff": lambda f: [f"ts_av_diff({f}, {d})" for d in days],
        "ts_mean": lambda f: [f"ts_mean({f}, {d})" for d in days],
        "ts_min_max_diff": lambda f: [f"ts_min_max_diff({f}, {d}, f=0.5)" for d in days],
        "ts_arg_max": lambda f: [f"ts_arg_max({f}, {d})" for d in days],
        "ts_min_max_cps": lambda f: [f"ts_min_max_cps({f}, {d}, f=2)" for d in days],
        "ts_rank": lambda f: [f"ts_rank({f}, {d}, constant=0)" for d in days],
        "ts_delay": lambda f: [f"ts_delay({f}, {d})" for d in days],
        "ts_quantile": lambda f: [f"ts_quantile({f}, {d}, driver='gaussian')" for d in days],
        "ts_count_nans": lambda f: [f"ts_count_nans({f}, {d})" for d in days],
        # "ts_covariance": lambda f: [f"ts_covariance({f}, {f}, {d})" for d in days],
        "ts_min_diff": lambda f: [f"ts_min_diff({f}, {d})" for d in days],
        "ts_decay_linear": lambda f: [f"ts_decay_linear({f}, {d}, dense=false)" for d in days],
        "ts_arg_min": lambda f: [f"ts_arg_min({f}, {d})" for d in days],
        "ts_regression": lambda f: [f"ts_regression({f}, {f}, {d}, lag=0, rettype=0)" for d in days],
        "ts_skewness": lambda f: [f"ts_skewness({f}, {d})" for d in days],
        "ts_delta": lambda f: [f"ts_delta({f}, {d})" for d in days],
        "ts_target_tvr_decay": lambda f: [f"ts_target_tvr_decay({f}, lambda_min=0, lambda_max=1, target_tvr=0.1)"],
    }
    for field in fields:
        alpha_set.append(field)
        for op in ops_set:
            if op in ts_op_templates:
                alpha_set += ts_op_templates[op](field)
            else:
                alpha_set.append(f"{op}({field})")
    return alpha_set


def load_task_pool(alpha_list, limit_of_children_simulations, limit_of_multi_simulations):
    '''
    Input:
        alpha_list : list of (alpha, decay) tuples
        limit_of_multi_simulations : number of children simulation in a multi-simulation
        limit_of_multi_simulations : number of simultaneous multi-simulations
    Output:
        task : [10 * (alpha, decay)] for a multi-simulation
        pool : [10 * [10 * (alpha, decay)]] for simultaneous multi-simulations
        pools : [[10 * [10 * (alpha, decay)]]]

    '''
    # logging.info("load task pool start")
    tasks = [alpha_list[i:i + limit_of_children_simulations] for i in range(0, len(alpha_list), limit_of_children_simulations)]
    pools = [tasks[i:i + limit_of_multi_simulations] for i in range(0, len(tasks), limit_of_multi_simulations)]
    # logging.info("load task pool done")
    return pools


def add_new_task(session, current_index, all_tasks, running_tasks, common_config):
    """补充新任务到运行队列"""
    if current_index >= len(all_tasks):
        return current_index

    new_task = all_tasks[current_index]
    new_url = send_single_task(session, generate_sim_data(new_task, common_config), current_index)
    if new_url:
        running_tasks.append({
            "progress_url": new_url,
            "task": new_task,
            "next_check": time.time(),
            "index": current_index,
            "start_time": time.time()
        })
        return current_index + 1

    return current_index

def multi_simulate(alpha_pools, common_config):   
    retry_strategy = Retry(
        total=5,
        backoff_factor=1,
        status_forcelist=[500, 502, 503, 504],
        allowed_methods=["POST", "GET"]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)

    # 扁平化所有任务池
    all_tasks = [task for pool in alpha_pools for task in pool]
    current_index = 1
    running_tasks = []  # 存储字典: {progress_url, task, next_check}
    max_concurrenttask_count = 8
    logging.info(f"all_tasks count: {len(all_tasks)}")

    with requests.Session() as s:
        s.mount("https://", adapter)
        s = login()  # 初始化会话

        # 初始填充10个任务
        while current_index <= max_concurrenttask_count and current_index < len(all_tasks):
            # 调用补充新任务函数
            current_index = add_new_task(s, current_index, all_tasks, running_tasks, common_config)

        # 动态监控循环
        while running_tasks:
            current_time = time.time()
            for task_info in running_tasks.copy():
                if task_info["next_check"] > current_time:
                    continue

                progress_url = task_info["progress_url"]
                index = task_info["index"]
                start_time = task_info["start_time"]
                try:
                    # 检查任务状态
                    response = s.get(progress_url, timeout=120)
                    retry_after = response.headers.get("Retry-After", 0)
                    # 处理retry_after逻辑
                    if retry_after != 0:
                        wait_time = min(max(20, float(retry_after)), 300)
                        task_info["next_check"] = current_time + wait_time
                        consume_time = time.time() - start_time
                        if consume_time > 300:
                            logging.info(f"🕒 任务{index}:{progress_url}已耗时{int(time.time() - start_time)}s,本次等待{wait_time}s")
                        if consume_time > 2000:
                            logging.warning( f"⚠️ 任务{index}:{progress_url}超时丢弃{int(time.time() - start_time)}s")
                            s.delete(progress_url)
                            running_tasks.remove(task_info)
                            # 调用补充新任务函数
                            current_index = add_new_task(s, current_index, all_tasks, running_tasks, common_config)
                        continue

                    # 解析任务状态
                    status = response.json().get("status", "UNKNOWN")
                    if status == "COMPLETE":
                        logging.info(f"✅ 任务{index}:{progress_url}完成, 耗时{int(time.time() - start_time)}s")
                        running_tasks.remove(task_info)
                        time.sleep(1)
                        # 调用补充新任务函数
                        current_index = add_new_task(s, current_index, all_tasks, running_tasks, common_config)
                    elif status == "ERROR":
                        logging.warning(f"❌ 任务{index}:{progress_url}失败:{str(response)}")
                        logging.error(f"failed task detail:{task_info["task"]}")
                        running_tasks.remove(task_info)
                        # 调用补充新任务函数
                        current_index = add_new_task(s, current_index, all_tasks, running_tasks, common_config)
                    else:
                        logging.warning(f"任务{index}:{progress_url}状态异常status: {status}.Response:{str(response)}")
                        task_info["next_check"] = current_time + 20  # 普通状态5秒后重查
                        s = login()

                except Exception as e:
                    logging.error(f"任务{index}:{progress_url}监控异常: {str(e)}")
                    task_info["next_check"] = current_time + 20  # 异常10秒后重试
                    s = login()

            # 控制循环频率
            time.sleep(5)


def send_single_task(session, sim_data, current_index):
    """带重试机制的任务提交函数"""
    retry_time = 10
    for attempt in range(retry_time):
        try:
            resp = session.post(
                'https://api.worldquantbrain.com/simulations',
                json=sim_data,
                timeout=(30, 120)
            )
            resp.raise_for_status()
            logging.info(f"任务{current_index}提交成功")
            return resp.headers.get('Location')
        except Exception as e:
            logging.error(f"任务{current_index}提交失败(重试{attempt + 1}/{retry_time}): {str(e)}")
            # logging.error(sim_data)
            time.sleep(60)
    return None


def generate_sim_data(alpha_list, common_config):
    sim_data_list = []
    for alpha, decay in alpha_list:
        simulation_data = {
            'type': 'REGULAR',
            'settings': {
                'instrumentType': common_config.instrument_type,
                'region': common_config.region,
                'universe': common_config.universe,
                'delay': common_config.delay,
                'decay': decay,
                'neutralization': common_config.neutralization,
                'truncation': common_config.truncation,
                'pasteurization': common_config.pasteurization,
                'testPeriod': common_config.test_period,
                'unitHandling': common_config.unit_handling,
                'nanHandling': common_config.nan_handling,
                'language': common_config.language,
                'visualization': False,
                'maxTrade': common_config.max_trade,
            },
            'regular': alpha}

        sim_data_list.append(simulation_data)
    return sim_data_list

def set_alpha_properties(
    s,
    alpha_id,
    name: str = None,
    color: str = None,
    selection_desc: str = "None",
    combo_desc: str = "None",
    tags: list[str] = None,
):
    """
    Function changes alpha's description parameters
    """

    params = {
        "color": color,
        "name": name,
        "tags": tags,
        "category": None,
        "regular": {"description": None},
        "combo": {"description": combo_desc},
        "selection": {"description": selection_desc},
    }
    response = s.patch(
        "https://api.worldquantbrain.com/alphas/" + alpha_id, json=params
    )

def get_alphas(start_date, end_date, region, alpha_num, usage, sharpe_th: float = 0.0, fitness_th: float = 0.0, margin_th: float = 0.0):
    s = login()
    output = []
    # 3E large 3C less
    count = 0
    for i in range(0, alpha_num, 100):
        url_e = "https://api.worldquantbrain.com/users/self/alphas?limit=100&offset=%d"%(i) \
                + "&status=UNSUBMITTED%1FIS_FAIL&dateCreated%3E=2025-" + start_date  \
                + "T00:00:00-04:00&dateCreated%3C2025-" + end_date \
                + "T00:00:00-04:00&is.fitness%3E" + str(fitness_th) + "&is.sharpe%3E" \
                + str(sharpe_th) + "&settings.region=" + region + "&order=-is.sharpe&hidden=false&type!=SUPER"
        url_c = "https://api.worldquantbrain.com/users/self/alphas?limit=100&offset=%d"%(i) \
                + "&status=UNSUBMITTED%1FIS_FAIL&dateCreated%3E=2025-" + start_date  \
                + "T00:00:00-04:00&dateCreated%3C2025-" + end_date \
                + "T00:00:00-04:00&is.fitness%3C-" + str(fitness_th) + "&is.sharpe%3C-" \
                + str(sharpe_th) + "&settings.region=" + region + "&order=is.sharpe&hidden=false&type!=SUPER"
        urls = [url_e]
        if usage != "submit":
            urls.append(url_c)

        logging.info(urls[:10])
        for url in urls:
            response = s.get(url)
            try:
                alpha_list = response.json()["results"]
                for j in range(len(alpha_list)):
                    alpha_id = alpha_list[j]["id"]
                    name = alpha_list[j]["name"]
                    dateCreated = alpha_list[j]["dateCreated"]
                    sharpe = alpha_list[j]["is"]["sharpe"]
                    fitness = alpha_list[j]["is"]["fitness"]
                    turnover = alpha_list[j]["is"]["turnover"]
                    margin = alpha_list[j]["is"]["margin"]
                    longCount = alpha_list[j]["is"]["longCount"]
                    shortCount = alpha_list[j]["is"]["shortCount"]
                    decay = alpha_list[j]["settings"]["decay"]
                    exp = alpha_list[j]['regular']['code']
                    tags = alpha_list[j]['tags']
                    count += 1
                    #if (sharpe > 1.2 and sharpe < 1.6) or (sharpe < -1.2 and sharpe > -1.6):
                    if (longCount + shortCount) > 100:
                        if sharpe < -sharpe_th:
                            exp = "-%s"%exp
                        rec = [alpha_id, exp, sharpe, turnover, fitness, margin, dateCreated, decay]
                        if turnover > 0.7:
                            rec.append(min(512,decay*4))
                        elif turnover > 0.6:
                            rec.append(min(512,decay*3+3))
                        elif turnover > 0.5:
                            rec.append(min(512,decay*3))
                        elif turnover > 0.4:
                            rec.append(min(512,decay*2))
                        elif turnover > 0.35:
                            rec.append(min(512,decay+4))
                        elif turnover > 0.3:
                            rec.append(min(512,decay+2))

                        if usage == "submit":
                            match tags:
                                case ["checked"]:
                                    continue
                                case ["submittable"]:
                                    continue
               
                        output.append(rec)

            except Exception as e:
                logging.error(e)
                logging.error("%d finished re-login"%i)
                s = login()

    logging.info("get_alpha count: %d"%count)

    return output

def prune(next_alpha_recs, prefix, keep_num):
    # prefix is the datafield prefix, fnd6, mdl175 ...
    # keep_num is the num of top sharpe same-datafield alpha
    output = []
    num_dict = defaultdict(int)
    for rec in next_alpha_recs:
        exp = rec[1]
        field = exp.split(prefix)[-1].split(",")[0]
        sharpe = rec[2]
        if sharpe < 0:
            field = "-%s"%field
        if num_dict[field] < keep_num:
            num_dict[field] += 1
            decay = rec[-1]
            exp = rec[1]
            output.append([exp,decay])
    return output


def get_group_second_order_factory(first_order, group_ops_param, region):
    second_order = []
    for fo in first_order:
        for group_op in group_ops_param:
            second_order += group_factory(group_op, fo, region)
    return second_order


def group_factory(op, field, region):
    output = []
    vectors = ["cap"] 
    
    # chn_group_13 = ['pv13_h_min2_sector', 'pv13_di_6l', 'pv13_rcsed_6l', 'pv13_di_5l', 'pv13_di_4l',
    #                     'pv13_di_3l', 'pv13_di_2l', 'pv13_di_1l', 'pv13_parent', 'pv13_level']
    
    
    chn_group_1 = ['sta1_top3000c30','sta1_top3000c20','sta1_top3000c10','sta1_top3000c2','sta1_top3000c5']
    
    # chn_group_2 = ['sta2_top3000_fact4_c10','sta2_top2000_fact4_c50','sta2_top3000_fact3_c20']
    
    hkg_group_13 = ['pv13_10_f3_g2_minvol_1m_sector', 'pv13_10_minvol_1m_sector', 'pv13_20_minvol_1m_sector', 
                    'pv13_2_minvol_1m_sector', 'pv13_5_minvol_1m_sector', 'pv13_1l_scibr', 'pv13_3l_scibr',
                    'pv13_2l_scibr', 'pv13_4l_scibr', 'pv13_5l_scibr']
    
    hkg_group_1 = ['sta1_allc50','sta1_allc5','sta1_allxjp_513_c20','sta1_top2000xjp_513_c5']
    
    hkg_group_2 = ['sta2_all_xjp_513_all_fact4_c10','sta2_top2000_xjp_513_top2000_fact3_c10',
                   'sta2_allfactor_xjp_513_13','sta2_top2000_xjp_513_top2000_fact3_c20']
    
    twn_group_13 = ['pv13_2_minvol_1m_sector','pv13_20_minvol_1m_sector','pv13_10_minvol_1m_sector',
                    'pv13_5_minvol_1m_sector','pv13_10_f3_g2_minvol_1m_sector','pv13_5_f3_g2_minvol_1m_sector',
                    'pv13_2_f4_g3_minvol_1m_sector']
    
    twn_group_1 = ['sta1_allc50','sta1_allxjp_513_c50','sta1_allxjp_513_c20','sta1_allxjp_513_c2',
                   'sta1_allc20','sta1_allxjp_513_c5','sta1_allxjp_513_c10','sta1_allc2','sta1_allc5']
    
    twn_group_2 = ['sta2_allfactor_xjp_513_0','sta2_all_xjp_513_all_fact3_c20',
                   'sta2_all_xjp_513_all_fact4_c20','sta2_all_xjp_513_all_fact4_c50']
    
    usa_group_13 = ['pv13_h_min2_3000_sector','pv13_r2_min20_3000_sector','pv13_r2_min2_3000_sector',
                    'pv13_r2_min2_3000_sector', 'pv13_h_min2_focused_pureplay_3000_sector']
    
    usa_group_1 = ['sta1_top3000c50','sta1_allc20','sta1_allc10','sta1_top3000c20','sta1_allc5']
    
    usa_group_2 = ['sta2_top3000_fact3_c50','sta2_top3000_fact4_c20','sta2_top3000_fact4_c10']
    
    usa_group_6 = ['mdl10_group_name']
    
    asi_group_13 = ['pv13_20_minvol_1m_sector', 'pv13_5_f3_g2_minvol_1m_sector', 'pv13_10_f3_g2_minvol_1m_sector',
                    'pv13_2_f4_g3_minvol_1m_sector', 'pv13_10_minvol_1m_sector', 'pv13_5_minvol_1m_sector']
    
    asi_group_1 = ['sta1_allc50', 'sta1_allc10', 'sta1_minvol1mc50','sta1_minvol1mc20',
                   'sta1_minvol1m_normc20', 'sta1_minvol1m_normc50']
    
    jpn_group_1 = ['sta1_alljpn_513_c5', 'sta1_alljpn_513_c50', 'sta1_alljpn_513_c2', 'sta1_alljpn_513_c20']
    
    jpn_group_2 = ['sta2_top2000_jpn_513_top2000_fact3_c20', 'sta2_all_jpn_513_all_fact1_c5',
                   'sta2_allfactor_jpn_513_9', 'sta2_all_jpn_513_all_fact1_c10']
    
    jpn_group_13 = ['pv13_2_minvol_1m_sector', 'pv13_2_f4_g3_minvol_1m_sector', 'pv13_10_minvol_1m_sector',
                    'pv13_10_f3_g2_minvol_1m_sector', 'pv13_all_delay_1_parent', 'pv13_all_delay_1_level']
    
    kor_group_13 = ['pv13_10_f3_g2_minvol_1m_sector', 'pv13_5_minvol_1m_sector', 'pv13_5_f3_g2_minvol_1m_sector',
                    'pv13_2_minvol_1m_sector', 'pv13_20_minvol_1m_sector', 'pv13_2_f4_g3_minvol_1m_sector']
    
    kor_group_1 = ['sta1_allc20','sta1_allc50','sta1_allc2','sta1_allc10','sta1_minvol1mc50',
                   'sta1_allxjp_513_c10', 'sta1_top2000xjp_513_c50']
    
    kor_group_2 =['sta2_all_xjp_513_all_fact1_c50','sta2_top2000_xjp_513_top2000_fact2_c50',
                  'sta2_all_xjp_513_all_fact4_c50','sta2_all_xjp_513_all_fact4_c5']
    
    eur_group_13 = ['pv13_5_sector', 'pv13_2_sector', 'pv13_v3_3l_scibr', 'pv13_v3_2l_scibr', 'pv13_2l_scibr',
                    'pv13_52_sector', 'pv13_v3_6l_scibr', 'pv13_v3_4l_scibr', 'pv13_v3_1l_scibr']
    
    eur_group_1 = ['sta1_allc10', 'sta1_allc2', 'sta1_top1200c2', 'sta1_allc20', 'sta1_top1200c10']
    
    eur_group_2 = ['sta2_top1200_fact3_c50','sta2_top1200_fact3_c20','sta2_top1200_fact4_c50']
    
    # glb_group_13 = ["pv13_10_f2_g3_sector", "pv13_2_f3_g2_sector", "pv13_2_sector", "pv13_52_all_delay_1_sector"]
        
    glb_group_1 = ['sta1_allc20', 'sta1_allc10', 'sta1_allc50', 'sta1_allc5']
    
    # glb_group_2 = ['sta2_all_fact4_c50', 'sta2_all_fact4_c20', 'sta2_all_fact3_c20', 'sta2_all_fact4_c10']
    
    glb_group_13 = ['pv13_2_sector', 'pv13_10_sector', 'pv13_3l_scibr', 'pv13_2l_scibr', 'pv13_1l_scibr',
                    'pv13_52_minvol_1m_all_delay_1_sector','pv13_52_minvol_1m_sector','pv13_52_minvol_1m_sector'] 
    
    amr_group_13 = ['pv13_4l_scibr', 'pv13_1l_scibr', 'pv13_hierarchy_min51_f1_sector',
                    'pv13_hierarchy_min2_600_sector', 'pv13_r2_min2_sector', 'pv13_h_min20_600_sector']
    
    #bps_group = "bucket(rank(fnd28_value_05480), range='0.1, 1, 0.1')"
    #pb_group = "bucket(rank(close/fnd28_value_05480), range='0.1, 1, 0.1')"
    cap_group = "bucket(rank(cap), range='0.1, 1, 0.1')"
    asset_group = "bucket(rank(assets),range='0.1, 1, 0.1')"
    sector_asset_group = "bucket(group_rank(assets, sector),range='0.1, 1, 0.1')"
    
    if region == "GLB":
        asset_group = "bucket(rank(fnd23_tot_assets),range='0.1, 1, 0.1')"
        sector_asset_group = "bucket(group_rank(fnd23_tot_assets, sector),range='0.1, 1, 0.1')"

    sector_cap_group = "bucket(group_rank(cap, sector),range='0.1, 1, 0.1')"

    vol_group = "bucket(rank(ts_std_dev(returns,20)),range = '0.1, 1, 0.1')"

    liquidity_group = "bucket(rank(close*volume),range = '0.1, 1, 0.1')"

    groups = ["market","sector", "industry", "subindustry",
            cap_group, asset_group, sector_cap_group, sector_asset_group, vol_group, liquidity_group]

    if region == "CHN":
        groups += chn_group_1
    if region == "TWN":
        groups += twn_group_13 + twn_group_1 + twn_group_2 
    if region == "ASI":
        groups += asi_group_13 + asi_group_1 
    if region == "USA":
        groups += usa_group_13 + usa_group_1 + usa_group_2  
    if region == "HKG":
        groups += hkg_group_13 + hkg_group_1 + hkg_group_2 
    if region == "KOR":
        groups += kor_group_13 + kor_group_1 + kor_group_2 
    if region == "EUR": 
        groups += eur_group_13 + eur_group_1 + eur_group_2 
    if region == "GLB":
        groups += glb_group_13 + glb_group_1
    if region == "AMR":
        groups += amr_group_13 
    if region == "JPN":
        groups += jpn_group_1 + jpn_group_2 + jpn_group_13 
        
    for group in groups:
        # group_mean, group_extra 需要weight参数
        if op in ["group_mean", "group_extra"]:
            for vector in vectors:
                alpha = f"{op}({field}, {vector}, densify({group}))"
                output.append(alpha)
        # group_backfill 需要d和std参数，常用d=20, std=4.0
        elif op == "group_backfill":
            d_list = [20, 60, 120]  # 可根据需要调整
            for d in d_list:
                alpha = f"{op}({field}, densify({group}), {d}, std=4.0)"
                output.append(alpha)
        # group_cartesian_product 需要两个group，两两组合
        elif op == "group_cartesian_product":
            for group2 in groups:
                if group != group2:
                    alpha = f"{op}(densify({group}), densify({group2}))"
                    output.append(alpha)
        # 只需要x, group的操作符
        elif op in ["group_rank", "group_scale", "group_zscore", "group_neutralize"]:
            alpha = f"{op}({field}, densify({group}))"
            output.append(alpha)
        # 兼容原有特殊分支
        elif op.startswith("group_vector"):
            for vector in vectors:
                alpha = f"{op}({field},{vector},densify({group}))"
                output.append(alpha)
        elif op.startswith("group_percentage"):
            alpha = f"{op}({field},densify({group}),percentage=0.5)"
            output.append(alpha)
        # 其它默认
        else:
            alpha = f"{op}({field}, densify({group}))"
            output.append(alpha)
        
    return output


def trade_when_factory(op,field,region, events: str = "open_events"):
    output = []
    apply_events = events
    match events:
        case "usa_events":
            apply_events = ["rank(rp_css_business) > 0.8", "ts_rank(rp_css_business, 22) > 0.8", "rank(vec_avg(mws82_sentiment)) > 0.8",
                  "ts_rank(vec_avg(mws82_sentiment),22) > 0.8", "rank(vec_avg(nws48_ssc)) > 0.8",
                  "ts_rank(vec_avg(nws48_ssc),22) > 0.8", "rank(vec_avg(mws50_ssc)) > 0.8", "ts_rank(vec_avg(mws50_ssc),22) > 0.8",
                  "ts_rank(vec_sum(scl12_alltype_buzzvec),22) > 0.9", "pcr_oi_270 < 1", "pcr_oi_270 > 1",]
        case "asi_events":
            apply_events = ["rank(vec_avg(mws38_score)) > 0.8", "ts_rank(vec_avg(mws38_score),22) > 0.8"]

        case "eur_events":
            apply_events = ["rank(rp_css_business) > 0.8", "ts_rank(rp_css_business, 22) > 0.8",
                  "rank(vec_avg(oth429_research_reports_fundamental_keywords_4_method_2_pos)) > 0.8",
                  "ts_rank(vec_avg(oth429_research_reports_fundamental_keywords_4_method_2_pos),22) > 0.8",
                  "rank(vec_avg(mws84_sentiment)) > 0.8", "ts_rank(vec_avg(mws84_sentiment),22) > 0.8",
                  "rank(vec_avg(mws85_sentiment)) > 0.8", "ts_rank(vec_avg(mws85_sentiment),22) > 0.8",
                  "rank(mdl110_analyst_sentiment) > 0.8", "ts_rank(mdl110_analyst_sentiment, 22) > 0.8",
                  "rank(vec_avg(nws3_scores_posnormscr)) > 0.8",
                  "ts_rank(vec_avg(nws3_scores_posnormscr),22) > 0.8",
                  "rank(vec_avg(mws36_sentiment_words_positive)) > 0.8",
                  "ts_rank(vec_avg(mws36_sentiment_words_positive),22) > 0.8"]

        case "glb_events":
            apply_events = ["rank(vec_avg(mdl109_news_sent_1m)) > 0.8",
                  "ts_rank(vec_avg(mdl109_news_sent_1m),22) > 0.8",
                  "rank(vec_avg(nws20_ssc)) > 0.8",
                  "ts_rank(vec_avg(nws20_ssc),22) > 0.8",
                  "vec_avg(nws20_ssc) > 0",
                  "rank(vec_avg(nws20_bee)) > 0.8",
                  "ts_rank(vec_avg(nws20_bee),22) > 0.8",
                  "rank(vec_avg(nws20_qmb)) > 0.8",
                  "ts_rank(vec_avg(nws20_qmb),22) > 0.8"]

        case "chn_events":
            apply_events = ["rank(vec_avg(oth111_xueqiunaturaldaybasicdivisionstat_senti_conform)) > 0.8",
                  "ts_rank(vec_avg(oth111_xueqiunaturaldaybasicdivisionstat_senti_conform),22) > 0.8",
                  "rank(vec_avg(oth111_gubanaturaldaydevicedivisionstat_senti_conform)) > 0.8",
                  "ts_rank(vec_avg(oth111_gubanaturaldaydevicedivisionstat_senti_conform),22) > 0.8",
                  "rank(vec_avg(oth111_baragedivisionstat_regi_senti_conform)) > 0.8",
                  "ts_rank(vec_avg(oth111_baragedivisionstat_regi_senti_conform),22) > 0.8"]

        case "kor_events":
            apply_events = ["rank(vec_avg(mdl110_analyst_sentiment)) > 0.8",
                  "ts_rank(vec_avg(mdl110_analyst_sentiment),22) > 0.8",
                  "rank(vec_avg(mws38_score)) > 0.8",
                  "ts_rank(vec_avg(mws38_score),22) > 0.8"]

        case "twn_events":
            apply_events = ["rank(vec_avg(mdl109_news_sent_1m)) > 0.8",
                  "ts_rank(vec_avg(mdl109_news_sent_1m),22) > 0.8",
                  "rank(rp_ess_business) > 0.8",
                  "ts_rank(rp_ess_business,22) > 0.8"]
        case _:  # _表示匹配到其他任何情况
            # logging.info(f"match case {events}")
            apply_events = []
            
    apply_events += ["ts_arg_max(volume, 5) == 0", "ts_corr(close, volume, 20) < 0",
                            "ts_corr(close, volume, 5) < 0", "ts_mean(volume,10)>ts_mean(volume,60)",
                            "group_rank(ts_std_dev(returns,60), sector) > 0.7", "ts_zscore(returns,60) > 2",
                            "ts_arg_min(volume, 5) > 3",
                            "ts_std_dev(returns, 5) > ts_std_dev(returns, 20)",
                            "ts_arg_max(close, 5) == 0", "ts_arg_max(close, 20) == 0",
                            "ts_corr(close, volume, 5) > 0", "ts_corr(close, volume, 5) > 0.3",
                            "ts_corr(close, volume, 5) > 0.5",
                            "ts_corr(close, volume, 20) > 0", "ts_corr(close, volume, 20) > 0.3",
                            "ts_corr(close, volume, 20) > 0.5",
                            "ts_regression(returns, %s, 5, lag = 0, rettype = 2) > 0" % field,
                            "ts_regression(returns, %s, 20, lag = 0, rettype = 2) > 0" % field,
                            "ts_regression(returns, ts_step(20), 20, lag = 0, rettype = 2) > 0",
                            "ts_regression(returns, ts_step(5), 5, lag = 0, rettype = 2) > 0"]

    exit_events = ["abs(returns) > 0.1", "-1"]

    for oe in apply_events:
        for ee in exit_events:
            alpha = "%s(%s, %s, %s)"%(op, oe, field, ee)
            output.append(alpha)
    return output

def check_submission(alpha_bag, gold_bag, start):
    depot = []
    s = login()
    # 创建字典跟踪每个alpha_id的NaN错误次数
    nan_error_counts = {}
    
    for idx, alpha_id in enumerate(alpha_bag):
        if idx < start:
            continue
        if idx % 5 == 0:
            logging.info("check submission. idx: %d", idx)
        if idx % 200 == 0:
            s = login()
        #print(idx)
        pc = get_check_submission(s, alpha_id)
        set_alpha_properties(s, alpha_id, name=alpha_id, tags=["checked"], color="YELLOW")

        if pc == "sleep":
            sleep(100)
            s = login()
            alpha_bag.append(alpha_id)
        elif pc != pc:
            # pc is nan
            logging.warning("check self-correlation error")
            # 增加NaN错误计数
            nan_error_counts[alpha_id] = nan_error_counts.get(alpha_id, 0) + 1
            # 检查是否超过限制
            if nan_error_counts[alpha_id] <= 2:
                logging.info(f"NaN错误第{nan_error_counts[alpha_id]}次，重试alpha: {alpha_id}")
                sleep(100)
                alpha_bag.append(alpha_id)
            else:
                logging.info(f"alpha: {alpha_id} 已超过5次NaN错误限制，丢弃")
                depot.append(alpha_id)
        elif pc == "fail":
            set_alpha_properties(s, alpha_id, name=alpha_id, tags=["checked"], color="RED")
            continue
        elif pc == "error":
            set_alpha_properties(s, alpha_id, name=alpha_id, tags=["checked"], color="BLUE")
            depot.append(alpha_id)
        else:
            logging.info(f"check submission. idx: {idx}, alpha_id: {alpha_id}")
            gold_bag.append((alpha_id, pc))
            set_alpha_properties(s, alpha_id, name=alpha_id, tags=["submittable"], color="GREEN")

    logging.info(f"depot len: {len(depot)}, depot: {depot}")
    return gold_bag

def get_check_submission(s, alpha_id):
    while True:
        result = s.get("https://api.worldquantbrain.com/alphas/" + alpha_id + "/check")
        if "retry-after" in result.headers:
            time.sleep(float(result.headers["Retry-After"]))
        else:
            break

    set_alpha_properties(s, alpha_id, name=alpha_id, tags=["checked"])

    try:
        if result.json().get("is", 0) == 0:
            logging.info("logged out")
            return "sleep"
        checks_df = pd.DataFrame(
                result.json()["is"]["checks"]
        )
        pc = checks_df[checks_df.name == "PROD_CORRELATION"]["value"].values[0]
        if not any(checks_df["result"] == "FAIL"):
            return pc
        else:
            return "fail"
    except Exception as e:
        logging.error(f"catch: {alpha_id}, {str(e)}")
        return "error"
    

def view_alphas(gold_bag):
    s = login()
    sharp_list = []
    for gold, pc in gold_bag:
        triple = locate_alpha(s, gold)
        info = [triple[0], triple[2], triple[3], triple[4], triple[5], triple[6], triple[1]]
        info.append(pc)
        sharp_list.append(info)

    sharp_list.sort(reverse=True, key = lambda x : x[1])
    for i in sharp_list:
        logging.info(i)
 
def locate_alpha(s, alpha_id):
    while True:
        alpha = s.get("https://api.worldquantbrain.com/alphas/" + alpha_id)
        if "retry-after" in alpha.headers:
            time.sleep(float(alpha.headers["Retry-After"]))
        else:
            break
    string = alpha.content.decode('utf-8')
    metrics = json.loads(string)
    #print(metrics["regular"]["code"])
    
    dateCreated = metrics["dateCreated"]
    sharpe = metrics["is"]["sharpe"]
    fitness = metrics["is"]["fitness"]
    turnover = metrics["is"]["turnover"]
    margin = metrics["is"]["margin"]
    decay = metrics["settings"]["decay"]
    exp = metrics['regular']['code']
    
    triple = [alpha_id, exp, sharpe, turnover, fitness, margin, dateCreated, decay]
    return triple



# some factory for other operators 
def vector_factory(op, field):
    output = []
    vectors = ["cap"]
    
    for vector in vectors:
    
        alpha = "%s(%s, %s)"%(op, field, vector)
        output.append(alpha)
    
    return output
 
 
def ts_comp_factory(op, field, factor, paras):
    output = []
    #l1, l2 = [3, 5, 10, 20, 60, 120, 240], paras
    l1, l2 = [5, 22, 66, 240], paras
    comb = list(product(l1, l2))
    
    for day,para in comb:
        
        if type(para) == float:
            alpha = "%s(%s, %d, %s=%.1f)"%(op, field, day, factor, para)
        elif type(para) == int:
            alpha = "%s(%s, %d, %s=%d)"%(op, field, day, factor, para)
            
        output.append(alpha)
    
    return output
 
def twin_field_factory(op, field, fields):
    
    output = []
    #days = [3, 5, 10, 20, 60, 120, 240]
    days = [5, 22, 66, 240]
    outset = list(set(fields) - set([field]))
    
    for day in days:
        for counterpart in outset:
            alpha = "%s(%s, %s, %d)"%(op, field, counterpart, day)
            output.append(alpha)
    
    return output
 

def filter_alpha_by_pnl(fo_tracker):
    """
    根据pnl筛选alpha，丢弃longCount为0超过5年的alpha
    """
    logging.info("filter_alpha_by_pnl")
    s = login()
    filtered = []
    for alpha in fo_tracker:
        alpha_id = alpha[0] if isinstance(alpha, (list, tuple)) else alpha
        try:
            url = f"https://api.worldquantbrain.com/alphas/{alpha_id}/recordsets/yearly-stats"
            resp = s.get(url, timeout=30)
            data = resp.json()
            records = data.get("records", [])
            zero_long_count = sum(1 for rec in records if len(rec) > 3 and rec[3] == 0)
            if zero_long_count > 5:
                set_alpha_properties(s, alpha_id, name=alpha_id, tags=["invalid"], color="RED")
                logging.info(f"alpha {alpha_id} 被丢弃，longCount为0的年份数: {zero_long_count}")
                continue
            filtered.append(alpha)
        except Exception as e:
            logging.error(f"filter_alpha_by_pnl error: {alpha_id}, {str(e)}")
            set_alpha_properties(s, alpha_id, name=alpha_id, tags=["invalid"], color="RED")
            sleep(10)
            s = login()
    return filtered 