name,category,scope,definition,description,documentation,level,details,content,lastModified
add,Arithmetic,['REGULAR'],"add(x, y, filter = false), x + y","Add all inputs (at least 2 inputs required). If filter = true, filter all input NaN to 0 before adding",,ALL,,,
multiply,Arithmetic,['REGULAR'],"multiply(x ,y, ... , filter=false), x * y",Multiply all inputs. At least 2 inputs are required. <PERSON><PERSON> sets the NaN values to 1,/operators/multiply,ALL,"{'id': 'multiply', 'title': 'multiply', 'lastModified': '2024-12-30T04:36:02.202600-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>Examples</b>:</p>', 'id': '9ebf7766-af87-412d-9763-a88f1f150a0e'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'multiply(rank(-returns), rank(volume/adv20), filter=true)'}, 'id': '4fd1d63a-9d5b-4193-95a0-7cd2370bae3b'}], 'sequence': 6572, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>Examples</b>:</p>', 'id': '9ebf7766-af87-412d-9763-a88f1f150a0e'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'multiply(rank(-returns), rank(volume/adv20), filter=true)'}, 'id': '4fd1d63a-9d5b-4193-95a0-7cd2370bae3b'}]",2024-12-30T04:36:02.202600-05:00
sign,Arithmetic,['REGULAR'],sign(x),if input = NaN; return NaN,,ALL,,,
subtract,Arithmetic,['REGULAR'],"subtract(x, y, filter=false), x - y","x-y. If filter = true, filter all input NaN to 0 before subtracting",,ALL,,,
log,Arithmetic,['REGULAR'],log(x),Natural logarithm. For example: Log(high/low) uses natural logarithm of high/low ratio as stock weights.,,ALL,,,
max,Arithmetic,['REGULAR'],"max(x, y, ..)",Maximum value of all inputs. At least 2 inputs are required,/operators/max,ALL,"{'id': 'max', 'title': 'max', 'lastModified': '2024-12-30T04:36:34.463416-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>Example:</b></p>', 'id': 'd75ec8d8-8a48-44d4-aaf3-da4febe7b906'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 2, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'max (close, vwap)'}, 'id': '80a65958-4544-4efb-8314-f36d580c7615'}], 'sequence': 6570, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>Example:</b></p>', 'id': 'd75ec8d8-8a48-44d4-aaf3-da4febe7b906'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 2, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'max (close, vwap)'}, 'id': '80a65958-4544-4efb-8314-f36d580c7615'}]",2024-12-30T04:36:34.463416-05:00
to_nan,Arithmetic,['REGULAR'],"to_nan(x, value=0, reverse=false)",Convert value to NaN or NaN to value if reverse=true,,,,,
abs,Arithmetic,['REGULAR'],abs(x),Absolute value of x,,ALL,,,
divide,Arithmetic,['REGULAR'],"divide(x, y), x / y",x / y,,ALL,,,
min,Arithmetic,['REGULAR'],"min(x, y ..)",Minimum value of all inputs. At least 2 inputs are required,/operators/min,ALL,"{'id': 'min', 'title': 'min', 'lastModified': '2024-12-30T04:36:21.252157-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>Example:</b></p>', 'id': '27f7c300-8b51-43ed-bcb9-4618e5578f42'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'min(close, vwap)'}, 'id': '6b177fca-d195-44c2-a0d8-bc6ea03cdafd'}], 'sequence': 6571, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>Example:</b></p>', 'id': '27f7c300-8b51-43ed-bcb9-4618e5578f42'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'min(close, vwap)'}, 'id': '6b177fca-d195-44c2-a0d8-bc6ea03cdafd'}]",2024-12-30T04:36:21.252157-05:00
signed_power,Arithmetic,['REGULAR'],"signed_power(x, y)",x raised to the power of y such that final result preserves sign of x,/operators/signed_power,ALL,"{'id': 'signed_power', 'title': 'signed_power', 'lastModified': '2024-12-30T04:40:14.620305-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>sign(x) * (abs(x) ^ y)</b><br/>x raised to the power of y such that final result preserves sign of x. For power of 2, x ^ y will be a parabola but signed_power(x, y) will be odd and one-to-one function (unique value of x for certain value of signed_power(x, y)) unlike parabola.</p>', 'id': 'a87fcfda-ba37-4d65-adef-5f56c7d082ef'}, {'type': 'IMAGE', 'value': {'title': 'signed_power.max-165x165.png', 'width': 165, 'height': 164, 'fileSize': 9198, 'url': 'https://api.worldquantbrain.com/content/images/ajQEQ1jyo_cQ9fKSSpERmtkLDYI=/321/original/signed_power.max-165x165.png'}, 'id': '2e0293f9-2f51-4615-928e-3bc5c4319f90'}, {'type': 'TEXT', 'value': '<p><b>Example:</b><br/>If x = 3, y = 2 ⇒ abs(x) = 3 ⇒ abs(x) ^ y = 9 and sign(x) = +1 ⇒ sign(x) * (abs(x) ^ y) = signed_power(x, y) = 9<br/>If x = -9, y = 0.5 ⇒ abs(x) = 9 ⇒ abs(x) ^ y = 3 and sign(x) = -1 ⇒ sign(x) * (abs(x) ^ y) = signed_power(x, y)</p>', 'id': 'e7ace4d3-f057-461a-b230-debeb092efe7'}], 'sequence': 6585, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>sign(x) * (abs(x) ^ y)</b><br/>x raised to the power of y such that final result preserves sign of x. For power of 2, x ^ y will be a parabola but signed_power(x, y) will be odd and one-to-one function (unique value of x for certain value of signed_power(x, y)) unlike parabola.</p>', 'id': 'a87fcfda-ba37-4d65-adef-5f56c7d082ef'}, {'type': 'IMAGE', 'value': {'title': 'signed_power.max-165x165.png', 'width': 165, 'height': 164, 'fileSize': 9198, 'url': 'https://api.worldquantbrain.com/content/images/ajQEQ1jyo_cQ9fKSSpERmtkLDYI=/321/original/signed_power.max-165x165.png'}, 'id': '2e0293f9-2f51-4615-928e-3bc5c4319f90'}, {'type': 'TEXT', 'value': '<p><b>Example:</b><br/>If x = 3, y = 2 ⇒ abs(x) = 3 ⇒ abs(x) ^ y = 9 and sign(x) = +1 ⇒ sign(x) * (abs(x) ^ y) = signed_power(x, y) = 9<br/>If x = -9, y = 0.5 ⇒ abs(x) = 9 ⇒ abs(x) ^ y = 3 and sign(x) = -1 ⇒ sign(x) * (abs(x) ^ y) = signed_power(x, y)</p>', 'id': 'e7ace4d3-f057-461a-b230-debeb092efe7'}]",2024-12-30T04:40:14.620305-05:00
inverse,Arithmetic,['REGULAR'],inverse(x),1 / x,,ALL,,,
sqrt,Arithmetic,['REGULAR'],sqrt(x),Square root of x,,ALL,,,
reverse,Arithmetic,['REGULAR'],reverse(x), - x,,ALL,,,
power,Arithmetic,['REGULAR'],"power(x, y)",x ^ y,/operators/power,ALL,"{'id': 'power', 'title': 'power', 'lastModified': '2025-04-01T05:10:12.015739-04:00', 'content': [{'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'power (returns, volume/adv20); power (returns, volume/adv20, precise=true)'}, 'id': 'b403f48b-e9ca-43db-bfe7-b8e793c116e4'}, {'type': 'TEXT', 'value': '<p></p><p>power (x, y) operator can be used to implement popular mathematical functions. For example, sigmoid(close) can be implemented using power(x) as:</p>', 'id': '3b419dda-7465-4fea-82cd-c89f64c1e4f1'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 1, 'neutralization': 'MARKET', 'truncation': 1.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': '1/(1+ power(2.7182, -close)'}, 'id': '548e0887-bc46-4c0e-adbd-425fa6b3a744'}], 'sequence': 6575, 'category': 'Operators'}","[{'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'power (returns, volume/adv20); power (returns, volume/adv20, precise=true)'}, 'id': 'b403f48b-e9ca-43db-bfe7-b8e793c116e4'}, {'type': 'TEXT', 'value': '<p></p><p>power (x, y) operator can be used to implement popular mathematical functions. For example, sigmoid(close) can be implemented using power(x) as:</p>', 'id': '3b419dda-7465-4fea-82cd-c89f64c1e4f1'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 1, 'neutralization': 'MARKET', 'truncation': 1.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': '1/(1+ power(2.7182, -close)'}, 'id': '548e0887-bc46-4c0e-adbd-425fa6b3a744'}]",2025-04-01T05:10:12.015739-04:00
densify,Arithmetic,['REGULAR'],densify(x),Converts a grouping field of many buckets into lesser number of only available buckets so as to make working with grouping fields computationally efficient,/operators/densify,ALL,"{'id': 'densify', 'title': 'densify', 'lastModified': '2024-12-30T04:40:23.398901-05:00', 'content': [{'type': 'TEXT', 'value': '<p>This operator converts a grouping field with many buckets into a lesser number of only the available buckets, making working with grouping fields computationally efficient. The example below will clarify the implementation.</p><p><b>Example:</b></p><p>Say a grouping field is provided as an integer (e.g., industry: tech -&gt; 0, airspace -&gt; 1, ...) and for a certain date, we have instruments with grouping field values among {0, 1, 2, 99}. Instead of creating 100 buckets and keeping 96 of them empty, it is better to just create 4 buckets with values {0, 1, 2, 3}. So, if the number of unique values in x is n, densify maps those values between 0 and (n-1). The order of magnitude need not be preserved.</p>', 'id': '769c9c88-1398-45fe-9c63-c2e8472a9e96'}], 'sequence': 6560, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>This operator converts a grouping field with many buckets into a lesser number of only the available buckets, making working with grouping fields computationally efficient. The example below will clarify the implementation.</p><p><b>Example:</b></p><p>Say a grouping field is provided as an integer (e.g., industry: tech -&gt; 0, airspace -&gt; 1, ...) and for a certain date, we have instruments with grouping field values among {0, 1, 2, 99}. Instead of creating 100 buckets and keeping 96 of them empty, it is better to just create 4 buckets with values {0, 1, 2, 3}. So, if the number of unique values in x is n, densify maps those values between 0 and (n-1). The order of magnitude need not be preserved.</p>', 'id': '769c9c88-1398-45fe-9c63-c2e8472a9e96'}]",2024-12-30T04:40:23.398901-05:00
or,Logical,['REGULAR'],"or(input1, input2)",Logical OR operator returns true if either or both inputs are true and returns false otherwise,,ALL,,,
and,Logical,['REGULAR'],"and(input1, input2)","Logical AND operator, returns true if both operands are true and returns false otherwise",,ALL,,,
not,Logical,['REGULAR'],not(x),"Returns the logical negation of x. If x is true (1), it returns false (0), and if input is false (0), it returns true (1).",,ALL,,,
is_nan,Logical,['REGULAR'],is_nan(input),If (input == NaN) return 1 else return 0,/operators/is_nan,ALL,"{'id': 'is_nan', 'title': 'is_nan', 'lastModified': '2025-04-01T05:12:42.251246-04:00', 'content': [{'type': 'TEXT', 'value': '<p></p><p>is_nan(x) operator can be used to identify NaN values and replace them to a default value using if_else statement. For example:</p>', 'id': '3037aea2-a13f-4b44-9e41-f99629459a3c'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 1, 'neutralization': 'MARKET', 'truncation': 1.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'if_else(is_nan(rank(sales)), 0.5, rank(sales))'}, 'id': '051070fa-2f46-417f-9aee-fb6997d4d850'}, {'type': 'TEXT', 'value': '<p>In this example, in case sales value is NaN for any instrument, then the expression will replace it with the mean value of rank, that is 0.5.</p>', 'id': '67188e86-609b-41dc-8a5c-b89ae1c5790b'}], 'sequence': 1232, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p></p><p>is_nan(x) operator can be used to identify NaN values and replace them to a default value using if_else statement. For example:</p>', 'id': '3037aea2-a13f-4b44-9e41-f99629459a3c'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 1, 'neutralization': 'MARKET', 'truncation': 1.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'if_else(is_nan(rank(sales)), 0.5, rank(sales))'}, 'id': '051070fa-2f46-417f-9aee-fb6997d4d850'}, {'type': 'TEXT', 'value': '<p>In this example, in case sales value is NaN for any instrument, then the expression will replace it with the mean value of rank, that is 0.5.</p>', 'id': '67188e86-609b-41dc-8a5c-b89ae1c5790b'}]",2025-04-01T05:12:42.251246-04:00
less,Logical,['REGULAR'],input1 < input2,"If input1 < input2 return true, else return false",,ALL,,,
equal,Logical,['REGULAR'],input1 == input2,Returns true if both inputs are same and returns false otherwise,,ALL,,,
greater,Logical,['REGULAR'],input1 > input2,Logic comparison operators to compares two inputs,,ALL,,,
if_else,Logical,['REGULAR'],"if_else(input1, input2, input 3)",If input1 is true then return input2 else return input3.,/operators/if_else,ALL,"{'id': 'if_else', 'title': 'if_else', 'lastModified': '2024-12-30T04:38:07.939341-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>if_else(event_condition, Alpha_expression_1, Alpha_expression_2)</b></p>', 'id': 'dc612fc0-2482-4db2-a3ed-ae4f894d26d6'}, {'type': 'TEXT', 'value': '<p>If the event condition provided is true, Alpha_expression_1 will be returned. If the event condition provided is false, Alpha_expression_2 will be returned.</p><p><b>Example:</b></p><p>We are interested in testing our hypothesis that if the stock price of a company has increased over the last 2 days, it may decrease in the future. Also, if the number of stocks bought and sold today is higher than the monthly average, then the reversion effect may be observed more profoundly.</p><p>We will implement this hypothesis by taking positions according to the difference of close price today and 3 days ago with alpha_2 using the ts_delta operator. When current volume is higher than average daily volume, we will take a larger position by multiplying by 2 to get alpha_1.</p>', 'id': '0a237d70-88bd-4665-b67e-310582e1cf0e'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'Event = volume > adv20;\r\nalpha_1 = 2 * (-ts_delta(close, 3));\r\nalpha_2 = (-ts_delta(close, 3));\r\nif_else(event, alpha_1, alpha_2)'}, 'id': 'cd215a47-01b2-4766-8729-9ed20dbe3a96'}], 'sequence': 111, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>if_else(event_condition, Alpha_expression_1, Alpha_expression_2)</b></p>', 'id': 'dc612fc0-2482-4db2-a3ed-ae4f894d26d6'}, {'type': 'TEXT', 'value': '<p>If the event condition provided is true, Alpha_expression_1 will be returned. If the event condition provided is false, Alpha_expression_2 will be returned.</p><p><b>Example:</b></p><p>We are interested in testing our hypothesis that if the stock price of a company has increased over the last 2 days, it may decrease in the future. Also, if the number of stocks bought and sold today is higher than the monthly average, then the reversion effect may be observed more profoundly.</p><p>We will implement this hypothesis by taking positions according to the difference of close price today and 3 days ago with alpha_2 using the ts_delta operator. When current volume is higher than average daily volume, we will take a larger position by multiplying by 2 to get alpha_1.</p>', 'id': '0a237d70-88bd-4665-b67e-310582e1cf0e'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'Event = volume > adv20;\r\nalpha_1 = 2 * (-ts_delta(close, 3));\r\nalpha_2 = (-ts_delta(close, 3));\r\nif_else(event, alpha_1, alpha_2)'}, 'id': 'cd215a47-01b2-4766-8729-9ed20dbe3a96'}]",2024-12-30T04:38:07.939341-05:00
not_equal,Logical,['REGULAR'],input1!= input2,Returns true if both inputs are NOT the same and returns false otherwise,,ALL,,,
less_equal,Logical,['REGULAR'],input1 <= input2,"Returns true if input1 <= input2, return false otherwise",,ALL,,,
greater_equal,Logical,['REGULAR'],input1 >= input2,"Returns true if input1 >= input2, return false otherwise",,ALL,,,
ts_corr,Time Series,['REGULAR'],"ts_corr(x, y, d)",Returns correlation of x and y for the past d days,/operators/ts_corr,ALL,"{'id': 'ts_corr', 'title': 'ts_corr', 'lastModified': '2024-12-30T04:40:36.534315-05:00', 'content': [{'type': 'TEXT', 'value': ""<p><b>ts_corr(x, y, d)</b></p><p>Pearson correlation measures the linear relationship between two variables. It's most effective when the variables are normally distributed and the relationship is linear.</p>"", 'id': '2dd1886d-dd97-4c19-aab3-8dcfca88e9fc'}, {'type': 'EQUATION', 'value': '$$Correlation(x,y) = \\frac{\\sum_{i=t-d+1}^t (x_i - \\bar{x})(y_i - \\bar{y})}{\\sqrt{\\sum_{i=t-d+1}^t (x_i - \\bar{x})^2 (y_i - \\bar{y})^2}}$$', 'id': '826b7600-437f-461d-9a2e-9878bd2c46c8'}, {'type': 'TEXT', 'value': '<p><b>Example:</b></p>', 'id': '542e9a12-5c25-4012-87f3-6da7c3006697'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'ts_corr(vwap, close, 20)'}, 'id': 'ecbd2cba-c575-4efd-8d31-83277445901b'}], 'sequence': 6595, 'category': 'Operators'}","[{'type': 'TEXT', 'value': ""<p><b>ts_corr(x, y, d)</b></p><p>Pearson correlation measures the linear relationship between two variables. It's most effective when the variables are normally distributed and the relationship is linear.</p>"", 'id': '2dd1886d-dd97-4c19-aab3-8dcfca88e9fc'}, {'type': 'EQUATION', 'value': '$$Correlation(x,y) = \\frac{\\sum_{i=t-d+1}^t (x_i - \\bar{x})(y_i - \\bar{y})}{\\sqrt{\\sum_{i=t-d+1}^t (x_i - \\bar{x})^2 (y_i - \\bar{y})^2}}$$', 'id': '826b7600-437f-461d-9a2e-9878bd2c46c8'}, {'type': 'TEXT', 'value': '<p><b>Example:</b></p>', 'id': '542e9a12-5c25-4012-87f3-6da7c3006697'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'ts_corr(vwap, close, 20)'}, 'id': 'ecbd2cba-c575-4efd-8d31-83277445901b'}]",2024-12-30T04:40:36.534315-05:00
ts_zscore,Time Series,['REGULAR'],"ts_zscore(x, d)","Z-score is a numerical measurement that describes a value's relationship to the mean of a group of values. Z-score is measured in terms of standard deviations from the mean: (x - tsmean(x,d)) / tsstddev(x,d). This operator may help reduce outliers and drawdown.",,ALL,,,
ts_product,Time Series,['REGULAR'],"ts_product(x, d)",Returns product of x for the past d days,/operators/ts_product,ALL,"{'id': 'ts_product', 'title': 'ts_product', 'lastModified': '2025-04-01T05:21:48.246260-04:00', 'content': [{'type': 'TEXT', 'value': '<p>ts_product(x, d) can be used to calculate geometric mean of data fields. The geometric mean is generally a better method for averaging rates of return, growth rates of fundamentals. For example, geometric mean of daily stock returns for past 10 days can be calculated as:</p>', 'id': '342a6a71-4fa9-4fbe-aec3-2e2343a34936'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 1, 'neutralization': 'MARKET', 'truncation': 1.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'power(ts_product(returns, 10), 1/10)'}, 'id': 'f0cccc04-bc36-49a8-9fb5-d3d5d11ca8be'}], 'sequence': 1233, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>ts_product(x, d) can be used to calculate geometric mean of data fields. The geometric mean is generally a better method for averaging rates of return, growth rates of fundamentals. For example, geometric mean of daily stock returns for past 10 days can be calculated as:</p>', 'id': '342a6a71-4fa9-4fbe-aec3-2e2343a34936'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 1, 'neutralization': 'MARKET', 'truncation': 1.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'power(ts_product(returns, 10), 1/10)'}, 'id': 'f0cccc04-bc36-49a8-9fb5-d3d5d11ca8be'}]",2025-04-01T05:21:48.246260-04:00
ts_std_dev,Time Series,['REGULAR'],"ts_std_dev(x, d)",Returns standard deviation of x for the past d days,,ALL,,,
ts_backfill,Time Series,['REGULAR'],"ts_backfill(x,lookback = d, k=1, ignore=""NAN"")","Backfill is the process of replacing the NAN or 0 values by a meaningful value (i.e., a first non-NaN value)",/operators/ts_backfill,ALL,"{'id': 'ts_backfill', 'title': 'ts_backfill', 'lastModified': '2024-12-30T04:37:42.095530-05:00', 'content': [{'type': 'TEXT', 'value': '<p>ts_backfill(x,lookback = d, k=1, ignore=""NAN"")</p><p>The ts_backfill operator replaces NaN values with the last available non-NaN value. If the input value of the data field x is NaN, the ts_backfill operator will check available input values of the same data field for the past d number of days, and output the most recent available non-NaN input value. If the k parameter is set, then the ts_backfill operator will output the kth most recent available non-NaN input value.</p><p>This operator improves weight coverage and may help to reduce drawdown risk.</p><p><b>Example:</b> ts_backfill(x, 252)</p><ul><li>If the input value for data field x = non-NaN, then output = x</li><li>If the input value for data field x = NaN, then output = most recent available non-NaN input value for x in the past 252 days</li></ul>', 'id': '831f9762-e976-4831-848d-235615125a33'}], 'sequence': 6594, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>ts_backfill(x,lookback = d, k=1, ignore=""NAN"")</p><p>The ts_backfill operator replaces NaN values with the last available non-NaN value. If the input value of the data field x is NaN, the ts_backfill operator will check available input values of the same data field for the past d number of days, and output the most recent available non-NaN input value. If the k parameter is set, then the ts_backfill operator will output the kth most recent available non-NaN input value.</p><p>This operator improves weight coverage and may help to reduce drawdown risk.</p><p><b>Example:</b> ts_backfill(x, 252)</p><ul><li>If the input value for data field x = non-NaN, then output = x</li><li>If the input value for data field x = NaN, then output = most recent available non-NaN input value for x in the past 252 days</li></ul>', 'id': '831f9762-e976-4831-848d-235615125a33'}]",2024-12-30T04:37:42.095530-05:00
days_from_last_change,Time Series,['REGULAR'],days_from_last_change(x),Amount of days since last change of x,,ALL,,,
last_diff_value,Time Series,['REGULAR'],"last_diff_value(x, d)",Returns last x value not equal to current x value from last d days,,ALL,,,
ts_scale,Time Series,['REGULAR'],"ts_scale(x, d, constant = 0)","Returns (x - ts_min(x, d)) / (ts_max(x, d) - ts_min(x, d)) + constant. This operator is similar to scale down operator but acts in time series space",/operators/ts_scale,ALL,"{'id': 'ts_scale', 'title': 'ts_scale', 'lastModified': '2024-12-30T04:28:08.688202-05:00', 'content': [{'type': 'TEXT', 'value': '<p></p><p>This operator returns (x – ts_min(x, d)) / (ts_max(x, d) – ts_min(x, d)) + constant<br/>This operator is similar to scale down operator but acts in time series space<br/></p><p><b>Example:</b><br/>If d = 6 and values for last 6 days are [6,2,8,5,9,4] with first element being today’s value, ts_min(x,d) = 2, ts_max(x,d) = 9<br/>ts_scale(x,d,constant = 1) = 1 + (6-2)/(9-2) = 1.57</p>', 'id': 'bac61b2e-cef9-4612-9004-cb113924fe09'}], 'sequence': 6608, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p></p><p>This operator returns (x – ts_min(x, d)) / (ts_max(x, d) – ts_min(x, d)) + constant<br/>This operator is similar to scale down operator but acts in time series space<br/></p><p><b>Example:</b><br/>If d = 6 and values for last 6 days are [6,2,8,5,9,4] with first element being today’s value, ts_min(x,d) = 2, ts_max(x,d) = 9<br/>ts_scale(x,d,constant = 1) = 1 + (6-2)/(9-2) = 1.57</p>', 'id': 'bac61b2e-cef9-4612-9004-cb113924fe09'}]",2024-12-30T04:28:08.688202-05:00
ts_step,Time Series,['REGULAR'],ts_step(1),Returns days' counter,,ALL,,,
ts_sum,Time Series,['REGULAR'],"ts_sum(x, d)",Sum values of x for the past d days.,,ALL,,,
ts_av_diff,Time Series,['REGULAR'],"ts_av_diff(x, d)","Returns x - tsmean(x, d), but deals with NaNs carefully. That is NaNs are ignored during mean computation",/operators/ts_av_diff,ALL,"{'id': 'ts_av_diff', 'title': 'ts_av_diff', 'lastModified': '2024-12-30T04:32:09.600819-05:00', 'content': [{'type': 'TEXT', 'value': '<p></p><p>This operator returns x – ts_mean(x, d), but it deals with NaNs carefully<br/></p><p><b>Example:</b><br/>If d = 6 and values for past 6 days are [6,2,8,5,9,NaN] then ts_mean(x,d) = 6 since NaN are ignored from mean computation. Hence, ts_av_diff(x,d) = 6-6 = 0</p>', 'id': '9cb31f83-e655-41b3-a0e5-f7d9487cdcfd'}], 'sequence': 6593, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p></p><p>This operator returns x – ts_mean(x, d), but it deals with NaNs carefully<br/></p><p><b>Example:</b><br/>If d = 6 and values for past 6 days are [6,2,8,5,9,NaN] then ts_mean(x,d) = 6 since NaN are ignored from mean computation. Hence, ts_av_diff(x,d) = 6-6 = 0</p>', 'id': '9cb31f83-e655-41b3-a0e5-f7d9487cdcfd'}]",2024-12-30T04:32:09.600819-05:00
ts_mean,Time Series,['REGULAR'],"ts_mean(x, d)",Returns average value of x for the past d days.,,ALL,,,
ts_arg_max,Time Series,['REGULAR'],"ts_arg_max(x, d)","Returns the relative index of the max value in the time series for the past d days. If the current day has the max value for the past d days, it returns 0. If previous day has the max value for the past d days, it returns 1",/operators/ts_arg_max,ALL,"{'id': 'ts_arg_max', 'title': 'ts_arg_max', 'lastModified': '2024-12-30T04:32:41.952253-05:00', 'content': [{'type': 'TEXT', 'value': '<p></p><p>It returns the relative index of the max value in the time series for the past d days. If the current day has the max value for the past d days, it returns 0. If previous day has the max value for the past d days, it returns 1.<br/></p><p><b>Example:</b><br/>If d = 6 and values for past 6 days are [6,2,8,5,9,4] with first element being today’s value then max value is 9 and it is present 4 days before today. Hence, ts_arg_max(x, d) = 4</p>', 'id': 'cd755966-731c-4e08-864a-cf48648e9a4a'}], 'sequence': 6591, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p></p><p>It returns the relative index of the max value in the time series for the past d days. If the current day has the max value for the past d days, it returns 0. If previous day has the max value for the past d days, it returns 1.<br/></p><p><b>Example:</b><br/>If d = 6 and values for past 6 days are [6,2,8,5,9,4] with first element being today’s value then max value is 9 and it is present 4 days before today. Hence, ts_arg_max(x, d) = 4</p>', 'id': 'cd755966-731c-4e08-864a-cf48648e9a4a'}]",2024-12-30T04:32:41.952253-05:00
ts_max,Time Series,['REGULAR'],"ts_max(x, d)",Returns max value of x for the past d days,,,,,
ts_rank,Time Series,['REGULAR'],"ts_rank(x, d, constant = 0)","Rank the values of x for each instrument over the past d days, then return the rank of the current value + constant. If not specified, by default, constant = 0.",,ALL,,,
ts_delay,Time Series,['REGULAR'],"ts_delay(x, d)",Returns x value d days ago,,ALL,,,
ts_quantile,Time Series,['REGULAR'],"ts_quantile(x,d, driver=""gaussian"" )","It calculates ts_rank and apply to its value an inverse cumulative density function from driver distribution. Possible values of driver (optional ) are ""gaussian"", ""uniform"", ""cauchy"" distribution where ""gaussian"" is the default.",,ALL,,,
ts_min,Time Series,['REGULAR'],"ts_min(x, d)",Returns min value of x for the past d days,,,,,
ts_count_nans,Time Series,['REGULAR'],"ts_count_nans(x ,d)",Returns the number of NaN values in x for the past d days,,ALL,,,
ts_covariance,Time Series,['REGULAR'],"ts_covariance(y, x, d)",Returns covariance of y and x for the past d days,,ALL,,,
ts_decay_linear,Time Series,['REGULAR'],"ts_decay_linear(x, d, dense = false)",Returns the linear decay on x for the past d days. Dense parameter=false means operator works in sparse mode and we treat NaN as 0. In dense mode we do not.,/operators/ts_decay_linear,ALL,"{'id': 'ts_decay_linear', 'title': 'ts_decay_linear', 'lastModified': '2024-12-30T04:37:07.926791-05:00', 'content': [{'type': 'TEXT', 'value': '<p>ts_decay_linear(x, d, dense = false)</p><p>Returns the linear decay on x for the past d days. Dense parameter=false means operator works in sparse mode and we treat NaN as 0. In dense mode we do not. Data smoothing techniques like linear decay reduce noise in time-series data by applying a decay factor to older observations, which helps to stabilize the dataset.</p><p>This operator improves turnover and drawdown.</p><p><b>Example:</b></p><ul><li>For a stock with the following prices over the last 5 days:<ul><li>Day 0: 30 (outlier)</li><li>Day -1: 5</li><li>Day -2: 4</li><li>Day -3: 5</li><li>Day -4: 6</li></ul></li><li>The calculation would be:<ul><li>Numerator = (30⋅5)+(5⋅4)+(4⋅3)+(5⋅2)+(6⋅1)=150+20+12+10+6=198</li><li>Denominator=5+4+3+2+1=15</li><li>Weighted Average=198/15=13.2</li></ul></li><li>The weighted average value of 13.2 is used instead of the outlier value of 20 for assigning weight.</li></ul>', 'id': '06595b17-9992-4751-b675-aa061ad43829'}], 'sequence': 6599, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>ts_decay_linear(x, d, dense = false)</p><p>Returns the linear decay on x for the past d days. Dense parameter=false means operator works in sparse mode and we treat NaN as 0. In dense mode we do not. Data smoothing techniques like linear decay reduce noise in time-series data by applying a decay factor to older observations, which helps to stabilize the dataset.</p><p>This operator improves turnover and drawdown.</p><p><b>Example:</b></p><ul><li>For a stock with the following prices over the last 5 days:<ul><li>Day 0: 30 (outlier)</li><li>Day -1: 5</li><li>Day -2: 4</li><li>Day -3: 5</li><li>Day -4: 6</li></ul></li><li>The calculation would be:<ul><li>Numerator = (30⋅5)+(5⋅4)+(4⋅3)+(5⋅2)+(6⋅1)=150+20+12+10+6=198</li><li>Denominator=5+4+3+2+1=15</li><li>Weighted Average=198/15=13.2</li></ul></li><li>The weighted average value of 13.2 is used instead of the outlier value of 20 for assigning weight.</li></ul>', 'id': '06595b17-9992-4751-b675-aa061ad43829'}]",2024-12-30T04:37:07.926791-05:00
jump_decay,Time Series,['REGULAR'],"jump_decay(x, d, sensitivity=0.5, force=0.1)",If there is a huge jump in current data compare to previous one,/operators/jump_decay,,"{'id': 'jump_decay', 'title': 'jump_decay', 'lastModified': '2024-12-30T04:41:05.009296-05:00', 'content': [{'type': 'TEXT', 'value': '<p>If there is a huge jump in current data compare to previous one, apply force:</p><p>jump_decay(x) = abs(x-ts_delay(x, 1)) &gt; sensitivity * ts_stddev(x,d) ? ts_delay(x,1) + ts_delta(x, 1) * force: x</p><p>If <b>stddev</b> enabled, jump threshold will be calculated as <b>sensitivity * stddev</b> otherwise it is <b>sensitivity</b></p><p><b>Example:</b></p>', 'id': '8ba183ff-fffd-4feb-a648-3c37255f0029'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'jump_decay(sales/assets,252,stddev=True,sensitivity=0.5,force=0.1)'}, 'id': '47ff2a84-e5b5-41da-b41d-518410ed753d'}], 'sequence': 6566, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>If there is a huge jump in current data compare to previous one, apply force:</p><p>jump_decay(x) = abs(x-ts_delay(x, 1)) &gt; sensitivity * ts_stddev(x,d) ? ts_delay(x,1) + ts_delta(x, 1) * force: x</p><p>If <b>stddev</b> enabled, jump threshold will be calculated as <b>sensitivity * stddev</b> otherwise it is <b>sensitivity</b></p><p><b>Example:</b></p>', 'id': '8ba183ff-fffd-4feb-a648-3c37255f0029'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'jump_decay(sales/assets,252,stddev=True,sensitivity=0.5,force=0.1)'}, 'id': '47ff2a84-e5b5-41da-b41d-518410ed753d'}]",2024-12-30T04:41:05.009296-05:00
ts_arg_min,Time Series,['REGULAR'],"ts_arg_min(x, d)","Returns the relative index of the min value in the time series for the past d days; If the current day has the min value for the past d days, it returns 0; If previous day has the min value for the past d days, it returns 1.",/operators/ts_arg_min,ALL,"{'id': 'ts_arg_min', 'title': 'ts_arg_min', 'lastModified': '2025-02-06T03:48:46.256322-05:00', 'content': [{'type': 'TEXT', 'value': '<p>ts_arg_min(x, d)</p><p>It returns the relative index of the min value in the time series for the past d days. If the current day has the min value for the past d days, it returns 0. If previous day has the min value for the past d days, it returns 1.<br/></p><p><b>Example:</b><br/>If d = 6 and values for past 6 days are [6,2,8,5,9,4] with first element being today’s value then min value is 2 and it is present 1 days before today. Hence, ts_arg_min(x, d) = 1</p>', 'id': '363c4ac8-f2e5-430c-bd4b-9f39bba7a0d1'}], 'sequence': 6592, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>ts_arg_min(x, d)</p><p>It returns the relative index of the min value in the time series for the past d days. If the current day has the min value for the past d days, it returns 0. If previous day has the min value for the past d days, it returns 1.<br/></p><p><b>Example:</b><br/>If d = 6 and values for past 6 days are [6,2,8,5,9,4] with first element being today’s value then min value is 2 and it is present 1 days before today. Hence, ts_arg_min(x, d) = 1</p>', 'id': '363c4ac8-f2e5-430c-bd4b-9f39bba7a0d1'}]",2025-02-06T03:48:46.256322-05:00
ts_regression,Time Series,['REGULAR'],"ts_regression(y, x, d, lag = 0, rettype = 0)",Returns various parameters related to regression function,/operators/ts_regression,ALL,"{'id': 'ts_regression', 'title': 'ts_regression', 'lastModified': '2024-12-30T04:40:45.890709-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>ts_regression(y, x, d, lag = 0, rettype = 0)</b></p><p>Given a set of two variables’ values (X: the independent variable, Y: the dependent variable) over a course of d days, an approximating linear function can be defined, such that sum of squared errors on this set assumes minimal value:</p>', 'id': '3e8602ae-712d-4523-a131-f1ea8368c5a5'}, {'type': 'IMAGE', 'value': {'title': 'OLS Definition', 'width': 386, 'height': 76, 'fileSize': 3484, 'url': 'https://api.worldquantbrain.com/content/images/5oDdWFJLKh6KjMna996tEv8N14A=/26/original/OLS_Definition.PNG'}, 'id': '18c0863b-80c1-4056-8a72-a863b20ab873'}, {'type': 'TEXT', 'value': '<p>Beta and Alpha in second line are OLS Linear Regression coefficients.</p><p></p><p>ts_regression operator <a href=""https://support.worldquantbrain.com/hc/en-us/articles/4902349883927-Click-here-for-a-list-of-terms-and-their-definitions#:~:text=details.-,Returns,-Returns"">returns</a> various parameters related to said regression. This is governed by “rettype” keyword argument, which has a default value of 0. Other “rettype” argument values correspond to:</p>', 'id': 'b0dc8994-4019-428f-ab5f-6fa363346e77'}, {'type': 'IMAGE', 'value': {'title': 'OLS Rettype List', 'width': 558, 'height': 158, 'fileSize': 9020, 'url': 'https://api.worldquantbrain.com/content/images/8VUjMZnI7iWVTp8VD_J1DfnBahI=/27/original/OLS_Rettype_List.PNG'}, 'id': '40690855-aafd-4a20-a5b7-fe1167c08ea1'}, {'type': 'TABLE', 'value': {'data': [['rettype argument', 'return value'], ['0', 'Error Term'], ['1', 'y-intercept (α)'], ['2', 'slope (β)'], ['3', 'y-estimate'], ['4', 'Sum of Squares of Error (SSE)'], ['5', 'Sum of Squares of Total (SST)'], ['6', 'R-Square'], ['7', 'Mean Square Error (MSE)'], ['8', 'Standard Error of β'], ['9', 'Standard Error of α']], 'firstRowIsTableHeader': True, 'firstColIsHeader': False}, 'id': '1b634017-9819-46e4-ba74-0abedebb1495'}, {'type': 'IMAGE', 'value': {'title': 'Regression Plot.png', 'width': 1074, 'height': 388, 'fileSize': 80617, 'url': 'https://api.worldquantbrain.com/content/images/ZoAbsMtl-jRUhrJtJ_2gj5vNjMo=/261/original/Regression_Plot.png'}, 'id': '1cc169be-ecf2-4d75-bd19-2a4a2642f490'}, {'type': 'TEXT', 'value': '<p>Here, ""di"" is current day index, “n”(may differ from d) is a number of valid (x, y) tuples used for calculation. All summations are over day index, using only valid tuples.</p><p>“lag” keyword argument may be optionally specified (default value is zero) to calculate lagged regression parameters instead:</p>', 'id': '5598d8d9-ecb8-46a2-9817-87f4caa1bf72'}, {'type': 'IMAGE', 'value': {'title': 'Lagged Regression', 'width': 123, 'height': 37, 'fileSize': 852, 'url': 'https://api.worldquantbrain.com/content/images/oo3xMLfZKCQGwkNV5YXlOBRFNqw=/28/original/LaggedRegression.PNG'}, 'id': '669aa867-7378-47b5-84f3-b645b3a5934f'}, {'type': 'TEXT', 'value': '<p>Example:</p><ul><li><ul><li>ts_regression(est_netprofit, est_netdebt, 252, lag = 0, rettype = 2)<ul><li>Taking the data from the past 252 trading days (1 year), return the β coefficient from the equation when estimating the est_netprofit using the est_netdebt</li></ul></li></ul></li></ul>', 'id': 'e5480698-bfaa-4512-960a-fbb7b498696b'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'MARKET', 'truncation': 5.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'ts_regression(ts_mean(volume, 2), ts_returns(close, 2), 252)'}, 'id': 'ed157a83-0cc6-4181-941b-179301609b1d'}], 'sequence': 6606, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>ts_regression(y, x, d, lag = 0, rettype = 0)</b></p><p>Given a set of two variables’ values (X: the independent variable, Y: the dependent variable) over a course of d days, an approximating linear function can be defined, such that sum of squared errors on this set assumes minimal value:</p>', 'id': '3e8602ae-712d-4523-a131-f1ea8368c5a5'}, {'type': 'IMAGE', 'value': {'title': 'OLS Definition', 'width': 386, 'height': 76, 'fileSize': 3484, 'url': 'https://api.worldquantbrain.com/content/images/5oDdWFJLKh6KjMna996tEv8N14A=/26/original/OLS_Definition.PNG'}, 'id': '18c0863b-80c1-4056-8a72-a863b20ab873'}, {'type': 'TEXT', 'value': '<p>Beta and Alpha in second line are OLS Linear Regression coefficients.</p><p></p><p>ts_regression operator <a href=""https://support.worldquantbrain.com/hc/en-us/articles/4902349883927-Click-here-for-a-list-of-terms-and-their-definitions#:~:text=details.-,Returns,-Returns"">returns</a> various parameters related to said regression. This is governed by “rettype” keyword argument, which has a default value of 0. Other “rettype” argument values correspond to:</p>', 'id': 'b0dc8994-4019-428f-ab5f-6fa363346e77'}, {'type': 'IMAGE', 'value': {'title': 'OLS Rettype List', 'width': 558, 'height': 158, 'fileSize': 9020, 'url': 'https://api.worldquantbrain.com/content/images/8VUjMZnI7iWVTp8VD_J1DfnBahI=/27/original/OLS_Rettype_List.PNG'}, 'id': '40690855-aafd-4a20-a5b7-fe1167c08ea1'}, {'type': 'TABLE', 'value': {'data': [['rettype argument', 'return value'], ['0', 'Error Term'], ['1', 'y-intercept (α)'], ['2', 'slope (β)'], ['3', 'y-estimate'], ['4', 'Sum of Squares of Error (SSE)'], ['5', 'Sum of Squares of Total (SST)'], ['6', 'R-Square'], ['7', 'Mean Square Error (MSE)'], ['8', 'Standard Error of β'], ['9', 'Standard Error of α']], 'firstRowIsTableHeader': True, 'firstColIsHeader': False}, 'id': '1b634017-9819-46e4-ba74-0abedebb1495'}, {'type': 'IMAGE', 'value': {'title': 'Regression Plot.png', 'width': 1074, 'height': 388, 'fileSize': 80617, 'url': 'https://api.worldquantbrain.com/content/images/ZoAbsMtl-jRUhrJtJ_2gj5vNjMo=/261/original/Regression_Plot.png'}, 'id': '1cc169be-ecf2-4d75-bd19-2a4a2642f490'}, {'type': 'TEXT', 'value': '<p>Here, ""di"" is current day index, “n”(may differ from d) is a number of valid (x, y) tuples used for calculation. All summations are over day index, using only valid tuples.</p><p>“lag” keyword argument may be optionally specified (default value is zero) to calculate lagged regression parameters instead:</p>', 'id': '5598d8d9-ecb8-46a2-9817-87f4caa1bf72'}, {'type': 'IMAGE', 'value': {'title': 'Lagged Regression', 'width': 123, 'height': 37, 'fileSize': 852, 'url': 'https://api.worldquantbrain.com/content/images/oo3xMLfZKCQGwkNV5YXlOBRFNqw=/28/original/LaggedRegression.PNG'}, 'id': '669aa867-7378-47b5-84f3-b645b3a5934f'}, {'type': 'TEXT', 'value': '<p>Example:</p><ul><li><ul><li>ts_regression(est_netprofit, est_netdebt, 252, lag = 0, rettype = 2)<ul><li>Taking the data from the past 252 trading days (1 year), return the β coefficient from the equation when estimating the est_netprofit using the est_netdebt</li></ul></li></ul></li></ul>', 'id': 'e5480698-bfaa-4512-960a-fbb7b498696b'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'MARKET', 'truncation': 5.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'ts_regression(ts_mean(volume, 2), ts_returns(close, 2), 252)'}, 'id': 'ed157a83-0cc6-4181-941b-179301609b1d'}]",2024-12-30T04:40:45.890709-05:00
kth_element,Time Series,['REGULAR'],"kth_element(x, d, k)",Returns K-th value of input by looking through lookback days. This operator can be used to backfill missing data if k=1,/operators/kth_element,ALL,"{'id': 'kth_element', 'title': 'kth_element', 'lastModified': '2024-12-30T04:32:27.552322-05:00', 'content': [{'type': 'TEXT', 'value': '<p></p><p>Returns k-th value of input by looking through lookback days while ignoring space separated scalars in ignore list. This operator is also known as <b>backfill</b> operator as it can be used to backfill missing data.</p><p><b>ignore</b> parameter is used to provide list of separated scalars to ignore from counting<br/></p><p><b>Example of backfill:</b></p>', 'id': '397caa66-63cc-42b3-88fc-d75b9e7c6fee'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'kth_element(sales/assets,252,k=""1"",ignore=""NAN 0"")'}, 'id': 'b8c6d955-4487-48d7-baa2-fc6fb55dcff2'}], 'sequence': 170, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p></p><p>Returns k-th value of input by looking through lookback days while ignoring space separated scalars in ignore list. This operator is also known as <b>backfill</b> operator as it can be used to backfill missing data.</p><p><b>ignore</b> parameter is used to provide list of separated scalars to ignore from counting<br/></p><p><b>Example of backfill:</b></p>', 'id': '397caa66-63cc-42b3-88fc-d75b9e7c6fee'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'kth_element(sales/assets,252,k=""1"",ignore=""NAN 0"")'}, 'id': 'b8c6d955-4487-48d7-baa2-fc6fb55dcff2'}]",2024-12-30T04:32:27.552322-05:00
hump,Time Series,['REGULAR'],"hump(x, hump = 0.01)",Limits amount and magnitude of changes in input (thus reducing turnover),/operators/hump,ALL,"{'id': 'hump', 'title': 'hump', 'lastModified': '2024-12-30T04:37:13.821810-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>hump(x, hump = 0.01)</b></p><p>This operator limits the frequency and magnitude of changes in the Alpha (thus reducing <a href=""https://support.worldquantbrain.com/hc/en-us/articles/4902349883927-Click-here-for-a-list-of-terms-and-their-definitions#:~:text=details.-,Turnover,-Average"">turnover</a>). If today\'s values show only a minor change (not exceeding the Threshold) from yesterday\'s value, the output of the hump operator stays the same as yesterday. If the change is bigger than the limit, the output is yesterday\'s value plus the limit in the direction of the change.</p><p>This operator may help reduce turnover and drawdown.</p><p>Flowchart of the Hump operator:</p>', 'id': 'b5b5d49b-9d54-45c4-bc98-117d8b958edd'}, {'type': 'IMAGE', 'value': {'title': 'LFlow_chart.PNG', 'width': 1401, 'height': 827, 'fileSize': 49020, 'url': 'https://api.worldquantbrain.com/content/images/-3BnAawkCAi5iE6UV830IP9la6A=/301/original/LFlow_chart.PNG'}, 'id': '33ddcaaa-6163-4dd7-a81c-796f9b80d1da'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'MARKET', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'hump(-ts_delta(close, 5), hump = 0.00001)'}, 'id': '3bf4aabf-7ff4-4f99-a2a9-c72e807cffa3'}], 'sequence': 6564, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>hump(x, hump = 0.01)</b></p><p>This operator limits the frequency and magnitude of changes in the Alpha (thus reducing <a href=""https://support.worldquantbrain.com/hc/en-us/articles/4902349883927-Click-here-for-a-list-of-terms-and-their-definitions#:~:text=details.-,Turnover,-Average"">turnover</a>). If today\'s values show only a minor change (not exceeding the Threshold) from yesterday\'s value, the output of the hump operator stays the same as yesterday. If the change is bigger than the limit, the output is yesterday\'s value plus the limit in the direction of the change.</p><p>This operator may help reduce turnover and drawdown.</p><p>Flowchart of the Hump operator:</p>', 'id': 'b5b5d49b-9d54-45c4-bc98-117d8b958edd'}, {'type': 'IMAGE', 'value': {'title': 'LFlow_chart.PNG', 'width': 1401, 'height': 827, 'fileSize': 49020, 'url': 'https://api.worldquantbrain.com/content/images/-3BnAawkCAi5iE6UV830IP9la6A=/301/original/LFlow_chart.PNG'}, 'id': '33ddcaaa-6163-4dd7-a81c-796f9b80d1da'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'MARKET', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'hump(-ts_delta(close, 5), hump = 0.00001)'}, 'id': '3bf4aabf-7ff4-4f99-a2a9-c72e807cffa3'}]",2024-12-30T04:37:13.821810-05:00
ts_delta,Time Series,['REGULAR'],"ts_delta(x, d)","Returns x - ts_delay(x, d)",,ALL,,,
ts_target_tvr_decay,Time Series,['REGULAR'],"ts_target_tvr_decay(x, lambda_min=0, lambda_max=1, target_tvr=0.1)","Tune ""ts_decay"" to have a turnover equal to a certain target, with optimization weight range between lambda_min, lambda_max",,,,,
ts_target_tvr_delta_limit,Time Series,['REGULAR'],"ts_target_tvr_delta_limit(x, y, lambda_min=0, lambda_max=1, target_tvr=0.1)","Tune ""ts_delta_limit"" to have a turnover equal to a certain target with optimization weight range between lambda_min, lambda_max. Also, please be aware of the scaling for x and y. Besides setting y as adv20 or volume related data, you can also set y as a constant.",,,,,
winsorize,Cross Sectional,['REGULAR'],"winsorize(x, std=4)","Winsorizes x to make sure that all values in x are between the lower and upper limits, which are specified as multiple of std.",,ALL,,,
rank,Cross Sectional,['REGULAR'],"rank(x, rate=2)","Ranks the input among all the instruments and returns an equally distributed number between 0.0 and 1.0. For precise sort, use the rate as 0",/operators/rank,ALL,"{'id': 'rank', 'title': 'rank', 'lastModified': '2024-12-30T04:39:11.372951-05:00', 'content': [{'type': 'TEXT', 'value': '<p>rank(x, rate=2):</p><p>The <a href=""https://support.worldquantbrain.com/hc/en-us/articles/4902349883927-Click-here-for-a-list-of-terms-and-their-definitions#:~:text=R-,Rank,-Rank"">Rank</a> operator ranks the value of the input data x for the given stock among all instruments, and returns float numbers equally distributed between 0.0 and 1.0. When rate is set to 0, the sorting is done precisely. The default value of rate is 2.</p><p>This operator may help reduce outliers and drawdown while improving the Sharpe.</p><p><b>Example:</b></p><p>Rank(close); Rank (close, rate=0) # Sorts precisely</p><p></p><p>X = (4,3,6,10,2) =&gt; Rank(x) = (0.5, 0.25, 0.75, 1, 0)</p>', 'id': 'b4ac9afe-7d3a-498e-883b-b679d5136f4d'}], 'sequence': 6577, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>rank(x, rate=2):</p><p>The <a href=""https://support.worldquantbrain.com/hc/en-us/articles/4902349883927-Click-here-for-a-list-of-terms-and-their-definitions#:~:text=R-,Rank,-Rank"">Rank</a> operator ranks the value of the input data x for the given stock among all instruments, and returns float numbers equally distributed between 0.0 and 1.0. When rate is set to 0, the sorting is done precisely. The default value of rate is 2.</p><p>This operator may help reduce outliers and drawdown while improving the Sharpe.</p><p><b>Example:</b></p><p>Rank(close); Rank (close, rate=0) # Sorts precisely</p><p></p><p>X = (4,3,6,10,2) =&gt; Rank(x) = (0.5, 0.25, 0.75, 1, 0)</p>', 'id': 'b4ac9afe-7d3a-498e-883b-b679d5136f4d'}]",2024-12-30T04:39:11.372951-05:00
vector_neut,Cross Sectional,['REGULAR'],"vector_neut(x, y)","For given vectors x and y, it finds a new vector x* (output) such that x* is orthogonal to y",/operators/vector_neut,,"{'id': 'vector_neut', 'title': 'vector_neut', 'lastModified': '2024-12-30T04:37:19.217102-05:00', 'content': [{'type': 'TEXT', 'value': ""<p>vector_neut(x,y)</p><p></p><p>Input1 neutralize to input2</p><p>For given vector A (i.e., input1) and B (i.e., input2), it finds a new vector A' (i.e., output) such that A' is orthogonal to B. It calculates projection of A onto B, and then subtracts projection vector from A to find the rejection vector (i.e., A') which is perpendicular to the B.</p><p>This operator may help reduce correlation, depending on the neutralization used.</p><p><b>Example</b>:</p>"", 'id': '9181880d-4e67-4a71-9343-61f90513479c'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'vector_neut(open,close)'}, 'id': 'ea0679c2-3d67-402a-9fa9-77729a157c7e'}], 'sequence': 6612, 'category': 'Operators'}","[{'type': 'TEXT', 'value': ""<p>vector_neut(x,y)</p><p></p><p>Input1 neutralize to input2</p><p>For given vector A (i.e., input1) and B (i.e., input2), it finds a new vector A' (i.e., output) such that A' is orthogonal to B. It calculates projection of A onto B, and then subtracts projection vector from A to find the rejection vector (i.e., A') which is perpendicular to the B.</p><p>This operator may help reduce correlation, depending on the neutralization used.</p><p><b>Example</b>:</p>"", 'id': '9181880d-4e67-4a71-9343-61f90513479c'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'INDUSTRY', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'vector_neut(open,close)'}, 'id': 'ea0679c2-3d67-402a-9fa9-77729a157c7e'}]",2024-12-30T04:37:19.217102-05:00
zscore,Cross Sectional,['REGULAR'],zscore(x),Z-score is a numerical measurement that describes a value's relationship to the mean of a group of values. Z-score is measured in terms of standard deviations from the mean,/operators/zscore,ALL,"{'id': 'zscore', 'title': 'zscore', 'lastModified': '2024-12-30T04:37:35.294396-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>zscore(x)</b></p>', 'id': '54666fe7-7f31-4520-822c-56618fbc46c1'}, {'type': 'TEXT', 'value': '<p>Z-score is a statistical tool that indicates how many standard deviations a data point lies from the average of a group of values. Essentially, it measures how unusual a data point is in relation to the mean, making it a handy tool for understanding deviation and comparison.</p><p>The formula to calculate a Z-score is:</p>', 'id': 'f62886a1-d338-4a75-8773-61d6e8d14441'}, {'type': 'EQUATION', 'value': '$$Z\\textrm{-}score = \\frac{x - mean(x)}{std(x)}$$', 'id': 'ddd53555-3721-42ab-830a-4dcebc8b7950'}, {'type': 'TEXT', 'value': ""<p>Where:</p><ul><li>x is an individual data point</li><li>mean(x) is the average of the data set</li><li>std(x) is the standard deviation of the data set</li></ul><p>By this definition, the mean of the Z-scores in a distribution is always 0, and the standard deviation is always 1.</p><p>A Z-score tells you how many standard deviations a particular data point is from the mean. If the Z-score is positive, the data point is above the mean, and if it's negative, it's below the mean.</p><p>Z-scores may be especially useful for normalizing and comparing different data fields for different stocks or different data fields. They allow researchers to calculate the probability of a score occurring within a standard normal distribution and compare two scores that are from different samples (which may have different means and standard deviations).</p><p>This operator may help reduce outliers.</p>"", 'id': 'ca653e3a-6093-4d5c-9d72-0dbd9f4b3efe'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'MARKET', 'truncation': 0.03, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'zscore(close)'}, 'id': '430a5d53-a734-4f86-9147-42c8bf088cdf'}], 'sequence': 105, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>zscore(x)</b></p>', 'id': '54666fe7-7f31-4520-822c-56618fbc46c1'}, {'type': 'TEXT', 'value': '<p>Z-score is a statistical tool that indicates how many standard deviations a data point lies from the average of a group of values. Essentially, it measures how unusual a data point is in relation to the mean, making it a handy tool for understanding deviation and comparison.</p><p>The formula to calculate a Z-score is:</p>', 'id': 'f62886a1-d338-4a75-8773-61d6e8d14441'}, {'type': 'EQUATION', 'value': '$$Z\\textrm{-}score = \\frac{x - mean(x)}{std(x)}$$', 'id': 'ddd53555-3721-42ab-830a-4dcebc8b7950'}, {'type': 'TEXT', 'value': ""<p>Where:</p><ul><li>x is an individual data point</li><li>mean(x) is the average of the data set</li><li>std(x) is the standard deviation of the data set</li></ul><p>By this definition, the mean of the Z-scores in a distribution is always 0, and the standard deviation is always 1.</p><p>A Z-score tells you how many standard deviations a particular data point is from the mean. If the Z-score is positive, the data point is above the mean, and if it's negative, it's below the mean.</p><p>Z-scores may be especially useful for normalizing and comparing different data fields for different stocks or different data fields. They allow researchers to calculate the probability of a score occurring within a standard normal distribution and compare two scores that are from different samples (which may have different means and standard deviations).</p><p>This operator may help reduce outliers.</p>"", 'id': 'ca653e3a-6093-4d5c-9d72-0dbd9f4b3efe'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'MARKET', 'truncation': 0.03, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'zscore(close)'}, 'id': '430a5d53-a734-4f86-9147-42c8bf088cdf'}]",2024-12-30T04:37:35.294396-05:00
scale_down,Cross Sectional,['REGULAR'],"scale_down(x,constant=0)",Scales all values in each day proportionately between 0 and 1 such that minimum value maps to 0 and maximum value maps to 1. Constant is the offset by which final result is subtracted,/operators/scale_down,,"{'id': 'scale_down', 'title': 'scale_down', 'lastModified': '2024-12-30T04:39:36.638693-05:00', 'content': [{'type': 'TEXT', 'value': '<p>scale_down(x,constant=0)</p><p>Scales all values in each day proportionately between 0 and 1 such that minimum value maps to 0 and maximum value maps to 1. constant is the offset by which final result is subtracted<br/></p><p><b>Example:</b><br/>If for a certain date, instrument values of certain input x is [15,7,0,20], max = 20 and min = 0<br/>scale_down(x,constant=0) = [(15-0)/20,(7-0)/20,(0-0)/’20,(20-0)/20] = [0.75,0.35,0,1]<br/>scale_down(x,constant=1) = [0.75-1,0.35-1,0-1,1-1] = [-0.25,-0.65,-1,0]</p>', 'id': '84763c98-9d50-437f-956d-9e70e0a07cf9'}], 'sequence': 6584, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>scale_down(x,constant=0)</p><p>Scales all values in each day proportionately between 0 and 1 such that minimum value maps to 0 and maximum value maps to 1. constant is the offset by which final result is subtracted<br/></p><p><b>Example:</b><br/>If for a certain date, instrument values of certain input x is [15,7,0,20], max = 20 and min = 0<br/>scale_down(x,constant=0) = [(15-0)/20,(7-0)/20,(0-0)/’20,(20-0)/20] = [0.75,0.35,0,1]<br/>scale_down(x,constant=1) = [0.75-1,0.35-1,0-1,1-1] = [-0.25,-0.65,-1,0]</p>', 'id': '84763c98-9d50-437f-956d-9e70e0a07cf9'}]",2024-12-30T04:39:36.638693-05:00
scale,Cross Sectional,['REGULAR'],"scale(x, scale=1, longscale=1, shortscale=1)",Scales input to booksize. We can also scale the long positions and short positions to separate scales by mentioning additional parameters to the operator,/operators/scale,ALL,"{'id': 'scale', 'title': 'scale', 'lastModified': '2024-12-30T04:37:47.747310-05:00', 'content': [{'type': 'TEXT', 'value': ""<p>scale (x, scale=1, longscale=1, shortscale=1)</p><p>The operator scales the input to the book size. We can optionally tune the book size by specifying the additional parameter 'scale=booksize_value'. We can also scale the long positions and short positions to separate scales by specifying additional parameters: longscale=long_booksize and shortscale=short_booksize. The default value of each leg of the scale is 0, which means no scaling, unless specified otherwise. Scale the alpha so that the sum of abs(x) over all instruments equals 1. To scale to a different book size, use Scale(x) * booksize.</p><p>This operator may help reduce outliers.</p><p>Please check examples for the application of the same</p><p><b>Examples</b>:</p><p>scale(returns, scale=4); scale (returns, scale= 1) + scale (close, scale=20); scale (returns, longscale=4, shortscale=3)</p>"", 'id': 'ad97f46f-9624-479d-8320-c65b01dd31c9'}], 'sequence': 6583, 'category': 'Operators'}","[{'type': 'TEXT', 'value': ""<p>scale (x, scale=1, longscale=1, shortscale=1)</p><p>The operator scales the input to the book size. We can optionally tune the book size by specifying the additional parameter 'scale=booksize_value'. We can also scale the long positions and short positions to separate scales by specifying additional parameters: longscale=long_booksize and shortscale=short_booksize. The default value of each leg of the scale is 0, which means no scaling, unless specified otherwise. Scale the alpha so that the sum of abs(x) over all instruments equals 1. To scale to a different book size, use Scale(x) * booksize.</p><p>This operator may help reduce outliers.</p><p>Please check examples for the application of the same</p><p><b>Examples</b>:</p><p>scale(returns, scale=4); scale (returns, scale= 1) + scale (close, scale=20); scale (returns, longscale=4, shortscale=3)</p>"", 'id': 'ad97f46f-9624-479d-8320-c65b01dd31c9'}]",2024-12-30T04:37:47.747310-05:00
normalize,Cross Sectional,['REGULAR'],"normalize(x, useStd = false, limit = 0.0)","Calculates the mean value of all valid alpha values for a certain date, then subtracts that mean from each element",/operators/normalize,ALL,"{'id': 'normalize', 'title': 'normalize', 'lastModified': '2024-12-30T04:39:00.075882-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>normalize(x, useStd = false, limit = 0.0)</b></p>', 'id': '35da9b48-e259-402c-a801-0fb5916cbecf'}, {'type': 'TEXT', 'value': '<p>This operator calculates the mean value of all valid alpha values for a certain date, then subtracts that mean from each element. If useStd= true, the operator calculates the standard deviation of the resulting values and divides each normalized element by it. If limit is not equal to 0.0, operator puts the limit of the resulting alpha values (between -limit to + limit).<br/>Example:<br/>If for a certain date, instrument value of certain input x is [3,5,6,2], mean = 4 and standard deviation = 1.82<br/>normalize(x, useStd = false, limit = 0.0) = [3-4,5-4,6-4,2-4] = [-1,1,2,-2]<br/>normalize(x, useStd = true, limit = 0.0) = [-1/1.82,1/1.82,2/1.82,-2/1.82] = [-0.55,0.55,1.1,-1.1]</p>', 'id': '64062b0e-a150-491e-85ec-d2a748e517c5'}], 'sequence': 101, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>normalize(x, useStd = false, limit = 0.0)</b></p>', 'id': '35da9b48-e259-402c-a801-0fb5916cbecf'}, {'type': 'TEXT', 'value': '<p>This operator calculates the mean value of all valid alpha values for a certain date, then subtracts that mean from each element. If useStd= true, the operator calculates the standard deviation of the resulting values and divides each normalized element by it. If limit is not equal to 0.0, operator puts the limit of the resulting alpha values (between -limit to + limit).<br/>Example:<br/>If for a certain date, instrument value of certain input x is [3,5,6,2], mean = 4 and standard deviation = 1.82<br/>normalize(x, useStd = false, limit = 0.0) = [3-4,5-4,6-4,2-4] = [-1,1,2,-2]<br/>normalize(x, useStd = true, limit = 0.0) = [-1/1.82,1/1.82,2/1.82,-2/1.82] = [-0.55,0.55,1.1,-1.1]</p>', 'id': '64062b0e-a150-491e-85ec-d2a748e517c5'}]",2024-12-30T04:39:00.075882-05:00
quantile,Cross Sectional,['REGULAR'],"quantile(x, driver = gaussian, sigma = 1.0)","Rank the raw vector, shift the ranked Alpha vector, apply distribution (gaussian, cauchy, uniform). If driver is uniform, it simply subtract each Alpha value with the mean of all Alpha values in the Alpha vector",/operators/quantile,ALL,"{'id': 'quantile', 'title': 'quantile', 'lastModified': '2024-12-30T04:37:29.705754-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>quantile(x, driver = gaussian, sigma = 1.0)</b></p>', 'id': 'c5e51524-f93b-4d2c-8192-8ef817a42ebe'}, {'type': 'TEXT', 'value': '<p>Rank the input raw Alpha vector<br/>The ranked Alpha value would be within [0, 1]</p><ol><li>Shift the ranked Alpha vector<br/>For every Alpha value in the ranked Alpha vector, it is shifted as: Alpha_value = 1/N + Alpha_value * (1 - 2/N), here assume there are N <a href=""https://support.worldquantbrain.com/hc/en-us/articles/4902349883927-Click-here-for-a-list-of-terms-and-their-definitions#:~:text=details.-,Instrument,-Instrument"">instruments</a> with value in the Alpha vector. The shifted Alpha value would be within [1/N, 1-1/N]</li><li>Apply distribution for each Alpha value in the ranked Alpha vector using the specified driver. Driver can be one of ""gaussian"", ""uniform"", ""cauchy"".</li></ol><p>Note : Sigma only affects the scale of the final value.</p><p>This operator may help reduce outliers.</p><p><b>Example</b>:</p>', 'id': 'fdacc486-e42d-44ef-a3be-876e95a5f034'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'MARKET', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'quantile(close, driver = gaussian, sigma = 0.5 )'}, 'id': 'fedb09f0-ef99-41e0-91ce-e838643cc7cb'}], 'sequence': 100, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>quantile(x, driver = gaussian, sigma = 1.0)</b></p>', 'id': 'c5e51524-f93b-4d2c-8192-8ef817a42ebe'}, {'type': 'TEXT', 'value': '<p>Rank the input raw Alpha vector<br/>The ranked Alpha value would be within [0, 1]</p><ol><li>Shift the ranked Alpha vector<br/>For every Alpha value in the ranked Alpha vector, it is shifted as: Alpha_value = 1/N + Alpha_value * (1 - 2/N), here assume there are N <a href=""https://support.worldquantbrain.com/hc/en-us/articles/4902349883927-Click-here-for-a-list-of-terms-and-their-definitions#:~:text=details.-,Instrument,-Instrument"">instruments</a> with value in the Alpha vector. The shifted Alpha value would be within [1/N, 1-1/N]</li><li>Apply distribution for each Alpha value in the ranked Alpha vector using the specified driver. Driver can be one of ""gaussian"", ""uniform"", ""cauchy"".</li></ol><p>Note : Sigma only affects the scale of the final value.</p><p>This operator may help reduce outliers.</p><p><b>Example</b>:</p>', 'id': 'fdacc486-e42d-44ef-a3be-876e95a5f034'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 3, 'neutralization': 'MARKET', 'truncation': 0.01, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': 'quantile(close, driver = gaussian, sigma = 0.5 )'}, 'id': 'fedb09f0-ef99-41e0-91ce-e838643cc7cb'}]",2024-12-30T04:37:29.705754-05:00
vec_min,Vector,['REGULAR'],vec_min(x),Minimum value form vector field x,,,,,
vec_sum,Vector,['REGULAR'],vec_sum(x),Sum of vector field x,,ALL,,,
vec_max,Vector,['REGULAR'],vec_max(x),Maximum value form vector field x,,,,,
vec_avg,Vector,['REGULAR'],vec_avg(x),Taking mean of the vector field x,,ALL,,,
bucket,Transformational,['REGULAR'],"bucket(rank(x), range=""0, 1, 0.1"" or buckets = ""2,5,6,7,10"")","Convert float values into indexes for user-specified buckets. Bucket is useful for creating group values, which can be passed to GROUP as input",/operators/bucket,ALL,"{'id': 'bucket', 'title': 'Bucket', 'lastModified': '2024-12-30T04:42:30.417131-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>Bucket</b></p><p>Convert float values into indexes for user-specified buckets. Bucket is useful for creating group values, which can be passed to group operators as input.</p><p>If <b>buckets</b> are specified as ""num_1, num_2, …, num_N"", it is converted into brackets consisting of [(num_1, num_2, idx_1), (num_2, num_3, idx_2), ..., (num_N-1, num_N, idx_N-1)]</p><p>Thus with buckets=""2, 5, 6, 7, 10"", the vector ""-1, 3, 6, 8, 12"" becomes ""0, 1, 2, 4, 5""</p><p>If <b>range</b> if specified as ""start, end, step"", it is converted into brackets consisting of [(start, start+step, idx_1), (start+step, start+2*step, idx_2), ..., (start+N*step, end, idx_N)].</p><p>Thus with range=""0.1, 1, 0.1"", the vector ""0.05, 0.5, 0.9"" becomes ""0, 4, 8""</p><p>Note that two hidden buckets corresponding to (-inf, start] and [end, +inf) are added by default. Use the <b>skipBegin</b>, <b>skipEnd</b> parameters to remove these buckets. Use <b>skipBoth</b> to set both <b>skipEnd</b> and <b>skipBegin</b> to true.</p><p>If you want to assign all NAN values into a separate group of their own, use <b>NANGroup</b>. The index value will be one after the last bucket</p><p></p><p><b>Examples:</b></p><p>my_group = bucket(rank(volume), range=""0.1,1,0.1"");</p><p>group_neutralize(sales/assets, my_group)</p><p>my_group = bucket(rank(volume), buckets =""0.2,0.5,0.7"", skipBoth=True, NANGroup=True);</p><p>group_neutralize(sales/assets, my_group)</p>', 'id': '0dd3c37f-6e04-4c3b-a58d-b31bad2b9162'}], 'sequence': 6558, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>Bucket</b></p><p>Convert float values into indexes for user-specified buckets. Bucket is useful for creating group values, which can be passed to group operators as input.</p><p>If <b>buckets</b> are specified as ""num_1, num_2, …, num_N"", it is converted into brackets consisting of [(num_1, num_2, idx_1), (num_2, num_3, idx_2), ..., (num_N-1, num_N, idx_N-1)]</p><p>Thus with buckets=""2, 5, 6, 7, 10"", the vector ""-1, 3, 6, 8, 12"" becomes ""0, 1, 2, 4, 5""</p><p>If <b>range</b> if specified as ""start, end, step"", it is converted into brackets consisting of [(start, start+step, idx_1), (start+step, start+2*step, idx_2), ..., (start+N*step, end, idx_N)].</p><p>Thus with range=""0.1, 1, 0.1"", the vector ""0.05, 0.5, 0.9"" becomes ""0, 4, 8""</p><p>Note that two hidden buckets corresponding to (-inf, start] and [end, +inf) are added by default. Use the <b>skipBegin</b>, <b>skipEnd</b> parameters to remove these buckets. Use <b>skipBoth</b> to set both <b>skipEnd</b> and <b>skipBegin</b> to true.</p><p>If you want to assign all NAN values into a separate group of their own, use <b>NANGroup</b>. The index value will be one after the last bucket</p><p></p><p><b>Examples:</b></p><p>my_group = bucket(rank(volume), range=""0.1,1,0.1"");</p><p>group_neutralize(sales/assets, my_group)</p><p>my_group = bucket(rank(volume), buckets =""0.2,0.5,0.7"", skipBoth=True, NANGroup=True);</p><p>group_neutralize(sales/assets, my_group)</p>', 'id': '0dd3c37f-6e04-4c3b-a58d-b31bad2b9162'}]",2024-12-30T04:42:30.417131-05:00
trade_when,Transformational,['REGULAR'],"trade_when(x, y, z)",Used in order to change Alpha values only under a specified condition and to hold Alpha values in other cases. It also allows to close Alpha positions (assign NaN values) under a specified condition,/operators/trade_when,ALL,"{'id': 'trade_when', 'title': 'trade_when', 'lastModified': '2024-12-30T04:39:05.839330-05:00', 'content': [{'type': 'TEXT', 'value': '<p>This operator can be used to change Alpha values only under a specified condition and to retain Alpha values in other cases. It also allows for closing Alpha positions (assigning NaN values) under a specified condition.</p><p>Trade_When (x=triggerTradeExp, y=AlphaExp, z=triggerExitExp)</p><p>If triggerExitExp &gt; 0, Alpha = NaN.</p><p>Else if triggerTradeExp &gt; 0, Alpha = AlphaExp;</p><p>else, Alpha = previousAlpha</p><p>This operator may help reduce correlation and reduce turnover.</p><p><b>Examples:</b></p><p>Trade_When (volume &gt;= ts_sum(volume,5)/5, rank(-returns), -1)</p><p>If (volume &gt;= ts_sum(volume,5)/5), Alpha = rank(-returns);</p><p>else trade previous Alpha;</p><p>exit condition is always false.</p><p>Trade_When (volume &gt;= ts_sum(volume,5)/5, rank(-returns), abs(returns) &gt; 0.1)</p><p>If abs(returns) &gt; 0.1, Alpha = nan;</p><p>else if volume &gt;= ts_sum(volume,5)/5, Alpha = rank(-returns);</p><p>else trade previous Alpha.</p>', 'id': 'fcc421e9-2939-4fda-873a-9bad9bc26c87'}], 'sequence': 6589, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>This operator can be used to change Alpha values only under a specified condition and to retain Alpha values in other cases. It also allows for closing Alpha positions (assigning NaN values) under a specified condition.</p><p>Trade_When (x=triggerTradeExp, y=AlphaExp, z=triggerExitExp)</p><p>If triggerExitExp &gt; 0, Alpha = NaN.</p><p>Else if triggerTradeExp &gt; 0, Alpha = AlphaExp;</p><p>else, Alpha = previousAlpha</p><p>This operator may help reduce correlation and reduce turnover.</p><p><b>Examples:</b></p><p>Trade_When (volume &gt;= ts_sum(volume,5)/5, rank(-returns), -1)</p><p>If (volume &gt;= ts_sum(volume,5)/5), Alpha = rank(-returns);</p><p>else trade previous Alpha;</p><p>exit condition is always false.</p><p>Trade_When (volume &gt;= ts_sum(volume,5)/5, rank(-returns), abs(returns) &gt; 0.1)</p><p>If abs(returns) &gt; 0.1, Alpha = nan;</p><p>else if volume &gt;= ts_sum(volume,5)/5, Alpha = rank(-returns);</p><p>else trade previous Alpha.</p>', 'id': 'fcc421e9-2939-4fda-873a-9bad9bc26c87'}]",2024-12-30T04:39:05.839330-05:00
group_min,Group,['REGULAR'],"group_min(x, group)",All elements in group equals to the min value of the group.,,,,,
group_mean,Group,['REGULAR'],"group_mean(x, weight, group)",All elements in group equals to the mean,/operators/group_mean,ALL,"{'id': 'group_mean', 'title': 'group_mean', 'lastModified': '2025-04-01T05:21:56.452359-04:00', 'content': [{'type': 'TEXT', 'value': '<p>group_mean(x, group) operator can be used to calculate harmonic mean of datafields. Harmonic mean gives equal weight to each value in terms of their reciprocal contribution and is considered a better method for calculating average for fundamental ratios and factors. For example, Harmonic mean of P/E ratio (price-to-earnings) for an industry can be calculated as:</p>', 'id': '979d6cdd-ffab-4f0e-b39d-87b4c3967a34'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 1, 'neutralization': 'MARKET', 'truncation': 1.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': '1 /(group_mean(eps/close,1, industry))'}, 'id': '9afebb46-2423-44f9-abc9-52d0749df6dd'}], 'sequence': 1234, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p>group_mean(x, group) operator can be used to calculate harmonic mean of datafields. Harmonic mean gives equal weight to each value in terms of their reciprocal contribution and is considered a better method for calculating average for fundamental ratios and factors. For example, Harmonic mean of P/E ratio (price-to-earnings) for an industry can be calculated as:</p>', 'id': '979d6cdd-ffab-4f0e-b39d-87b4c3967a34'}, {'type': 'SIMULATION_EXAMPLE', 'value': {'settings': {'instrumentType': 'EQUITY', 'region': 'USA', 'universe': 'TOP3000', 'delay': 1, 'decay': 1, 'neutralization': 'MARKET', 'truncation': 1.0, 'pasteurization': 'ON', 'unitHandling': 'VERIFY', 'nanHandling': 'OFF', 'language': 'FASTEXPR', 'maxTrade': 'OFF'}, 'type': 'REGULAR', 'regular': '1 /(group_mean(eps/close,1, industry))'}, 'id': '9afebb46-2423-44f9-abc9-52d0749df6dd'}]",2025-04-01T05:21:56.452359-04:00
group_max,Group,['REGULAR'],"group_max(x, group)",Maximum of x for all instruments in the same group.,,,,,
group_rank,Group,['REGULAR'],"group_rank(x, group)",Each elements in a group is assigned the corresponding rank in this group,/operators/group_rank,ALL,"{'id': 'group_rank', 'title': 'group_rank', 'lastModified': '2024-12-30T04:38:04.640773-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>group_rank(x, group)</b></p>', 'id': 'f129cb02-886a-47f1-881b-98d11cc2e2a2'}, {'type': 'TEXT', 'value': '<p>Group operators are a type of cross-sectional operator that compares stocks at a finer level, where the cross-sectional operation is applied within each group, rather than across the entire market. The group_rank operator allocates the stocks to their specified group, then within each group, it ranks the stocks based on their input value for data field x and returns an equally distributed number between 0.0 and 1.0.</p><p>This operator may help reduce both outliers and drawdown while reducing correlation.</p><p><b>Example:</b> group_rank(x, subindustry)</p><ul><li>The stocks are first grouped into their respective subindustry.</li><li>Within each subindustry, the stocks within that subindustry are ranked based on their input value for data field x and assigned an equally distributed number between 0.0 and 1.0.</li></ul>', 'id': '200535c0-ba9e-4049-bec5-ac8824e240d2'}], 'sequence': 110, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>group_rank(x, group)</b></p>', 'id': 'f129cb02-886a-47f1-881b-98d11cc2e2a2'}, {'type': 'TEXT', 'value': '<p>Group operators are a type of cross-sectional operator that compares stocks at a finer level, where the cross-sectional operation is applied within each group, rather than across the entire market. The group_rank operator allocates the stocks to their specified group, then within each group, it ranks the stocks based on their input value for data field x and returns an equally distributed number between 0.0 and 1.0.</p><p>This operator may help reduce both outliers and drawdown while reducing correlation.</p><p><b>Example:</b> group_rank(x, subindustry)</p><ul><li>The stocks are first grouped into their respective subindustry.</li><li>Within each subindustry, the stocks within that subindustry are ranked based on their input value for data field x and assigned an equally distributed number between 0.0 and 1.0.</li></ul>', 'id': '200535c0-ba9e-4049-bec5-ac8824e240d2'}]",2024-12-30T04:38:04.640773-05:00
group_backfill,Group,['REGULAR'],"group_backfill(x, group, d, std = 4.0)","If a certain value for a certain date and instrument is NaN, from the set of same group instruments, calculate winsorized mean of all non-NaN values over last d days",/operators/group_backfill,ALL,"{'id': 'group_backfill', 'title': 'group_backfill', 'lastModified': '2024-12-30T04:38:46.252719-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>group_backfill(x, group, d, std = 4.0)</b></p>', 'id': 'c1088a3a-0c32-4455-a2fd-5fe022cb92b7'}, {'type': 'TEXT', 'value': '<p>If a certain value for a certain date and instrument is NaN, from the set of same group instruments, calculate winsorized mean of all non-NaN values over last d days. Winsorized mean means inputs are truncated by std * stddev where stddev is the standard deviation of inputs.<br/></p><p><b>Example:</b><br/>If d = 4 and there are 3 instruments(i1, i2, i3) in a group whose values for past 4 days are x[i1] = [4,2,5,5], x[i2] = [7,NaN,2,9], x[i3] = [NaN,-4,2,NaN] where first element is most recent, then if we want to backfill x, we will only have to backfill x[i3]’s first element because every other instrument’s first element is non-NaN.</p><p>The non-NaN values of other groups are [4,2,5,5,7,2,9,-4,2]. Mean = 3.56, Standard deviation is 3.71 and none of the item is outside the range of 3.56 – 4 * 3.71 and 3.56 + 4 * 3.71. Hence, we don’t need to clip elements to those limits. Hence, Winsorized mean = backfilled value = 3.56.</p><p>For three instruments, group_backfill(x, group, d, std = 4.0) = [4,7,3.56]</p>', 'id': 'f8502895-cdef-42ae-9f1a-66701afadc67'}], 'sequence': 106, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>group_backfill(x, group, d, std = 4.0)</b></p>', 'id': 'c1088a3a-0c32-4455-a2fd-5fe022cb92b7'}, {'type': 'TEXT', 'value': '<p>If a certain value for a certain date and instrument is NaN, from the set of same group instruments, calculate winsorized mean of all non-NaN values over last d days. Winsorized mean means inputs are truncated by std * stddev where stddev is the standard deviation of inputs.<br/></p><p><b>Example:</b><br/>If d = 4 and there are 3 instruments(i1, i2, i3) in a group whose values for past 4 days are x[i1] = [4,2,5,5], x[i2] = [7,NaN,2,9], x[i3] = [NaN,-4,2,NaN] where first element is most recent, then if we want to backfill x, we will only have to backfill x[i3]’s first element because every other instrument’s first element is non-NaN.</p><p>The non-NaN values of other groups are [4,2,5,5,7,2,9,-4,2]. Mean = 3.56, Standard deviation is 3.71 and none of the item is outside the range of 3.56 – 4 * 3.71 and 3.56 + 4 * 3.71. Hence, we don’t need to clip elements to those limits. Hence, Winsorized mean = backfilled value = 3.56.</p><p>For three instruments, group_backfill(x, group, d, std = 4.0) = [4,7,3.56]</p>', 'id': 'f8502895-cdef-42ae-9f1a-66701afadc67'}]",2024-12-30T04:38:46.252719-05:00
group_scale,Group,['REGULAR'],"group_scale(x, group)",Normalizes the values in a group to be between 0 and 1. (x - groupmin) / (groupmax - groupmin),,ALL,,,
group_zscore,Group,['REGULAR'],"group_zscore(x, group)",Calculates group Z-score - numerical measurement that describes a value's relationship to the mean of a group of values. Z-score is measured in terms of standard deviations from the mean. zscore = (data - mean) / stddev of x for each instrument within its group.,,ALL,,,
group_neutralize,Group,['REGULAR'],"group_neutralize(x, group)","Neutralizes Alpha against groups. These groups can be subindustry, industry, sector, country or a constant",/operators/group_neutralize,ALL,"{'id': 'group_neutralize', 'title': 'group_neutralize', 'lastModified': '2024-12-30T04:37:59.230728-05:00', 'content': [{'type': 'TEXT', 'value': '<p><b>group_neutralize(x, group)</b></p>', 'id': '95bdc66d-3ad2-46c6-8c96-a61efc3b7c85'}, {'type': 'TEXT', 'value': '<p>Neutralize alpha against groups. Difference between normalize and group_neutralize is in normalize, every element is subtracted by mean of all values of all instruments on that day whereas in group_neutralize, element is subtracted by mean of all values of the group of instruments that it belongs on that day.</p><p>This operator may help reduce correlation, depending on the neutralization used.</p><p><b>Example:</b><br/>If values of field x on a certain date for 10 instruments is [3,2,6,5,8,9,1,4,8,0] and first 5 instruments belong to one group, last 5 belong to other, then mean of first group = (3+2+6+5+8)/5 = 4.8 and mean of second group = (9+1+4+8+0)/5 = 4.4. Subtracting means from instruments of respective groups gives [3-4.8, 2-4.8, 6-4.8, 5-4.8, 8-4.8, 9-4.4, 1-4.4, 4-4.4, 8-4.4, 0-4.4] = [-1.8, -2.8, 1.2, 0.2, 3.2, 4.6, -3.4, -0.4, 3.6, -4.4]</p>', 'id': '558074f0-ccdb-46f7-9c84-73ab1dd2175e'}], 'sequence': 108, 'category': 'Operators'}","[{'type': 'TEXT', 'value': '<p><b>group_neutralize(x, group)</b></p>', 'id': '95bdc66d-3ad2-46c6-8c96-a61efc3b7c85'}, {'type': 'TEXT', 'value': '<p>Neutralize alpha against groups. Difference between normalize and group_neutralize is in normalize, every element is subtracted by mean of all values of all instruments on that day whereas in group_neutralize, element is subtracted by mean of all values of the group of instruments that it belongs on that day.</p><p>This operator may help reduce correlation, depending on the neutralization used.</p><p><b>Example:</b><br/>If values of field x on a certain date for 10 instruments is [3,2,6,5,8,9,1,4,8,0] and first 5 instruments belong to one group, last 5 belong to other, then mean of first group = (3+2+6+5+8)/5 = 4.8 and mean of second group = (9+1+4+8+0)/5 = 4.4. Subtracting means from instruments of respective groups gives [3-4.8, 2-4.8, 6-4.8, 5-4.8, 8-4.8, 9-4.4, 1-4.4, 4-4.4, 8-4.4, 0-4.4] = [-1.8, -2.8, 1.2, 0.2, 3.2, 4.6, -3.4, -0.4, 3.6, -4.4]</p>', 'id': '558074f0-ccdb-46f7-9c84-73ab1dd2175e'}]",2024-12-30T04:37:59.230728-05:00
group_cartesian_product,Group,['REGULAR'],"group_cartesian_product(g1, g2)","Merge two groups into one group. If originally there are len_1 and len_2 group indices in g1 and g2, there will be len_1 * len_2 indices in the new group.",,,,,
