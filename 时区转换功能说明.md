# 时区转换功能说明

## 🎯 功能概述

已成功为Alpha提取系统添加时区转换功能，现在您可以使用**北京时间**设置参数，系统会自动转换为美东时间调用API。

## ⏰ 时区转换逻辑

### 转换规则
- **输入**: 北京时间 (UTC+8)
- **输出**: 美东时间 (UTC-5)
- **时差**: 北京时间比美东时间快13小时

### 转换示例
```
北京时间: 2025-07-01 00:00:00 → 美东时间: 2025-06-30T11:00:00-05:00
北京时间: 2025-07-01 12:00:00 → 美东时间: 2025-06-30T23:00:00-05:00
北京时间: 2025-07-20 23:59:59 → 美东时间: 2025-07-20T10:59:59-05:00
```

## 📝 参数设置方式

### 新的参数格式
```python
# 4. Alpha提取时间范围 (使用北京时间，系统会自动转换为美东时间)
ALPHA_DATE_RANGE = {
    'start_date_beijing': '2025-07-01 00:00:00',  # 开始时间 (北京时间)
    'end_date_beijing': '2025-07-20 23:59:59',    # 结束时间 (北京时间)
}
```

### 格式说明
- **格式**: `'YYYY-MM-DD HH:MM:SS'`
- **示例**: `'2025-07-01 09:30:00'` (北京时间上午9点30分)
- **注意**: 不需要添加时区信息，系统会自动处理

## 🔧 代码实现

### 转换函数
```python
def beijing_to_eastern(beijing_time_str):
    """将北京时间转换为美东时间"""
    beijing_dt = datetime.strptime(beijing_time_str, '%Y-%m-%d %H:%M:%S')
    beijing_tz = timezone(timedelta(hours=8))
    beijing_dt = beijing_dt.replace(tzinfo=beijing_tz)
    eastern_tz = timezone(timedelta(hours=-5))
    eastern_dt = beijing_dt.astimezone(eastern_tz)
    return eastern_dt.isoformat()
```

### 自动转换
系统在Alpha提取时会自动：
1. 读取北京时间参数
2. 转换为美东时间
3. 调用API
4. 显示转换结果

## 📊 运行时显示

运行时会显示时间转换信息：
```
时间范围转换:
北京时间: 2025-07-01 00:00:00 到 2025-07-20 23:59:59
美东时间: 2025-06-30T11:00:00-05:00 到 2025-07-20T10:59:59-05:00
```

## 🎯 使用场景

### 场景1: 获取工作日Alpha
```python
ALPHA_DATE_RANGE = {
    'start_date_beijing': '2025-07-01 09:00:00',  # 北京时间工作日开始
    'end_date_beijing': '2025-07-05 18:00:00',    # 北京时间工作日结束
}
```

### 场景2: 获取特定月份Alpha
```python
ALPHA_DATE_RANGE = {
    'start_date_beijing': '2025-06-01 00:00:00',  # 6月1日开始
    'end_date_beijing': '2025-06-30 23:59:59',    # 6月30日结束
}
```

### 场景3: 获取最近一周Alpha
```python
ALPHA_DATE_RANGE = {
    'start_date_beijing': '2025-07-14 00:00:00',  # 一周前
    'end_date_beijing': '2025-07-21 23:59:59',    # 现在
}
```

## ⚠️ 注意事项

### 夏令时
- 当前使用标准时间 (UTC-5)
- 夏令时期间美东时间为 UTC-4
- 如需精确处理夏令时，可进一步优化

### 日期跨越
- 由于时差，北京时间的日期可能对应美东时间的前一天
- 例如：北京时间 7月1日 00:00 → 美东时间 6月30日 11:00

### 参数验证
- 确保时间格式正确：`'YYYY-MM-DD HH:MM:SS'`
- 确保开始时间早于结束时间
- 建议使用24小时制

## 🚀 优势

1. **用户友好**: 使用熟悉的北京时间设置
2. **自动转换**: 无需手动计算时差
3. **透明显示**: 显示转换前后的时间
4. **向后兼容**: 不影响现有功能

## 📋 修改总结

### 新增内容
- `beijing_to_eastern()` 时区转换函数
- 新的参数格式 `start_date_beijing` 和 `end_date_beijing`
- 自动时区转换逻辑
- 转换结果显示

### 修改文件
- `alpha提取.py` - 主程序
- `参数配置说明.md` - 参数说明文档
- `时区转换功能说明.md` - 本说明文档

现在您可以直接使用北京时间设置参数，系统会自动处理时区转换！
