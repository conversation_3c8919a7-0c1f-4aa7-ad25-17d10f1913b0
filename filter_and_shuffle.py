import random

INPUT_FILE = '/Users/<USER>/Downloads/新三阶段/alpha_expressions_1000.txt'
OUTPUT_FILE = '/Users/<USER>/Downloads/新三阶段/filtered_shuffled_expressions.txt'

# Substrings to identify alpha_ids to be excluded based on their likely lack of economic meaning
EXCLUSION_SUBSTRINGS = [
    '_flag',
    '_xrefmap',
    'curperiodnum',
    'curperiodtype',
    'security_type',
    'country_code',
    'currency_code',
    'exchange_code',
    'gics_code',
    'isin',
    'sedol',
    'cusip'
]

def filter_and_shuffle_expressions(input_path, output_path, exclusions):
    """Filters expressions based on a list of excluded substrings and shuffles the result."""
    print(f"Reading expressions from {input_path}...")
    with open(input_path, 'r') as f:
        all_expressions = f.readlines()
    
    print(f"Total expressions read: {len(all_expressions)}")

    filtered_expressions = []
    for expression in all_expressions:
        # Extract the data field from the expression, assuming format like rank(divide(subtract(ts_mean(data_field, ...))))
        try:
            data_field = expression.split('(')[-1].split(',')[0]
        except IndexError:
            continue # Skip malformed lines

        # Check if the data_field should be excluded
        should_exclude = False
        for sub in exclusions:
            if sub in data_field:
                should_exclude = True
                break
        
        if not should_exclude:
            filtered_expressions.append(expression.strip())

    print(f"Expressions remaining after filtering: {len(filtered_expressions)}")

    print("Shuffling expressions...")
    random.shuffle(filtered_expressions)

    print(f"Saving shuffled expressions to {output_path}...")
    with open(output_path, 'w') as f:
        for expression in filtered_expressions:
            f.write(expression + '\n')
    
    print("Done.")

if __name__ == "__main__":
    filter_and_shuffle_expressions(INPUT_FILE, OUTPUT_FILE, EXCLUSION_SUBSTRINGS)