#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
因子随机化工具使用示例
演示各种使用场景和功能
"""

from factor_randomizer import FactorRandomizer
import os

def example_1_single_file_basic():
    """示例1: 基本单文件随机化"""
    print("=" * 50)
    print("示例1: 基本单文件随机化")
    print("=" * 50)
    
    randomizer = FactorRandomizer(seed=42)  # 使用固定种子确保结果可重现
    
    # 随机化整个文件
    success = randomizer.randomize_single_file(
        input_file="decoded_expressions (12).txt",
        output_file="randomized_factors_all.txt"
    )
    
    if success:
        print("✅ 成功生成 randomized_factors_all.txt")
    else:
        print("❌ 处理失败")

def example_2_single_file_limited():
    """示例2: 限制数量的单文件随机化"""
    print("\n" + "=" * 50)
    print("示例2: 选择前100个随机因子")
    print("=" * 50)
    
    randomizer = FactorRandomizer(seed=123)
    
    # 只选择100个随机因子
    success = randomizer.randomize_single_file(
        input_file="decoded_expressions (12).txt",
        output_file="randomized_factors_100.txt",
        num_factors=100
    )
    
    if success:
        print("✅ 成功生成包含100个因子的 randomized_factors_100.txt")

def example_3_mix_multiple_files():
    """示例3: 混合多个文件"""
    print("\n" + "=" * 50)
    print("示例3: 混合多个文件")
    print("=" * 50)
    
    # 首先创建一些示例文件用于演示
    create_sample_files()
    
    randomizer = FactorRandomizer(seed=456)
    
    # 配置多个文件及其选择数量
    file_configs = [
        {'file': 'decoded_expressions (12).txt', 'count': 50},
        {'file': 'sample_factors_1.txt', 'count': 30},
        {'file': 'sample_factors_2.txt', 'count': 20}
    ]
    
    success = randomizer.mix_and_randomize_files(
        file_configs=file_configs,
        output_file="mixed_factors.txt",
        total_factors=80  # 最终只要80个因子
    )
    
    if success:
        print("✅ 成功生成混合文件 mixed_factors.txt")

def example_4_distribution_based():
    """示例4: 按比例分布选择"""
    print("\n" + "=" * 50)
    print("示例4: 按比例分布选择因子")
    print("=" * 50)
    
    randomizer = FactorRandomizer(seed=789)
    
    # 按比例从不同文件选择
    file_configs = [
        {'file': 'decoded_expressions (12).txt', 'ratio': 0.001},  # 选择0.1%
        {'file': 'sample_factors_1.txt', 'ratio': 0.5},           # 选择50%
        {'file': 'sample_factors_2.txt', 'ratio': 0.8}            # 选择80%
    ]
    
    success = randomizer.randomize_with_distribution(
        file_configs=file_configs,
        output_file="distributed_factors.txt"
    )
    
    if success:
        print("✅ 成功生成按比例分布的 distributed_factors.txt")

def example_5_reproducible_results():
    """示例5: 可重现的结果"""
    print("\n" + "=" * 50)
    print("示例5: 演示可重现的随机化结果")
    print("=" * 50)
    
    # 使用相同种子生成两次，结果应该相同
    seed = 999
    
    print("第一次生成...")
    randomizer1 = FactorRandomizer(seed=seed)
    randomizer1.randomize_single_file(
        "decoded_expressions (12).txt",
        "reproducible_1.txt",
        num_factors=10
    )
    
    print("第二次生成...")
    randomizer2 = FactorRandomizer(seed=seed)
    randomizer2.randomize_single_file(
        "decoded_expressions (12).txt",
        "reproducible_2.txt",
        num_factors=10
    )
    
    # 检查两个文件是否相同
    try:
        with open("reproducible_1.txt", 'r') as f1, open("reproducible_2.txt", 'r') as f2:
            content1 = f1.read()
            content2 = f2.read()
            
        if content1 == content2:
            print("✅ 两次生成的结果完全相同，随机种子工作正常")
        else:
            print("❌ 两次生成的结果不同")
    except:
        print("❌ 无法比较文件")

def create_sample_files():
    """创建一些示例文件用于演示"""
    # 创建示例文件1
    sample_factors_1 = [
        "ts_mean(close, 10)",
        "ts_std(volume, 20)",
        "rank(returns)",
        "ts_delta(high, 5)",
        "correlation(close, volume, 15)"
    ]
    
    with open("sample_factors_1.txt", 'w', encoding='utf-8') as f:
        for factor in sample_factors_1:
            f.write(factor + '\n')
    
    # 创建示例文件2
    sample_factors_2 = [
        "ts_max(low, 30)",
        "ts_min(high, 25)",
        "decay_linear(returns, 10)",
        "ts_rank(volume, 20)",
        "neutralize(close, industry)"
    ]
    
    with open("sample_factors_2.txt", 'w', encoding='utf-8') as f:
        for factor in sample_factors_2:
            f.write(factor + '\n')

def cleanup_sample_files():
    """清理示例文件"""
    sample_files = [
        "sample_factors_1.txt",
        "sample_factors_2.txt",
        "randomized_factors_all.txt",
        "randomized_factors_100.txt",
        "mixed_factors.txt",
        "distributed_factors.txt",
        "reproducible_1.txt",
        "reproducible_2.txt"
    ]
    
    for file in sample_files:
        try:
            if os.path.exists(file):
                os.remove(file)
                print(f"🗑️  已删除 {file}")
        except:
            pass

def main():
    """运行所有示例"""
    print("🚀 因子随机化工具示例演示")
    print("这些示例将演示工具的各种功能")
    print()
    
    # 检查主要输入文件是否存在
    if not os.path.exists("decoded_expressions (12).txt"):
        print("❌ 找不到 decoded_expressions (12).txt 文件")
        print("请确保该文件在当前目录中")
        return
    
    try:
        # 运行所有示例
        example_1_single_file_basic()
        example_2_single_file_limited()
        example_3_mix_multiple_files()
        example_4_distribution_based()
        example_5_reproducible_results()
        
        print("\n" + "=" * 50)
        print("🎉 所有示例运行完成！")
        print("=" * 50)
        
        # 询问是否清理文件
        choice = input("\n是否删除生成的示例文件？(y/n): ").lower()
        if choice == 'y':
            cleanup_sample_files()
            print("✅ 清理完成")
        else:
            print("📁 示例文件已保留")
            
    except Exception as e:
        print(f"❌ 运行示例时出错: {str(e)}")

if __name__ == "__main__":
    main()
