# WorldQuant Brain 操作符 2025 主文档完整分析

## 概述

本文档完整整理了 `operators_2025-master.csv` 文件中包含的所有操作符信息。该文件包含了 80 个不同的操作符，分为以下几个主要类别：

- **Arithmetic（算术运算）**: 17个操作符
- **Logical（逻辑运算）**: 10个操作符
- **Time Series（时间序列）**: 28个操作符
- **Cross Sectional（横截面）**: 8个操作符
- **Vector（向量）**: 4个操作符
- **Transformational（转换）**: 2个操作符
- **Group（分组）**: 11个操作符

## 详细操作符列表

### 1. 算术运算操作符 (Arithmetic) - 17个

#### 1.1 add
- **定义**: `add(x, y, filter = false), x + y`
- **描述**: 加法运算，至少需要2个输入。如果 filter = true，在相加前将所有输入的NaN过滤为0
- **级别**: ALL
- **文档**: 无

#### 1.2 multiply
- **定义**: `multiply(x ,y, ... , filter=false), x * y`
- **描述**: 乘法运算，至少需要2个输入。Filter参数将NaN值设置为1
- **级别**: ALL
- **文档**: /operators/multiply
- **最后修改**: 2024-12-30T04:36:02.202600-05:00
- **示例**: `multiply(rank(-returns), rank(volume/adv20), filter=true)`

#### 1.3 sign
- **定义**: `sign(x)`
- **描述**: 如果输入 = NaN，返回 NaN
- **级别**: ALL
- **文档**: 无

#### 1.4 subtract
- **定义**: `subtract(x, y, filter=false), x - y`
- **描述**: x-y。如果 filter = true，在相减前将所有输入的NaN过滤为0
- **级别**: ALL
- **文档**: 无

#### 1.5 log
- **定义**: `log(x)`
- **描述**: 自然对数。例如：Log(high/low) 使用高低价比率的自然对数作为股票权重
- **级别**: ALL
- **文档**: 无

#### 1.6 max
- **定义**: `max(x, y, ..)`
- **描述**: 所有输入的最大值。至少需要2个输入
- **级别**: ALL
- **文档**: /operators/max
- **最后修改**: 2024-12-30T04:36:34.463416-05:00
- **示例**: `max (close, vwap)`

#### 1.7 to_nan
- **定义**: `to_nan(x, value=0, reverse=false)`
- **描述**: 将值转换为NaN，或如果reverse=true则将NaN转换为值
- **级别**: 无
- **文档**: 无

#### 1.8 abs
- **定义**: `abs(x)`
- **描述**: x的绝对值
- **级别**: ALL
- **文档**: 无

#### 1.9 divide
- **定义**: `divide(x, y), x / y`
- **描述**: x / y
- **级别**: ALL
- **文档**: 无

#### 1.10 min
- **定义**: `min(x, y ..)`
- **描述**: 所有输入的最小值。至少需要2个输入
- **级别**: ALL
- **文档**: /operators/min
- **最后修改**: 2024-12-30T04:36:21.252157-05:00
- **示例**: `min(close, vwap)`

#### 1.11 signed_power
- **定义**: `signed_power(x, y)`
- **描述**: x的y次幂，保持x的符号。公式：sign(x) * (abs(x) ^ y)
- **级别**: ALL
- **文档**: /operators/signed_power
- **最后修改**: 2024-12-30T04:40:14.620305-05:00
- **详细说明**: 对于幂为2的情况，x ^ y是抛物线，但signed_power(x, y)是奇函数和一对一函数
- **示例**:
  - 如果 x = 3, y = 2 ⇒ abs(x) = 3 ⇒ abs(x) ^ y = 9 且 sign(x) = +1 ⇒ signed_power(x, y) = 9
  - 如果 x = -9, y = 0.5 ⇒ abs(x) = 9 ⇒ abs(x) ^ y = 3 且 sign(x) = -1 ⇒ signed_power(x, y) = -3

#### 1.12 inverse
- **定义**: `inverse(x)`
- **描述**: 1 / x
- **级别**: ALL
- **文档**: 无

#### 1.13 sqrt
- **定义**: `sqrt(x)`
- **描述**: x的平方根
- **级别**: ALL
- **文档**: 无

#### 1.14 reverse
- **定义**: `reverse(x), - x`
- **描述**: -x
- **级别**: ALL
- **文档**: 无

#### 1.15 power
- **定义**: `power(x, y)`
- **描述**: x ^ y
- **级别**: ALL
- **文档**: /operators/power
- **最后修改**: 2025-04-01T05:10:12.015739-04:00
- **示例**:
  - `power (returns, volume/adv20); power (returns, volume/adv20, precise=true)`
  - power操作符可用于实现流行的数学函数，例如sigmoid(close)可以实现为：`1/(1+ power(2.7182, -close)`

#### 1.16 densify
- **定义**: `densify(x)`
- **描述**: 将具有许多桶的分组字段转换为较少数量的可用桶，使分组字段的计算更高效
- **级别**: ALL
- **文档**: /operators/densify
- **最后修改**: 2024-12-30T04:40:23.398901-05:00
- **详细说明**: 如果分组字段提供为整数（例如，行业：tech -> 0, airspace -> 1, ...），对于某个日期，我们有分组字段值为{0, 1, 2, 99}的工具。与其创建100个桶并保持96个空桶，不如只创建4个值为{0, 1, 2, 3}的桶。如果x中唯一值的数量为n，densify将这些值映射到0和(n-1)之间。不需要保持数量级顺序。

### 2. 逻辑运算操作符 (Logical) - 10个

#### 2.1 or
- **定义**: `or(input1, input2)`
- **描述**: 逻辑OR操作符，如果任一或两个输入为真则返回真，否则返回假
- **级别**: ALL
- **文档**: 无

#### 2.2 and
- **定义**: `and(input1, input2)`
- **描述**: 逻辑AND操作符，只有当两个操作数都为真时返回真，否则返回假
- **级别**: ALL
- **文档**: 无

#### 2.3 not
- **定义**: `not(x)`
- **描述**: 返回x的逻辑否定。如果x为真(1)，返回假(0)；如果输入为假(0)，返回真(1)
- **级别**: ALL
- **文档**: 无

#### 2.4 is_nan
- **定义**: `is_nan(input)`
- **描述**: 如果(input == NaN)返回1，否则返回0
- **级别**: ALL
- **文档**: /operators/is_nan
- **最后修改**: 2025-04-01T05:12:42.251246-04:00
- **详细说明**: is_nan(x)操作符可用于识别NaN值并使用if_else语句将其替换为默认值
- **示例**: `if_else(is_nan(rank(sales)), 0.5, rank(sales))` - 如果任何工具的销售值为NaN，表达式将用排名的均值0.5替换它

#### 2.5 less
- **定义**: `input1 < input2`
- **描述**: 如果input1 < input2返回真，否则返回假
- **级别**: ALL
- **文档**: 无

#### 2.6 equal
- **定义**: `input1 == input2`
- **描述**: 如果两个输入相同返回真，否则返回假
- **级别**: ALL
- **文档**: 无

#### 2.7 greater
- **定义**: `input1 > input2`
- **描述**: 逻辑比较操作符，比较两个输入
- **级别**: ALL
- **文档**: 无

#### 2.8 if_else
- **定义**: `if_else(input1, input2, input 3)`
- **描述**: 如果input1为真则返回input2，否则返回input3
- **级别**: ALL
- **文档**: /operators/if_else
- **最后修改**: 2024-12-30T04:38:07.939341-05:00
- **详细说明**: `if_else(event_condition, Alpha_expression_1, Alpha_expression_2)` - 如果提供的事件条件为真，将返回Alpha_expression_1。如果事件条件为假，将返回Alpha_expression_2
- **示例**:
  ```
  Event = volume > adv20;
  alpha_1 = 2 * (-ts_delta(close, 3));
  alpha_2 = (-ts_delta(close, 3));
  if_else(event, alpha_1, alpha_2)
  ```
- **应用场景**: 测试假设：如果公司股价在过去2天上涨，未来可能下跌。如果今天买卖的股票数量高于月平均值，则可能更明显地观察到反转效应

#### 2.9 not_equal
- **定义**: `input1!= input2`
- **描述**: 如果两个输入不相同返回真，否则返回假
- **级别**: ALL
- **文档**: 无

#### 2.10 less_equal
- **定义**: `input1 <= input2`
- **描述**: 如果input1 <= input2返回真，否则返回假
- **级别**: ALL
- **文档**: 无

#### 2.11 greater_equal
- **定义**: `input1 >= input2`
- **描述**: 如果input1 >= input2返回真，否则返回假
- **级别**: ALL
- **文档**: 无

### 3. 时间序列操作符 (Time Series) - 28个

#### 3.1 ts_corr
- **定义**: `ts_corr(x, y, d)`
- **描述**: 返回x和y在过去d天的相关性
- **级别**: ALL
- **文档**: /operators/ts_corr
- **最后修改**: 2024-12-30T04:40:36.534315-05:00
- **详细说明**: 皮尔逊相关性衡量两个变量之间的线性关系。当变量呈正态分布且关系为线性时最有效
- **公式**: $$Correlation(x,y) = \frac{\sum_{i=t-d+1}^t (x_i - \bar{x})(y_i - \bar{y})}{\sqrt{\sum_{i=t-d+1}^t (x_i - \bar{x})^2 (y_i - \bar{y})^2}}$$
- **示例**: `ts_corr(vwap, close, 20)`

#### 3.2 ts_zscore
- **定义**: `ts_zscore(x, d)`
- **描述**: Z分数是描述值与一组值均值关系的数值测量。Z分数以距离均值的标准差数量来衡量：(x - tsmean(x,d)) / tsstddev(x,d)。此操作符可能有助于减少异常值和回撤
- **级别**: ALL
- **文档**: 无

#### 3.3 ts_product
- **定义**: `ts_product(x, d)`
- **描述**: 返回x在过去d天的乘积
- **级别**: ALL
- **文档**: /operators/ts_product
- **最后修改**: 2025-04-01T05:21:48.246260-04:00
- **详细说明**: ts_product(x, d)可用于计算数据字段的几何平均值。几何平均值通常是平均收益率、基本面增长率的更好方法
- **示例**: `power(ts_product(returns, 10), 1/10)` - 计算过去10天日股票收益的几何平均值

#### 3.4 ts_std_dev
- **定义**: `ts_std_dev(x, d)`
- **描述**: 返回x在过去d天的标准差
- **级别**: ALL
- **文档**: 无

#### 3.5 ts_backfill
- **定义**: `ts_backfill(x,lookback = d, k=1, ignore="NAN")`
- **描述**: 回填是用有意义的值（即第一个非NaN值）替换NAN或0值的过程
- **级别**: ALL
- **文档**: /operators/ts_backfill
- **最后修改**: 2024-12-30T04:37:42.095530-05:00
- **详细说明**: ts_backfill操作符用最后可用的非NaN值替换NaN值。如果数据字段x的输入值为NaN，ts_backfill操作符将检查过去d天同一数据字段的可用输入值，并输出最近可用的非NaN输入值。如果设置了k参数，ts_backfill操作符将输出第k个最近可用的非NaN输入值
- **功能**: 此操作符改善权重覆盖并可能有助于减少回撤风险
- **示例**: ts_backfill(x, 252)
  - 如果数据字段x的输入值 = 非NaN，则输出 = x
  - 如果数据字段x的输入值 = NaN，则输出 = 过去252天x的最近可用非NaN输入值

#### 3.6 days_from_last_change
- **定义**: `days_from_last_change(x)`
- **描述**: 自x上次变化以来的天数
- **级别**: ALL
- **文档**: 无

#### 3.7 last_diff_value
- **定义**: `last_diff_value(x, d)`
- **描述**: 从过去d天返回与当前x值不相等的最后x值
- **级别**: ALL
- **文档**: 无

#### 3.8 ts_scale
- **定义**: `ts_scale(x, d, constant = 0)`
- **描述**: 返回 (x - ts_min(x, d)) / (ts_max(x, d) - ts_min(x, d)) + constant。此操作符类似于scale down操作符，但在时间序列空间中操作
- **级别**: ALL
- **文档**: /operators/ts_scale
- **最后修改**: 2024-12-30T04:28:08.688202-05:00
- **示例**: 如果d = 6且过去6天的值为[6,2,8,5,9,4]（第一个元素为今天的值），ts_min(x,d) = 2，ts_max(x,d) = 9，则ts_scale(x,d,constant = 1) = 1 + (6-2)/(9-2) = 1.57

#### 3.9 ts_step
- **定义**: `ts_step(1)`
- **描述**: 返回天数计数器
- **级别**: ALL
- **文档**: 无

#### 3.10 ts_sum
- **定义**: `ts_sum(x, d)`
- **描述**: 对x在过去d天的值求和
- **级别**: ALL
- **文档**: 无

#### 3.11 ts_av_diff
- **定义**: `ts_av_diff(x, d)`
- **描述**: 返回 x - tsmean(x, d)，但仔细处理NaN。即在均值计算期间忽略NaN
- **级别**: ALL
- **文档**: /operators/ts_av_diff
- **最后修改**: 2024-12-30T04:32:09.600819-05:00
- **示例**: 如果d = 6且过去6天的值为[6,2,8,5,9,NaN]，则ts_mean(x,d) = 6，因为NaN在均值计算中被忽略。因此，ts_av_diff(x,d) = 6-6 = 0

#### 3.12 ts_mean
- **定义**: `ts_mean(x, d)`
- **描述**: 返回x在过去d天的平均值
- **级别**: ALL
- **文档**: 无

#### 3.13 ts_arg_max
- **定义**: `ts_arg_max(x, d)`
- **描述**: 返回过去d天时间序列中最大值的相对索引。如果当前日有过去d天的最大值，返回0。如果前一天有过去d天的最大值，返回1
- **级别**: ALL
- **文档**: /operators/ts_arg_max
- **最后修改**: 2024-12-30T04:32:41.952253-05:00
- **示例**: 如果d = 6且过去6天的值为[6,2,8,5,9,4]（第一个元素为今天的值），则最大值为9，它在今天之前4天。因此，ts_arg_max(x, d) = 4

#### 3.14 ts_max
- **定义**: `ts_max(x, d)`
- **描述**: 返回x在过去d天的最大值
- **级别**: 无
- **文档**: 无

#### 3.15 ts_rank
- **定义**: `ts_rank(x, d, constant = 0)`
- **描述**: 对每个工具在过去d天的x值进行排名，然后返回当前值的排名 + constant。如果未指定，默认情况下constant = 0
- **级别**: ALL
- **文档**: 无

#### 3.16 ts_delay
- **定义**: `ts_delay(x, d)`
- **描述**: 返回d天前的x值
- **级别**: ALL
- **文档**: 无

#### 3.17 ts_quantile
- **定义**: `ts_quantile(x,d, driver="gaussian" )`
- **描述**: 计算ts_rank并将其值应用驱动分布的逆累积密度函数。驱动的可能值（可选）为"gaussian"、"uniform"、"cauchy"分布，其中"gaussian"为默认值
- **级别**: ALL
- **文档**: 无

#### 3.18 ts_min
- **定义**: `ts_min(x, d)`
- **描述**: 返回x在过去d天的最小值
- **级别**: 无
- **文档**: 无

#### 3.19 ts_count_nans
- **定义**: `ts_count_nans(x ,d)`
- **描述**: 返回x在过去d天的NaN值数量
- **级别**: ALL
- **文档**: 无

#### 3.20 ts_covariance
- **定义**: `ts_covariance(y, x, d)`
- **描述**: 返回y和x在过去d天的协方差
- **级别**: ALL
- **文档**: 无

#### 3.21 ts_decay_linear
- **定义**: `ts_decay_linear(x, d, dense = false)`
- **描述**: 返回x在过去d天的线性衰减。Dense参数=false意味着操作符在稀疏模式下工作，我们将NaN视为0。在密集模式下我们不这样做
- **级别**: ALL
- **文档**: /operators/ts_decay_linear
- **最后修改**: 2024-12-30T04:37:07.926791-05:00
- **详细说明**: 线性衰减等数据平滑技术通过对较旧的观察应用衰减因子来减少时间序列数据中的噪声，这有助于稳定数据集。此操作符改善换手率和回撤
- **示例**: 对于过去5天价格的股票：
  - 第0天：30（异常值）
  - 第-1天：5
  - 第-2天：4
  - 第-3天：5
  - 第-4天：6
  - 计算：
    - 分子 = (30⋅5)+(5⋅4)+(4⋅3)+(5⋅2)+(6⋅1)=150+20+12+10+6=198
    - 分母=5+4+3+2+1=15
    - 加权平均=198/15=13.2
  - 使用加权平均值13.2而不是异常值20来分配权重

#### 3.22 jump_decay
- **定义**: `jump_decay(x, d, sensitivity=0.5, force=0.1)`
- **描述**: 如果当前数据与前一个数据相比有巨大跳跃，应用force
- **级别**: 无
- **文档**: /operators/jump_decay
- **最后修改**: 2024-12-30T04:41:05.009296-05:00
- **详细说明**:
  - 公式：jump_decay(x) = abs(x-ts_delay(x, 1)) > sensitivity * ts_stddev(x,d) ? ts_delay(x,1) + ts_delta(x, 1) * force: x
  - 如果启用stddev，跳跃阈值将计算为sensitivity * stddev，否则为sensitivity
- **示例**: `jump_decay(sales/assets,252,stddev=True,sensitivity=0.5,force=0.1)`

#### 3.23 ts_arg_min
- **定义**: `ts_arg_min(x, d)`
- **描述**: 返回过去d天时间序列中最小值的相对索引；如果当前日有过去d天的最小值，返回0；如果前一天有过去d天的最小值，返回1
- **级别**: ALL
- **文档**: /operators/ts_arg_min
- **最后修改**: 2025-02-06T03:48:46.256322-05:00
- **示例**: 如果d = 6且过去6天的值为[6,2,8,5,9,4]（第一个元素为今天的值），则最小值为2，它在今天之前1天。因此，ts_arg_min(x, d) = 1

#### 3.24 ts_regression
- **定义**: `ts_regression(y, x, d, lag = 0, rettype = 0)`
- **描述**: 返回与回归函数相关的各种参数
- **级别**: ALL
- **文档**: /operators/ts_regression
- **最后修改**: 2024-12-30T04:40:45.890709-05:00
- **详细说明**: 给定两个变量值的集合（X：自变量，Y：因变量）在d天的过程中，可以定义一个近似线性函数，使得该集合上的平方误差和假设最小值
- **返回值类型**:
  - rettype=0: 误差项
  - rettype=1: y截距(α)
  - rettype=2: 斜率(β)
  - rettype=3: y估计值
  - rettype=4: 误差平方和(SSE)
  - rettype=5: 总平方和(SST)
  - rettype=6: R平方
  - rettype=7: 均方误差(MSE)
  - rettype=8: β的标准误差
  - rettype=9: α的标准误差
- **示例**: `ts_regression(ts_mean(volume, 2), ts_returns(close, 2), 252)`

#### 3.25 kth_element
- **定义**: `kth_element(x, d, k)`
- **描述**: 通过查看回溯天数返回输入的第K个值。此操作符可用于在k=1时回填缺失数据
- **级别**: ALL
- **文档**: /operators/kth_element
- **最后修改**: 2024-12-30T04:32:27.552322-05:00
- **详细说明**: 返回输入的第k个值，通过查看回溯天数，同时忽略忽略列表中的空格分隔标量。此操作符也称为回填操作符，因为它可用于回填缺失数据
- **示例**: `kth_element(sales/assets,252,k="1",ignore="NAN 0")`

#### 3.26 hump
- **定义**: `hump(x, hump = 0.01)`
- **描述**: 限制输入变化的数量和幅度（从而减少换手率）
- **级别**: ALL
- **文档**: /operators/hump
- **最后修改**: 2024-12-30T04:37:13.821810-05:00
- **详细说明**: 此操作符限制Alpha变化的频率和幅度（从而减少换手率）。如果今天的值与昨天的值相比只显示轻微变化（不超过阈值），hump操作符的输出保持与昨天相同。如果变化大于限制，输出是昨天的值加上变化方向的限制
- **功能**: 此操作符可能有助于减少换手率和回撤
- **示例**: `hump(-ts_delta(close, 5), hump = 0.00001)`

#### 3.27 ts_delta
- **定义**: `ts_delta(x, d)`
- **描述**: 返回 x - ts_delay(x, d)
- **级别**: ALL
- **文档**: 无

#### 3.28 ts_target_tvr_decay
- **定义**: `ts_target_tvr_decay(x, lambda_min=0, lambda_max=1, target_tvr=0.1)`
- **描述**: 调整"ts_decay"以使换手率等于某个目标，优化权重范围在lambda_min和lambda_max之间
- **级别**: 无
- **文档**: 无

#### 3.29 ts_target_tvr_delta_limit
- **定义**: `ts_target_tvr_delta_limit(x, y, lambda_min=0, lambda_max=1, target_tvr=0.1)`
- **描述**: 调整"ts_delta_limit"以使换手率等于某个目标，优化权重范围在lambda_min和lambda_max之间。另外，请注意x和y的缩放。除了将y设置为adv20或成交量相关数据外，您还可以将y设置为常数
- **级别**: 无
- **文档**: 无

### 4. 横截面操作符 (Cross Sectional) - 8个

#### 4.1 winsorize
- **定义**: `winsorize(x, std=4)`
- **描述**: 对x进行温莎化处理，确保x中的所有值都在下限和上限之间，这些限制被指定为std的倍数
- **级别**: ALL
- **文档**: 无

#### 4.2 rank
- **定义**: `rank(x, rate=2)`
- **描述**: 在所有工具中对输入进行排名，并返回0.0到1.0之间的等分布数字。对于精确排序，使用rate为0
- **级别**: ALL
- **文档**: /operators/rank
- **最后修改**: 2024-12-30T04:39:11.372951-05:00
- **详细说明**: Rank操作符对给定股票在所有工具中的输入数据x值进行排名，并返回0.0到1.0之间等分布的浮点数。当rate设置为0时，排序是精确的。rate的默认值为2
- **功能**: 此操作符可能有助于减少异常值和回撤，同时改善夏普比率
- **示例**:
  - `Rank(close); Rank (close, rate=0)` # 精确排序
  - X = (4,3,6,10,2) => Rank(x) = (0.5, 0.25, 0.75, 1, 0)

#### 4.3 vector_neut
- **定义**: `vector_neut(x, y)`
- **描述**: 对于给定的向量x和y，找到一个新向量x*（输出），使得x*与y正交
- **级别**: 无
- **文档**: /operators/vector_neut
- **最后修改**: 2024-12-30T04:37:19.217102-05:00
- **详细说明**: 输入1中性化到输入2。对于给定向量A（即输入1）和B（即输入2），找到一个新向量A'（即输出），使得A'与B正交。它计算A在B上的投影，然后从A中减去投影向量以找到垂直于B的拒绝向量（即A'）
- **功能**: 此操作符可能有助于减少相关性，取决于使用的中性化
- **示例**: `vector_neut(open,close)`

#### 4.4 zscore
- **定义**: `zscore(x)`
- **描述**: Z分数是描述值与一组值均值关系的数值测量。Z分数以距离均值的标准差数量来衡量
- **级别**: ALL
- **文档**: /operators/zscore
- **最后修改**: 2024-12-30T04:37:35.294396-05:00
- **详细说明**: Z分数是一个统计工具，表示数据点距离一组值平均值有多少个标准差。本质上，它衡量数据点相对于均值的异常程度，使其成为理解偏差和比较的便利工具
- **公式**: $$Z\textrm{-}score = \frac{x - mean(x)}{std(x)}$$
- **功能**: Z分数对于标准化和比较不同股票的不同数据字段或不同数据字段可能特别有用。它们允许研究人员计算分数在标准正态分布内出现的概率，并比较来自不同样本的两个分数。此操作符可能有助于减少异常值
- **示例**: `zscore(close)`

#### 4.5 scale_down
- **定义**: `scale_down(x,constant=0)`
- **描述**: 将每天的所有值按比例缩放到0和1之间，使最小值映射到0，最大值映射到1。Constant是最终结果减去的偏移量
- **级别**: 无
- **文档**: /operators/scale_down
- **最后修改**: 2024-12-30T04:39:36.638693-05:00
- **示例**: 如果对于某个日期，某个输入x的工具值为[15,7,0,20]，max = 20，min = 0
  - scale_down(x,constant=0) = [(15-0)/20,(7-0)/20,(0-0)/20,(20-0)/20] = [0.75,0.35,0,1]
  - scale_down(x,constant=1) = [0.75-1,0.35-1,0-1,1-1] = [-0.25,-0.65,-1,0]

#### 4.6 scale
- **定义**: `scale(x, scale=1, longscale=1, shortscale=1)`
- **描述**: 将输入缩放到账面规模。我们还可以通过向操作符提及额外参数来将多头头寸和空头头寸缩放到单独的规模
- **级别**: ALL
- **文档**: /operators/scale
- **最后修改**: 2024-12-30T04:37:47.747310-05:00
- **详细说明**: 操作符将输入缩放到账面规模。我们可以通过指定额外参数'scale=booksize_value'来可选地调整账面规模。我们还可以通过指定额外参数将多头头寸和空头头寸缩放到单独的规模：longscale=long_booksize和shortscale=short_booksize。除非另有说明，否则规模的每条腿的默认值为0，这意味着不缩放。缩放alpha使所有工具上abs(x)的总和等于1。要缩放到不同的账面规模，使用Scale(x) * booksize
- **功能**: 此操作符可能有助于减少异常值
- **示例**: `scale(returns, scale=4); scale (returns, scale= 1) + scale (close, scale=20); scale (returns, longscale=4, shortscale=3)`

#### 4.7 normalize
- **定义**: `normalize(x, useStd = false, limit = 0.0)`
- **描述**: 计算某个日期所有有效alpha值的均值，然后从每个元素中减去该均值
- **级别**: ALL
- **文档**: /operators/normalize
- **最后修改**: 2024-12-30T04:39:00.075882-05:00
- **详细说明**: 此操作符计算某个日期所有有效alpha值的均值，然后从每个元素中减去该均值。如果useStd=true，操作符计算结果值的标准差并将每个标准化元素除以它。如果limit不等于0.0，操作符对结果alpha值设置限制（在-limit到+limit之间）
- **示例**: 如果对于某个日期，某个输入x的工具值为[3,5,6,2]，mean = 4，标准差 = 1.82
  - normalize(x, useStd = false, limit = 0.0) = [3-4,5-4,6-4,2-4] = [-1,1,2,-2]
  - normalize(x, useStd = true, limit = 0.0) = [-1/1.82,1/1.82,2/1.82,-2/1.82] = [-0.55,0.55,1.1,-1.1]

#### 4.8 quantile
- **定义**: `quantile(x, driver = gaussian, sigma = 1.0)`
- **描述**: 对原始向量进行排名，移位排名的Alpha向量，应用分布（gaussian、cauchy、uniform）。如果driver是uniform，它简单地从Alpha向量中的所有Alpha值中减去每个Alpha值的均值
- **级别**: ALL
- **文档**: /operators/quantile
- **最后修改**: 2024-12-30T04:37:29.705754-05:00
- **详细说明**:
  1. 对输入原始Alpha向量进行排名 - 排名的Alpha值将在[0, 1]内
  2. 移位排名的Alpha向量 - 对于排名Alpha向量中的每个Alpha值，它被移位为：Alpha_value = 1/N + Alpha_value * (1 - 2/N)，这里假设Alpha向量中有N个具有值的工具。移位的Alpha值将在[1/N, 1-1/N]内
  3. 使用指定的driver为排名Alpha向量中的每个Alpha值应用分布。Driver可以是"gaussian"、"uniform"、"cauchy"之一
- **注意**: Sigma只影响最终值的规模
- **功能**: 此操作符可能有助于减少异常值
- **示例**: `quantile(close, driver = gaussian, sigma = 0.5 )`

### 5. 向量操作符 (Vector) - 4个

#### 5.1 vec_min
- **定义**: `vec_min(x)`
- **描述**: 向量字段x的最小值
- **级别**: 无
- **文档**: 无

#### 5.2 vec_sum
- **定义**: `vec_sum(x)`
- **描述**: 向量字段x的总和
- **级别**: ALL
- **文档**: 无

#### 5.3 vec_max
- **定义**: `vec_max(x)`
- **描述**: 向量字段x的最大值
- **级别**: 无
- **文档**: 无

#### 5.4 vec_avg
- **定义**: `vec_avg(x)`
- **描述**: 取向量字段x的均值
- **级别**: ALL
- **文档**: 无

### 6. 转换操作符 (Transformational) - 2个

#### 6.1 bucket
- **定义**: `bucket(rank(x), range="0, 1, 0.1" or buckets = "2,5,6,7,10")`
- **描述**: 将浮点值转换为用户指定桶的索引。Bucket对于创建组值很有用，可以作为输入传递给GROUP
- **级别**: ALL
- **文档**: /operators/bucket
- **最后修改**: 2024-12-30T04:42:30.417131-05:00
- **详细说明**:
  - 如果buckets指定为"num_1, num_2, …, num_N"，它被转换为由[(num_1, num_2, idx_1), (num_2, num_3, idx_2), ..., (num_N-1, num_N, idx_N-1)]组成的括号
  - 如果range指定为"start, end, step"，它被转换为由[(start, start+step, idx_1), (start+step, start+2*step, idx_2), ..., (start+N*step, end, idx_N)]组成的括号
  - 默认添加对应于(-inf, start]和[end, +inf)的两个隐藏桶。使用skipBegin、skipEnd参数删除这些桶。使用skipBoth将skipEnd和skipBegin都设置为true
  - 如果要将所有NAN值分配到它们自己的单独组中，使用NANGroup。索引值将是最后一个桶之后的一个
- **示例**:
  ```
  my_group = bucket(rank(volume), range="0.1,1,0.1");
  group_neutralize(sales/assets, my_group)

  my_group = bucket(rank(volume), buckets ="0.2,0.5,0.7", skipBoth=True, NANGroup=True);
  group_neutralize(sales/assets, my_group)
  ```

#### 6.2 trade_when
- **定义**: `trade_when(x, y, z)`
- **描述**: 用于仅在指定条件下更改Alpha值并在其他情况下保持Alpha值。它还允许在指定条件下关闭Alpha头寸（分配NaN值）
- **级别**: ALL
- **文档**: /operators/trade_when
- **最后修改**: 2024-12-30T04:39:05.839330-05:00
- **详细说明**:
  - Trade_When (x=triggerTradeExp, y=AlphaExp, z=triggerExitExp)
  - 如果triggerExitExp > 0，Alpha = NaN
  - 否则如果triggerTradeExp > 0，Alpha = AlphaExp
  - 否则，Alpha = previousAlpha
- **功能**: 此操作符可能有助于减少相关性和减少换手率
- **示例**:
  - `Trade_When (volume >= ts_sum(volume,5)/5, rank(-returns), -1)` - 如果(volume >= ts_sum(volume,5)/5)，Alpha = rank(-returns)；否则交易前一个Alpha；退出条件始终为假
  - `Trade_When (volume >= ts_sum(volume,5)/5, rank(-returns), abs(returns) > 0.1)` - 如果abs(returns) > 0.1，Alpha = nan；否则如果volume >= ts_sum(volume,5)/5，Alpha = rank(-returns)；否则交易前一个Alpha

### 7. 分组操作符 (Group) - 11个

#### 7.1 group_min
- **定义**: `group_min(x, group)`
- **描述**: 组中的所有元素等于组的最小值
- **级别**: 无
- **文档**: 无

#### 7.2 group_mean
- **定义**: `group_mean(x, weight, group)`
- **描述**: 组中的所有元素等于均值
- **级别**: ALL
- **文档**: /operators/group_mean
- **最后修改**: 2025-04-01T05:21:56.452359-04:00
- **详细说明**: group_mean(x, group)操作符可用于计算数据字段的调和平均值。调和平均值在其倒数贡献方面给每个值相等的权重，被认为是计算基本比率和因子平均值的更好方法
- **示例**: `1 /(group_mean(eps/close,1, industry))` - 计算行业的P/E比率（市盈率）的调和平均值

#### 7.3 group_max
- **定义**: `group_max(x, group)`
- **描述**: 同一组中所有工具的x的最大值
- **级别**: 无
- **文档**: 无

#### 7.4 group_rank
- **定义**: `group_rank(x, group)`
- **描述**: 组中的每个元素被分配该组中相应的排名
- **级别**: ALL
- **文档**: /operators/group_rank
- **最后修改**: 2024-12-30T04:38:04.640773-05:00
- **详细说明**: 分组操作符是一种横截面操作符，在更精细的级别上比较股票，其中横截面操作在每个组内应用，而不是在整个市场上。group_rank操作符将股票分配到其指定组，然后在每个组内，根据数据字段x的输入值对组内股票进行排名，并返回0.0到1.0之间的等分布数字
- **功能**: 此操作符可能有助于减少异常值和回撤，同时减少相关性
- **示例**: group_rank(x, subindustry) - 股票首先按其各自的子行业分组。在每个子行业内，该子行业内的股票根据数据字段x的输入值进行排名，并分配0.0到1.0之间的等分布数字

#### 7.5 group_backfill
- **定义**: `group_backfill(x, group, d, std = 4.0)`
- **描述**: 如果某个日期和工具的某个值为NaN，从同一组工具的集合中，计算过去d天所有非NaN值的温莎化均值
- **级别**: ALL
- **文档**: /operators/group_backfill
- **最后修改**: 2024-12-30T04:38:46.252719-05:00
- **详细说明**: 温莎化均值意味着输入被std * stddev截断，其中stddev是输入的标准差
- **示例**: 如果d = 4且组中有3个工具(i1, i2, i3)，过去4天的值为x[i1] = [4,2,5,5], x[i2] = [7,NaN,2,9], x[i3] = [NaN,-4,2,NaN]（第一个元素最新），那么如果我们想要回填x，我们只需要回填x[i3]的第一个元素，因为其他每个工具的第一个元素都是非NaN。其他组的非NaN值为[4,2,5,5,7,2,9,-4,2]。均值 = 3.56，标准差为3.71，没有项目超出3.56 – 4 * 3.71和3.56 + 4 * 3.71的范围。因此，我们不需要将元素剪切到这些限制。因此，温莎化均值 = 回填值 = 3.56。对于三个工具，group_backfill(x, group, d, std = 4.0) = [4,7,3.56]

#### 7.6 group_scale
- **定义**: `group_scale(x, group)`
- **描述**: 将组中的值标准化为0到1之间。(x - groupmin) / (groupmax - groupmin)
- **级别**: ALL
- **文档**: 无

#### 7.7 group_zscore
- **定义**: `group_zscore(x, group)`
- **描述**: 计算组Z分数 - 描述值与一组值均值关系的数值测量。Z分数以距离均值的标准差数量来衡量。zscore = (data - mean) / stddev，对于每个工具在其组内的x
- **级别**: ALL
- **文档**: 无

#### 7.8 group_neutralize
- **定义**: `group_neutralize(x, group)`
- **描述**: 对组中性化Alpha。这些组可以是子行业、行业、部门、国家或常数
- **级别**: ALL
- **文档**: /operators/group_neutralize
- **最后修改**: 2024-12-30T04:37:59.230728-05:00
- **详细说明**: 对组中性化alpha。normalize和group_neutralize之间的区别是，在normalize中，每个元素都减去该天所有工具所有值的均值，而在group_neutralize中，元素减去该天它所属工具组所有值的均值
- **功能**: 此操作符可能有助于减少相关性，取决于使用的中性化
- **示例**: 如果某个日期10个工具的字段x值为[3,2,6,5,8,9,1,4,8,0]，前5个工具属于一组，后5个属于另一组，则第一组均值 = (3+2+6+5+8)/5 = 4.8，第二组均值 = (9+1+4+8+0)/5 = 4.4。从各自组的工具中减去均值得到[3-4.8, 2-4.8, 6-4.8, 5-4.8, 8-4.8, 9-4.4, 1-4.4, 4-4.4, 8-4.4, 0-4.4] = [-1.8, -2.8, 1.2, 0.2, 3.2, 4.6, -3.4, -0.4, 3.6, -4.4]

#### 7.9 group_cartesian_product
- **定义**: `group_cartesian_product(g1, g2)`
- **描述**: 将两个组合并为一个组。如果原来g1和g2中有len_1和len_2个组索引，新组中将有len_1 * len_2个索引
- **级别**: 无
- **文档**: 无

## 总结

### 操作符统计
- **总计**: 80个操作符
- **有文档链接**: 21个操作符
- **有详细示例**: 15个操作符
- **最近更新**: 大部分在2024年12月30日至2025年4月1日之间

### 主要功能分类
1. **数据处理**: 回填、缺失值处理、异常值处理
2. **统计分析**: 相关性、回归、排名、标准化
3. **风险管理**: 温莎化、中性化、换手率控制
4. **性能优化**: 衰减、平滑、分组优化
5. **条件逻辑**: 条件选择、逻辑运算、阈值控制

### 使用建议
1. **数据质量**: 使用ts_backfill、group_backfill处理缺失数据
2. **异常值控制**: 使用winsorize、zscore、normalize减少异常值影响
3. **换手率管理**: 使用hump、ts_decay_linear、trade_when控制交易频率
4. **相关性管理**: 使用group_neutralize、vector_neut降低策略相关性
5. **性能提升**: 使用densify优化分组计算，使用rank改善夏普比率

这个完整的操作符文档为量化研究提供了全面的工具集，涵盖了从基础数学运算到高级统计分析和风险管理的各个方面。

### 2. 逻辑运算操作符 (Logical)

#### 基础逻辑运算
- **and**: `and(input1, input2)` - 逻辑与
- **or**: `or(input1, input2)` - 逻辑或
- **not**: `not(x)` - 逻辑非

#### 比较运算
- **equal**: `input1 == input2` - 等于
- **not_equal**: `input1 != input2` - 不等于
- **less**: `input1 < input2` - 小于
- **less_equal**: `input1 <= input2` - 小于等于
- **greater**: `input1 > input2` - 大于
- **greater_equal**: `input1 >= input2` - 大于等于

#### 条件判断
- **is_nan**: `is_nan(input)` - 判断是否为NaN
- **if_else**: `if_else(condition, value1, value2)` - 条件选择

### 3. 时间序列操作符 (Time Series)

#### 统计函数
- **ts_mean**: `ts_mean(x, d)` - 时间序列均值
- **ts_sum**: `ts_sum(x, d)` - 时间序列求和
- **ts_std_dev**: `ts_std_dev(x, d)` - 时间序列标准差
- **ts_max**: `ts_max(x, d)` - 时间序列最大值
- **ts_min**: `ts_min(x, d)` - 时间序列最小值
- **ts_product**: `ts_product(x, d)` - 时间序列乘积

#### 相关性分析
- **ts_corr**: `ts_corr(x, y, d)` - 时间序列相关性
- **ts_covariance**: `ts_covariance(y, x, d)` - 时间序列协方差
- **ts_regression**: `ts_regression(y, x, d, lag=0, rettype=0)` - 时间序列回归

#### 位置和排序
- **ts_rank**: `ts_rank(x, d, constant=0)` - 时间序列排名
- **ts_arg_max**: `ts_arg_max(x, d)` - 最大值位置索引
- **ts_arg_min**: `ts_arg_min(x, d)` - 最小值位置索引

#### 数据处理
- **ts_delay**: `ts_delay(x, d)` - 时间延迟
- **ts_delta**: `ts_delta(x, d)` - 时间差分
- **ts_backfill**: `ts_backfill(x, lookback=d, k=1, ignore="NAN")` - 数据回填
- **ts_zscore**: `ts_zscore(x, d)` - 时间序列Z分数

#### 高级处理
- **ts_decay_linear**: `ts_decay_linear(x, d, dense=false)` - 线性衰减
- **ts_scale**: `ts_scale(x, d, constant=0)` - 时间序列缩放
- **jump_decay**: `jump_decay(x, d, sensitivity=0.5, force=0.1)` - 跳跃衰减
- **hump**: `hump(x, hump=0.01)` - 限制变化幅度
- **kth_element**: `kth_element(x, d, k)` - 第k个元素

#### 其他
- **ts_av_diff**: `ts_av_diff(x, d)` - 与均值的差异
- **ts_count_nans**: `ts_count_nans(x, d)` - 计算NaN数量
- **ts_quantile**: `ts_quantile(x, d, driver="gaussian")` - 时间序列分位数
- **ts_step**: `ts_step(1)` - 天数计数器
- **days_from_last_change**: `days_from_last_change(x)` - 距离上次变化的天数
- **last_diff_value**: `last_diff_value(x, d)` - 最后不同值

#### 目标换手率优化
- **ts_target_tvr_decay**: `ts_target_tvr_decay(x, lambda_min=0, lambda_max=1, target_tvr=0.1)` - 目标换手率衰减
- **ts_target_tvr_delta_limit**: `ts_target_tvr_delta_limit(x, y, lambda_min=0, lambda_max=1, target_tvr=0.1)` - 目标换手率差分限制

### 4. 横截面操作符 (Cross Sectional)

#### 标准化
- **rank**: `rank(x, rate=2)` - 排名，返回0-1之间的值
- **zscore**: `zscore(x)` - Z分数标准化
- **normalize**: `normalize(x, useStd=false, limit=0.0)` - 标准化
- **quantile**: `quantile(x, driver=gaussian, sigma=1.0)` - 分位数转换

#### 缩放
- **scale**: `scale(x, scale=1, longscale=1, shortscale=1)` - 缩放到账面规模
- **scale_down**: `scale_down(x, constant=0)` - 缩放到0-1区间

#### 异常值处理
- **winsorize**: `winsorize(x, std=4)` - 温莎化处理

#### 向量运算
- **vector_neut**: `vector_neut(x, y)` - 向量中性化

### 5. 向量操作符 (Vector)

- **vec_sum**: `vec_sum(x)` - 向量求和
- **vec_avg**: `vec_avg(x)` - 向量平均值
- **vec_max**: `vec_max(x)` - 向量最大值
- **vec_min**: `vec_min(x)` - 向量最小值

### 6. 转换操作符 (Transformational)

- **bucket**: `bucket(rank(x), range="0,1,0.1" or buckets="2,5,6,7,10")` - 分桶操作
- **trade_when**: `trade_when(x, y, z)` - 条件交易

### 7. 分组操作符 (Group)

#### 统计函数
- **group_mean**: `group_mean(x, weight, group)` - 分组均值
- **group_max**: `group_max(x, group)` - 分组最大值
- **group_min**: `group_min(x, group)` - 分组最小值

#### 标准化
- **group_rank**: `group_rank(x, group)` - 分组排名
- **group_zscore**: `group_zscore(x, group)` - 分组Z分数
- **group_scale**: `group_scale(x, group)` - 分组缩放
- **group_neutralize**: `group_neutralize(x, group)` - 分组中性化

#### 数据处理
- **group_backfill**: `group_backfill(x, group, d, std=4.0)` - 分组回填

#### 其他
- **group_cartesian_product**: `group_cartesian_product(g1, g2)` - 分组笛卡尔积

## 重要特性说明

### 1. 适用级别 (Level)
大部分操作符标记为 "ALL"，表示适用于所有级别。

### 2. 作用域 (Scope)
所有操作符的作用域都是 ['REGULAR']，表示常规操作。

### 3. 文档链接
部分操作符提供了详细的文档链接，包含示例和详细说明。

### 4. 最后修改时间
文档显示了各操作符的最后修改时间，大部分在2024年12月30日到2025年4月1日之间更新。

## 使用建议

1. **性能优化**: 使用 `densify` 优化分组字段的计算效率
2. **风险控制**: 使用 `winsorize`, `zscore`, `normalize` 等操作符处理异常值
3. **换手率控制**: 使用 `hump`, `ts_decay_linear` 等操作符降低换手率
4. **相关性管理**: 使用 `group_neutralize`, `vector_neut` 等操作符降低相关性
5. **数据质量**: 使用 `ts_backfill`, `group_backfill` 等操作符处理缺失数据

这些操作符为量化研究提供了强大的工具集，涵盖了从基础数学运算到高级统计分析的各个方面。
