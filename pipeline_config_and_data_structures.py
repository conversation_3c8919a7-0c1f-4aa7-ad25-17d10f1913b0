#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 导入类型提示相关的模块，用于代码注解，增强可读性和健壮性
from typing import Dict, List, Optional, Any, Tuple
# 导入datetime类，用于处理日期和时间
from datetime import datetime
# 从dataclasses模块导入dataclass装饰器，它能自动为类生成特殊方法，如__init__()
from dataclasses import dataclass
# 导入pandas库，用于数据处理和CSV文件操作
import pandas as pd
# 导入logging库
import logging

# 默认的因子文件名，可在此处修改
DEFAULT_FACTORS_FILE = "filtered_shuffled_expressions.txt"

@dataclass # @dataclass装饰器：自动生成__init__、__repr__等样板代码
class MultiAlphaTask: # 定义一个类，用于封装一个Multi-Alpha回测任务的所有相关信息
    """MultiAlpha任务数据结构""" # 类的文档字符串，简要说明其用途
    batch_id: int # 任务批次的唯一标识符
    alpha_data: List[Dict] # 一个列表，其中每个字典代表一个要仿真的alpha的配置数据
    factor_ids: List[str] # 存储该批次任务中所有alpha的因子ID
    expressions: List[str] # 存储该批次任务中所有alpha的表达式字符串
    submit_time: Optional[datetime] = None # 任务提交到API的时间，初始化时为None
    complete_time: Optional[datetime] = None # 任务完成的时间，初始化时为None
    progress_url: Optional[str] = None # 用于查询任务进度的URL，初始化时为None
    simulation_id: Optional[str] = None # 整个Multi-Alpha仿真的ID，初始化时为None
    children: List[str] = None # Multi-Alpha任务分解成的子仿真任务的ID列表，初始化时为None
    alpha_ids: List[str] = None # 成功生成的Alpha ID列表，初始化时为None
    status: str = "PENDING"  # 任务的当前状态，默认为"PENDING"
    error_diagnosis: Optional[Dict] = None  # 存储错误诊断信息的字典，初始化时为None
    recovery_attempted: Optional[bool] = None  # 标记是否已尝试过错误恢复，初始化时为None

@dataclass # @dataclass装饰器：同样用于简化类的定义
class PerformanceStats: # 定义一个类，用于跟踪和统计流水线运行期间的性能指标
    """实时性能统计""" # 类的文档字符串
    total_tasks: int = 0 # 流水线需要处理的总任务数，默认为0
    pending_tasks: int = 0 # 尚未开始处理的待定任务数，默认为0
    submitted_tasks: int = 0 # 已经提交但尚未完成的任务数，默认为0
    running_tasks: int = 0 # 当前正在运行的任务数，默认为0
    completed_tasks: int = 0 # 已经成功完成的任务数，默认为0
    failed_tasks: int = 0 # 执行失败的任务数，默认为0
    total_alphas: int = 0 # 需要处理的alpha因子总数，默认为0
    extracted_alpha_ids: int = 0 # 已经成功提取的Alpha ID数量
    unique_extracted_alpha_ids: int = 0 # 已经成功提取的唯一Alpha ID数量
    start_time: Optional[datetime] = None # 流水线开始运行的时间，初始化时为None
    active_slots: int = 0 # 当前正在工作的并发槽位数，默认为0
    total_slots: int = 8 # 配置的总并发槽位数，默认为8
    
    @property # 将一个方法转换为可以直接访问的属性
    def completion_rate(self) -> float: # 计算并返回任务的完成率
        # 如果总任务数大于0，则计算完成百分比；否则返回0，避免除零错误
        return (self.completed_tasks / self.total_tasks * 100) if self.total_tasks > 0 else 0
    
    @property # 将方法转换为属性
    def success_rate(self) -> float: # 计算并返回已处理任务的成功率
        # 如果已完成和失败的任务总数大于0，则计算成功百分比；否则返回0
        return (self.completed_tasks / (self.completed_tasks + self.failed_tasks) * 100) if (self.completed_tasks + self.failed_tasks) > 0 else 0
    
    @property # 将方法转换为属性
    def elapsed_time(self) -> float: # 计算并返回从开始到现在的总耗时（秒）
        # 如果start_time已设置，则计算当前时间与开始时间的差值
        return (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
    
    @property # 将方法转换为属性
    def alphas_per_minute(self) -> float: # 计算并返回平均每分钟提取的alpha数量
        minutes = self.elapsed_time / 60 # 将总耗时从秒转换为分钟
        # 如果运行时间大于0，则计算每分钟的alpha提取速率；否则返回0
        return (self.extracted_alpha_ids / minutes) if minutes > 0 else 0
    @property # 计算基于唯一 Alpha ID 的每分钟速率
    def unique_alphas_per_minute(self) -> float:
        minutes = self.elapsed_time / 60
        return (self.unique_extracted_alpha_ids / minutes) if minutes > 0 else 0 

def create_alpha_simulation_data(factor_id: str, expression: str) -> Dict: # 方法，为单个alpha创建仿真所需的配置字典
    """创建单个Alpha的仿真数据""" # 方法的文档字符串
    return { # 返回一个字典
        'type': 'REGULAR', # 仿真类型
        'settings': { # 仿真设置
            'instrumentType': 'EQUITY', # 标的类型：股票
            'region': 'ASI', # 地区：亚洲
            'universe': 'MINVOL1M', # 股票池
            'delay': 1, # 延迟
            'decay': 0, # 衰减
            'neutralization': 'SUBINDUSTRY', # 中性化方法
            'truncation': 0.08, # 截断
            'pasteurization': 'ON', # 巴氏消毒
            'unitHandling': 'VERIFY', # 单位处理
            'nanHandling': 'OFF', # NaN值处理
            'language': 'FASTEXPR', # 表达式语言
            'visualization': False, # 是否可视化
            'maxTrade': 'ON' # 最大交易限制
        }, # 结束设置字典
        'regular': expression, # alpha表达式
        '_metadata': {'factor_id': factor_id} # 内部元数据，存储因子ID
    } # 结束返回的字典 

def load_alpha_factors(factors_file: str, logger: logging.Logger, limit: Optional[int] = None) -> List[Tuple[str, str]]: # 方法，加载Alpha因子
    """加载Alpha因子数据，支持单列表达式txt格式""" # 方法的文档字符串
    logger.info(f"📁 开始加载因子文件: {factors_file}") # 记录日志
    try: # 使用try-except处理文件加载错误
        if factors_file.endswith('.txt'): # 如果文件是.txt格式
            # 兼容单列表达式txt格式
            with open(factors_file, 'r', encoding='utf-8') as f: # 打开文件
                lines = [line.strip() for line in f if line.strip()] # 读取所有非空行
            if limit: # 如果设置了数量限制
                lines = lines[:limit] # 只取前limit行
            alpha_list = [(str(i+1), expr) for i, expr in enumerate(lines)] # 生成(因子ID, 表达式)元组列表
            logger.info(f"✅ 成功加载 {len(alpha_list)} 个Alpha因子 (txt单列模式)") # 记录成功日志
            if alpha_list: # 如果列表不为空
                logger.info("📋 前5个因子示例:") # 打印标题
                for i, (factor_id, expr) in enumerate(alpha_list[:5]): # 遍历前5个因子
                    display_expr = expr[:50] + "..." if len(expr) > 50 else expr # 截断过长的表达式
                    logger.info(f"  {i+1}. {factor_id}: {display_expr}") # 打印示例
            return alpha_list # 返回alpha列表
        else: # 如果文件不是.txt格式，则假定为CSV
            df = pd.read_csv(factors_file) # 使用pandas读取CSV文件
            logger.info(f"📊 原始数据形状: {df.shape}") # 记录数据形状
            # 检查列名并适配不同的文件格式
            if 'factor_id' in df.columns and 'expression' in df.columns: # 标准格式
                factor_id_col = 'factor_id' # 设置因子ID列名
                expression_col = 'expression' # 设置表达式列名
            elif 'alpha_expression' in df.columns: # 另一种常见格式
                factor_id_col = 'order' # 使用'order'列作为因子ID
                expression_col = 'alpha_expression' # 设置表达式列名
                if 'order' not in df.columns: # 如果没有'order'列
                    df['order'] = range(1, len(df) + 1) # 创建一个自增的'order'列
            else: # 如果格式不支持
                raise ValueError(f"文件格式不支持，需要包含 'factor_id'+'expression' 或 'alpha_expression' 列") # 抛出异常
            valid_data = df[[factor_id_col, expression_col]].dropna() # 选取有效数据并去除空值
            if limit: # 如果设置了数量限制
                valid_data = valid_data.head(limit) # 只取前limit行
            alpha_list = [] # 初始化alpha列表
            for _, row in valid_data.iterrows(): # 遍历数据行
                factor_id = str(row[factor_id_col]) # 获取因子ID
                expression = str(row[expression_col]) # 获取表达式
                alpha_list.append((factor_id, expression)) # 添加到列表
            logger.info(f"✅ 成功加载 {len(alpha_list)} 个Alpha因子") # 记录成功日志
            if alpha_list: # 如果列表不为空
                logger.info("📋 前5个因子示例:") # 打印标题
                for i, (factor_id, expr) in enumerate(alpha_list[:5]): # 遍历前5个
                    display_expr = expr[:50] + "..." if len(expr) > 50 else expr # 截断表达式
                    logger.info(f"  {i+1}. {factor_id}: {display_expr}") # 打印示例
            return alpha_list # 返回alpha列表
    except Exception as e: # 捕获任何异常
        logger.error(f"❌ 加载因子文件失败: {str(e)}") # 记录错误日志
        return [] # 返回空列表 