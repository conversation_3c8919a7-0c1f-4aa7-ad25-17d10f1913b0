# Alpha挖掘建议复现指南

基于 `ASI_fundamental28翻译后字段(1).xlsx` 的Y列alpha挖掘建议，使用 `operators_2025_master_analysis.md` 中的操作符进行复现。

## 核心操作符说明

### 1. 算术运算操作符
- `add(x, y)`: 加法运算
- `subtract(x, y)`: 减法运算  
- `multiply(x, y)`: 乘法运算
- `divide(x, y)`: 除法运算
- `max(x, y)`: 最大值
- `min(x, y)`: 最小值

### 2. 逻辑运算操作符
- `and(x, y)`: 逻辑与
- `or(x, y)`: 逻辑或
- `greater(x, y)`: 大于比较
- `less(x, y)`: 小于比较
- `if_else(condition, value1, value2)`: 条件选择

### 3. 时间序列操作符
- `ts_delay(x, d)`: d天前的值
- `ts_delta(x, d)`: x - ts_delay(x, d)
- `ts_mean(x, d)`: 过去d天均值
- `ts_sum(x, d)`: 过去d天求和
- `ts_std_dev(x, d)`: 过去d天标准差
- `ts_quantile(x, d)`: 时间序列分位数
- `ts_decay_linear(x, d)`: 线性衰减
- `ts_regression(y, x, d, rettype)`: 时间序列回归

### 4. 横截面操作符
- `rank(x)`: 排名，返回0-1之间的值
- `zscore(x)`: Z分数标准化
- `winsorize(x, std)`: 温莎化处理
- `scale(x)`: 缩放到账面规模

### 5. 分组操作符
- `group_neutralize(x, group)`: 分组中性化
- `group_rank(x, group)`: 分组排名

## Alpha策略复现

### 策略1: 高Beta+低估值组合
**原建议**: "高Beta+低估值组合，博取市场反弹弹性"

```python
# 高Beta选股
high_beta = greater(beta, ts_mean(beta, 252))

# 低估值选股 (低市盈率)
low_pe = less(pe_ratio, ts_mean(pe_ratio, 252))

# 组合条件
condition = and(high_beta, low_pe)

# 构建alpha信号
alpha = if_else(condition, 
               multiply(rank(beta), rank(reverse(pe_ratio))), 
               0)

# 标准化处理
alpha = zscore(alpha)
```

### 策略2: TTM经营现金流质量
**原建议**: "TTM经营现金流为正且环比上升"

```python
# TTM经营现金流为正
positive_cf = greater(operating_cash_flow_ttm, 0)

# 环比上升 (与上季度比较)
cf_growth = greater(operating_cash_flow_ttm, ts_delay(operating_cash_flow_ttm, 63))

# 组合条件
condition = and(positive_cf, cf_growth)

# 构建alpha信号
alpha = if_else(condition, 
               rank(operating_cash_flow_ttm), 
               0)

# 行业中性化
alpha = group_neutralize(alpha, industry)
```

### 策略3: EPS增长动量
**原建议**: "高EPS增长+低市盈率，成长alpha"

```python
# EPS增长率
eps_growth = divide(subtract(eps, ts_delay(eps, 252)), abs(ts_delay(eps, 252)))

# 高EPS增长
high_growth = greater(eps_growth, ts_quantile(eps_growth, 252))

# 低市盈率
low_pe = less(pe_ratio, ts_quantile(pe_ratio, 252))

# 组合条件
condition = and(high_growth, low_pe)

# 构建alpha信号
alpha = if_else(condition, 
               multiply(rank(eps_growth), rank(reverse(pe_ratio))), 
               0)

# 时间序列平滑
alpha = ts_decay_linear(alpha, 20)
```

### 策略4: 破净反转
**原建议**: "低市净率+高ROE，破净反转alpha"

```python
# 低市净率
low_pb = less(pb_ratio, 1.0)

# 高ROE
high_roe = greater(roe, ts_mean(roe, 252))

# 组合条件
condition = and(low_pb, high_roe)

# 构建alpha信号
alpha = if_else(condition, 
               multiply(rank(reverse(pb_ratio)), rank(roe)), 
               0)

# 异常值处理
alpha = winsorize(alpha, std=3)
alpha = zscore(alpha)
```

### 策略5: 毛利率质量
**原建议**: "高毛利+低估值，盈利质量alpha"

```python
# 高毛利率
high_margin = greater(gross_margin, ts_quantile(gross_margin, 252))

# 低估值 (低市销率)
low_ps = less(ps_ratio, ts_quantile(ps_ratio, 252))

# 组合条件
condition = and(high_margin, low_ps)

# 构建alpha信号
alpha = if_else(condition, 
               multiply(rank(gross_margin), rank(reverse(ps_ratio))), 
               0)

# 行业中性化
alpha = group_neutralize(alpha, industry)
alpha = scale(alpha)
```

### 策略6: ROE动量
**原建议**: "ROE持续改善+股价滞后，动量alpha"

```python
# ROE改善趋势 (使用回归斜率)
roe_trend = ts_regression(roe, ts_step(1), 8, rettype=2)
roe_improving = greater(roe_trend, 0)

# 股价动量滞后
price_momentum = ts_delta(close, 20)
price_lagging = less(price_momentum, ts_quantile(price_momentum, 252))

# 组合条件
condition = and(roe_improving, price_lagging)

# 构建alpha信号
alpha = if_else(condition, 
               multiply(rank(roe_trend), rank(reverse(price_momentum))), 
               0)

# 换手率控制
alpha = hump(alpha, hump=0.01)
```

### 策略7: 现金流盈利质量
**原建议**: "现金流>净利润，盈利质量alpha"

```python
# 现金流大于净利润
cf_quality = greater(operating_cash_flow, net_income)

# 盈利质量比率
quality_ratio = divide(operating_cash_flow, max(net_income, 0.01))

# 高质量盈利
high_quality = and(cf_quality, greater(quality_ratio, 1.2))

# 构建alpha信号
alpha = if_else(high_quality, 
               rank(quality_ratio), 
               0)

# 异常值处理和标准化
alpha = winsorize(alpha, std=4)
alpha = normalize(alpha, useStd=true)
```

## 高级策略组合

### 多因子估值修复策略
结合多个估值指标和质量指标：

```python
# 低估值因子组合
low_pe = rank(reverse(pe_ratio))
low_pb = rank(reverse(pb_ratio))
low_ps = rank(reverse(ps_ratio))

# 质量因子
high_roe = rank(roe)
high_margin = rank(gross_margin)

# 估值因子权重
valuation_score = multiply(add(add(low_pe, low_pb), low_ps), 1/3)

# 质量因子权重
quality_score = multiply(add(high_roe, high_margin), 1/2)

# 综合评分
alpha = multiply(add(valuation_score, quality_score), 1/2)

# 行业中性化和风险控制
alpha = group_neutralize(alpha, industry)
alpha = winsorize(alpha, std=3)
alpha = scale(alpha)
```

## 风险控制技巧

### 1. 异常值处理
```python
# 温莎化处理，限制极值
alpha = winsorize(alpha, std=4)

# Z分数标准化
alpha = zscore(alpha)
```

### 2. 换手率控制
```python
# 使用hump操作符限制变化幅度
alpha = hump(alpha, hump=0.01)

# 使用线性衰减平滑
alpha = ts_decay_linear(alpha, 10)
```

### 3. 行业中性化
```python
# 行业中性化
alpha = group_neutralize(alpha, industry)

# 分组排名
alpha = group_rank(alpha, sector)
```

## 使用注意事项

1. **数据字段映射**: 确保使用正确的字段名称，如 `beta`, `pe_ratio`, `operating_cash_flow` 等
2. **时间窗口选择**: 根据策略特性选择合适的回看天数，如252天(一年)、63天(一季度)
3. **风险控制**: 始终包含异常值处理和标准化步骤
4. **回测验证**: 在实际使用前进行充分的回测验证
5. **参数调优**: 根据实际表现调整参数，如分位数阈值、时间窗口等

这些策略复现了Excel文件Y列中的主要alpha挖掘建议，使用了operators文档中的标准操作符，可以直接在WorldQuant Brain平台上实现。
