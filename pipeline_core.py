#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 导入asyncio库，用于支持异步IO操作和并发
import asyncio
# 导入time库，用于处理时间相关的操作，如获取时间戳、延时
import time
# 导入logging库，用于记录程序运行日志
import logging
# 导入pandas库，用于数据处理和CSV文件操作
import pandas as pd
# 从datetime库导入datetime和timedelta，用于处理日期和时间
from datetime import datetime, timedelta
# 从typing库导入类型提示，增强代码可读性
from typing import Dict, List, Optional, Tuple, Any
# 从collections库导入defaultdict，用于创建带有默认值的字典
from collections import defaultdict
# 导入json库，用于处理JSON格式的数据
import json
# 导入os库，用于与操作系统交互，如文件路径操作
import os
# 导入random库，用于生成随机数
import random
# 导入csv库，用于处理CSV文件操作
import csv

# 从wqb包中导入WQBSession和wqb_logger，用于与WQB API交互和日志记录
from wqb.wqb_session import WQBSession, wqb_logger
# 从配置文件中导入数据结构类和配置
from pipeline_config_and_data_structures import MultiAlphaTask, PerformanceStats, create_alpha_simulation_data, load_alpha_factors, DEFAULT_FACTORS_FILE

class APIRateManager: # 定义一个类，用于管理对WQB API的请求频率
    """
    🚀 全局API请求频率管理器 - 完成即释放高效调度
    
    WQB API限制: 5个请求/分钟
    改进: 请求完成后立即释放槽位，而不是等待时间过期
    """ # 类的文档字符串，解释其设计目标和特性
    
    def __init__(self, max_requests: int = 5, time_window: int = 60): # 类的构造函数
        self.max_requests = max_requests  # #在一个时间窗口内允许的最大请求数
        self.time_window = time_window    # 时间窗口的长度（秒）
        self.request_timestamps = []      # 用于记录每个请求时间戳的列表（作为备用机制）
        self.active_requests = {}         # 一个字典，存储当前活跃的请求 {请求ID: 开始时间}
        self.next_request_id = 0          # 用于生成唯一请求ID的计数器
        # 使用Semaphore实现令牌桶限流，替代了原有的Lock和递归逻辑
        self.semaphore = asyncio.Semaphore(self.max_requests) # 初始化一个Semaphore，令牌数量为最大请求数
        
        # 🔥 新增：预测性限流参数
        self.safety_margin = 0.8          # 安全系数，仅使用80%的配额以避免触及硬限制
        self.request_history = []         # 记录近期请求历史，用于预测请求速率
        self.prediction_window = 30       # 用于预测的请求历史时间窗口（秒）
        
    async def acquire_request_slot(self) -> int: # 异步方法，用于获取一个API请求槽位
        """
        获取请求槽位，返回请求ID
        
        Returns:
            int: 请求ID，用于后续释放槽位
        """ # 方法的文档字符串
        # 使用Semaphore代替Lock获取令牌，如果令牌桶为空，则异步等待
        await self.semaphore.acquire() # 获取一个令牌
        request_id = self.next_request_id # 分配一个新的请求ID
        self.next_request_id += 1 # 增加ID计数器
        # 记录请求的激活时间，用于统计和调试
        self.active_requests[request_id] = time.time() # 记录当前时间
        return request_id # 返回分配的请求ID
    
    async def release_request_slot(self, request_id: int) -> None: # 异步方法，用于释放一个请求槽位
        """
        释放请求槽位 - 完成即释放机制
        
        Args:
            request_id: acquire_request_slot返回的请求ID
        """ # 方法的文档字符串
        # 释放请求槽位并归还令牌到Semaphore
        if request_id in self.active_requests: # 检查请求ID是否存在
            del self.active_requests[request_id] # 从活跃请求字典中移除
            self.semaphore.release() # 释放一个令牌，使其可用于其他请求
    
    async def acquire_multiple_slots(self, max_slots: int) -> List[int]: # 异步方法，用于一次性获取多个请求槽位
        """
        🚀 智能并行机制 - 获取多个可用槽位
        
        Args:
            max_slots: 最多想要获取的槽位数
            
        Returns:
            List[int]: 实际获取到的请求ID列表
        """ # 方法的文档字符串
        async with self.semaphore: # 使用Semaphore上下文管理器
            current_time = time.time() # 获取当前时间戳
            
            # 清理过期的活跃请求 (作为备用机制)
            expired_ids = [ # 列表推导式，找出已过期的请求ID
                req_id for req_id, start_time in self.active_requests.items() # 遍历活跃请求
                if current_time - start_time > self.time_window # 判断条件：当前时间与开始时间差大于时间窗口
            ] # 结束列表推导
            for req_id in expired_ids: # 遍历已过期的ID
                del self.active_requests[req_id] # 从活跃请求中删除
            
            # 计算当前可用的槽位数
            available_slots = self.max_requests - len(self.active_requests) # 可用数 = 最大请求数 - 当前活跃数
            actual_slots = min(max_slots, available_slots) # 实际可获取的槽位数取两者中的较小值
            
            # 获取槽位并分配ID
            acquired_ids = [] # 初始化一个空列表，用于存放获取到的ID
            for _ in range(actual_slots): # 循环实际可获取的次数
                request_id = self.next_request_id # 分配新ID
                self.next_request_id += 1 # ID计数器加一
                self.active_requests[request_id] = current_time # 记录请求的开始时间
                self.request_timestamps.append(current_time)  # (备用机制)将时间戳加入列表
                acquired_ids.append(request_id) # 将获取的ID加入列表
            
            return acquired_ids # 返回获取到的ID列表
    
    async def release_multiple_slots(self, request_ids: List[int]) -> None: # 异步方法，用于批量释放多个槽位
        """
        批量释放多个槽位
        
        Args:
            request_ids: 要释放的请求ID列表
        """ # 方法的文档字符串
        async with self.semaphore: # 使用Semaphore上下文管理器
            for request_id in request_ids: # 遍历要释放的ID列表
                if request_id in self.active_requests: # 检查ID是否存在
                    del self.active_requests[request_id] # 从活跃请求中删除
    
    def get_available_slots(self) -> int: # 方法，获取当前立即可用的槽位数量
        """
        获取当前立即可用的槽位数量
        
        Returns:
            int: 可用槽位数
        """ # 方法的文档字符串
        current_time = time.time() # 获取当前时间
        # 清理过期的请求 (备用机制)
        active_count = len([ # 计算仍然活跃的请求数量
            req_id for req_id, start_time in self.active_requests.items() # 遍历活跃请求
            if current_time - start_time < self.time_window # 判断条件
        ]) # 结束列表推导
        return max(0, self.max_requests - active_count) # 返回可用槽位数，最小为0
    
    def get_current_usage(self) -> Dict: # 方法，获取当前API使用情况的详细信息
        """获取当前使用情况""" # 方法的文档字符串
        current_time = time.time() # 获取当前时间
        
        # 🚀 基于活跃请求计算使用情况
        active_count = len(self.active_requests) # 获取当前活跃请求的总数
        
        # 计算下一次槽位可用的时间
        next_available = 0 # 初始化下次可用时间为0
        if active_count >= self.max_requests and self.request_timestamps: # 如果活跃请求已满且有时间戳记录
            # 如果没有空闲槽位，基于最早的时间戳计算
            oldest_time = min(self.request_timestamps) # 找到最早的请求时间
            next_available = max(0, self.time_window - (current_time - oldest_time)) # 计算剩余等待时间
        
        return { # 返回一个包含使用情况的字典
            'active_requests': active_count, # 当前活跃请求数
            'max_requests': self.max_requests, # 最大请求数限制
            'utilization': active_count / self.max_requests * 100, # 使用率百分比
            'next_slot_available_in': next_available, # 下一个槽位可用的秒数
            'immediate_slots': max(0, self.max_requests - active_count)  # 立即可用的槽位数
        }

# 🎯 全局API频率管理器实例
# 创建一个APIRateManager的全局实例，供整个应用使用
global_rate_manager = APIRateManager(max_requests=5, time_window=60)

class OptimizedMultiAlphaPipeline: # 定义主流水线类
    """优化的MultiAlpha流水线系统""" # 类的文档字符串
    
    def __init__(self, factors_file: str = DEFAULT_FACTORS_FILE, # 类的构造函数，接收多个配置参数
                 resume_from: int = 0, # 从指定的任务数开始继续，用于断点续传
                 random_seed: int = 42, # 随机种子，用于确保可复现性
                 max_multi_slots: int = 8, # MultiAlpha任务的最大并发槽位数
                 multialpha_delay: float = 2.0, # 每次提交MultiAlpha请求前的保护性延迟
                 polling_interval: float = 15.0, # 轮询任务状态的间隔时间（秒）
                 polling_concurrency: int = 1): # 错误诊断时并发查询子任务状态的数量
        self.factors_file = factors_file # 要处理的因子文件名
        self.max_slots = max_multi_slots  # 设置并发槽位的最大数量
        self.alphas_per_multi = 10 # 每个MultiAlpha任务包含的alpha因子数量
        self.resume_from = resume_from  # 断点续传的起始任务位置
        self.random_seed = random_seed # 设置随机种子
        self.multialpha_delay = multialpha_delay  # 设置MultiAlpha请求的延迟时间
        # 用户可配置：进度轮询间隔和错误诊断并发度
        self.polling_interval = polling_interval  # 设置轮询状态检查间隔（秒）
        self.polling_concurrency = polling_concurrency  # 设置错误诊断子请求并发数量
        
        # 设置随机种子以保证结果的可复现性
        random.seed(random_seed) # 设置随机数生成器的种子
        
        # 生成一个基于当前时间的时间戳，用于命名文件
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S") # 格式化时间戳
        
        # 初始化流水线的各个组件
        self.setup_logging() # 调用方法设置日志系统
        self.setup_wqb_session() # 调用方法设置WQB会话
        
        # 初始化任务队列和状态管理列表
        self.task_queue = asyncio.Queue() # 创建一个异步任务队列
        self.completed_tasks: List[MultiAlphaTask] = [] # 用于存储已完成任务的列表
        self.failed_tasks: List[MultiAlphaTask] = [] # 用于存储失败任务的列表
        self.active_tasks: Dict[int, MultiAlphaTask] = {}  # 一个字典，用于跟踪每个槽位上正在处理的任务
        
        # 🔥 新增：智能重试系统
        self.retry_alphas: List[Tuple[str, str]] = []  # 存储待重试的"好"Alpha（因子ID和表达式）
        self.blacklisted_alphas: List[str] = []  # 存储已确认有问题的Alpha表达式的黑名单
        self.retry_task_counter = 0  # 重试任务的计数器
        self.truly_error_alphas_count = 0  # 真正发生错误的Alpha数量计数器
        
        # 📝 错误Alpha记录系统
        self.error_alphas_file = f"error_alphas_{self.timestamp}.csv" # 基于时间戳生成错误记录文件名
        self.error_alphas_data = [] # 用于在内存中暂存错误数据的列表
        
        # 💾 进度记录系统
        self.progress_file = f"pipeline_progress_{self.timestamp}.json" # 基于时间戳生成进度记录文件名
        self.completed_task_count = 0 # 当前会话完成的任务数计数器
        
        # 初始化性能统计对象
        self.stats = PerformanceStats(total_slots=self.max_slots) # 创建PerformanceStats实例
        
        # 日志显示控制 - 优化显示频率
        self.last_stats_display = 0 # 上次显示统计信息的时间戳
        self.stats_display_interval = 30  # 设置统计信息显示的间隔为30秒
        
        # 🔥 新增：槽位启动控制
        self.slot_startup_delays = []  # 用于存储每个槽位启动延迟时间的列表
        self._calculate_slot_startup_delays() # 调用方法计算这些延迟
        
        self.logger.info(f"🚀 优化MultiAlpha流水线系统初始化") # 记录初始化日志
        self.logger.info(f"📁 目标文件: {self.factors_file}") # 记录目标文件名
        self.logger.info(f"🎲 随机种子: {self.random_seed}") # 记录随机种子
        if self.resume_from > 0: # 如果设置了断点续传
            self.logger.info(f"🔄 断点续传: 从第{self.resume_from}个任务开始") # 记录续传位置
        self.logger.info(f"⚙️ 配置: {self.max_slots}Multi槽位, {self.alphas_per_multi}Alpha/批次") # 记录配置信息
        self.logger.info(f"🛡️ 保护延迟: {self.multialpha_delay}秒/MultiAlpha请求") # 记录保护延迟
        self.logger.info(f"📊 槽位启动策略: 指数退避") # 记录槽位启动策略
        
        # 🔧 处理手动断点设置的基准保存
        self.handle_manual_resume_baseline() # 调用方法处理断点续传的基准
        # 初始化提取Alpha ID文件
        self.extracted_alpha_ids_file = "extracted_alpha_ids.csv"
        self._initialize_extracted_ids_file()
    
    def _calculate_slot_startup_delays(self): # 方法，用于计算每个槽位的启动延迟
        """计算每个槽位的启动延迟（指数退避）""" # 方法的文档字符串
        base_delay = 5.0  # 设置基础延迟时间为5秒
        for i in range(self.max_slots): # 遍历所有槽位
            if i == 0: # 对于第一个槽位
                delay = 0  # 立即启动，无延迟
            elif i == 1: # 对于第二个槽位
                delay = base_delay  # 延迟等于基础延迟（5秒）
            elif i == 2: # 对于第三个槽位
                delay = base_delay * 2  # 延迟翻倍（10秒）
            else: # 对于后续的槽位
                # 使用指数退避策略计算延迟，但设置一个上限
                delay = min(base_delay * (2 ** (i - 2)), 60)  # 延迟时间最多不超过60秒
            
            # 为延迟添加一个随机抖动（±20%），以避免所有槽位在完全相同的时间点同时启动
            jitter = delay * 0.2 * (random.random() - 0.5) * 2 # 计算抖动值
            final_delay = max(0, delay + jitter) # 最终延迟为计算延迟加上抖动值，确保不为负
            self.slot_startup_delays.append(final_delay) # 将计算出的延迟时间添加到列表中
        
        self.logger.info(f"📊 槽位启动延迟计划: {[f'{d:.1f}s' for d in self.slot_startup_delays]}") # 记录最终的槽位启动延迟计划
    
    def setup_logging(self): # 方法，用于设置和配置日志系统
        """设置优化的日志系统""" # 方法的文档字符串
        log_filename = f"pipeline_{self.timestamp}.log" # 基于时间戳生成日志文件名
        
        # 创建一个自定义的日志格式化器，用于在控制台输出带颜色的日志
        class ColoredFormatter(logging.Formatter): # 定义一个继承自logging.Formatter的类
            """彩色日志格式化器""" # 类的文档字符串
            
            COLORS = { # 定义一个字典，存储不同日志级别的颜色代码
                'INFO': '\033[32m',      # 绿色
                'WARNING': '\033[33m',   # 黄色
                'ERROR': '\033[31m',     # 红色
                'RESET': '\033[0m'       # 重置颜色
            } # 结束字典定义
            
            def format(self, record): # 重写format方法，用于自定义日志消息的格式
                if hasattr(record, 'no_color'): # 如果记录中有no_color属性
                    return super().format(record) # 使用默认格式
                
                color = self.COLORS.get(record.levelname, self.COLORS['RESET']) # 根据日志级别获取颜色
                reset = self.COLORS['RESET'] # 获取重置颜色的代码
                record.msg = f"{color}{record.msg}{reset}" # 为日志消息添加颜色
                return super().format(record) # 返回格式化后的日志记录
        
        # 配置基础的日志设置
        logging.basicConfig( # 调用basicConfig进行配置
            level=logging.INFO, # 设置日志记录的最低级别为INFO
            format='%(asctime)s - %(levelname)s - %(message)s', # 定义日志消息的格式
            handlers=[ # 指定日志处理器列表
                logging.FileHandler(log_filename, encoding='utf-8'), # 一个将日志写入文件的处理器
                logging.StreamHandler() # 一个将日志输出到控制台的处理器
            ] # 结束处理器列表
        ) # 结束basicConfig调用
        
        # 获取控制台处理器并进行单独配置
        console_handler = [h for h in logging.getLogger().handlers if isinstance(h, logging.StreamHandler)][0] # 找到控制台处理器
        console_handler.setLevel(logging.INFO) # 设置控制台只显示INFO及以上级别的日志
        console_handler.setFormatter(ColoredFormatter('%(levelname)s - %(message)s')) # 为控制台处理器设置彩色格式化器
        
        # 将其他库（如requests）的日志级别设为WARNING，以减少不必要的干扰
        logging.getLogger('urllib3').setLevel(logging.WARNING) # 设置urllib3库的日志级别
        logging.getLogger('requests').setLevel(logging.WARNING) # 设置requests库的日志级别
        
        self.logger = logging.getLogger(self.__class__.__name__) # 获取当前类的logger实例
        # 只初始化一次wqb_logger，避免重复创建
        self._wqb_logger_instance = None # 初始化wqb_logger实例为None
    
    @staticmethod # 静态方法装饰器，表示该方法不依赖于类的实例
    def find_latest_progress_file() -> Optional[str]: # 查找最新的进度文件
        """查找最新的进度文件""" # 方法的文档字符串
        import glob # 导入glob库，用于查找符合特定规则的文件路径名
        progress_files = glob.glob("pipeline_progress_*.json") # 查找所有匹配模式的进度文件
        if not progress_files: # 如果没有找到任何进度文件
            return None # 返回None
        
        # 按文件的最后修改时间排序，返回最新的那个文件
        latest_file = max(progress_files, key=os.path.getmtime) # 使用max函数和getmtime找到最新文件
        return latest_file # 返回最新文件的路径
    
    @staticmethod # 静态方法装饰器
    def load_progress_info(progress_file: str) -> Optional[Dict]: # 加载指定的进度文件信息
        """加载进度信息""" # 方法的文档字符串
        try: # 使用try-except块处理可能的文件读写错误
            with open(progress_file, 'r', encoding='utf-8') as f: # 打开文件
                return json.load(f) # 使用json库加载文件内容并返回
        except Exception: # 如果发生任何异常
            return None # 返回None
    
    @staticmethod # 静态方法装饰器
    def detect_resume_point() -> Tuple[int, Optional[Dict]]: # 自动检测断点续传的位置
        """自动检测断点续传位置 - 使用历史最大进度
        
        Returns:
            Tuple[int, Optional[Dict]]: (建议的resume_from, 进度信息)
        """ # 方法的文档字符串
        progress_file = OptimizedMultiAlphaPipeline.find_latest_progress_file() # 查找最新的进度文件
        if not progress_file: # 如果没找到
            return 0, None # 从头开始
        
        progress_info = OptimizedMultiAlphaPipeline.load_progress_info(progress_file) # 加载进度信息
        if not progress_info: # 如果加载失败
            return 0, None # 从头开始
        
        # 🔧 优先使用历史最大进度，以防止因意外中断导致断点回退
        completed_tasks = progress_info.get('completed_tasks', 0) # 获取已完成任务数
        max_progress_ever = progress_info.get('max_progress_ever', completed_tasks) # 获取历史最大进度
        suggested_resume = max(completed_tasks, max_progress_ever)  # 建议的续传点取两者的最大值
        
        total_tasks = progress_info.get('total_tasks', 0) # 获取总任务数
        
        # 在进度信息字典中添加一些计算出的辅助信息
        progress_info['suggested_resume'] = suggested_resume # 建议的续传点
        progress_info['progress_percentage'] = (suggested_resume / total_tasks * 100) if total_tasks > 0 else 0 # 进度百分比
        progress_info['remaining_tasks'] = total_tasks - suggested_resume # 剩余任务数
        progress_info['current_vs_max'] = f"当前:{completed_tasks}, 历史最大:{max_progress_ever}, 建议:{suggested_resume}" # 进度对比说明
        
        return suggested_resume, progress_info # 返回建议的续传点和进度信息
    
    def handle_manual_resume_baseline(self): # 方法，处理手动设置断点续传时的基准保存
        """处理手动断点设置的基准保存
        
        当用户手动设置断点时，如果这个断点比历史最大进度更大，
        立即保存这个新的基准，确保断点设置生效
        """ # 方法的文档字符串
        if self.resume_from <= 0: # 如果没有手动设置断点
            return  # 直接返回，无需处理
        
        # 检查是否存在历史进度文件
        latest_progress_file = self.find_latest_progress_file() # 查找最新进度文件
        historical_max_progress = 0 # 初始化历史最大进度为0
        
        if latest_progress_file: # 如果找到了进度文件
            progress_info = self.load_progress_info(latest_progress_file) # 加载信息
            if progress_info: # 如果信息有效
                historical_max_progress = max( # 计算历史最大进度
                    progress_info.get('completed_tasks', 0), # 取当前完成数
                    progress_info.get('max_progress_ever', 0) # 和历史最大进度中的较大值
                ) # 结束max调用
        
        # 如果手动设置的断点大于历史最大进度，则立即保存为新的基准
        if self.resume_from > historical_max_progress: # 判断条件
            self.logger.info(f"🔧 手动断点({self.resume_from})大于历史进度({historical_max_progress})，保存新基准") # 记录日志
            
            # 临时保存当前会话已完成的任务数
            original_completed = self.completed_task_count # 保存
            
            # 将当前会话完成数设为0，这样保存的总进度就是手动设置的resume_from值
            self.completed_task_count = 0 # 重置
            
            # 保存新的基准进度文件
            try: # 使用try-except处理可能的错误
                self.save_progress() # 调用保存进度的方法
                self.logger.info(f"✅ 手动断点基准已保存: 从第{self.resume_from}个任务开始") # 记录成功日志
            except Exception as e: # 如果发生异常
                self.logger.error(f"❌ 保存手动断点基准失败: {e}") # 记录错误日志
            
            # 恢复当前会话的完成任务数
            self.completed_task_count = original_completed # 恢复
        else: # 如果手动断点不大于历史进度
            self.logger.debug(f"📋 手动断点({self.resume_from})不大于历史进度({historical_max_progress})，无需更新基准") # 记录调试日志
    
    def save_error_alpha(self, factor_id: str, expression: str, error_status: str, # 方法，用于保存有问题的Alpha信息
                        error_message: str = "", batch_id: int = 0): # 接收因子ID、表达式、错误状态等信息
        """保存有问题的Alpha到CSV文件""" # 方法的文档字符串
        error_record = { # 创建一个字典来存储错误记录
            'timestamp': datetime.now().isoformat(), # 记录当前时间
            'batch_id': batch_id, # 记录批次ID
            'factor_id': factor_id, # 记录因子ID
            'expression': expression, # 记录表达式
            'error_status': error_status, # 记录错误状态
            'error_message': error_message # 记录错误消息
        } # 结束字典定义
        
        self.error_alphas_data.append(error_record) # 将错误记录添加到内存列表中
        
        # 增加真正错误Alpha的计数
        self.truly_error_alphas_count += 1 # 计数器加一
        
        # 立即将内存中的所有错误数据保存到CSV文件
        try: # 使用try-except处理可能的I/O错误
            df = pd.DataFrame(self.error_alphas_data) # 使用pandas创建一个DataFrame
            df.to_csv(self.error_alphas_file, index=False, encoding='utf-8') # 将DataFrame保存为CSV文件
        except Exception as e: # 如果发生异常
            self.logger.error(f"❌ 保存错误Alpha文件失败: {e}") # 记录错误日志
    
    def save_progress(self): # 方法，用于保存当前流水线的进度
        """保存当前进度 - 增量保存，确保不会回退""" # 方法的文档字符串
        # 🔧 计算真实的总进度：断点起始位置 + 当前会话完成数
        actual_total_progress = self.resume_from + self.completed_task_count # 计算总进度
        
        # 检查现有进度文件，确保不会用一个较小的进度覆盖已有的较大进度
        try: # 使用try-except处理文件操作错误
            if os.path.exists(self.progress_file): # 如果进度文件已存在
                with open(self.progress_file, 'r', encoding='utf-8') as f: # 打开文件
                    existing_data = json.load(f) # 加载已有数据
                    existing_progress = existing_data.get('completed_tasks', 0) # 获取已保存的进度
                    
                    # 如果当前计算的总进度不大于已保存的进度，则不执行保存操作
                    if actual_total_progress <= existing_progress: # 判断条件
                        self.logger.debug(f"📋 当前总进度({actual_total_progress})不大于已保存进度({existing_progress})，跳过保存") # 记录调试日志
                        return # 直接返回
        except Exception as e: # 如果发生异常
            self.logger.warning(f"⚠️ 读取现有进度文件失败: {e}") # 记录警告日志
        
        progress_data = { # 创建一个字典，存储当前的进度数据
            'timestamp': datetime.now().isoformat(), # 当前时间戳
            'completed_tasks': actual_total_progress,  # 保存真实的总进度
            'total_tasks': self.stats.total_tasks, # 总任务数
            'total_alphas': self.stats.total_alphas, # 总alpha数
            'extracted_alpha_ids': self.stats.extracted_alpha_ids, # 已提取的ID数
            'failed_tasks': self.stats.failed_tasks, # 失败任务数
            'random_seed': self.random_seed, # 随机种子
            'factors_file': self.factors_file, # 因子文件名
            'max_progress_ever': actual_total_progress,  # 同时更新历史最大进度
            'session_info': {  # 新增会话信息，方便调试
                'resume_from': self.resume_from, # 本次会话的起始点
                'session_completed': self.completed_task_count, # 本次会话完成的任务数
                'total_progress': actual_total_progress # 总进度
            } # 结束会话信息字典
        } # 结束进度数据字典
        
        try: # 使用try-except处理文件写入错误
            with open(self.progress_file, 'w', encoding='utf-8') as f: # 以写入模式打开进度文件
                json.dump(progress_data, f, indent=2, ensure_ascii=False) # 将进度数据以JSON格式写入文件
            self.logger.debug(f"💾 进度已保存: 总进度{actual_total_progress}个任务 (起始{self.resume_from} + 会话{self.completed_task_count})") # 记录调试日志
        except Exception as e: # 如果发生异常
            self.logger.error(f"❌ 保存进度文件失败: {e}") # 记录错误日志
    
    def setup_wqb_session(self, relogin=False): # 方法，设置WQB会话
        """设置WQB会话，relogin=True时只刷新session，不新建logger""" # 方法的文档字符串
        try: # 使用try-except处理可能的错误
            # 从mm.txt文件中读取认证信息（邮箱和密码）
            with open('mm.txt', 'r') as f: # 打开文件
                lines = f.read().strip().split('\n') # 读取并处理内容
                email = lines[0].strip() # 获取邮箱
                password = lines[1].strip() # 获取密码
            if not relogin: # 如果不是重登录
                self.logger.info(f"📧 创建WQB会话: {email}") # 记录日志
                # 只初始化一次wqb_logger，避免重复创建
                if self._wqb_logger_instance is None: # 判断实例是否已创建
                    from wqb.wqb_session import wqb_logger # 导入
                    self._wqb_logger_instance = wqb_logger(name=f"optimized_pipeline_{int(time.time())}") # 创建实例
                self.wqb_session = WQBSession((email, password), logger=self._wqb_logger_instance) # 创建WQB会话实例
            else: # 如果是重登录
                # 只刷新session，不新建logger
                self.wqb_session = WQBSession((email, password), logger=self._wqb_logger_instance) # 重新创建会话实例
                self.logger.info("WQBSession已重登录") # 记录日志
            # 测试认证是否成功
            auth_response = self.wqb_session.get_authentication() # 调用认证接口
            if auth_response and auth_response.status_code == 200: # 如果响应有效且状态码为200
                if not relogin: # 如果不是重登录
                    self.logger.info(f"✅ WQB认证成功! 状态码: {auth_response.status_code}") # 记录成功日志
            else: # 如果认证失败
                self.logger.error(f"❌ WQB认证失败: {auth_response.status_code if auth_response else 'No Response'}") # 记录失败日志
                raise Exception("WQB认证失败") # 抛出异常
        except Exception as e: # 捕获任何异常
            if not relogin: # 如果不是重登录
                self.logger.error(f"❌ 创建WQB会话异常: {e}") # 记录错误日志
            else: # 如果是重登录
                self.logger.error("WQBSession重登录失败") # 记录错误日志
            raise # 重新抛出异常
    
    async def warm_up_request(self):
        """预热请求，建立初始的请求节奏"""
        self.logger.info("🔥 开始请求预热...")
        try:
            request_id = await global_rate_manager.acquire_request_slot()
            try:
                response = self.wqb_session.get_authentication()
                if response and response.status_code == 200:
                    self.logger.info("✅ 预热请求成功")
                else:
                    self.logger.warning("⚠️ 预热请求失败，但继续执行")
            finally:
                await global_rate_manager.release_request_slot(request_id)
            await asyncio.sleep(2.0)
        except Exception as e:
            self.logger.warning(f"⚠️ 预热请求异常: {e}，继续执行")

    def create_multialpha_tasks(self, alpha_list: List[Tuple[str, str]]) -> List[MultiAlphaTask]:
        """将Alpha因子列表转换为MultiAlpha任务，支持断点续传"""
        self.logger.info(f"📦 创建MultiAlpha任务，每批{self.alphas_per_multi}个Alpha")
        # 支持断点续传：跳过已完成的任务
        if self.resume_from > 0:
            skip_count = self.resume_from * self.alphas_per_multi
            if skip_count < len(alpha_list):
                alpha_list = alpha_list[skip_count:]
                self.logger.info(f"🔄 断点续传: 跳过前{skip_count}个Alpha，剩余{len(alpha_list)}个")
            else:
                self.logger.warning(f"⚠️ 断点续传位置({skip_count})超出总Alpha数({len(alpha_list)})")
                return []
        tasks = []
        batch_id = self.resume_from + 1  # 从断点位置开始编号
        for i in range(0, len(alpha_list), self.alphas_per_multi):
            batch_alphas = alpha_list[i:i + self.alphas_per_multi]
            if len(batch_alphas) < 3:
                if tasks:
                    last_task = tasks[-1]
                    for factor_id, expression in batch_alphas:
                        last_task.factor_ids.append(factor_id)
                        last_task.expressions.append(expression)
                        last_task.alpha_data.append(create_alpha_simulation_data(factor_id, expression))
                    continue
                else:
                    self.logger.warning(f"⚠️ 跳过少于3个Alpha的批次: {len(batch_alphas)}")
                    continue
            factor_ids = [factor_id for factor_id, _ in batch_alphas]
            expressions = [expression for _, expression in batch_alphas]
            alpha_data = [create_alpha_simulation_data(factor_id, expression) for factor_id, expression in batch_alphas]
            task = MultiAlphaTask(
                batch_id=batch_id,
                alpha_data=alpha_data,
                factor_ids=factor_ids,
                expressions=expressions,
                children=[],
                alpha_ids=[]
            )
            tasks.append(task)
            batch_id += 1
        self.logger.info(f"✅ 创建{len(tasks)}个任务 (编号{self.resume_from + 1}-{batch_id - 1})")
        return tasks

    # 插入缺失的 slot_worker 与 process_multialpha_lifecycle 方法
    async def slot_worker(self, slot_id: int):  # 槽位工作协程，处理一个槽位的所有任务
        """槽位工作协程，处理一个槽位的所有任务"""
        # 🔥 获取该槽位的启动延迟
        startup_delay = self.slot_startup_delays[slot_id] if slot_id < len(self.slot_startup_delays) else 0  # 计算启动延迟
        if startup_delay > 0:  # 如果需要延迟启动
            self.logger.info(f"🔧 槽位{slot_id} 等待 {startup_delay:.1f} 秒后启动...")  # 记录延迟日志
            await asyncio.sleep(startup_delay)  # 等待启动延迟
        # 记录槽位正式启动
        self.logger.info(f"🔧 槽位{slot_id} 启动")  # 记录槽位启动
        # 🔥 槽位级别的初始随机延迟
        initial_delay = random.uniform(0, 5)  # 随机生成0-5秒的延迟
        await asyncio.sleep(initial_delay)  # 应用随机延迟
        while True:  # 持续处理任务循环
            try:  # 捕获执行过程中的异常
                task = await self.task_queue.get()  # 从队列获取下一个任务
                if task is None:  # 如果收到结束信号
                    break  # 跳出循环，结束槽位协程
                # 标记槽位繁忙并更新统计数据
                self.active_tasks[slot_id] = task  # 存储当前槽位的任务
                self.stats.active_slots += 1  # 活跃槽位计数+1
                self.stats.submitted_tasks += 1  # 提交任务计数+1
                self.stats.pending_tasks -= 1  # 待处理任务计数-1
                # 执行完整的 MultiAlpha 生命周期
                success = await self.process_multialpha_lifecycle(slot_id, task)  # 调用生命周期处理方法
                if success:  # 如果处理成功
                    self.completed_tasks.append(task)  # 添加到已完成任务列表
                    self.stats.completed_tasks += 1  # 完成任务计数+1
                    self.stats.running_tasks -= 1  # 运行任务计数-1
                    self.completed_task_count += 1  # 会话完成任务计数+1
                    if task.alpha_ids:  # 如果成功提取到了 Alpha ID
                        self.stats.extracted_alpha_ids += len(task.alpha_ids)  # 累加提取的 ID 数量
                    # 实时保存进度
                    self.save_progress()  # 调用保存进度方法
                    if self.completed_task_count % 10 == 0:  # 每完成 10 个任务
                        total_progress = self.resume_from + self.completed_task_count  # 计算总进度
                        self.logger.info(f"💾 断点进度已保存: 总进度{total_progress}个任务 (起始{self.resume_from} + 会话{self.completed_task_count})")  # 记录日志
                else:  # 如果处理失败
                    self.failed_tasks.append(task)  # 添加到失败任务列表
                    self.stats.failed_tasks += 1  # 失败任务计数+1
                    self.stats.running_tasks -= 1  # 运行任务计数-1
                # 清理槽位状态并标记任务完成
                del self.active_tasks[slot_id]  # 删除槽位任务记录
                self.stats.active_slots -= 1  # 活跃槽位计数-1
                self.task_queue.task_done()  # 标记队列任务完成
                # 根据需要显示进度
                await self.display_progress_if_needed()  # 调用显示进度方法
            except asyncio.CancelledError:  # 如果协程被取消
                self.logger.info(f"🛑 槽位{slot_id} 被取消")  # 记录取消日志
                break  # 跳出循环，结束槽位
            except Exception as e:  # 捕获其他异常
                self.logger.error(f"❌ [槽位{slot_id}] 处理异常: {e}")  # 记录错误日志
                # 清理状态
                if slot_id in self.active_tasks:  # 如果槽位仍有任务
                    failed_task = self.active_tasks[slot_id]  # 获取失败任务
                    failed_task.status = "FAILED"  # 标记任务状态为失败
                    self.failed_tasks.append(failed_task)  # 添加到失败任务列表
                    self.stats.failed_tasks += 1  # 失败任务计数+1
                    self.stats.running_tasks -= 1  # 运行任务计数-1
                    del self.active_tasks[slot_id]  # 删除槽位任务记录
                    self.stats.active_slots -= 1  # 活跃槽位计数-1
                self.task_queue.task_done()  # 标记队列任务完成
                await asyncio.sleep(5)  # 短暂休息后继续执行
        self.logger.info(f"🏁 槽位{slot_id} 结束")  # 记录槽位结束

    async def process_multialpha_lifecycle(self, slot_id: int, task: MultiAlphaTask) -> bool:  # 处理 MultiAlpha 的完整生命周期
        """处理 MultiAlpha 的完整生命周期"""
        try:  # 捕获生命周期处理过程中的异常
            # 第 1 步：提交 MultiAlpha
            success = await self.submit_multialpha(slot_id, task)  # 调用提交方法
            if not success:  # 如果提交失败
                return False  # 返回 False，提前退出
            # 第 2 步：等待任务完成
            success = await self.wait_for_completion(slot_id, task)  # 调用等待完成方法
            if not success:  # 如果等待失败
                return False  # 返回 False，提前退出
            # 更新统计：如果有 Alpha 数据
            if task.alpha_data:  # 如果存在 alpha_data
                self.stats.completed_tasks += 1  # 完成任务计数+1
                self.stats.running_tasks -= 1  # 运行任务计数-1
                self.completed_task_count += 1  # 会话完成任务计数+1
                self.stats.extracted_alpha_ids += len(task.alpha_data)  # 累加 alpha 数量
            # 第 3 步：提取 Alpha ID
            success = await self.extract_alpha_ids(slot_id, task)  # 调用提取方法
            if not success:  # 如果提取失败
                return False  # 返回 False，提前退出
            # 第 4 步：验证 Alpha ID
            await self.verify_alpha_ids(slot_id, task)  # 调用验证方法
            task.status = "COMPLETED"  # 标记任务完成状态
            task.complete_time = datetime.now()  # 记录完成时间
            return True  # 返回 True，表示成功
        except Exception as e:  # 捕获异常
            self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id} 生命周期异常: {e}")  # 记录错误日志
            task.status = "FAILED"  # 标记任务失败
            return False  # 返回 False

    async def run_pipeline(self, limit: Optional[int] = None) -> Dict:  # 异步方法，运行整个流水线
        """运行流水线系统"""
        start_time = time.time()  # 记录流水线开始时间
        self.stats.start_time = datetime.now()  # 记录性能统计的开始时间
        self.logger.info("🚀 启动优化MultiAlpha流水线系统")  # 打印启动日志
        self.logger.info("=" * 80)  # 打印分隔线
        try:  # 捕获整个流水线运行过程中的异常
            await self.warm_up_request()  # 执行预热请求，建立初始API节奏
            self.logger.info("📁 加载Alpha因子...")  # 打印加载因子日志
            alpha_list = load_alpha_factors(self.factors_file, self.logger, limit=limit)  # 加载Alpha因子
            # 去重步骤: 确保不会测试重复表达式
            unique_alpha_list = []
            seen_expr = set()
            for fid, expr in alpha_list:
                if expr not in seen_expr:
                    unique_alpha_list.append((fid, expr))
                    seen_expr.add(expr)
            if len(unique_alpha_list) < len(alpha_list):
                self.logger.info(f"💡 去除 {len(alpha_list)-len(unique_alpha_list)} 个重复Alpha表达式")
            alpha_list = unique_alpha_list
            if not alpha_list:  # 如果加载失败
                return {'status': 'failed', 'error': '无法加载Alpha因子'}  # 返回失败信息
            tasks = self.create_multialpha_tasks(alpha_list)  # 创建MultiAlpha任务
            if not tasks:  # 如果任务创建失败
                return {'status': 'failed', 'error': '无法创建任务'}  # 返回失败信息
            self.stats.total_tasks = len(tasks)  # 设置总任务数
            self.stats.pending_tasks = len(tasks)  # 设置待处理任务数
            self.stats.total_alphas = sum(len(task.alpha_data) for task in tasks)  # 统计总Alpha数
            for task in tasks:  # 遍历所有任务
                await self.task_queue.put(task)  # 将任务加入队列
            self.logger.info(f"🚀 开始渐进式启动 {self.max_slots} 个槽位...")  # 打印槽位启动日志
            workers = []  # 初始化工作协程列表
            for slot_id in range(self.max_slots):  # 启动所有槽位
                worker = asyncio.create_task(self.slot_worker(slot_id))  # 创建槽位协程
                workers.append(worker)  # 添加到协程列表
            await asyncio.sleep(5)  # 等待部分槽位启动
            self.display_realtime_stats()  # 显示初始统计
            await self.task_queue.join()  # 等待所有任务完成
            await self.finalize_remaining_retry_alphas()  # 处理剩余重试Alpha
            if self.stats.pending_tasks > 0:  # 如果还有待处理任务
                await self.task_queue.join()  # 继续等待
            for _ in range(self.max_slots):  # 发送结束信号
                await self.task_queue.put(None)  # 向队列发送None
            await asyncio.gather(*workers, return_exceptions=True)  # 等待所有槽位结束
            self.save_progress()  # 保存最终进度
            end_time = time.time()  # 记录结束时间
            duration = end_time - start_time  # 计算耗时
            final_stats = {  # 汇总最终统计信息
                'status': 'completed',
                'start_time': datetime.fromtimestamp(start_time),
                'end_time': datetime.fromtimestamp(end_time),
                'duration_seconds': duration,
                'total_tasks': len(tasks),
                'completed_tasks': len(self.completed_tasks),
                'failed_tasks': len(self.failed_tasks),
                'total_alphas': self.stats.total_alphas,
                'extracted_alpha_ids': self.stats.extracted_alpha_ids,
                'success_rate': self.stats.success_rate,
                'alphas_per_minute': self.stats.alphas_per_minute,
                'unique_extracted_alpha_ids': self.stats.unique_extracted_alpha_ids,
                'unique_alphas_per_minute': self.stats.unique_alphas_per_minute,
                'successful_alpha_ids': [aid for task in self.completed_tasks for aid in (task.alpha_ids or [])],
                'error_alphas_file': self.error_alphas_file,
                'progress_file': self.progress_file
            }
            self.display_final_report(final_stats)  # 显示最终报告
            return final_stats  # 返回统计结果
        except Exception as e:  # 捕获异常
            self.logger.error(f"❌ 流水线运行异常: {e}")  # 打印错误日志
            try:  # 异常退出时也要保存当前进度
                self.save_progress()  # 保存进度
                total_progress = self.resume_from + self.completed_task_count  # 计算总进度
                self.logger.info(f"💾 异常退出前已保存进度: 总进度{total_progress}个任务 (起始{self.resume_from} + 会话{self.completed_task_count})")  # 打印日志
            except:
                pass  # 忽略保存异常
            import traceback  # 导入traceback模块
            traceback.print_exc()  # 打印异常堆栈
            return {'status': 'failed', 'error': str(e)}  # 返回失败信息

    def display_final_report(self, stats: Dict):  # 显示最终报告
        """显示最终报告"""
        self.logger.info("=" * 80)  # 打印分隔线
        self.logger.info("🎯 优化MultiAlpha流水线最终报告")  # 打印标题
        self.logger.info("=" * 80)  # 打印分隔线
        self.logger.info(f"📊 总任务数: {stats['total_tasks']}")  # 打印总任务数
        self.logger.info(f"✅ 完成任务: {stats['completed_tasks']}")  # 打印完成任务数
        self.logger.info(f"❌ 失败任务: {stats['failed_tasks']}")  # 打印失败任务数
        self.logger.info(f"🎯 总Alpha数: {stats['total_alphas']}")  # 打印总Alpha数
        self.logger.info(f"🆔 提取Alpha ID: {stats['extracted_alpha_ids']}")  # 打印提取的Alpha ID数
        self.logger.info(f"📈 成功率: {stats['success_rate']:.1f}%")  # 打印成功率
        self.logger.info(f"⚡ 平均效率(唯一): {stats['unique_alphas_per_minute']:.1f} 唯一Alpha/分钟")  # 打印唯一平均效率
        duration_min = stats['duration_seconds'] / 60  # 计算总耗时（分钟）
        self.logger.info(f"⏱️ 总耗时: {duration_min:.2f} 分钟")  # 打印总耗时
        total_progress = self.resume_from + self.completed_task_count  # 计算总进度
        if self.resume_from > 0:  # 如果有断点续传
            self.logger.info(f"🔄 断点信息: 起始位置{self.resume_from} + 本次完成{self.completed_task_count} = 总进度{total_progress}")  # 打印断点信息
        else:
            self.logger.info(f"📊 总进度: {total_progress}个任务完成")  # 打印总进度
        if stats['successful_alpha_ids']:  # 如果有成功的Alpha ID
            sample_count = min(10, len(stats['successful_alpha_ids']))  # 取前10个示例
            self.logger.info(f"🆔 Alpha ID示例 (前{sample_count}个): {stats['successful_alpha_ids'][:sample_count]}")  # 打印示例
        if self.retry_task_counter > 0 or self.blacklisted_alphas:  # 如果有重试或黑名单
            self.logger.info("=" * 80)  # 打印分隔线
            self.logger.info("🧠 智能恢复汇总")  # 打印标题
            self.logger.info("=" * 80)  # 打印分隔线
            self.logger.info(f"🔄 创建的重试任务: {self.retry_task_counter}个")  # 打印重试任务数
            self.logger.info(f"❌ 真正错误的Alpha: {self.truly_error_alphas_count}个")  # 打印错误Alpha数
            self.logger.info(f"🚫 黑名单Alpha数量: {len(self.blacklisted_alphas)}个 (包含取消状态)")  # 打印黑名单数量
            recovered_alphas = 0  # 初始化恢复的Alpha计数
            for task in self.completed_tasks:  # 遍历已完成任务
                if task.batch_id < 0:  # 负数为重试任务
                    recovered_alphas += len(task.alpha_ids or [])  # 累加恢复的Alpha数
            if recovered_alphas > 0:  # 如果有恢复的Alpha
                self.logger.info(f"✨ 成功恢复的Alpha: {recovered_alphas}个")  # 打印恢复数
            if self.blacklisted_alphas:  # 如果有黑名单
                sample_count = min(3, len(self.blacklisted_alphas))  # 取前3个示例
                self.logger.warning("🚫 黑名单Alpha示例:")  # 打印标题
                for i, expr in enumerate(self.blacklisted_alphas[:sample_count]):  # 遍历示例
                    display_expr = expr[:80] + "..." if len(expr) > 80 else expr  # 截断
                    self.logger.warning(f"   {i+1}. {display_expr}")  # 打印示例
        if self.failed_tasks:  # 如果有失败任务
            self.logger.info("=" * 80)  # 打印分隔线
            self.logger.info("🔍 错误诊断汇总")  # 打印标题
            self.logger.info("=" * 80)  # 打印分隔线
            diagnosed_errors = []  # 初始化诊断错误列表
            all_cancelled_count = 0  # 初始化全部取消计数
            for task in self.failed_tasks:  # 遍历失败任务
                if hasattr(task, 'error_diagnosis') and task.error_diagnosis:  # 如果有诊断信息
                    if task.error_diagnosis.get('all_cancelled'):
                        all_cancelled_count += 1  # 全部取消计数+1
                    else:
                        diagnosed_errors.append({
                            'batch_id': task.batch_id,
                            'error_info': task.error_diagnosis
                        })
            if diagnosed_errors:  # 如果有具体诊断错误
                self.logger.info(f"🎯 已诊断的具体错误: {len(diagnosed_errors)}个")  # 打印数量
                for error in diagnosed_errors:  # 遍历错误
                    batch_id = error['batch_id']  # 获取批次ID
                    error_info = error['error_info']  # 获取错误信息
                    self.logger.error(f"   批次{batch_id}: {error_info.get('alpha_expression', 'Unknown')} - {error_info.get('status', 'Unknown')}")  # 打印错误
                    if 'error_message' in error_info:
                        self.logger.error(f"              错误消息: {error_info['error_message']}")  # 打印错误消息
            if all_cancelled_count > 0:  # 如果有全部取消
                self.logger.warning(f"⚠️ 全部取消的批次: {all_cancelled_count}个 (可能是参数或网络问题)")  # 打印警告
            self.logger.info(f"💡 建议: 检查具体的Alpha表达式语法或重新运行失败的批次")  # 打印建议
        self.logger.info("=" * 80)  # 打印分隔线

    async def finalize_remaining_retry_alphas(self):  # 处理剩余的重试Alpha（不足一个完整批次的）
        """处理剩余的重试Alpha（不足一个完整批次的）"""
        if not self.retry_alphas:  # 如果没有重试Alpha
            return  # 直接返回
        if len(self.retry_alphas) >= 3:  # 如果剩余Alpha大于等于3
            self.retry_task_counter += 1  # 重试任务计数+1
            retry_task = self.create_retry_task(self.retry_alphas, self.retry_task_counter)  # 创建重试任务
            await self.task_queue.put(retry_task)  # 加入任务队列
            self.stats.total_tasks += 1  # 总任务数+1
            self.stats.pending_tasks += 1  # 待处理任务数+1
            self.retry_alphas = []  # 清空重试队列

    async def verify_alpha_ids(self, slot_id: int, task):  # 验证Alpha ID（简化版）
        """验证Alpha ID（简化版）"""
        pass  # 简化验证，只做基本检查

    async def extract_alpha_ids(self, slot_id: int, task) -> bool:  # 串行Alpha ID提取（简化版）
        """串行Alpha ID提取（简化版）"""
        if not task.children:  # 如果没有children
            self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id}没有children")  # 打印错误
            return False  # 返回失败
        self.logger.info(f"🔍 [槽位{slot_id}] 开始串行提取{len(task.children)}个Alpha ID")  # 打印日志
        alpha_ids = []  # 初始化Alpha ID列表
        for i, child_id in enumerate(task.children):  # 遍历所有children
            try:
                child_url = f"https://api.worldquantbrain.com/simulations/{child_id}"  # 构造子仿真URL
                request_id = await global_rate_manager.acquire_request_slot()  # 获取API请求槽位
                try:
                    response = self.wqb_session.get(child_url, timeout=30)  # 发送GET请求
                finally:
                    await global_rate_manager.release_request_slot(request_id)  # 释放槽位
                if not response or (response and response.status_code == 429):  # 如果请求失败或被限流
                    retry_after = int(response.headers.get('Retry-After', 10)) if (response and response.headers and 'Retry-After' in response.headers) else 10  # 获取重试等待时间
                    self.logger.warning(f"🚫 [槽位{slot_id}] Alpha ID提取遇到频率限制或疑似429，自动重登录WQBSession，等待{retry_after}秒")  # 打印警告
                    try:
                        self.setup_wqb_session()  # 自动重登录
                    except Exception as e:
                        self.logger.error(f"❌ [槽位{slot_id}] 自动重登录失败: {e}")  # 打印错误
                    await asyncio.sleep(retry_after)  # 等待
                    retry_request_id = await global_rate_manager.acquire_request_slot()  # 再次获取槽位
                    try:
                        response = self.wqb_session.get(child_url, timeout=30)  # 再次请求
                    finally:
                        await global_rate_manager.release_request_slot(retry_request_id)  # 释放槽位
                if not response or response.status_code != 200:  # 如果仍然失败
                    alpha_ids.append(None)  # 添加None
                    continue  # 跳过
                child_data = response.json()  # 解析响应
                alpha_id = child_data.get('alpha')  # 获取Alpha ID
                alpha_ids.append(alpha_id if alpha_id else None)  # 添加到列表
            except Exception as e:
                self.logger.warning(f"⚠️ [槽位{slot_id}] Alpha ID提取异常: {child_id} - {e}")  # 打印警告
                try:
                    self.setup_wqb_session()  # 自动重登录
                except Exception as e:
                    self.logger.error(f"❌ [槽位{slot_id}] 自动重登录失败: {e}")  # 打印错误
                alpha_ids.append(None)  # 添加None
        valid_alpha_ids = [aid for aid in alpha_ids if aid]  # 过滤有效ID
        task.alpha_ids = valid_alpha_ids  # 赋值
        self.logger.info(f"✅ [槽位{slot_id}] 成功提取{len(valid_alpha_ids)}/{len(task.children)}个Alpha ID")  # 打印日志
        # 实时保存提取的Alpha ID到CSV文件
        self._append_new_alpha_ids(valid_alpha_ids)
        return len(valid_alpha_ids) > 0  # 返回是否有有效ID

    async def wait_for_completion(self, slot_id: int, task, max_wait: int = 600) -> bool:  # 等待MultiAlpha完成
        """等待MultiAlpha完成"""
        if not task.progress_url:  # 如果没有进度URL
            self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id}没有进度URL")  # 打印错误
            return False  # 返回失败
        start_time = time.time()  # 记录开始时间
        check_interval = self.polling_interval  # 轮询间隔
        while time.time() - start_time < max_wait:  # 在最大等待时间内循环
            try:
                request_id = await global_rate_manager.acquire_request_slot()  # 获取API请求槽位
                try:
                    response = self.wqb_session.get(task.progress_url, timeout=60)  # 查询进度
                finally:
                    await global_rate_manager.release_request_slot(request_id)  # 释放槽位
                response_text = ""
                try:
                    response_text = response.text if hasattr(response, 'text') else str(response.content)  # 获取响应文本
                except:
                    response_text = f"Status: {response.status_code if response else 'None'}"  # 获取状态
                is_rate_limit_error = (
                    (response and response.status_code == 429) or
                    (response_text and ("SIMULATION_LIMIT_EXCEEDED" in response_text or "429" in response_text or "max 3 tries ran out" in response_text or "rate limit" in response_text.lower()))
                )  # 判断是否限流
                if is_rate_limit_error or not response:  # 如果限流或无响应
                    retry_after = int(response.headers.get('Retry-After', 30)) if (response and response.headers and 'Retry-After' in response.headers) else 30  # 获取重试等待时间
                    if is_rate_limit_error:
                        retry_after = max(retry_after, 60) if "SIMULATION_LIMIT_EXCEEDED" in response_text else retry_after  # 特殊处理
                    self.logger.info("WQBSession已重登录")  # 打印日志
                    try:
                        self.setup_wqb_session(relogin=True)  # 重登录
                    except Exception:
                        self.logger.error("WQBSession重登录失败")  # 打印错误
                    continue  # 立即重试
                if response.status_code != 200:  # 如果不是200
                    await asyncio.sleep(check_interval)  # 等待
                    continue  # 继续轮询
                response_data = response.json()  # 解析响应
                status = response_data.get('status', 'UNKNOWN')  # 获取状态
                if status == 'COMPLETE':  # 如果完成
                    task.children = response_data.get('children', [])  # 获取children
                    return True  # 返回成功
                elif status == 'ERROR':  # 如果出错
                    self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id}失败: {response_data}")  # 打印错误
                    self.logger.info(f"🔍 [槽位{slot_id}] 开始自动错误诊断...")  # 打印日志
                    error_diagnosis = await self.quick_error_diagnosis(slot_id, task)  # 错误诊断
                    if error_diagnosis:
                        if error_diagnosis.get('all_cancelled'):
                            self.logger.warning(f"💡 [槽位{slot_id}] 所有Alpha都被取消，可能是参数或网络问题")  # 打印警告
                        else:
                            self.logger.error(f"🎯 [槽位{slot_id}] 已定位到具体错误Alpha，详情见上方日志")  # 打印错误
                            task.error_diagnosis = error_diagnosis  # 保存诊断
                    self.logger.info(f"🧠 [槽位{slot_id}] 启动智能错误恢复...")  # 打印日志
                    recovery_success = await self.intelligent_error_recovery(slot_id, task)  # 智能恢复
                    if recovery_success:
                        self.logger.info(f"✨ [槽位{slot_id}] 智能恢复完成，好的Alpha已加入重试队列")  # 打印日志
                        task.recovery_attempted = True  # 标记
                    else:
                        self.logger.warning(f"⚠️ [槽位{slot_id}] 智能恢复失败")  # 打印警告
                        task.recovery_attempted = False  # 标记
                    return False  # 返回失败
                await asyncio.sleep(check_interval)  # 继续等待
            except Exception as e:
                self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id}状态检查异常: {e}")  # 打印错误
                try:
                    self.setup_wqb_session(relogin=True)  # 重登录
                except Exception:
                    self.logger.error("WQBSession重登录失败")  # 打印错误
                await asyncio.sleep(check_interval)  # 等待
        self.logger.warning(f"⏰ [槽位{slot_id}] 任务{task.batch_id}等待超时")  # 打印警告
        return False  # 返回失败

    async def submit_multialpha(self, slot_id: int, task) -> bool:  # 提交MultiAlpha，支持429错误重试
        """提交MultiAlpha，支持429错误重试"""
        max_retries = 3  # 最大重试次数
        retry_count = 0  # 当前重试计数
        while retry_count < max_retries:  # 在最大重试次数内循环
            try:
                clean_alpha_data = []  # 初始化清理后的alpha数据
                for alpha in task.alpha_data:  # 遍历alpha数据
                    clean_alpha = {k: v for k, v in alpha.items() if k != '_metadata'}  # 移除元数据
                    clean_alpha_data.append(clean_alpha)  # 添加到新列表
                task.submit_time = datetime.now()  # 记录提交时间
                task.status = "SUBMITTED"  # 标记状态
                request_id = await global_rate_manager.acquire_request_slot()  # 获取API请求槽位
                if self.multialpha_delay > 0:  # 如果有延迟
                    self.logger.debug(f"🛡️ [槽位{slot_id}] 任务{task.batch_id} 执行{self.multialpha_delay}秒保护延迟...")  # 打印日志
                    await asyncio.sleep(self.multialpha_delay)  # 延迟
                try:
                    response = self.wqb_session.post(
                        'https://api.worldquantbrain.com/simulations',
                        json=clean_alpha_data,
                        timeout=120
                    )  # 提交POST请求
                finally:
                    await global_rate_manager.release_request_slot(request_id)  # 释放槽位
                response_text = ""
                try:
                    response_text = response.text if hasattr(response, 'text') else str(response.content)  # 获取响应文本
                except:
                    response_text = f"Status: {response.status_code if response else 'None'}"  # 获取状态
                is_rate_limit_error = (
                    (response and response.status_code == 429) or
                    (response_text and ("SIMULATION_LIMIT_EXCEEDED" in response_text or "429" in response_text or "max 3 tries ran out" in response_text or "rate limit" in response_text.lower()))
                )  # 判断是否限流
                if is_rate_limit_error or not response:  # 如果限流或无响应
                    retry_count += 1  # 重试计数+1
                    if retry_count < max_retries:  # 如果还可以重试
                        self.logger.warning(f"[槽位{slot_id}] 自动重登录WQBSession")  # 打印警告
                        try:
                            self.setup_wqb_session()  # 自动重登录
                        except Exception as e:
                            self.logger.error(f"[槽位{slot_id}] 自动重登录失败")  # 打印错误
                            await asyncio.sleep(10)  # 等待
                            continue  # 继续重试
                        wait_time = 60 if is_rate_limit_error else 10  # 设置等待时间
                        await asyncio.sleep(wait_time)  # 等待
                        continue  # 继续重试
                    else:
                        self.logger.error(f"[槽位{slot_id}] 任务{task.batch_id} 达到最大重试次数，请求仍返回空响应或429")  # 打印错误
                        return False  # 返回失败
                if response.status_code not in [200, 201, 202]:  # 如果不是成功状态码
                    error_msg = f"HTTP {response.status_code}: {response_text[:200]}"  # 构造错误信息
                    self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id}提交失败: {error_msg}")  # 打印错误
                    return False  # 返回失败
                task.progress_url = response.headers.get('Location')  # 获取进度URL
                response_data = response.json() if response.content else {}  # 解析响应
                task.simulation_id = response_data.get('id')  # 获取仿真ID
                task.children = response_data.get('children', [])  # 获取children
                task.status = "RUNNING"  # 标记状态
                self.stats.running_tasks += 1  # 运行任务计数+1
                self.stats.submitted_tasks -= 1  # 提交任务计数-1
                if retry_count > 0:  # 如果有重试
                    self.logger.info(f"✅ [槽位{slot_id}] 任务{task.batch_id}重试成功提交")  # 打印日志
                return True  # 返回成功
            except Exception as e:
                self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id}提交异常: {e}")  # 打印错误
                retry_count += 1  # 重试计数+1
                if retry_count < max_retries:  # 如果还可以重试
                    self.logger.warning(f"🔄 [槽位{slot_id}] 任务{task.batch_id}将在3秒后重试 ({retry_count}/{max_retries})")  # 打印警告
                    try:
                        self.setup_wqb_session()  # 自动重登录
                    except Exception as e:
                        self.logger.error(f"❌ [槽位{slot_id}] 自动重登录失败: {e}")  # 打印错误
                    await asyncio.sleep(3)  # 等待
                    continue  # 继续重试
                return False  # 返回失败
        return False  # 返回失败

    async def quick_error_diagnosis(self, slot_id: int, task) -> Optional[Dict]:  # Multi-Simulation错误快速定位
        """Multi-Simulation错误快速定位"""
        if not task.progress_url:  # 如果没有进度URL
            self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id}没有进度URL，无法进行错误诊断")  # 打印错误
            return None  # 返回None
        self.logger.info(f"🔍 [槽位{slot_id}] 开始错误快速定位：{task.progress_url}")  # 打印日志
        try:
            request_id = await global_rate_manager.acquire_request_slot()  # 获取API请求槽位
            try:
                response = self.wqb_session.get(task.progress_url, timeout=30)  # 查询进度
            finally:
                await global_rate_manager.release_request_slot(request_id)  # 释放槽位
            if not response or response.status_code != 200:  # 如果请求失败
                self.logger.error(f"❌ [槽位{slot_id}] 无法获取MultiAlpha状态: HTTP {response.status_code if response else 'None'}")  # 打印错误
                return None  # 返回None
            response_data = response.json()  # 解析响应
            status = response_data.get('status', 'UNKNOWN')  # 获取状态
            children = response_data.get('children', [])  # 获取children
            self.logger.info(f"🔍 [槽位{slot_id}] MultiAlpha状态: {status}, Children数量: {len(children)}")  # 打印日志
            if not children:  # 如果没有children
                self.logger.warning(f"⚠️ [槽位{slot_id}] 没有找到children，无法进行错误定位")  # 打印警告
                return None  # 返回None
            self.logger.info(f"🔍 [槽位{slot_id}] 开始并发错误诊断，共{len(children)}个child， 并发度 {self.polling_concurrency}")  # 打印日志
            error_info = None  # 初始化错误信息
            cancelled_count = 0  # 初始化取消计数
            sem = asyncio.Semaphore(self.polling_concurrency)  # 创建并发信号量
            async def check_child(i, child_id):  # 定义子任务检查函数
                child_url = f"https://api.worldquantbrain.com/simulations/{child_id}"  # 构造子仿真URL
                async with sem:  # 并发控制
                    req_id = await global_rate_manager.acquire_request_slot()  # 获取API请求槽位
                    try:
                        resp = self.wqb_session.get(child_url, timeout=30)  # 查询子仿真
                    finally:
                        await global_rate_manager.release_request_slot(req_id)  # 释放槽位
                    if not resp or resp.status_code != 200:  # 如果请求失败
                        return None  # 返回None
                    data = resp.json()  # 解析响应
                    status = data.get('status', 'UNKNOWN')  # 获取状态
                    if status == 'CANCELLED':  # 如果被取消
                        return ('CANCELLED', None)  # 返回取消
                    info = {
                        'child_id': child_id,
                        'child_index': i+1,
                        'status': status,
                        'full_response': data
                    }  # 构造信息
                    msg = data.get('message', '')  # 获取消息
                    if msg:
                        info['error_message'] = msg  # 添加错误消息
                    if i < len(task.expressions):
                        info['alpha_expression'] = task.expressions[i]  # 添加表达式
                    return ('ERROR', info)  # 返回错误
            tasks = [asyncio.create_task(check_child(i, cid)) for i, cid in enumerate(children)]  # 创建并发任务
            for future in asyncio.as_completed(tasks):  # 遍历完成的任务
                res = await future  # 获取结果
                if not res:
                    continue  # 跳过
                kind, info = res  # 解包
                if kind == 'CANCELLED':
                    cancelled_count += 1  # 取消计数+1
                    continue  # 跳过
                error_info = info  # 记录第一个错误
                for t in tasks:
                    if not t.done():
                        t.cancel()  # 取消其他任务
                break  # 跳出循环
            if error_info:  # 如果有错误
                self.logger.info(f"✅ [槽位{slot_id}] 错误定位完成!")  # 打印日志
                self.logger.info(f"📊 [槽位{slot_id}] 统计: {cancelled_count}个CANCELLED, 1个错误源已定位")  # 打印统计
                return error_info  # 返回错误信息
            else:
                self.logger.warning(f"⚠️ [槽位{slot_id}] 所有{len(children)}个children都是CANCELLED状态")  # 打印警告
                self.logger.warning(f"💡 [槽位{slot_id}] 可能是整体请求参数问题或网络问题")  # 打印建议
                return {
                    'all_cancelled': True,
                    'cancelled_count': cancelled_count,
                    'total_children': len(children)
                }  # 返回全部取消信息
        except Exception as e:
            self.logger.error(f"❌ [槽位{slot_id}] 错误诊断异常: {e}")  # 打印错误
            return None  # 返回None

    async def intelligent_error_recovery(self, slot_id: int, task) -> bool:  # 智能错误恢复
        """智能错误恢复"""
        if not task.progress_url:  # 如果没有进度URL
            self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id}没有进度URL，无法进行智能恢复")  # 打印错误
            return False  # 返回失败
        self.logger.info(f"🧠 [槽位{slot_id}] 开始智能错误恢复：任务{task.batch_id}")  # 打印日志
        try:
            response = self.wqb_session.get(task.progress_url, timeout=30)  # 查询进度
            if not response or response.status_code != 200:  # 如果请求失败
                self.logger.error(f"❌ [槽位{slot_id}] 无法获取MultiAlpha状态进行恢复")  # 打印错误
                return False  # 返回失败
            response_data = response.json()  # 解析响应
            children = response_data.get('children', [])  # 获取children
            if not children:  # 如果没有children
                self.logger.warning(f"⚠️ [槽位{slot_id}] 没有children，无法进行智能恢复")  # 打印警告
                return False  # 返回失败
            good_alphas = []  # 初始化好Alpha列表
            bad_alphas = []  # 初始化坏Alpha列表
            self.logger.info(f"🔍 [槽位{slot_id}] 分析{len(children)}个Alpha的状态...")  # 打印日志
            for i, child_id in enumerate(children):  # 遍历所有children
                try:
                    child_url = f"https://api.worldquantbrain.com/simulations/{child_id}"  # 构造子仿真URL
                    child_request_id = await global_rate_manager.acquire_request_slot()  # 获取API请求槽位
                    try:
                        child_response = self.wqb_session.get(child_url, timeout=30)  # 查询子仿真
                    finally:
                        await global_rate_manager.release_request_slot(child_request_id)  # 释放槽位
                    if not child_response or child_response.status_code != 200:  # 如果请求失败
                        self.logger.warning(f"⚠️ [槽位{slot_id}] Child {i+1}查询失败，假设为坏Alpha")  # 打印警告
                        if i < len(task.expressions):
                            bad_alphas.append((task.factor_ids[i], task.expressions[i]))  # 添加到坏Alpha
                        continue  # 跳过
                    child_data = child_response.json()  # 解析响应
                    child_status = child_data.get('status', 'UNKNOWN')  # 获取状态
                    if i < len(task.expressions):  # 获取对应的Alpha信息
                        factor_id = task.factor_ids[i]  # 获取因子ID
                        expression = task.expressions[i]  # 获取表达式
                        if child_status in ['COMPLETE', 'RUNNING', 'PENDING']:
                            good_alphas.append((factor_id, expression))  # 好Alpha
                            self.logger.debug(f"✅ [槽位{slot_id}] Alpha {i+1}: {expression[:50]}... - 状态良好")  # 打印日志
                        elif child_status == 'CANCELLED':
                            good_alphas.append((factor_id, expression))  # 被取消但非真正错误
                            self.logger.debug(f"🔄 [槽位{slot_id}] Alpha {i+1}: {expression[:50]}... - 被取消但非真正错误，将重新测试")  # 打印日志
                        else:
                            error_message = child_data.get('message', '')  # 获取错误信息
                            if error_message or child_status in ['ERROR', 'FAILED']:
                                bad_alphas.append((factor_id, expression))  # 真正错误
                                self.save_error_alpha(factor_id, expression, child_status, error_message, task.batch_id)  # 保存错误
                                self.logger.warning(f"❌ [槽位{slot_id}] Alpha {i+1}: {expression[:50]}... - 真正错误: {child_status} - {error_message}")  # 打印警告
                            else:
                                good_alphas.append((factor_id, expression))  # 无具体错误信息的状态
                                self.logger.debug(f"🔄 [槽位{slot_id}] Alpha {i+1}: {expression[:50]}... - 状态{child_status}但无错误信息，将重新测试")  # 打印日志
                except Exception as e:
                    self.logger.error(f"❌ [槽位{slot_id}] Child {i+1}分析异常: {e}")  # 打印错误
                    if i < len(task.expressions):
                        bad_alphas.append((task.factor_ids[i], task.expressions[i]))  # 添加到坏Alpha
            for factor_id, expression in bad_alphas:  # 将坏Alpha加入黑名单
                if expression not in self.blacklisted_alphas:
                    self.blacklisted_alphas.append(expression)  # 添加到黑名单
            for factor_id, expression in good_alphas:  # 将好Alpha加入重试队列
                if expression not in self.blacklisted_alphas:
                    self.retry_alphas.append((factor_id, expression))  # 添加到重试队列
            retry_tasks_created = await self.create_retry_tasks_if_possible()  # 检查是否可以创建重试任务
            self.logger.info(f"✅ [槽位{slot_id}] 智能恢复处理完成: 好Alpha {len(good_alphas)}个, 坏Alpha {len(bad_alphas)}个, 创建重试任务 {retry_tasks_created}个")  # 打印日志
            return True  # 返回成功
        except Exception as e:
            self.logger.error(f"❌ [槽位{slot_id}] 智能恢复异常: {e}")  # 打印错误
            return False  # 返回失败

    async def create_retry_tasks_if_possible(self) -> int:  # 创建重试任务（如果有足够的Alpha）
        """创建重试任务（如果有足够的Alpha）"""
        created_tasks = 0  # 初始化创建任务计数
        while len(self.retry_alphas) >= self.alphas_per_multi:  # 当重试队列中有足够的Alpha时
            retry_batch = self.retry_alphas[:self.alphas_per_multi]  # 取出前N个Alpha
            self.retry_alphas = self.retry_alphas[self.alphas_per_multi:]  # 移除已取出的
            self.retry_task_counter += 1  # 重试任务计数+1
            retry_task = self.create_retry_task(retry_batch, self.retry_task_counter)  # 创建重试任务
            await self.task_queue.put(retry_task)  # 加入任务队列
            self.stats.total_tasks += 1  # 总任务数+1
            self.stats.pending_tasks += 1  # 待处理任务数+1
            created_tasks += 1  # 创建任务计数+1
        return created_tasks  # 返回创建任务数

    def create_retry_task(self, alpha_batch, retry_id: int):  # 创建重试任务
        """创建重试任务"""
        factor_ids = [factor_id for factor_id, _ in alpha_batch]  # 获取因子ID列表
        expressions = [expression for _, expression in alpha_batch]  # 获取表达式列表
        alpha_data = [create_alpha_simulation_data(factor_id, expression) for factor_id, expression in alpha_batch]  # 创建仿真数据
        retry_task = MultiAlphaTask(
            batch_id=-retry_id,  # 负数表示重试任务
            alpha_data=alpha_data,
            factor_ids=factor_ids,
            expressions=expressions,
            children=[],
            alpha_ids=[]
        )  # 构造MultiAlphaTask
        return retry_task  # 返回重试任务

    def display_realtime_stats(self):
        """显示简化的实时统计信息"""
        # 计算预计完成时间（ETA）
        eta_str = "计算中..."  # 初始化ETA字符串
        if self.stats.completed_tasks > 5:  # 至少完成5个任务才计算ETA
            avg_time_per_task = self.stats.elapsed_time / self.stats.completed_tasks  # 计算平均每个任务耗时
            remaining_time = avg_time_per_task * self.stats.pending_tasks  # 预计剩余时间
            from datetime import datetime, timedelta  # 导入datetime
            eta = datetime.now() + timedelta(seconds=remaining_time)  # 预计完成时间
            eta_str = eta.strftime("%H:%M:%S")  # 格式化为字符串
        # 获取API频率使用情况
        rate_usage = global_rate_manager.get_current_usage()  # 获取API频率信息
        # 简化显示
        self.logger.info("=" * 60)  # 打印分隔线
        self.logger.info(f"📊 进度: {self.stats.completed_tasks}/{self.stats.total_tasks} ({self.stats.completion_rate:.1f}%) | "
                        f"成功率: {self.stats.success_rate:.1f}%")  # 打印进度和成功率
        self.logger.info(f"⚡ 唯一效率: {self.stats.unique_alphas_per_minute:.1f} 唯一Alpha/分钟 | "
                        f"唯一提取: {self.stats.unique_extracted_alpha_ids}/{self.stats.total_alphas}")  # 打印唯一效率和唯一提取数
        self.logger.info(f"🔧 槽位: {self.stats.active_slots}/{self.stats.total_slots} | "
                        f"待处理: {self.stats.pending_tasks} | 失败: {self.stats.failed_tasks}")  # 打印槽位和任务数
        # 智能并行状态展示
        immediate_slots = rate_usage['immediate_slots']  # 获取立即可用槽位数
        if immediate_slots >= 3:
            parallel_mode = "🚀 智能并行"
        elif immediate_slots >= 2:
            parallel_mode = "⚡ 部分并行"
        elif immediate_slots == 1:
            parallel_mode = "🔄 串行模式"
        else:
            parallel_mode = "🚫 等待模式"
        self.logger.info(f"📡 API频率: {rate_usage['active_requests']}/{rate_usage['max_requests']} ({rate_usage['utilization']:.1f}%) | "
                        f"{parallel_mode}: {immediate_slots}槽位 | 下次可用: {rate_usage['next_slot_available_in']:.1f}s")  # 打印API频率
        if len(self.error_alphas_data) > 0:
            self.logger.info(f"🚫 错误Alpha: {len(self.error_alphas_data)}个 (已保存至 {self.error_alphas_file})")  # 打印错误Alpha数
        self.logger.info(f"⏰ 预计完成: {eta_str}")  # 打印预计完成时间
        self.logger.info("=" * 60)  # 打印分隔线

    async def display_progress_if_needed(self):
        """根据需要显示进度统计"""
        import time  # 导入time模块
        current_time = time.time()  # 获取当前时间戳
        # 每隔指定时间显示一次统计
        if current_time - self.last_stats_display >= self.stats_display_interval:
            self.display_realtime_stats()  # 调用显示统计方法
            self.last_stats_display = current_time  # 更新上次显示时间

    def _initialize_extracted_ids_file(self):
        """Initialize the CSV file for storing extracted alpha IDs and load existing IDs."""
        if os.path.exists(self.extracted_alpha_ids_file):
            try:
                with open(self.extracted_alpha_ids_file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    next(reader, None)  # skip header
                    self.extracted_ids_set = set(row[0] for row in reader)
            except Exception:
                self.extracted_ids_set = set()
        else:
            try:
                with open(self.extracted_alpha_ids_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['alpha_id'])
            except Exception as e:
                self.logger.error(f"❌ 初始化Alpha ID CSV失败: {e}")
            self.extracted_ids_set = set()

    def _append_new_alpha_ids(self, alpha_ids: List[str]):
        """Append new alpha IDs to CSV file if not already present."""
        new_ids = [aid for aid in alpha_ids if aid not in self.extracted_ids_set]
        if not new_ids:
            return
        try:
            with open(self.extracted_alpha_ids_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for aid in new_ids:
                    writer.writerow([aid])
                    self.extracted_ids_set.add(aid)
        except Exception as e:
            self.logger.error(f"❌ 保存Alpha ID到CSV文件失败: {e}")
        # 更新唯一提取计数
        self.stats.unique_extracted_alpha_ids += len(new_ids)
