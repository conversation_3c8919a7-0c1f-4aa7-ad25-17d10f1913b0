#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
因子随机化工具 - 简化配置版本
用于随机打乱因子文件中的因子，支持多文件混合和自定义分布
只需修改下面的配置参数，然后直接运行即可
"""

# ==================== 配置参数区域 ====================
# 请根据需要修改以下参数

# 运行模式: 'single', 'mix', 'distribution'
MODE = 'single'

# 单文件模式配置
SINGLE_FILE_CONFIG = {
    'input_file': 'decoded_expressions (12).txt',  # 输入文件路径
    'output_file': 'randomized_factors.txt',       # 输出文件路径
    'num_factors': 100,                            # 要选择的因子数量，None表示全部
}

# 多文件混合模式配置
MIX_FILES_CONFIG = {
    'output_file': 'mixed_factors.txt',            # 输出文件路径
    'total_factors': 200,                          # 最终输出总数，None表示全部
    'files': [                                     # 文件列表配置
        {'file': 'decoded_expressions (12).txt', 'count': 150},
        {'file': 'another_factors.txt', 'count': 100},
        # 可以继续添加更多文件...
    ]
}

# 按比例分布模式配置
DISTRIBUTION_CONFIG = {
    'output_file': 'distributed_factors.txt',      # 输出文件路径
    'files': [                                     # 文件比例配置
        {'file': 'decoded_expressions (12).txt', 'ratio': 0.7},  # 70%
        {'file': 'another_factors.txt', 'ratio': 0.3},           # 30%
        # 可以继续添加更多文件...
    ]
}

# 随机种子（设置为None表示每次随机，设置数字确保结果可重现）
RANDOM_SEED = 42

# 是否显示详细日志
VERBOSE = True

# ==================== 配置参数区域结束 ====================

import random
import os
from typing import List, Dict, Tuple, Optional
import logging

class FactorRandomizer:
    """因子随机化工具类"""
    
    def __init__(self, seed: Optional[int] = None):
        """
        初始化随机化工具
        
        Args:
            seed: 随机种子，用于确保结果可重现
        """
        if seed is not None:
            random.seed(seed)
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('FactorRandomizer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_factors_from_file(self, file_path: str) -> List[str]:
        """
        从文件加载因子表达式
        
        Args:
            file_path: 因子文件路径
            
        Returns:
            因子表达式列表
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                factors = [line.strip() for line in f if line.strip()]
            self.logger.info(f"从 {file_path} 加载了 {len(factors)} 个因子")
            return factors
        except Exception as e:
            self.logger.error(f"加载文件 {file_path} 失败: {str(e)}")
            return []
    
    def randomize_single_file(self, 
                            input_file: str, 
                            output_file: str, 
                            num_factors: Optional[int] = None) -> bool:
        """
        随机化单个文件中的因子
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            num_factors: 要选择的因子数量，None表示使用全部
            
        Returns:
            是否成功
        """
        factors = self.load_factors_from_file(input_file)
        if not factors:
            return False
        
        # 随机打乱
        random.shuffle(factors)
        
        # 选择指定数量的因子
        if num_factors is not None:
            factors = factors[:min(num_factors, len(factors))]
            self.logger.info(f"选择了 {len(factors)} 个因子")
        
        # 保存到输出文件
        return self._save_factors_to_file(factors, output_file)
    
    def mix_and_randomize_files(self, 
                              file_configs: List[Dict], 
                              output_file: str, 
                              total_factors: Optional[int] = None) -> bool:
        """
        混合多个文件并随机化
        
        Args:
            file_configs: 文件配置列表，每个配置包含 {'file': 文件路径, 'count': 要选择的数量}
            output_file: 输出文件路径
            total_factors: 最终输出的总因子数量，None表示使用全部
            
        Returns:
            是否成功
        """
        all_factors = []
        
        for config in file_configs:
            file_path = config['file']
            count = config.get('count', None)
            
            factors = self.load_factors_from_file(file_path)
            if not factors:
                continue
            
            # 随机打乱当前文件的因子
            random.shuffle(factors)
            
            # 选择指定数量
            if count is not None:
                factors = factors[:min(count, len(factors))]
            
            all_factors.extend(factors)
            self.logger.info(f"从 {file_path} 添加了 {len(factors)} 个因子")
        
        if not all_factors:
            self.logger.error("没有加载到任何因子")
            return False
        
        # 对混合后的因子进行最终随机化
        random.shuffle(all_factors)
        
        # 选择最终数量
        if total_factors is not None:
            all_factors = all_factors[:min(total_factors, len(all_factors))]
            self.logger.info(f"最终选择了 {len(all_factors)} 个因子")
        
        return self._save_factors_to_file(all_factors, output_file)
    
    def randomize_with_distribution(self, 
                                  file_configs: List[Dict], 
                                  output_file: str) -> bool:
        """
        按照指定分布从多个文件中选择因子
        
        Args:
            file_configs: 文件配置列表，每个配置包含 {'file': 文件路径, 'ratio': 比例}
            output_file: 输出文件路径
            
        Returns:
            是否成功
        """
        # 首先加载所有文件并计算总数
        file_factors = {}
        total_available = 0
        
        for config in file_configs:
            file_path = config['file']
            factors = self.load_factors_from_file(file_path)
            if factors:
                file_factors[file_path] = factors
                total_available += len(factors)
        
        if not file_factors:
            self.logger.error("没有加载到任何因子")
            return False
        
        # 计算每个文件应该选择的数量
        all_factors = []
        
        for config in file_configs:
            file_path = config['file']
            ratio = config.get('ratio', 0)
            
            if file_path not in file_factors:
                continue
            
            factors = file_factors[file_path]
            count = int(len(factors) * ratio)
            
            # 随机选择
            random.shuffle(factors)
            selected = factors[:count]
            all_factors.extend(selected)
            
            self.logger.info(f"从 {file_path} 按比例 {ratio:.2%} 选择了 {len(selected)} 个因子")
        
        # 最终随机化
        random.shuffle(all_factors)
        
        return self._save_factors_to_file(all_factors, output_file)
    
    def _save_factors_to_file(self, factors: List[str], output_file: str) -> bool:
        """
        保存因子到文件
        
        Args:
            factors: 因子列表
            output_file: 输出文件路径
            
        Returns:
            是否成功
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for factor in factors:
                    f.write(factor + '\n')
            
            self.logger.info(f"成功保存 {len(factors)} 个因子到 {output_file}")
            return True
        except Exception as e:
            self.logger.error(f"保存文件 {output_file} 失败: {str(e)}")
            return False

def main():
    """主函数 - 使用配置参数运行"""
    print("🚀 因子随机化工具")
    print(f"运行模式: {MODE}")
    print(f"随机种子: {RANDOM_SEED}")
    print("-" * 50)

    # 创建随机化器
    randomizer = FactorRandomizer(seed=RANDOM_SEED)

    # 根据模式执行不同操作
    if MODE == 'single':
        print("📁 单文件随机化模式")
        config = SINGLE_FILE_CONFIG

        # 检查输入文件是否存在
        if not os.path.exists(config['input_file']):
            print(f"❌ 输入文件不存在: {config['input_file']}")
            return

        print(f"输入文件: {config['input_file']}")
        print(f"输出文件: {config['output_file']}")
        print(f"选择数量: {config['num_factors'] or '全部'}")

        success = randomizer.randomize_single_file(
            config['input_file'],
            config['output_file'],
            config['num_factors']
        )

    elif MODE == 'mix':
        print("🔀 多文件混合模式")
        config = MIX_FILES_CONFIG

        # 检查所有输入文件
        for file_config in config['files']:
            if not os.path.exists(file_config['file']):
                print(f"❌ 输入文件不存在: {file_config['file']}")
                return

        print(f"输出文件: {config['output_file']}")
        print(f"最终总数: {config['total_factors'] or '全部'}")
        print("文件配置:")
        for file_config in config['files']:
            print(f"  - {file_config['file']}: {file_config['count']} 个")

        success = randomizer.mix_and_randomize_files(
            config['files'],
            config['output_file'],
            config['total_factors']
        )

    elif MODE == 'distribution':
        print("📊 按比例分布模式")
        config = DISTRIBUTION_CONFIG

        # 检查所有输入文件
        for file_config in config['files']:
            if not os.path.exists(file_config['file']):
                print(f"❌ 输入文件不存在: {file_config['file']}")
                return

        print(f"输出文件: {config['output_file']}")
        print("文件比例配置:")
        for file_config in config['files']:
            print(f"  - {file_config['file']}: {file_config['ratio']:.1%}")

        success = randomizer.randomize_with_distribution(
            config['files'],
            config['output_file']
        )

    else:
        print(f"❌ 不支持的模式: {MODE}")
        print("支持的模式: 'single', 'mix', 'distribution'")
        return

    print("-" * 50)
    if success:
        print("✅ 随机化完成！")
    else:
        print("❌ 随机化失败！")

if __name__ == "__main__":
    main()
