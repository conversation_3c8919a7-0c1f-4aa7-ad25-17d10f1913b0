from datetime import datetime, timezone, timedelta

def beijing_to_eastern(beijing_time_str):
    """将北京时间转换为美东时间"""
    beijing_dt = datetime.strptime(beijing_time_str, '%Y-%m-%d %H:%M:%S')
    beijing_tz = timezone(timedelta(hours=8))
    beijing_dt = beijing_dt.replace(tzinfo=beijing_tz)
    eastern_tz = timezone(timedelta(hours=-5))
    eastern_dt = beijing_dt.astimezone(eastern_tz)
    return eastern_dt.isoformat()

# 测试转换
print("时区转换测试:")
print("="*50)

test_cases = [
    '2025-07-01 00:00:00',
    '2025-07-01 12:00:00', 
    '2025-07-20 23:59:59',
    '2025-01-01 09:30:00',
]

for beijing_time in test_cases:
    eastern_time = beijing_to_eastern(beijing_time)
    print(f"北京时间: {beijing_time} → 美东时间: {eastern_time}")

print("="*50)
print("转换说明: 北京时间(UTC+8) → 美东时间(UTC-5)，相差13小时")
