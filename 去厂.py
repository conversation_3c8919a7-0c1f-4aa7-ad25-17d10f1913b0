def get_sharpe_two_years(s, alpha_id):
    """获取Alpha在2021年和2022年的Sharpe值"""
    # 处理API限流
    while True:
        response = s.get(f"https://api.worldquantbrain.com/alphas/{alpha_id}/recordsets/yearly-stats")
        
        # 检查是否需要重试
        retry_after = response.headers.get("Retry-After")
        if retry_after:
            logging.debug(f"API限流，Alpha {alpha_id} 等待 {retry_after} 秒后重试")
            time.sleep(float(retry_after))
            continue
        break
        
    # 解析响应
    result = {'2021': None, '2022': None}
    
    try:
        data = response.json()
        for record in data.get("records", []):
            if not record:
                continue
            record_year = record[0]
            
            if record_year in ['2021', '2022']:  #任意俩年
                if len(record) > 6 and record[6] is not None:
                    sharpe = float(record[6])
                    result[record_year] = sharpe
                    logging.debug(f"Alpha {alpha_id} {record_year}年Sharpe值: {sharpe:.4f}")
                else:
                    logging.warning(f"Alpha {alpha_id} {record_year}年Sharpe值为空")
        
        if result['2021'] is None and result['2022'] is None:
            logging.warning(f"Alpha {alpha_id} 未找到2021年和2022年的数据")
        
        return result
        
    except (json.JSONDecodeError, IndexError, KeyError) as e:
        logging.error(f"解析Alpha {alpha_id} 数据失败: {str(e)}")
        return result