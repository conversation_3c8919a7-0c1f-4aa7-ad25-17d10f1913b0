# 修正表达式文件格式

# Alpha ID和对应的表达式
alpha_expressions = {
    'x5mOrNN': 'ts_delta(ts_zscore(pv37_all_adbs,240), 240)-ts_delta(ts_zscore(pv37_all_adbs,100), 100)',
    'L3lWE7v': 'ts_delta(ts_zscore(pv37_all_fbbs,240), 240)-ts_delta(ts_zscore(pv37_all_fbbs,100), 100)',
    '5YqxPlo': 'ts_delta(ts_zscore(pv37_all_fbds,240), 240)-ts_delta(ts_zscore(pv37_all_fbds,100), 100)',
    'MV5W5N6': 'ts_delta(ts_zscore(pv37_all_iads,240), 240)-ts_delta(ts_zscore(pv37_all_iads,100), 100)',
    'ZNW5j1d': 'ts_delta(ts_zscore(pv37_all_iabs,240), 240)-ts_delta(ts_zscore(pv37_all_iabs,100), 100)',
    'PKXag3J': 'ts_delta(ts_zscore(pv37_all_sopi,240), 240)-ts_delta(ts_zscore(pv37_all_sopi,100), 100)',
    'eEqp97M': 'ts_delta(ts_zscore(pv37_all_itpv,240), 240)-ts_delta(ts_zscore(pv37_all_itpv,100), 100)',
    'oZ95x7b': 'ts_delta(ts_zscore(pv37_all_sopp,240), 240)-ts_delta(ts_zscore(pv37_all_sopp,100), 100)',
    'k9L2qkd': 'ts_delta(ts_zscore(pv37_all_tait,240), 240)-ts_delta(ts_zscore(pv37_all_tait,100), 100)',
    'Og5P6KJ': 'ts_delta(ts_zscore(pv37_all_sbit,240), 240)-ts_delta(ts_zscore(pv37_all_sbit,100), 100)',
    'ExrXQ0K': 'ts_delta(ts_zscore(pv37_all_viat,240), 240)-ts_delta(ts_zscore(pv37_all_viat,100), 100)',
    'dvlLd6v': 'ts_delta(ts_zscore(pv37_intfv_all_caic,240), 240)-ts_delta(ts_zscore(pv37_intfv_all_caic,100), 100)',
    'Og5aReq': 'ts_delta(ts_zscore(pv37_intfv_all_cnin,240), 240)-ts_delta(ts_zscore(pv37_intfv_all_cnin,100), 100)',
    'gLopNoQ': 'ts_delta(ts_zscore(pv37_intfv_all_inds,240), 240)-ts_delta(ts_zscore(pv37_intfv_all_inds,100), 100)',
    'oZ9M365': 'ts_delta(ts_zscore(pv37_all_vbes,240), 240)-ts_delta(ts_zscore(pv37_all_vbes,100), 100)',
    'vPJVVA3': 'ts_delta(ts_zscore(pv37_intfv_all_tbie,240), 240)-ts_delta(ts_zscore(pv37_intfv_all_tbie,100), 100)',
    'x5maPVW': 'ts_delta(ts_zscore(pv37_intfv_mfm_inds,240), 240)-ts_delta(ts_zscore(pv37_intfv_mfm_inds,100), 100)',
    'AEOMvxR': 'ts_delta(ts_zscore(pv37_all_viac,240), 240)-ts_delta(ts_zscore(pv37_all_viac,100), 100)'
}

# 保存纯表达式文件（每行一个表达式）
with open('final_alpha_expressions.txt', 'w', encoding='utf-8') as f:
    for expression in alpha_expressions.values():
        f.write(f"{expression}\n")

# 保存带Alpha ID的版本（只保存表达式代码）
with open('final_alpha_expressions_with_ids.txt', 'w', encoding='utf-8') as f:
    for alpha_id, expression in alpha_expressions.items():
        f.write(f"{alpha_id}: {expression}\n")

print("✅ 表达式文件格式修正完成！")
print(f"📁 生成文件:")
print(f"  - final_alpha_expressions.txt (纯表达式，每行一个)")
print(f"  - final_alpha_expressions_with_ids.txt (带Alpha ID的表达式)")
print(f"📊 总计: {len(alpha_expressions)} 个高质量Alpha表达式")
